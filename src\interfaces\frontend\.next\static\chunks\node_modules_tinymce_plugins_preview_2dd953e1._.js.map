{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/preview/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const eq = (t) => (a) => t === a;\n    const isUndefined = eq(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const never = constant(false);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find$1 = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const findMap = (arr, f) => {\n        for (let i = 0; i < arr.length; i++) {\n            const r = f(arr[i], i);\n            if (r.isSome()) {\n                return r;\n            }\n        }\n        return Optional.none();\n    };\n\n    const contains = (str, substr, start = 0, end) => {\n        const idx = str.indexOf(substr, start);\n        if (idx !== -1) {\n            return isUndefined(end) ? true : idx + substr.length <= end;\n        }\n        else {\n            return false;\n        }\n    };\n\n    const cached = (f) => {\n        let called = false;\n        let r;\n        return (...args) => {\n            if (!called) {\n                called = true;\n                r = f.apply(null, args);\n            }\n            return r;\n        };\n    };\n\n    const DeviceType = (os, browser, userAgent, mediaMatch) => {\n        const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n        const isiPhone = os.isiOS() && !isiPad;\n        const isMobile = os.isiOS() || os.isAndroid();\n        const isTouch = isMobile || mediaMatch('(pointer:coarse)');\n        const isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n        const isPhone = isiPhone || isMobile && !isTablet;\n        const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n        const isDesktop = !isPhone && !isTablet && !iOSwebview;\n        return {\n            isiPad: constant(isiPad),\n            isiPhone: constant(isiPhone),\n            isTablet: constant(isTablet),\n            isPhone: constant(isPhone),\n            isTouch: constant(isTouch),\n            isAndroid: os.isAndroid,\n            isiOS: os.isiOS,\n            isWebView: constant(iOSwebview),\n            isDesktop: constant(isDesktop)\n        };\n    };\n\n    const firstMatch = (regexes, s) => {\n        for (let i = 0; i < regexes.length; i++) {\n            const x = regexes[i];\n            if (x.test(s)) {\n                return x;\n            }\n        }\n        return undefined;\n    };\n    const find = (regexes, agent) => {\n        const r = firstMatch(regexes, agent);\n        if (!r) {\n            return { major: 0, minor: 0 };\n        }\n        const group = (i) => {\n            return Number(agent.replace(r, '$' + i));\n        };\n        return nu$2(group(1), group(2));\n    };\n    const detect$3 = (versionRegexes, agent) => {\n        const cleanedAgent = String(agent).toLowerCase();\n        if (versionRegexes.length === 0) {\n            return unknown$2();\n        }\n        return find(versionRegexes, cleanedAgent);\n    };\n    const unknown$2 = () => {\n        return nu$2(0, 0);\n    };\n    const nu$2 = (major, minor) => {\n        return { major, minor };\n    };\n    const Version = {\n        nu: nu$2,\n        detect: detect$3,\n        unknown: unknown$2\n    };\n\n    const detectBrowser$1 = (browsers, userAgentData) => {\n        return findMap(userAgentData.brands, (uaBrand) => {\n            const lcBrand = uaBrand.brand.toLowerCase();\n            return find$1(browsers, (browser) => { var _a; return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase()); })\n                .map((info) => ({\n                current: info.name,\n                version: Version.nu(parseInt(uaBrand.version, 10), 0)\n            }));\n        });\n    };\n\n    const detect$2 = (candidates, userAgent) => {\n        const agent = String(userAgent).toLowerCase();\n        return find$1(candidates, (candidate) => {\n            return candidate.search(agent);\n        });\n    };\n    // They (browser and os) are the same at the moment, but they might\n    // not stay that way.\n    const detectBrowser = (browsers, userAgent) => {\n        return detect$2(browsers, userAgent).map((browser) => {\n            const version = Version.detect(browser.versionRegexes, userAgent);\n            return {\n                current: browser.name,\n                version\n            };\n        });\n    };\n    const detectOs = (oses, userAgent) => {\n        return detect$2(oses, userAgent).map((os) => {\n            const version = Version.detect(os.versionRegexes, userAgent);\n            return {\n                current: os.name,\n                version\n            };\n        });\n    };\n\n    const normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    const checkContains = (target) => {\n        return (uastring) => {\n            return contains(uastring, target);\n        };\n    };\n    const browsers = [\n        // This is legacy Edge\n        {\n            name: 'Edge',\n            versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n            search: (uastring) => {\n                return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n            }\n        },\n        // This is Google Chrome and Chromium Edge\n        {\n            name: 'Chromium',\n            brand: 'Chromium',\n            versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/, normalVersionRegex],\n            search: (uastring) => {\n                return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n            }\n        },\n        {\n            name: 'IE',\n            versionRegexes: [/.*?msie\\ ?([0-9]+)\\.([0-9]+).*/, /.*?rv:([0-9]+)\\.([0-9]+).*/],\n            search: (uastring) => {\n                return contains(uastring, 'msie') || contains(uastring, 'trident');\n            }\n        },\n        // INVESTIGATE: Is this still the Opera user agent?\n        {\n            name: 'Opera',\n            versionRegexes: [normalVersionRegex, /.*?opera\\/([0-9]+)\\.([0-9]+).*/],\n            search: checkContains('opera')\n        },\n        {\n            name: 'Firefox',\n            versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n            search: checkContains('firefox')\n        },\n        {\n            name: 'Safari',\n            versionRegexes: [normalVersionRegex, /.*?cpu os ([0-9]+)_([0-9]+).*/],\n            search: (uastring) => {\n                return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n            }\n        }\n    ];\n    const oses = [\n        {\n            name: 'Windows',\n            search: checkContains('win'),\n            versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n        },\n        {\n            name: 'iOS',\n            search: (uastring) => {\n                return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n            },\n            versionRegexes: [/.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/, /.*cpu os ([0-9]+)_([0-9]+).*/, /.*cpu iphone os ([0-9]+)_([0-9]+).*/]\n        },\n        {\n            name: 'Android',\n            search: checkContains('android'),\n            versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n        },\n        {\n            name: 'macOS',\n            search: checkContains('mac os x'),\n            versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n        },\n        {\n            name: 'Linux',\n            search: checkContains('linux'),\n            versionRegexes: []\n        },\n        { name: 'Solaris',\n            search: checkContains('sunos'),\n            versionRegexes: []\n        },\n        {\n            name: 'FreeBSD',\n            search: checkContains('freebsd'),\n            versionRegexes: []\n        },\n        {\n            name: 'ChromeOS',\n            search: checkContains('cros'),\n            versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n        }\n    ];\n    const PlatformInfo = {\n        browsers: constant(browsers),\n        oses: constant(oses)\n    };\n\n    const edge = 'Edge';\n    const chromium = 'Chromium';\n    const ie = 'IE';\n    const opera = 'Opera';\n    const firefox = 'Firefox';\n    const safari = 'Safari';\n    const unknown$1 = () => {\n        return nu$1({\n            current: undefined,\n            version: Version.unknown()\n        });\n    };\n    const nu$1 = (info) => {\n        const current = info.current;\n        const version = info.version;\n        const isBrowser = (name) => () => current === name;\n        return {\n            current,\n            version,\n            isEdge: isBrowser(edge),\n            isChromium: isBrowser(chromium),\n            // NOTE: isIe just looks too weird\n            isIE: isBrowser(ie),\n            isOpera: isBrowser(opera),\n            isFirefox: isBrowser(firefox),\n            isSafari: isBrowser(safari)\n        };\n    };\n    const Browser = {\n        unknown: unknown$1,\n        nu: nu$1,\n        edge: constant(edge),\n        chromium: constant(chromium),\n        ie: constant(ie),\n        opera: constant(opera),\n        firefox: constant(firefox),\n        safari: constant(safari)\n    };\n\n    const windows = 'Windows';\n    const ios = 'iOS';\n    const android = 'Android';\n    const linux = 'Linux';\n    const macos = 'macOS';\n    const solaris = 'Solaris';\n    const freebsd = 'FreeBSD';\n    const chromeos = 'ChromeOS';\n    // Though there is a bit of dupe with this and Browser, trying to\n    // reuse code makes it much harder to follow and change.\n    const unknown = () => {\n        return nu({\n            current: undefined,\n            version: Version.unknown()\n        });\n    };\n    const nu = (info) => {\n        const current = info.current;\n        const version = info.version;\n        const isOS = (name) => () => current === name;\n        return {\n            current,\n            version,\n            isWindows: isOS(windows),\n            // TODO: Fix capitalisation\n            isiOS: isOS(ios),\n            isAndroid: isOS(android),\n            isMacOS: isOS(macos),\n            isLinux: isOS(linux),\n            isSolaris: isOS(solaris),\n            isFreeBSD: isOS(freebsd),\n            isChromeOS: isOS(chromeos)\n        };\n    };\n    const OperatingSystem = {\n        unknown,\n        nu,\n        windows: constant(windows),\n        ios: constant(ios),\n        android: constant(android),\n        linux: constant(linux),\n        macos: constant(macos),\n        solaris: constant(solaris),\n        freebsd: constant(freebsd),\n        chromeos: constant(chromeos)\n    };\n\n    const detect$1 = (userAgent, userAgentDataOpt, mediaMatch) => {\n        const browsers = PlatformInfo.browsers();\n        const oses = PlatformInfo.oses();\n        const browser = userAgentDataOpt.bind((userAgentData) => detectBrowser$1(browsers, userAgentData))\n            .orThunk(() => detectBrowser(browsers, userAgent))\n            .fold(Browser.unknown, Browser.nu);\n        const os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n        const deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n        return {\n            browser,\n            os,\n            deviceType\n        };\n    };\n    const PlatformDetection = {\n        detect: detect$1\n    };\n\n    const mediaMatch = (query) => window.matchMedia(query).matches;\n    // IMPORTANT: Must be in a thunk, otherwise rollup thinks calling this immediately\n    // causes side effects and won't tree shake this away\n    // Note: navigator.userAgentData is not part of the native typescript types yet\n    let platform = cached(() => PlatformDetection.detect(window.navigator.userAgent, Optional.from((window.navigator.userAgentData)), mediaMatch));\n    const detect = () => platform();\n\n    const isMacOS = () => detect().os.isMacOS();\n    const isiOS = () => detect().os.isiOS();\n\n    const getPreventClicksOnLinksScript = () => {\n        const isMacOSOrIOS = isMacOS() || isiOS();\n        const fn = (isMacOSOrIOS) => {\n            document.addEventListener('click', (e) => {\n                for (let elm = e.target; elm; elm = elm.parentNode) {\n                    if (elm.nodeName === 'A') {\n                        const anchor = elm;\n                        const href = anchor.getAttribute('href');\n                        if (href && href.startsWith('#')) {\n                            e.preventDefault();\n                            const targetElement = document.getElementById(href.substring(1));\n                            if (targetElement) {\n                                targetElement.scrollIntoView({ behavior: 'smooth' });\n                            }\n                            return;\n                        }\n                        const isMetaKeyPressed = isMacOSOrIOS ? e.metaKey : e.ctrlKey && !e.altKey;\n                        if (!isMetaKeyPressed) {\n                            e.preventDefault();\n                        }\n                    }\n                }\n            }, false);\n        };\n        return `<script>(${fn.toString()})(${isMacOSOrIOS})</script>`;\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const getContentStyle = option('content_style');\n    const shouldUseContentCssCors = option('content_css_cors');\n    const getBodyClass = option('body_class');\n    const getBodyId = option('body_id');\n\n    const getPreviewHtml = (editor) => {\n        var _a;\n        let headHtml = '';\n        const encode = editor.dom.encode;\n        const contentStyle = (_a = getContentStyle(editor)) !== null && _a !== void 0 ? _a : '';\n        headHtml += `<base href=\"${encode(editor.documentBaseURI.getURI())}\">`;\n        const cors = shouldUseContentCssCors(editor) ? ' crossorigin=\"anonymous\"' : '';\n        global.each(editor.contentCSS, (url) => {\n            headHtml += '<link type=\"text/css\" rel=\"stylesheet\" href=\"' + encode(editor.documentBaseURI.toAbsolute(url)) + '\"' + cors + '>';\n        });\n        if (contentStyle) {\n            headHtml += '<style type=\"text/css\">' + contentStyle + '</style>';\n        }\n        const bodyId = getBodyId(editor);\n        const bodyClass = getBodyClass(editor);\n        const directionality = editor.getBody().dir;\n        const dirAttr = directionality ? ' dir=\"' + encode(directionality) + '\"' : '';\n        const previewHtml = ('<!DOCTYPE html>' +\n            '<html>' +\n            '<head>' +\n            headHtml +\n            '</head>' +\n            '<body id=\"' + encode(bodyId) + '\" class=\"mce-content-body ' + encode(bodyClass) + '\"' + dirAttr + '>' +\n            editor.getContent() +\n            getPreventClicksOnLinksScript() +\n            '</body>' +\n            '</html>');\n        return previewHtml;\n    };\n\n    const open = (editor) => {\n        const content = getPreviewHtml(editor);\n        const dataApi = editor.windowManager.open({\n            title: 'Preview',\n            size: 'large',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        name: 'preview',\n                        type: 'iframe',\n                        sandboxed: true,\n                        transparent: false\n                    }\n                ]\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'close',\n                    text: 'Close',\n                    primary: true\n                }\n            ],\n            initialData: {\n                preview: content\n            }\n        });\n        // Focus the close button, as by default the first element in the body is selected\n        // which we don't want to happen here since the body only has the iframe content\n        dataApi.focus('close');\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('mcePreview', () => {\n            open(editor);\n        });\n    };\n\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mcePreview');\n        editor.ui.registry.addButton('preview', {\n            icon: 'preview',\n            tooltip: 'Preview',\n            onAction,\n            context: 'any'\n        });\n        editor.ui.registry.addMenuItem('preview', {\n            icon: 'preview',\n            text: 'Preview',\n            onAction,\n            context: 'any'\n        });\n    };\n\n    var Plugin = () => {\n        global$1.add('preview', (editor) => {\n            register$1(editor);\n            register(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IAEzC,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,SAAS;IAEvB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,SAAS,CAAC,IAAI;QAChB,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,MAAM,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,MAAM,WAAW,SAAC,KAAK;YAAQ,yEAAQ,GAAG;QACtC,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ;QAChC,IAAI,QAAQ,CAAC,GAAG;YACZ,OAAO,YAAY,OAAO,OAAO,MAAM,OAAO,MAAM,IAAI;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IAEA,MAAM,SAAS,CAAC;QACZ,IAAI,SAAS;QACb,IAAI;QACJ,OAAO;6CAAI;gBAAA;;YACP,IAAI,CAAC,QAAQ;gBACT,SAAS;gBACT,IAAI,EAAE,KAAK,CAAC,MAAM;YACtB;YACA,OAAO;QACX;IACJ;IAEA,MAAM,aAAa,CAAC,IAAI,SAAS,WAAW;QACxC,MAAM,SAAS,GAAG,KAAK,MAAM,QAAQ,IAAI,CAAC,eAAe;QACzD,MAAM,WAAW,GAAG,KAAK,MAAM,CAAC;QAChC,MAAM,WAAW,GAAG,KAAK,MAAM,GAAG,SAAS;QAC3C,MAAM,UAAU,YAAY,WAAW;QACvC,MAAM,WAAW,UAAU,CAAC,YAAY,YAAY,WAAW;QAC/D,MAAM,UAAU,YAAY,YAAY,CAAC;QACzC,MAAM,aAAa,QAAQ,QAAQ,MAAM,GAAG,KAAK,MAAM,UAAU,IAAI,CAAC,eAAe;QACrF,MAAM,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;QAC5C,OAAO;YACH,QAAQ,SAAS;YACjB,UAAU,SAAS;YACnB,UAAU,SAAS;YACnB,SAAS,SAAS;YAClB,SAAS,SAAS;YAClB,WAAW,GAAG,SAAS;YACvB,OAAO,GAAG,KAAK;YACf,WAAW,SAAS;YACpB,WAAW,SAAS;QACxB;IACJ;IAEA,MAAM,aAAa,CAAC,SAAS;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,IAAI,OAAO,CAAC,EAAE;YACpB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACX,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,SAAS;QACnB,MAAM,IAAI,WAAW,SAAS;QAC9B,IAAI,CAAC,GAAG;YACJ,OAAO;gBAAE,OAAO;gBAAG,OAAO;YAAE;QAChC;QACA,MAAM,QAAQ,CAAC;YACX,OAAO,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;QACzC;QACA,OAAO,KAAK,MAAM,IAAI,MAAM;IAChC;IACA,MAAM,WAAW,CAAC,gBAAgB;QAC9B,MAAM,eAAe,OAAO,OAAO,WAAW;QAC9C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC7B,OAAO;QACX;QACA,OAAO,KAAK,gBAAgB;IAChC;IACA,MAAM,YAAY;QACd,OAAO,KAAK,GAAG;IACnB;IACA,MAAM,OAAO,CAAC,OAAO;QACjB,OAAO;YAAE;YAAO;QAAM;IAC1B;IACA,MAAM,UAAU;QACZ,IAAI;QACJ,QAAQ;QACR,SAAS;IACb;IAEA,MAAM,kBAAkB,CAAC,UAAU;QAC/B,OAAO,QAAQ,cAAc,MAAM,EAAE,CAAC;YAClC,MAAM,UAAU,QAAQ,KAAK,CAAC,WAAW;YACzC,OAAO,OAAO,UAAU,CAAC;gBAAc,IAAI;gBAAI,OAAO,YAAY,CAAC,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,EAAE;YAAG,GAC3I,GAAG,CAAC,CAAC,OAAS,CAAC;oBAChB,SAAS,KAAK,IAAI;oBAClB,SAAS,QAAQ,EAAE,CAAC,SAAS,QAAQ,OAAO,EAAE,KAAK;gBACvD,CAAC;QACL;IACJ;IAEA,MAAM,WAAW,CAAC,YAAY;QAC1B,MAAM,QAAQ,OAAO,WAAW,WAAW;QAC3C,OAAO,OAAO,YAAY,CAAC;YACvB,OAAO,UAAU,MAAM,CAAC;QAC5B;IACJ;IACA,mEAAmE;IACnE,qBAAqB;IACrB,MAAM,gBAAgB,CAAC,UAAU;QAC7B,OAAO,SAAS,UAAU,WAAW,GAAG,CAAC,CAAC;YACtC,MAAM,UAAU,QAAQ,MAAM,CAAC,QAAQ,cAAc,EAAE;YACvD,OAAO;gBACH,SAAS,QAAQ,IAAI;gBACrB;YACJ;QACJ;IACJ;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,OAAO,SAAS,MAAM,WAAW,GAAG,CAAC,CAAC;YAClC,MAAM,UAAU,QAAQ,MAAM,CAAC,GAAG,cAAc,EAAE;YAClD,OAAO;gBACH,SAAS,GAAG,IAAI;gBAChB;YACJ;QACJ;IACJ;IAEA,MAAM,qBAAqB;IAC3B,MAAM,gBAAgB,CAAC;QACnB,OAAO,CAAC;YACJ,OAAO,SAAS,UAAU;QAC9B;IACJ;IACA,MAAM,WAAW;QACb,sBAAsB;QACtB;YACI,MAAM;YACN,gBAAgB;gBAAC;aAAiC;YAClD,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,YAAY,SAAS,UAAU,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU;YAC7H;QACJ;QACA,0CAA0C;QAC1C;YACI,MAAM;YACN,OAAO;YACP,gBAAgB;gBAAC;gBAAmC;aAAmB;YACvE,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,aAAa,CAAC,SAAS,UAAU;YAC/D;QACJ;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAkC;aAA6B;YAChF,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,WAAW,SAAS,UAAU;YAC5D;QACJ;QACA,mDAAmD;QACnD;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAoB;aAAiC;YACtE,QAAQ,cAAc;QAC1B;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;aAAsC;YACvD,QAAQ,cAAc;QAC1B;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAoB;aAAgC;YACrE,QAAQ,CAAC;gBACL,OAAO,CAAC,SAAS,UAAU,aAAa,SAAS,UAAU,UAAU,KAAK,SAAS,UAAU;YACjG;QACJ;KACH;IACD,MAAM,OAAO;QACT;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAwC;QAC7D;QACA;YACI,MAAM;YACN,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,aAAa,SAAS,UAAU;YAC9D;YACA,gBAAgB;gBAAC;gBAAuC;gBAAgC;aAAsC;QAClI;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAoC;QACzD;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAsC;QAC3D;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YAAE,MAAM;YACJ,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAkC;QACvD;KACH;IACD,MAAM,eAAe;QACjB,UAAU,SAAS;QACnB,MAAM,SAAS;IACnB;IAEA,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,KAAK;IACX,MAAM,QAAQ;IACd,MAAM,UAAU;IAChB,MAAM,SAAS;IACf,MAAM,YAAY;QACd,OAAO,KAAK;YACR,SAAS;YACT,SAAS,QAAQ,OAAO;QAC5B;IACJ;IACA,MAAM,OAAO,CAAC;QACV,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,YAAY,CAAC,OAAS,IAAM,YAAY;QAC9C,OAAO;YACH;YACA;YACA,QAAQ,UAAU;YAClB,YAAY,UAAU;YACtB,kCAAkC;YAClC,MAAM,UAAU;YAChB,SAAS,UAAU;YACnB,WAAW,UAAU;YACrB,UAAU,UAAU;QACxB;IACJ;IACA,MAAM,UAAU;QACZ,SAAS;QACT,IAAI;QACJ,MAAM,SAAS;QACf,UAAU,SAAS;QACnB,IAAI,SAAS;QACb,OAAO,SAAS;QAChB,SAAS,SAAS;QAClB,QAAQ,SAAS;IACrB;IAEA,MAAM,UAAU;IAChB,MAAM,MAAM;IACZ,MAAM,UAAU;IAChB,MAAM,QAAQ;IACd,MAAM,QAAQ;IACd,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,WAAW;IACjB,iEAAiE;IACjE,wDAAwD;IACxD,MAAM,UAAU;QACZ,OAAO,GAAG;YACN,SAAS;YACT,SAAS,QAAQ,OAAO;QAC5B;IACJ;IACA,MAAM,KAAK,CAAC;QACR,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,OAAO,CAAC,OAAS,IAAM,YAAY;QACzC,OAAO;YACH;YACA;YACA,WAAW,KAAK;YAChB,2BAA2B;YAC3B,OAAO,KAAK;YACZ,WAAW,KAAK;YAChB,SAAS,KAAK;YACd,SAAS,KAAK;YACd,WAAW,KAAK;YAChB,WAAW,KAAK;YAChB,YAAY,KAAK;QACrB;IACJ;IACA,MAAM,kBAAkB;QACpB;QACA;QACA,SAAS,SAAS;QAClB,KAAK,SAAS;QACd,SAAS,SAAS;QAClB,OAAO,SAAS;QAChB,OAAO,SAAS;QAChB,SAAS,SAAS;QAClB,SAAS,SAAS;QAClB,UAAU,SAAS;IACvB;IAEA,MAAM,WAAW,CAAC,WAAW,kBAAkB;QAC3C,MAAM,WAAW,aAAa,QAAQ;QACtC,MAAM,OAAO,aAAa,IAAI;QAC9B,MAAM,UAAU,iBAAiB,IAAI,CAAC,CAAC,gBAAkB,gBAAgB,UAAU,gBAC9E,OAAO,CAAC,IAAM,cAAc,UAAU,YACtC,IAAI,CAAC,QAAQ,OAAO,EAAE,QAAQ,EAAE;QACrC,MAAM,KAAK,SAAS,MAAM,WAAW,IAAI,CAAC,gBAAgB,OAAO,EAAE,gBAAgB,EAAE;QACrF,MAAM,aAAa,WAAW,IAAI,SAAS,WAAW;QACtD,OAAO;YACH;YACA;YACA;QACJ;IACJ;IACA,MAAM,oBAAoB;QACtB,QAAQ;IACZ;IAEA,MAAM,aAAa,CAAC,QAAU,OAAO,UAAU,CAAC,OAAO,OAAO;IAC9D,kFAAkF;IAClF,qDAAqD;IACrD,+EAA+E;IAC/E,IAAI,WAAW,OAAO,IAAM,kBAAkB,MAAM,CAAC,OAAO,SAAS,CAAC,SAAS,EAAE,SAAS,IAAI,CAAE,OAAO,SAAS,CAAC,aAAa,GAAI;IAClI,MAAM,SAAS,IAAM;IAErB,MAAM,UAAU,IAAM,SAAS,EAAE,CAAC,OAAO;IACzC,MAAM,QAAQ,IAAM,SAAS,EAAE,CAAC,KAAK;IAErC,MAAM,gCAAgC;QAClC,MAAM,eAAe,aAAa;QAClC,MAAM,KAAK,CAAC;YACR,SAAS,gBAAgB,CAAC,SAAS,CAAC;gBAChC,IAAK,IAAI,MAAM,EAAE,MAAM,EAAE,KAAK,MAAM,IAAI,UAAU,CAAE;oBAChD,IAAI,IAAI,QAAQ,KAAK,KAAK;wBACtB,MAAM,SAAS;wBACf,MAAM,OAAO,OAAO,YAAY,CAAC;wBACjC,IAAI,QAAQ,KAAK,UAAU,CAAC,MAAM;4BAC9B,EAAE,cAAc;4BAChB,MAAM,gBAAgB,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;4BAC7D,IAAI,eAAe;gCACf,cAAc,cAAc,CAAC;oCAAE,UAAU;gCAAS;4BACtD;4BACA;wBACJ;wBACA,MAAM,mBAAmB,eAAe,EAAE,OAAO,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE,MAAM;wBAC1E,IAAI,CAAC,kBAAkB;4BACnB,EAAE,cAAc;wBACpB;oBACJ;gBACJ;YACJ,GAAG;QACP;QACA,OAAO,AAAC,YAA6B,OAAlB,GAAG,QAAQ,IAAG,MAAiB,OAAb,cAAa;IACtD;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,kBAAkB,OAAO;IAC/B,MAAM,0BAA0B,OAAO;IACvC,MAAM,eAAe,OAAO;IAC5B,MAAM,YAAY,OAAO;IAEzB,MAAM,iBAAiB,CAAC;QACpB,IAAI;QACJ,IAAI,WAAW;QACf,MAAM,SAAS,OAAO,GAAG,CAAC,MAAM;QAChC,MAAM,eAAe,CAAC,KAAK,gBAAgB,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACrF,YAAY,AAAC,eAAsD,OAAxC,OAAO,OAAO,eAAe,CAAC,MAAM,KAAI;QACnE,MAAM,OAAO,wBAAwB,UAAU,6BAA6B;QAC5E,OAAO,IAAI,CAAC,OAAO,UAAU,EAAE,CAAC;YAC5B,YAAY,kDAAkD,OAAO,OAAO,eAAe,CAAC,UAAU,CAAC,QAAQ,MAAM,OAAO;QAChI;QACA,IAAI,cAAc;YACd,YAAY,4BAA4B,eAAe;QAC3D;QACA,MAAM,SAAS,UAAU;QACzB,MAAM,YAAY,aAAa;QAC/B,MAAM,iBAAiB,OAAO,OAAO,GAAG,GAAG;QAC3C,MAAM,UAAU,iBAAiB,WAAW,OAAO,kBAAkB,MAAM;QAC3E,MAAM,cAAe,oBACjB,WACA,WACA,WACA,YACA,eAAe,OAAO,UAAU,+BAA+B,OAAO,aAAa,MAAM,UAAU,MACnG,OAAO,UAAU,KACjB,kCACA,YACA;QACJ,OAAO;IACX;IAEA,MAAM,OAAO,CAAC;QACV,MAAM,UAAU,eAAe;QAC/B,MAAM,UAAU,OAAO,aAAa,CAAC,IAAI,CAAC;YACtC,OAAO;YACP,MAAM;YACN,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;wBACN,WAAW;wBACX,aAAa;oBACjB;iBACH;YACL;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;gBACT,SAAS;YACb;QACJ;QACA,kFAAkF;QAClF,gFAAgF;QAChF,QAAQ,KAAK,CAAC;IAClB;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,cAAc;YAC5B,KAAK;QACT;IACJ;IAEA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW;YACpC,MAAM;YACN,SAAS;YACT;YACA,SAAS;QACb;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW;YACtC,MAAM;YACN,MAAM;YACN;YACA,SAAS;QACb;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,WAAW,CAAC;YACrB,WAAW;YACX,SAAS;QACb;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/preview/index.js"], "sourcesContent": ["// Exports the \"preview\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/preview')\n//   ES2015:\n//     import 'tinymce/plugins/preview'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,SAAS;AACT,cAAc;AACd,yCAAyC;AACzC,YAAY;AACZ,uCAAuC", "ignoreList": [0], "debugId": null}}]}