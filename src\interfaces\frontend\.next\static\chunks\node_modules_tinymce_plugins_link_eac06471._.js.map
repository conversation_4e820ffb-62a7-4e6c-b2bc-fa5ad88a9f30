{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/link/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq = (t) => (a) => t === a;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isNull = eq(null);\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isArrayOf = (value, pred) => {\n        if (isArray(value)) {\n            for (let i = 0, len = value.length; i < len; ++i) {\n                if (!(pred(value[i]))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        return false;\n    };\n\n    const noop = () => { };\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const tripleEquals = (a, b) => {\n        return a === b;\n    };\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    /* eslint-enable */\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const foldl = (xs, f, acc) => {\n        each$1(xs, (x, i) => {\n            acc = f(acc, x, i);\n        });\n        return acc;\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n    const findMap = (arr, f) => {\n        for (let i = 0; i < arr.length; i++) {\n            const r = f(arr[i], i);\n            if (r.isSome()) {\n                return r;\n            }\n        }\n        return Optional.none();\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const objAcc = (r) => (x, i) => {\n        r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n        each(obj, (x, i) => {\n            (pred(x, i) ? onTrue : onFalse)(x, i);\n        });\n    };\n    const filter = (obj, pred) => {\n        const t = {};\n        internalFilter(obj, pred, objAcc(t), noop);\n        return t;\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    /**\n     * **Is** the value stored inside this Optional object equal to `rhs`?\n     */\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists((left) => comparator(left, rhs));\n    const cat = (arr) => {\n        const r = [];\n        const push = (x) => {\n            r.push(x);\n        };\n        for (let i = 0; i < arr.length; i++) {\n            arr[i].each(push);\n        }\n        return r;\n    };\n    // This can help with type inference, by specifying the type param on the none case, so the caller doesn't have to.\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const singleton = (doRevoke) => {\n        const subject = Cell(Optional.none());\n        const revoke = () => subject.get().each(doRevoke);\n        const clear = () => {\n            revoke();\n            subject.set(Optional.none());\n        };\n        const isSet = () => subject.get().isSome();\n        const get = () => subject.get();\n        const set = (s) => {\n            revoke();\n            subject.set(Optional.some(s));\n        };\n        return {\n            clear,\n            isSet,\n            get,\n            set\n        };\n    };\n    const value = () => {\n        const subject = singleton(noop);\n        const on = (f) => subject.get().each(f);\n        return {\n            ...subject,\n            on\n        };\n    };\n\n    const removeFromStart = (str, numChars) => {\n        return str.substring(numChars);\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const removeLeading = (str, prefix) => {\n        return startsWith(str, prefix) ? removeFromStart(str, prefix.length) : str;\n    };\n    /** Does 'str' start with 'prefix'?\n     *  Note: all strings start with the empty string.\n     *        More formally, for all strings x, startsWith(x, \"\").\n     *        This is so that for all strings x and y, startsWith(y + x, y)\n     */\n    const startsWith = (str, prefix) => {\n        return checkRange(str, prefix, 0);\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$1 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('link_assume_external_targets', {\n            processor: (value) => {\n                const valid = isString(value) || isBoolean(value);\n                if (valid) {\n                    if (value === true) {\n                        return { value: 1 /* AssumeExternalTargets.WARN */, valid };\n                    }\n                    else if (value === \"http\" /* AssumeExternalTargets.ALWAYS_HTTP */ || value === \"https\" /* AssumeExternalTargets.ALWAYS_HTTPS */) {\n                        return { value, valid };\n                    }\n                    else {\n                        return { value: 0 /* AssumeExternalTargets.OFF */, valid };\n                    }\n                }\n                else {\n                    return { valid: false, message: 'Must be a string or a boolean.' };\n                }\n            },\n            default: false\n        });\n        registerOption('link_context_toolbar', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('link_list', {\n            processor: (value) => isString(value) || isFunction(value) || isArrayOf(value, isObject)\n        });\n        registerOption('link_default_target', {\n            processor: 'string'\n        });\n        registerOption('link_default_protocol', {\n            processor: 'string',\n            default: 'https'\n        });\n        registerOption('link_target_list', {\n            processor: (value) => isBoolean(value) || isArrayOf(value, isObject),\n            default: true\n        });\n        registerOption('link_rel_list', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('link_class_list', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('link_title', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('allow_unsafe_link_target', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('link_quicklink', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('link_attributes_postprocess', {\n            processor: 'function',\n        });\n    };\n    const assumeExternalTargets = option('link_assume_external_targets');\n    const hasContextToolbar = option('link_context_toolbar');\n    const getLinkList = option('link_list');\n    const getDefaultLinkTarget = option('link_default_target');\n    const getDefaultLinkProtocol = option('link_default_protocol');\n    const getTargetList = option('link_target_list');\n    const getRelList = option('link_rel_list');\n    const getLinkClassList = option('link_class_list');\n    const shouldShowLinkTitle = option('link_title');\n    const allowUnsafeLinkTarget = option('allow_unsafe_link_target');\n    const useQuickLink = option('link_quicklink');\n    const attributesPostProcess = option('link_attributes_postprocess');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const isAnchor = (elm) => isNonNullable(elm) && elm.nodeName.toLowerCase() === 'a';\n    const isLink = (elm) => isAnchor(elm) && !!getHref(elm);\n    const collectNodesInRange = (rng, predicate) => {\n        if (rng.collapsed) {\n            return [];\n        }\n        else {\n            const contents = rng.cloneContents();\n            const firstChild = contents.firstChild;\n            const walker = new global$3(firstChild, contents);\n            const elements = [];\n            let current = firstChild;\n            do {\n                if (predicate(current)) {\n                    elements.push(current);\n                }\n            } while ((current = walker.next()));\n            return elements;\n        }\n    };\n    const hasProtocol = (url) => /^\\w+:/i.test(url);\n    const getHref = (elm) => {\n        var _a, _b;\n        // Returns the real href value not the resolved a.href value\n        return (_b = (_a = elm.getAttribute('data-mce-href')) !== null && _a !== void 0 ? _a : elm.getAttribute('href')) !== null && _b !== void 0 ? _b : '';\n    };\n    const applyRelTargetRules = (rel, isUnsafe) => {\n        const rules = ['noopener'];\n        const rels = rel ? rel.split(/\\s+/) : [];\n        const toString = (rels) => global$2.trim(rels.sort().join(' '));\n        const addTargetRules = (rels) => {\n            rels = removeTargetRules(rels);\n            return rels.length > 0 ? rels.concat(rules) : rules;\n        };\n        const removeTargetRules = (rels) => rels.filter((val) => global$2.inArray(rules, val) === -1);\n        const newRels = isUnsafe ? addTargetRules(rels) : removeTargetRules(rels);\n        return newRels.length > 0 ? toString(newRels) : '';\n    };\n    const trimCaretContainers = (text) => text.replace(/\\uFEFF/g, '');\n    const getAnchorElement = (editor, selectedElm) => {\n        selectedElm = selectedElm || getLinksInSelection(editor.selection.getRng())[0] || editor.selection.getNode();\n        if (isImageFigure(selectedElm)) {\n            // for an image contained in a figure we look for a link inside the selected element\n            return Optional.from(editor.dom.select('a[href]', selectedElm)[0]);\n        }\n        else {\n            return Optional.from(editor.dom.getParent(selectedElm, 'a[href]'));\n        }\n    };\n    const isInAnchor = (editor, selectedElm) => getAnchorElement(editor, selectedElm).isSome();\n    const getAnchorText = (selection, anchorElm) => {\n        const text = anchorElm.fold(() => selection.getContent({ format: 'text' }), (anchorElm) => anchorElm.innerText || anchorElm.textContent || '');\n        return trimCaretContainers(text);\n    };\n    const getLinksInSelection = (rng) => collectNodesInRange(rng, isLink);\n    const getLinks$1 = (elements) => global$2.grep(elements, isLink);\n    const hasLinks = (elements) => getLinks$1(elements).length > 0;\n    const hasLinksInSelection = (rng) => getLinksInSelection(rng).length > 0;\n    const isOnlyTextSelected = (editor) => {\n        // Allow anchor and inline text elements to be in the selection but nothing else\n        const inlineTextElements = editor.schema.getTextInlineElements();\n        const isElement = (elm) => elm.nodeType === 1 && !isAnchor(elm) && !has(inlineTextElements, elm.nodeName.toLowerCase());\n        // If selection is inside a block anchor then always treat it as non text only\n        const isInBlockAnchor = getAnchorElement(editor).exists((anchor) => anchor.hasAttribute('data-mce-block'));\n        if (isInBlockAnchor) {\n            return false;\n        }\n        const rng = editor.selection.getRng();\n        if (!rng.collapsed) {\n            // Collect all non inline text elements in the range and make sure no elements were found\n            const elements = collectNodesInRange(rng, isElement);\n            return elements.length === 0;\n        }\n        else {\n            return true;\n        }\n    };\n    const isImageFigure = (elm) => isNonNullable(elm) && elm.nodeName === 'FIGURE' && /\\bimage\\b/i.test(elm.className);\n\n    const getLinkAttrs = (data) => {\n        const attrs = ['title', 'rel', 'class', 'target'];\n        return foldl(attrs, (acc, key) => {\n            data[key].each((value) => {\n                // If dealing with an empty string, then treat that as being null so the attribute is removed\n                acc[key] = value.length > 0 ? value : null;\n            });\n            return acc;\n        }, {\n            href: data.href\n        });\n    };\n    const handleExternalTargets = (href, assumeExternalTargets) => {\n        if ((assumeExternalTargets === \"http\" /* AssumeExternalTargets.ALWAYS_HTTP */\n            || assumeExternalTargets === \"https\" /* AssumeExternalTargets.ALWAYS_HTTPS */)\n            && !hasProtocol(href)) {\n            return assumeExternalTargets + '://' + href;\n        }\n        return href;\n    };\n    const applyLinkOverrides = (editor, linkAttrs) => {\n        const newLinkAttrs = { ...linkAttrs };\n        if (getRelList(editor).length === 0 && !allowUnsafeLinkTarget(editor)) {\n            const newRel = applyRelTargetRules(newLinkAttrs.rel, newLinkAttrs.target === '_blank');\n            newLinkAttrs.rel = newRel ? newRel : null;\n        }\n        if (Optional.from(newLinkAttrs.target).isNone() && getTargetList(editor) === false) {\n            newLinkAttrs.target = getDefaultLinkTarget(editor);\n        }\n        newLinkAttrs.href = handleExternalTargets(newLinkAttrs.href, assumeExternalTargets(editor));\n        return newLinkAttrs;\n    };\n    const updateLink = (editor, anchorElm, text, linkAttrs) => {\n        // If we have text, then update the anchor elements text content\n        text.each((text) => {\n            if (has(anchorElm, 'innerText')) {\n                anchorElm.innerText = text;\n            }\n            else {\n                anchorElm.textContent = text;\n            }\n        });\n        editor.dom.setAttribs(anchorElm, linkAttrs);\n        // Move the cursor behind the updated link, so the user can go on typing.\n        const rng = editor.dom.createRng();\n        rng.setStartAfter(anchorElm);\n        rng.setEndAfter(anchorElm);\n        editor.selection.setRng(rng);\n    };\n    const createLink = (editor, selectedElm, text, linkAttrs) => {\n        const dom = editor.dom;\n        if (isImageFigure(selectedElm)) {\n            linkImageFigure(dom, selectedElm, linkAttrs);\n        }\n        else {\n            text.fold(() => {\n                editor.execCommand('mceInsertLink', false, linkAttrs);\n                // Now the newly inserted link is selected. Move the cursor behind the new link, so the user can go on typing.\n                const end = editor.selection.getEnd();\n                const rng = dom.createRng();\n                rng.setStartAfter(end);\n                rng.setEndAfter(end);\n                editor.selection.setRng(rng);\n            }, (text) => {\n                editor.insertContent(dom.createHTML('a', linkAttrs, dom.encode(text)));\n            });\n        }\n    };\n    const linkDomMutation = (editor, attachState, data) => {\n        const selectedElm = editor.selection.getNode();\n        const anchorElm = getAnchorElement(editor, selectedElm);\n        const linkAttrs = applyLinkOverrides(editor, getLinkAttrs(data));\n        const attributesPostProcess$1 = attributesPostProcess(editor);\n        if (isNonNullable(attributesPostProcess$1)) {\n            attributesPostProcess$1(linkAttrs);\n        }\n        editor.undoManager.transact(() => {\n            if (data.href === attachState.href) {\n                attachState.attach();\n            }\n            anchorElm.fold(() => {\n                createLink(editor, selectedElm, data.text, linkAttrs);\n            }, (elm) => {\n                editor.focus();\n                updateLink(editor, elm, data.text, linkAttrs);\n            });\n        });\n    };\n    const unlinkSelection = (editor) => {\n        const dom = editor.dom, selection = editor.selection;\n        const bookmark = selection.getBookmark();\n        const rng = selection.getRng().cloneRange();\n        // Extend the selection out to the entire anchor element\n        const startAnchorElm = dom.getParent(rng.startContainer, 'a[href]', editor.getBody());\n        const endAnchorElm = dom.getParent(rng.endContainer, 'a[href]', editor.getBody());\n        if (startAnchorElm) {\n            rng.setStartBefore(startAnchorElm);\n        }\n        if (endAnchorElm) {\n            rng.setEndAfter(endAnchorElm);\n        }\n        selection.setRng(rng);\n        // Remove the link\n        editor.execCommand('unlink');\n        selection.moveToBookmark(bookmark);\n    };\n    const unlinkDomMutation = (editor) => {\n        editor.undoManager.transact(() => {\n            const node = editor.selection.getNode();\n            if (isImageFigure(node)) {\n                unlinkImageFigure(editor, node);\n            }\n            else {\n                unlinkSelection(editor);\n            }\n            editor.focus();\n        });\n    };\n    /*\n     * RTC uses unwrapped options.\n     *\n     * To best simulate this, we unwrap to null and filter out empty values.\n     */\n    const unwrapOptions = (data) => {\n        const { class: cls, href, rel, target, text, title } = data;\n        return filter({\n            class: cls.getOrNull(),\n            href,\n            rel: rel.getOrNull(),\n            target: target.getOrNull(),\n            text: text.getOrNull(),\n            title: title.getOrNull()\n        }, (v, _k) => isNull(v) === false);\n    };\n    const sanitizeData = (editor, data) => {\n        const getOption = editor.options.get;\n        const uriOptions = {\n            allow_html_data_urls: getOption('allow_html_data_urls'),\n            allow_script_urls: getOption('allow_script_urls'),\n            allow_svg_data_urls: getOption('allow_svg_data_urls')\n        };\n        // Sanitize the URL\n        const href = data.href;\n        return {\n            ...data,\n            href: global$4.isDomSafe(href, 'a', uriOptions) ? href : ''\n        };\n    };\n    const link = (editor, attachState, data) => {\n        const sanitizedData = sanitizeData(editor, data);\n        editor.hasPlugin('rtc', true) ? editor.execCommand('createlink', false, unwrapOptions(sanitizedData)) : linkDomMutation(editor, attachState, sanitizedData);\n    };\n    const unlink = (editor) => {\n        editor.hasPlugin('rtc', true) ? editor.execCommand('unlink') : unlinkDomMutation(editor);\n    };\n    const unlinkImageFigure = (editor, fig) => {\n        var _a;\n        const img = editor.dom.select('img', fig)[0];\n        if (img) {\n            const a = editor.dom.getParents(img, 'a[href]', fig)[0];\n            if (a) {\n                (_a = a.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(img, a);\n                editor.dom.remove(a);\n            }\n        }\n    };\n    const linkImageFigure = (dom, fig, attrs) => {\n        var _a;\n        const img = dom.select('img', fig)[0];\n        if (img) {\n            const a = dom.create('a', attrs);\n            (_a = img.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(a, img);\n            a.appendChild(img);\n        }\n    };\n\n    const getValue = (item) => isString(item.value) ? item.value : '';\n    const getText = (item) => {\n        if (isString(item.text)) {\n            return item.text;\n        }\n        else if (isString(item.title)) {\n            return item.title;\n        }\n        else {\n            return '';\n        }\n    };\n    const sanitizeList = (list, extractValue) => {\n        const out = [];\n        global$2.each(list, (item) => {\n            const text = getText(item);\n            if (item.menu !== undefined) {\n                const items = sanitizeList(item.menu, extractValue);\n                out.push({ text, items }); // list group\n            }\n            else {\n                const value = extractValue(item);\n                out.push({ text, value }); // list value\n            }\n        });\n        return out;\n    };\n    const sanitizeWith = (extracter = getValue) => (list) => Optional.from(list).map((list) => sanitizeList(list, extracter));\n    const sanitize = (list) => sanitizeWith(getValue)(list);\n    // NOTE: May need to care about flattening.\n    const createUi = (name, label) => (items) => ({\n        name,\n        type: 'listbox',\n        label,\n        items\n    });\n    const ListOptions = {\n        sanitize,\n        sanitizeWith,\n        createUi,\n        getValue\n    };\n\n    const isListGroup = (item) => hasNonNullableKey(item, 'items');\n    const findTextByValue = (value, catalog) => findMap(catalog, (item) => {\n        if (isListGroup(item)) {\n            return findTextByValue(value, item.items);\n        }\n        else {\n            return someIf(item.value === value, item);\n        }\n    });\n    const getDelta = (persistentText, fieldName, catalog, data) => {\n        const value = data[fieldName];\n        const hasPersistentText = persistentText.length > 0;\n        return value !== undefined ? findTextByValue(value, catalog).map((i) => ({\n            url: {\n                value: i.value,\n                meta: {\n                    text: hasPersistentText ? persistentText : i.text,\n                    attach: noop\n                }\n            },\n            text: hasPersistentText ? persistentText : i.text\n        })) : Optional.none();\n    };\n    const findCatalog = (catalogs, fieldName) => {\n        if (fieldName === 'link') {\n            return catalogs.link;\n        }\n        else if (fieldName === 'anchor') {\n            return catalogs.anchor;\n        }\n        else {\n            return Optional.none();\n        }\n    };\n    const init = (initialData, linkCatalog) => {\n        const persistentData = {\n            text: initialData.text,\n            title: initialData.title\n        };\n        const getTitleFromUrlChange = (url) => { var _a; return someIf(persistentData.title.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.title).getOr('')); };\n        const getTextFromUrlChange = (url) => { var _a; return someIf(persistentData.text.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.text).getOr(url.value)); };\n        const onUrlChange = (data) => {\n            const text = getTextFromUrlChange(data.url);\n            const title = getTitleFromUrlChange(data.url);\n            // We are going to change the text/title because it has not been manually entered by the user.\n            if (text.isSome() || title.isSome()) {\n                return Optional.some({\n                    ...text.map((text) => ({ text })).getOr({}),\n                    ...title.map((title) => ({ title })).getOr({})\n                });\n            }\n            else {\n                return Optional.none();\n            }\n        };\n        const onCatalogChange = (data, change) => {\n            const catalog = findCatalog(linkCatalog, change).getOr([]);\n            return getDelta(persistentData.text, change, catalog, data);\n        };\n        const onChange = (getData, change) => {\n            const name = change.name;\n            if (name === 'url') {\n                return onUrlChange(getData());\n            }\n            else if (contains(['anchor', 'link'], name)) {\n                return onCatalogChange(getData(), name);\n            }\n            else if (name === 'text' || name === 'title') {\n                // Update the persistent text/title state, as a user has input custom text\n                persistentData[name] = getData()[name];\n                return Optional.none();\n            }\n            else {\n                return Optional.none();\n            }\n        };\n        return {\n            onChange\n        };\n    };\n    const DialogChanges = {\n        init,\n        getDelta\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    // Delay confirm since onSubmit will move focus\n    const delayedConfirm = (editor, message, callback) => {\n        const rng = editor.selection.getRng();\n        global$1.setEditorTimeout(editor, () => {\n            editor.windowManager.confirm(message, (state) => {\n                editor.selection.setRng(rng);\n                callback(state);\n            });\n        });\n    };\n    const tryEmailTransform = (data) => {\n        const url = data.href;\n        const suggestMailTo = url.indexOf('@') > 0 && url.indexOf('/') === -1 && url.indexOf('mailto:') === -1;\n        return suggestMailTo ? Optional.some({\n            message: 'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?',\n            preprocess: (oldData) => ({ ...oldData, href: 'mailto:' + url })\n        }) : Optional.none();\n    };\n    const tryProtocolTransform = (assumeExternalTargets, defaultLinkProtocol) => (data) => {\n        const url = data.href;\n        const suggestProtocol = (assumeExternalTargets === 1 /* AssumeExternalTargets.WARN */ && !hasProtocol(url) ||\n            assumeExternalTargets === 0 /* AssumeExternalTargets.OFF */ && /^\\s*www(\\.|\\d\\.)/i.test(url));\n        return suggestProtocol ? Optional.some({\n            message: `The URL you entered seems to be an external link. Do you want to add the required ${defaultLinkProtocol}:// prefix?`,\n            preprocess: (oldData) => ({ ...oldData, href: defaultLinkProtocol + '://' + url })\n        }) : Optional.none();\n    };\n    const preprocess = (editor, data) => findMap([tryEmailTransform, tryProtocolTransform(assumeExternalTargets(editor), getDefaultLinkProtocol(editor))], (f) => f(data)).fold(() => Promise.resolve(data), (transform) => new Promise((callback) => {\n        delayedConfirm(editor, transform.message, (state) => {\n            callback(state ? transform.preprocess(data) : data);\n        });\n    }));\n    const DialogConfirms = {\n        preprocess\n    };\n\n    // NOTE: you currently need anchors in the content for this field to appear\n    const getAnchors = (editor) => {\n        const anchorNodes = editor.dom.select('a:not([href])');\n        const anchors = bind(anchorNodes, (anchor) => {\n            const id = anchor.name || anchor.id;\n            return id ? [{ text: id, value: '#' + id }] : [];\n        });\n        return anchors.length > 0 ? Optional.some([{ text: 'None', value: '' }].concat(anchors)) : Optional.none();\n    };\n    const AnchorListOptions = {\n        getAnchors\n    };\n\n    // Looks like tinymce currently renders menus, but doesn't\n    // let you choose from one.\n    const getClasses = (editor) => {\n        const list = getLinkClassList(editor);\n        if (list.length > 0) {\n            return ListOptions.sanitize(list);\n        }\n        return Optional.none();\n    };\n    const ClassListOptions = {\n        getClasses\n    };\n\n    const parseJson = (text) => {\n        // Do some proper modelling.\n        try {\n            return Optional.some(JSON.parse(text));\n        }\n        catch (_a) {\n            return Optional.none();\n        }\n    };\n    const getLinks = (editor) => {\n        const extractor = (item) => editor.convertURL(item.value || item.url || '', 'href');\n        const linkList = getLinkList(editor);\n        return new Promise((resolve) => {\n            // TODO - better handling of failure\n            if (isString(linkList)) {\n                fetch(linkList)\n                    .then((res) => res.ok ? res.text().then(parseJson) : Promise.reject())\n                    .then(resolve, () => resolve(Optional.none()));\n            }\n            else if (isFunction(linkList)) {\n                linkList((output) => resolve(Optional.some(output)));\n            }\n            else {\n                resolve(Optional.from(linkList));\n            }\n        }).then((optItems) => optItems.bind(ListOptions.sanitizeWith(extractor)).map((items) => {\n            if (items.length > 0) {\n                const noneItem = [{ text: 'None', value: '' }];\n                return noneItem.concat(items);\n            }\n            else {\n                return items;\n            }\n        }));\n    };\n    const LinkListOptions = {\n        getLinks\n    };\n\n    const getRels = (editor, initialTarget) => {\n        const list = getRelList(editor);\n        if (list.length > 0) {\n            const isTargetBlank = is(initialTarget, '_blank');\n            const enforceSafe = allowUnsafeLinkTarget(editor) === false;\n            const safeRelExtractor = (item) => applyRelTargetRules(ListOptions.getValue(item), isTargetBlank);\n            const sanitizer = enforceSafe ? ListOptions.sanitizeWith(safeRelExtractor) : ListOptions.sanitize;\n            return sanitizer(list);\n        }\n        return Optional.none();\n    };\n    const RelOptions = {\n        getRels\n    };\n\n    // In current tinymce, targets can be nested menus.\n    // Do we really want to support that?\n    const fallbacks = [\n        { text: 'Current window', value: '' },\n        { text: 'New window', value: '_blank' }\n    ];\n    const getTargets = (editor) => {\n        const list = getTargetList(editor);\n        if (isArray(list)) {\n            return ListOptions.sanitize(list).orThunk(() => Optional.some(fallbacks));\n        }\n        else if (list === false) {\n            return Optional.none();\n        }\n        return Optional.some(fallbacks);\n    };\n    const TargetOptions = {\n        getTargets\n    };\n\n    const nonEmptyAttr = (dom, elem, name) => {\n        const val = dom.getAttrib(elem, name);\n        return val !== null && val.length > 0 ? Optional.some(val) : Optional.none();\n    };\n    const extractFromAnchor = (editor, anchor) => {\n        const dom = editor.dom;\n        const onlyText = isOnlyTextSelected(editor);\n        const text = onlyText ? Optional.some(getAnchorText(editor.selection, anchor)) : Optional.none();\n        const url = anchor.bind((anchorElm) => Optional.from(dom.getAttrib(anchorElm, 'href')));\n        const target = anchor.bind((anchorElm) => Optional.from(dom.getAttrib(anchorElm, 'target')));\n        const rel = anchor.bind((anchorElm) => nonEmptyAttr(dom, anchorElm, 'rel'));\n        const linkClass = anchor.bind((anchorElm) => nonEmptyAttr(dom, anchorElm, 'class'));\n        const title = anchor.bind((anchorElm) => nonEmptyAttr(dom, anchorElm, 'title'));\n        return {\n            url,\n            text,\n            title,\n            target,\n            rel,\n            linkClass\n        };\n    };\n    const collect = (editor, linkNode) => LinkListOptions.getLinks(editor).then((links) => {\n        const anchor = extractFromAnchor(editor, linkNode);\n        return {\n            anchor,\n            catalogs: {\n                targets: TargetOptions.getTargets(editor),\n                // This should be initial target. Is anchor.target that?\n                rels: RelOptions.getRels(editor, anchor.target),\n                classes: ClassListOptions.getClasses(editor),\n                anchor: AnchorListOptions.getAnchors(editor),\n                link: links\n            },\n            optNode: linkNode,\n            flags: {\n                titleEnabled: shouldShowLinkTitle(editor)\n            }\n        };\n    });\n    const DialogInfo = {\n        collect\n    };\n\n    const handleSubmit = (editor, info) => (api) => {\n        const data = api.getData();\n        if (!data.url.value) {\n            unlink(editor);\n            // Temporary fix. TODO: TINY-2811\n            api.close();\n            return;\n        }\n        // Check if a key is defined, meaning it was a field in the dialog. If it is,\n        // then check if it's changed and return none if nothing has changed.\n        const getChangedValue = (key) => Optional.from(data[key]).filter((value) => !is(info.anchor[key], value));\n        const changedData = {\n            href: data.url.value,\n            text: getChangedValue('text'),\n            target: getChangedValue('target'),\n            rel: getChangedValue('rel'),\n            class: getChangedValue('linkClass'),\n            title: getChangedValue('title')\n        };\n        const attachState = {\n            href: data.url.value,\n            attach: data.url.meta !== undefined && data.url.meta.attach ? data.url.meta.attach : noop\n        };\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        DialogConfirms.preprocess(editor, changedData).then((pData) => {\n            link(editor, attachState, pData);\n        });\n        api.close();\n    };\n    const collectData = (editor) => {\n        const anchorNode = getAnchorElement(editor);\n        return DialogInfo.collect(editor, anchorNode);\n    };\n    const getInitialData = (info, defaultTarget) => {\n        const anchor = info.anchor;\n        const url = anchor.url.getOr('');\n        return {\n            url: {\n                value: url,\n                meta: {\n                    original: {\n                        value: url\n                    }\n                }\n            },\n            text: anchor.text.getOr(''),\n            title: anchor.title.getOr(''),\n            anchor: url,\n            link: url,\n            rel: anchor.rel.getOr(''),\n            target: anchor.target.or(defaultTarget).getOr(''),\n            linkClass: anchor.linkClass.getOr('')\n        };\n    };\n    const makeDialog = (settings, onSubmit, editor) => {\n        const urlInput = [\n            {\n                name: 'url',\n                type: 'urlinput',\n                filetype: 'file',\n                label: 'URL',\n                picker_text: 'Browse links'\n            }\n        ];\n        const displayText = settings.anchor.text.map(() => ({\n            name: 'text',\n            type: 'input',\n            label: 'Text to display'\n        })).toArray();\n        const titleText = settings.flags.titleEnabled ? [\n            {\n                name: 'title',\n                type: 'input',\n                label: 'Title'\n            }\n        ] : [];\n        const defaultTarget = Optional.from(getDefaultLinkTarget(editor));\n        const initialData = getInitialData(settings, defaultTarget);\n        const catalogs = settings.catalogs;\n        const dialogDelta = DialogChanges.init(initialData, catalogs);\n        const body = {\n            type: 'panel',\n            items: flatten([\n                urlInput,\n                displayText,\n                titleText,\n                cat([\n                    catalogs.anchor.map(ListOptions.createUi('anchor', 'Anchors')),\n                    catalogs.rels.map(ListOptions.createUi('rel', 'Rel')),\n                    catalogs.targets.map(ListOptions.createUi('target', 'Open link in...')),\n                    catalogs.link.map(ListOptions.createUi('link', 'Link list')),\n                    catalogs.classes.map(ListOptions.createUi('linkClass', 'Class'))\n                ])\n            ])\n        };\n        return {\n            title: 'Insert/Edit Link',\n            size: 'normal',\n            body,\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData,\n            onChange: (api, { name }) => {\n                dialogDelta.onChange(api.getData, { name }).each((newData) => {\n                    api.setData(newData);\n                });\n            },\n            onSubmit\n        };\n    };\n    const open = (editor) => {\n        const data = collectData(editor);\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        data.then((info) => {\n            const onSubmit = handleSubmit(editor, info);\n            return makeDialog(info, onSubmit, editor);\n        }).then((spec) => {\n            editor.windowManager.open(spec);\n        });\n    };\n\n    const register = (editor) => {\n        editor.addCommand('mceLink', (_ui, value) => {\n            if ((value === null || value === void 0 ? void 0 : value.dialog) === true || !useQuickLink(editor)) {\n                open(editor);\n            }\n            else {\n                editor.dispatch('contexttoolbar-show', {\n                    toolbarKey: 'quicklink'\n                });\n            }\n        });\n    };\n\n    const setup$2 = (editor) => {\n        editor.addShortcut('Meta+K', '', () => {\n            editor.execCommand('mceLink');\n        });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const appendClickRemove = (link, evt) => {\n        document.body.appendChild(link);\n        link.dispatchEvent(evt);\n        document.body.removeChild(link);\n    };\n    const openLink = (url) => {\n        const link = document.createElement('a');\n        link.target = '_blank';\n        link.href = url;\n        link.rel = 'noreferrer noopener';\n        const evt = new MouseEvent('click', {\n            bubbles: true,\n            cancelable: true,\n            view: window\n        });\n        document.dispatchEvent(evt);\n        appendClickRemove(link, evt);\n    };\n    const hasOnlyAltModifier = (e) => {\n        return e.altKey === true && e.shiftKey === false && e.ctrlKey === false && e.metaKey === false;\n    };\n    const gotoLink = (editor, a) => {\n        if (a) {\n            const href = getHref(a);\n            if (/^#/.test(href)) {\n                const targetEl = editor.dom.select(`${href},[name=\"${removeLeading(href, '#')}\"]`);\n                if (targetEl.length) {\n                    editor.selection.scrollIntoView(targetEl[0], true);\n                }\n            }\n            else {\n                openLink(a.href);\n            }\n        }\n    };\n    const isSelectionOnImageWithEmbeddedLink = (editor) => {\n        const rng = editor.selection.getRng();\n        const node = rng.startContainer;\n        // Handle a case where an image embedded with a link is selected\n        return isLink(node) && rng.startContainer === rng.endContainer && editor.dom.select('img', node).length === 1;\n    };\n    const getLinkFromElement = (editor, element) => {\n        const links = getLinks$1(editor.dom.getParents(element));\n        return someIf(links.length === 1, links[0]);\n    };\n    const getLinkInSelection = (editor) => {\n        const links = getLinksInSelection(editor.selection.getRng());\n        return someIf(links.length > 0, links[0]).or(getLinkFromElement(editor, editor.selection.getNode()));\n    };\n    const getLinkFromSelection = (editor) => editor.selection.isCollapsed() || isSelectionOnImageWithEmbeddedLink(editor)\n        ? getLinkFromElement(editor, editor.selection.getStart())\n        : getLinkInSelection(editor);\n    const setup$1 = (editor) => {\n        const selectedLink = value();\n        const getSelectedLink = () => selectedLink.get().or(getLinkFromSelection(editor));\n        const gotoSelectedLink = () => getSelectedLink().each((link) => gotoLink(editor, link));\n        editor.on('contextmenu', (e) => {\n            getLinkFromElement(editor, e.target).each(selectedLink.set);\n        });\n        editor.on('SelectionChange', () => {\n            if (!selectedLink.isSet()) {\n                getLinkFromSelection(editor).each(selectedLink.set);\n            }\n        });\n        editor.on('click', (e) => {\n            selectedLink.clear();\n            const links = getLinks$1(editor.dom.getParents(e.target));\n            if (links.length === 1 && global.metaKeyPressed(e)) {\n                e.preventDefault();\n                gotoLink(editor, links[0]);\n            }\n        });\n        editor.on('keydown', (e) => {\n            selectedLink.clear();\n            if (!e.isDefaultPrevented() && e.keyCode === 13 && hasOnlyAltModifier(e)) {\n                getSelectedLink().each((link) => {\n                    e.preventDefault();\n                    gotoLink(editor, link);\n                });\n            }\n        });\n        return {\n            gotoSelectedLink\n        };\n    };\n\n    const openDialog = (editor) => () => {\n        editor.execCommand('mceLink', false, { dialog: true });\n    };\n    const toggleState = (editor, toggler) => {\n        editor.on('NodeChange', toggler);\n        return () => editor.off('NodeChange', toggler);\n    };\n    const toggleLinkState = (editor) => (api) => {\n        const updateState = () => {\n            api.setActive(!editor.mode.isReadOnly() && isInAnchor(editor, editor.selection.getNode()));\n            api.setEnabled(editor.selection.isEditable());\n        };\n        updateState();\n        return toggleState(editor, updateState);\n    };\n    const toggleLinkMenuState = (editor) => (api) => {\n        const updateState = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        updateState();\n        return toggleState(editor, updateState);\n    };\n    const toggleRequiresLinkState = (editor) => (api) => {\n        const hasLinks$1 = (parents) => hasLinks(parents) || hasLinksInSelection(editor.selection.getRng());\n        const parents = editor.dom.getParents(editor.selection.getStart());\n        const updateEnabled = (parents) => {\n            api.setEnabled(hasLinks$1(parents) && editor.selection.isEditable());\n        };\n        updateEnabled(parents);\n        return toggleState(editor, (e) => updateEnabled(e.parents));\n    };\n    const setupButtons = (editor, openLink) => {\n        editor.ui.registry.addToggleButton('link', {\n            icon: 'link',\n            tooltip: 'Insert/edit link',\n            shortcut: 'Meta+K',\n            onAction: openDialog(editor),\n            onSetup: toggleLinkState(editor)\n        });\n        editor.ui.registry.addButton('openlink', {\n            icon: 'new-tab',\n            tooltip: 'Open link',\n            onAction: openLink.gotoSelectedLink,\n            onSetup: toggleRequiresLinkState(editor)\n        });\n        editor.ui.registry.addButton('unlink', {\n            icon: 'unlink',\n            tooltip: 'Remove link',\n            onAction: () => unlink(editor),\n            onSetup: toggleRequiresLinkState(editor)\n        });\n    };\n    const setupMenuItems = (editor, openLink) => {\n        editor.ui.registry.addMenuItem('openlink', {\n            text: 'Open link',\n            icon: 'new-tab',\n            onAction: openLink.gotoSelectedLink,\n            onSetup: toggleRequiresLinkState(editor)\n        });\n        editor.ui.registry.addMenuItem('link', {\n            icon: 'link',\n            text: 'Link...',\n            shortcut: 'Meta+K',\n            onAction: openDialog(editor),\n            onSetup: toggleLinkMenuState(editor)\n        });\n        editor.ui.registry.addMenuItem('unlink', {\n            icon: 'unlink',\n            text: 'Remove link',\n            onAction: () => unlink(editor),\n            onSetup: toggleRequiresLinkState(editor)\n        });\n    };\n    const setupContextMenu = (editor) => {\n        const inLink = 'link unlink openlink';\n        const noLink = 'link';\n        editor.ui.registry.addContextMenu('link', {\n            update: (element) => {\n                const isEditable = editor.dom.isEditable(element);\n                if (!isEditable) {\n                    return '';\n                }\n                return hasLinks(editor.dom.getParents(element, 'a')) ? inLink : noLink;\n            }\n        });\n    };\n    const setupContextToolbars = (editor, openLink) => {\n        const collapseSelectionToEnd = (editor) => {\n            editor.selection.collapse(false);\n        };\n        const onSetupLink = (buttonApi) => {\n            const node = editor.selection.getNode();\n            buttonApi.setEnabled(isInAnchor(editor, node) && editor.selection.isEditable());\n            return noop;\n        };\n        /**\n         * if we're editing a link, don't change the text.\n         * if anything other than text is selected, don't change the text.\n         * TINY-9593: If there is a text selection return `Optional.none`\n         * because `mceInsertLink` command will handle the selection.\n         */\n        const getLinkText = (value) => {\n            const anchor = getAnchorElement(editor);\n            const onlyText = isOnlyTextSelected(editor);\n            if (anchor.isNone() && onlyText) {\n                const text = getAnchorText(editor.selection, anchor);\n                return someIf(text.length === 0, value);\n            }\n            else {\n                return Optional.none();\n            }\n        };\n        editor.ui.registry.addContextForm('quicklink', {\n            launch: {\n                type: 'contextformtogglebutton',\n                icon: 'link',\n                tooltip: 'Link',\n                onSetup: toggleLinkState(editor)\n            },\n            label: 'Link',\n            predicate: (node) => hasContextToolbar(editor) && isInAnchor(editor, node),\n            initValue: () => {\n                const elm = getAnchorElement(editor);\n                return elm.fold(constant(''), getHref);\n            },\n            commands: [\n                {\n                    type: 'contextformtogglebutton',\n                    icon: 'link',\n                    tooltip: 'Link',\n                    primary: true,\n                    onSetup: (buttonApi) => {\n                        const node = editor.selection.getNode();\n                        // TODO: Make a test for this later.\n                        buttonApi.setActive(isInAnchor(editor, node));\n                        return toggleLinkState(editor)(buttonApi);\n                    },\n                    onAction: (formApi) => {\n                        const value = formApi.getValue();\n                        const text = getLinkText(value);\n                        const attachState = { href: value, attach: noop };\n                        link(editor, attachState, {\n                            href: value,\n                            text,\n                            title: Optional.none(),\n                            rel: Optional.none(),\n                            target: Optional.from(getDefaultLinkTarget(editor)),\n                            class: Optional.none()\n                        });\n                        collapseSelectionToEnd(editor);\n                        formApi.hide();\n                    }\n                },\n                {\n                    type: 'contextformbutton',\n                    icon: 'unlink',\n                    tooltip: 'Remove link',\n                    onSetup: onSetupLink,\n                    // TODO: The original inlite action was quite complex. Are we missing something with this?\n                    onAction: (formApi) => {\n                        unlink(editor);\n                        formApi.hide();\n                    }\n                },\n                {\n                    type: 'contextformbutton',\n                    icon: 'new-tab',\n                    tooltip: 'Open link',\n                    onSetup: onSetupLink,\n                    onAction: (formApi) => {\n                        openLink.gotoSelectedLink();\n                        formApi.hide();\n                    }\n                }\n            ]\n        });\n    };\n    const setup = (editor) => {\n        const openLink = setup$1(editor);\n        setupButtons(editor, openLink);\n        setupMenuItems(editor, openLink);\n        setupContextMenu(editor);\n        setupContextToolbars(editor, openLink);\n    };\n\n    var Plugin = () => {\n        global$5.add('link', (editor) => {\n            register$1(editor);\n            register(editor);\n            setup(editor);\n            setup$2(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,WAAW,OAAO;IACxB,MAAM,WAAW,OAAO;IACxB,MAAM,UAAU,OAAO;IACvB,MAAM,SAAS,GAAG;IAClB,MAAM,YAAY,aAAa;IAC/B,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAChC,MAAM,YAAY,CAAC,OAAO;QACtB,IAAI,QAAQ,QAAQ;YAChB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;gBAC9C,IAAI,CAAE,KAAK,KAAK,CAAC,EAAE,GAAI;oBACnB,OAAO;gBACX;YACJ;YACA,OAAO;QACX;QACA,OAAO;IACX;IAEA,MAAM,OAAO,KAAQ;IACrB,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC,GAAG;QACrB,OAAO,MAAM;IACjB;IAEA;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,gBAAgB,MAAM,SAAS,CAAC,OAAO;IAC7C,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,iBAAiB,GACjB,MAAM,aAAa,CAAC,IAAI,IAAM,cAAc,IAAI,CAAC,IAAI;IACrD,MAAM,WAAW,CAAC,IAAI,IAAM,WAAW,IAAI,KAAK,CAAC;IACjD,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,QAAQ,CAAC,IAAI,GAAG;QAClB,OAAO,IAAI,CAAC,GAAG;YACX,MAAM,EAAE,KAAK,GAAG;QACpB;QACA,OAAO;IACX;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,IAAI,IAAM,QAAQ,IAAI,IAAI;IACxC,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAC9D,MAAM,UAAU,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,MAAM,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,SAAS,CAAC,IAAM,CAAC,GAAG;YACtB,CAAC,CAAC,EAAE,GAAG;QACX;IACA,MAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ;QACvC,KAAK,KAAK,CAAC,GAAG;YACV,CAAC,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,GAAG;QACvC;IACJ;IACA,MAAM,SAAS,CAAC,KAAK;QACjB,MAAM,IAAI,CAAC;QACX,eAAe,KAAK,MAAM,OAAO,IAAI;QACrC,OAAO;IACX;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IACnD,MAAM,oBAAoB,CAAC,KAAK,MAAQ,IAAI,KAAK,QAAQ,GAAG,CAAC,IAAI,KAAK,aAAa,GAAG,CAAC,IAAI,KAAK;IAEhG,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA;;KAEC,GACD,MAAM,KAAK,SAAC,KAAK;YAAK,8EAAa;eAAiB,IAAI,MAAM,CAAC,CAAC,OAAS,WAAW,MAAM;;IAC1F,MAAM,MAAM,CAAC;QACT,MAAM,IAAI,EAAE;QACZ,MAAM,OAAO,CAAC;YACV,EAAE,IAAI,CAAC;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IACA,mHAAmH;IACnH,MAAM,SAAS,CAAC,GAAG,IAAM,IAAI,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;IAE7D,MAAM,YAAY,CAAC;QACf,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,SAAS,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC7B;QACA,MAAM,QAAQ,IAAM,QAAQ,GAAG,GAAG,MAAM;QACxC,MAAM,MAAM,IAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,CAAC;YACT;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC;QAC9B;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,QAAQ;QACV,MAAM,UAAU,UAAU;QAC1B,MAAM,KAAK,CAAC,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,GAAG,OAAO;YACV;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,KAAK;QAC1B,OAAO,IAAI,SAAS,CAAC;IACzB;IAEA,MAAM,aAAa,CAAC,KAAK,QAAQ,QAAU,WAAW,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,QAAQ,OAAO,MAAM,MAAM;IACxI,MAAM,gBAAgB,CAAC,KAAK;QACxB,OAAO,WAAW,KAAK,UAAU,gBAAgB,KAAK,OAAO,MAAM,IAAI;IAC3E;IACA;;;;KAIC,GACD,MAAM,aAAa,CAAC,KAAK;QACrB,OAAO,WAAW,KAAK,QAAQ;IACnC;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,gCAAgC;YAC3C,WAAW,CAAC;gBACR,MAAM,QAAQ,SAAS,UAAU,UAAU;gBAC3C,IAAI,OAAO;oBACP,IAAI,UAAU,MAAM;wBAChB,OAAO;4BAAE,OAAO,EAAE,8BAA8B;4BAAI;wBAAM;oBAC9D,OACK,IAAI,UAAU,OAAO,qCAAqC,OAAM,UAAU,QAAQ,sCAAsC,KAAI;wBAC7H,OAAO;4BAAE;4BAAO;wBAAM;oBAC1B,OACK;wBACD,OAAO;4BAAE,OAAO,EAAE,6BAA6B;4BAAI;wBAAM;oBAC7D;gBACJ,OACK;oBACD,OAAO;wBAAE,OAAO;wBAAO,SAAS;oBAAiC;gBACrE;YACJ;YACA,SAAS;QACb;QACA,eAAe,wBAAwB;YACnC,WAAW;YACX,SAAS;QACb;QACA,eAAe,aAAa;YACxB,WAAW,CAAC,QAAU,SAAS,UAAU,WAAW,UAAU,UAAU,OAAO;QACnF;QACA,eAAe,uBAAuB;YAClC,WAAW;QACf;QACA,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS;QACb;QACA,eAAe,oBAAoB;YAC/B,WAAW,CAAC,QAAU,UAAU,UAAU,UAAU,OAAO;YAC3D,SAAS;QACb;QACA,eAAe,iBAAiB;YAC5B,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,mBAAmB;YAC9B,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,cAAc;YACzB,WAAW;YACX,SAAS;QACb;QACA,eAAe,4BAA4B;YACvC,WAAW;YACX,SAAS;QACb;QACA,eAAe,kBAAkB;YAC7B,WAAW;YACX,SAAS;QACb;QACA,eAAe,+BAA+B;YAC1C,WAAW;QACf;IACJ;IACA,MAAM,wBAAwB,OAAO;IACrC,MAAM,oBAAoB,OAAO;IACjC,MAAM,cAAc,OAAO;IAC3B,MAAM,uBAAuB,OAAO;IACpC,MAAM,yBAAyB,OAAO;IACtC,MAAM,gBAAgB,OAAO;IAC7B,MAAM,aAAa,OAAO;IAC1B,MAAM,mBAAmB,OAAO;IAChC,MAAM,sBAAsB,OAAO;IACnC,MAAM,wBAAwB,OAAO;IACrC,MAAM,eAAe,OAAO;IAC5B,MAAM,wBAAwB,OAAO;IAErC,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,WAAW,CAAC,MAAQ,cAAc,QAAQ,IAAI,QAAQ,CAAC,WAAW,OAAO;IAC/E,MAAM,SAAS,CAAC,MAAQ,SAAS,QAAQ,CAAC,CAAC,QAAQ;IACnD,MAAM,sBAAsB,CAAC,KAAK;QAC9B,IAAI,IAAI,SAAS,EAAE;YACf,OAAO,EAAE;QACb,OACK;YACD,MAAM,WAAW,IAAI,aAAa;YAClC,MAAM,aAAa,SAAS,UAAU;YACtC,MAAM,SAAS,IAAI,SAAS,YAAY;YACxC,MAAM,WAAW,EAAE;YACnB,IAAI,UAAU;YACd,GAAG;gBACC,IAAI,UAAU,UAAU;oBACpB,SAAS,IAAI,CAAC;gBAClB;YACJ,QAAU,UAAU,OAAO,IAAI,GAAK;YACpC,OAAO;QACX;IACJ;IACA,MAAM,cAAc,CAAC,MAAQ,SAAS,IAAI,CAAC;IAC3C,MAAM,UAAU,CAAC;QACb,IAAI,IAAI;QACR,4DAA4D;QAC5D,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,YAAY,CAAC,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,YAAY,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACtJ;IACA,MAAM,sBAAsB,CAAC,KAAK;QAC9B,MAAM,QAAQ;YAAC;SAAW;QAC1B,MAAM,OAAO,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE;QACxC,MAAM,WAAW,CAAC,OAAS,SAAS,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC;QAC1D,MAAM,iBAAiB,CAAC;YACpB,OAAO,kBAAkB;YACzB,OAAO,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,CAAC,SAAS;QAClD;QACA,MAAM,oBAAoB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,SAAS,OAAO,CAAC,OAAO,SAAS,CAAC;QAC3F,MAAM,UAAU,WAAW,eAAe,QAAQ,kBAAkB;QACpE,OAAO,QAAQ,MAAM,GAAG,IAAI,SAAS,WAAW;IACpD;IACA,MAAM,sBAAsB,CAAC,OAAS,KAAK,OAAO,CAAC,WAAW;IAC9D,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,cAAc,eAAe,oBAAoB,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,OAAO;QAC1G,IAAI,cAAc,cAAc;YAC5B,oFAAoF;YACpF,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,WAAW,YAAY,CAAC,EAAE;QACrE,OACK;YACD,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,aAAa;QAC3D;IACJ;IACA,MAAM,aAAa,CAAC,QAAQ,cAAgB,iBAAiB,QAAQ,aAAa,MAAM;IACxF,MAAM,gBAAgB,CAAC,WAAW;QAC9B,MAAM,OAAO,UAAU,IAAI,CAAC,IAAM,UAAU,UAAU,CAAC;gBAAE,QAAQ;YAAO,IAAI,CAAC,YAAc,UAAU,SAAS,IAAI,UAAU,WAAW,IAAI;QAC3I,OAAO,oBAAoB;IAC/B;IACA,MAAM,sBAAsB,CAAC,MAAQ,oBAAoB,KAAK;IAC9D,MAAM,aAAa,CAAC,WAAa,SAAS,IAAI,CAAC,UAAU;IACzD,MAAM,WAAW,CAAC,WAAa,WAAW,UAAU,MAAM,GAAG;IAC7D,MAAM,sBAAsB,CAAC,MAAQ,oBAAoB,KAAK,MAAM,GAAG;IACvE,MAAM,qBAAqB,CAAC;QACxB,gFAAgF;QAChF,MAAM,qBAAqB,OAAO,MAAM,CAAC,qBAAqB;QAC9D,MAAM,YAAY,CAAC,MAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI,oBAAoB,IAAI,QAAQ,CAAC,WAAW;QACpH,8EAA8E;QAC9E,MAAM,kBAAkB,iBAAiB,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,YAAY,CAAC;QACxF,IAAI,iBAAiB;YACjB,OAAO;QACX;QACA,MAAM,MAAM,OAAO,SAAS,CAAC,MAAM;QACnC,IAAI,CAAC,IAAI,SAAS,EAAE;YAChB,yFAAyF;YACzF,MAAM,WAAW,oBAAoB,KAAK;YAC1C,OAAO,SAAS,MAAM,KAAK;QAC/B,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,gBAAgB,CAAC,MAAQ,cAAc,QAAQ,IAAI,QAAQ,KAAK,YAAY,aAAa,IAAI,CAAC,IAAI,SAAS;IAEjH,MAAM,eAAe,CAAC;QAClB,MAAM,QAAQ;YAAC;YAAS;YAAO;YAAS;SAAS;QACjD,OAAO,MAAM,OAAO,CAAC,KAAK;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACZ,6FAA6F;gBAC7F,GAAG,CAAC,IAAI,GAAG,MAAM,MAAM,GAAG,IAAI,QAAQ;YAC1C;YACA,OAAO;QACX,GAAG;YACC,MAAM,KAAK,IAAI;QACnB;IACJ;IACA,MAAM,wBAAwB,CAAC,MAAM;QACjC,IAAI,CAAC,0BAA0B,OAAO,qCAAqC,OACpE,0BAA0B,QAAQ,sCAAsC,GAAE,KAC1E,CAAC,YAAY,OAAO;YACvB,OAAO,wBAAwB,QAAQ;QAC3C;QACA,OAAO;IACX;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,eAAe;YAAE,GAAG,SAAS;QAAC;QACpC,IAAI,WAAW,QAAQ,MAAM,KAAK,KAAK,CAAC,sBAAsB,SAAS;YACnE,MAAM,SAAS,oBAAoB,aAAa,GAAG,EAAE,aAAa,MAAM,KAAK;YAC7E,aAAa,GAAG,GAAG,SAAS,SAAS;QACzC;QACA,IAAI,SAAS,IAAI,CAAC,aAAa,MAAM,EAAE,MAAM,MAAM,cAAc,YAAY,OAAO;YAChF,aAAa,MAAM,GAAG,qBAAqB;QAC/C;QACA,aAAa,IAAI,GAAG,sBAAsB,aAAa,IAAI,EAAE,sBAAsB;QACnF,OAAO;IACX;IACA,MAAM,aAAa,CAAC,QAAQ,WAAW,MAAM;QACzC,gEAAgE;QAChE,KAAK,IAAI,CAAC,CAAC;YACP,IAAI,IAAI,WAAW,cAAc;gBAC7B,UAAU,SAAS,GAAG;YAC1B,OACK;gBACD,UAAU,WAAW,GAAG;YAC5B;QACJ;QACA,OAAO,GAAG,CAAC,UAAU,CAAC,WAAW;QACjC,yEAAyE;QACzE,MAAM,MAAM,OAAO,GAAG,CAAC,SAAS;QAChC,IAAI,aAAa,CAAC;QAClB,IAAI,WAAW,CAAC;QAChB,OAAO,SAAS,CAAC,MAAM,CAAC;IAC5B;IACA,MAAM,aAAa,CAAC,QAAQ,aAAa,MAAM;QAC3C,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI,cAAc,cAAc;YAC5B,gBAAgB,KAAK,aAAa;QACtC,OACK;YACD,KAAK,IAAI,CAAC;gBACN,OAAO,WAAW,CAAC,iBAAiB,OAAO;gBAC3C,8GAA8G;gBAC9G,MAAM,MAAM,OAAO,SAAS,CAAC,MAAM;gBACnC,MAAM,MAAM,IAAI,SAAS;gBACzB,IAAI,aAAa,CAAC;gBAClB,IAAI,WAAW,CAAC;gBAChB,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B,GAAG,CAAC;gBACA,OAAO,aAAa,CAAC,IAAI,UAAU,CAAC,KAAK,WAAW,IAAI,MAAM,CAAC;YACnE;QACJ;IACJ;IACA,MAAM,kBAAkB,CAAC,QAAQ,aAAa;QAC1C,MAAM,cAAc,OAAO,SAAS,CAAC,OAAO;QAC5C,MAAM,YAAY,iBAAiB,QAAQ;QAC3C,MAAM,YAAY,mBAAmB,QAAQ,aAAa;QAC1D,MAAM,0BAA0B,sBAAsB;QACtD,IAAI,cAAc,0BAA0B;YACxC,wBAAwB;QAC5B;QACA,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,IAAI,KAAK,IAAI,KAAK,YAAY,IAAI,EAAE;gBAChC,YAAY,MAAM;YACtB;YACA,UAAU,IAAI,CAAC;gBACX,WAAW,QAAQ,aAAa,KAAK,IAAI,EAAE;YAC/C,GAAG,CAAC;gBACA,OAAO,KAAK;gBACZ,WAAW,QAAQ,KAAK,KAAK,IAAI,EAAE;YACvC;QACJ;IACJ;IACA,MAAM,kBAAkB,CAAC;QACrB,MAAM,MAAM,OAAO,GAAG,EAAE,YAAY,OAAO,SAAS;QACpD,MAAM,WAAW,UAAU,WAAW;QACtC,MAAM,MAAM,UAAU,MAAM,GAAG,UAAU;QACzC,wDAAwD;QACxD,MAAM,iBAAiB,IAAI,SAAS,CAAC,IAAI,cAAc,EAAE,WAAW,OAAO,OAAO;QAClF,MAAM,eAAe,IAAI,SAAS,CAAC,IAAI,YAAY,EAAE,WAAW,OAAO,OAAO;QAC9E,IAAI,gBAAgB;YAChB,IAAI,cAAc,CAAC;QACvB;QACA,IAAI,cAAc;YACd,IAAI,WAAW,CAAC;QACpB;QACA,UAAU,MAAM,CAAC;QACjB,kBAAkB;QAClB,OAAO,WAAW,CAAC;QACnB,UAAU,cAAc,CAAC;IAC7B;IACA,MAAM,oBAAoB,CAAC;QACvB,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,MAAM,OAAO,OAAO,SAAS,CAAC,OAAO;YACrC,IAAI,cAAc,OAAO;gBACrB,kBAAkB,QAAQ;YAC9B,OACK;gBACD,gBAAgB;YACpB;YACA,OAAO,KAAK;QAChB;IACJ;IACA;;;;KAIC,GACD,MAAM,gBAAgB,CAAC;QACnB,MAAM,EAAE,OAAO,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;QACvD,OAAO,OAAO;YACV,OAAO,IAAI,SAAS;YACpB;YACA,KAAK,IAAI,SAAS;YAClB,QAAQ,OAAO,SAAS;YACxB,MAAM,KAAK,SAAS;YACpB,OAAO,MAAM,SAAS;QAC1B,GAAG,CAAC,GAAG,KAAO,OAAO,OAAO;IAChC;IACA,MAAM,eAAe,CAAC,QAAQ;QAC1B,MAAM,YAAY,OAAO,OAAO,CAAC,GAAG;QACpC,MAAM,aAAa;YACf,sBAAsB,UAAU;YAChC,mBAAmB,UAAU;YAC7B,qBAAqB,UAAU;QACnC;QACA,mBAAmB;QACnB,MAAM,OAAO,KAAK,IAAI;QACtB,OAAO;YACH,GAAG,IAAI;YACP,MAAM,SAAS,SAAS,CAAC,MAAM,KAAK,cAAc,OAAO;QAC7D;IACJ;IACA,MAAM,OAAO,CAAC,QAAQ,aAAa;QAC/B,MAAM,gBAAgB,aAAa,QAAQ;QAC3C,OAAO,SAAS,CAAC,OAAO,QAAQ,OAAO,WAAW,CAAC,cAAc,OAAO,cAAc,kBAAkB,gBAAgB,QAAQ,aAAa;IACjJ;IACA,MAAM,SAAS,CAAC;QACZ,OAAO,SAAS,CAAC,OAAO,QAAQ,OAAO,WAAW,CAAC,YAAY,kBAAkB;IACrF;IACA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,IAAI;QACJ,MAAM,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE;QAC5C,IAAI,KAAK;YACL,MAAM,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE;YACvD,IAAI,GAAG;gBACH,CAAC,KAAK,EAAE,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,KAAK;gBAC9E,OAAO,GAAG,CAAC,MAAM,CAAC;YACtB;QACJ;IACJ;IACA,MAAM,kBAAkB,CAAC,KAAK,KAAK;QAC/B,IAAI;QACJ,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE;QACrC,IAAI,KAAK;YACL,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK;YAC1B,CAAC,KAAK,IAAI,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,GAAG;YAC9E,EAAE,WAAW,CAAC;QAClB;IACJ;IAEA,MAAM,WAAW,CAAC,OAAS,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;IAC/D,MAAM,UAAU,CAAC;QACb,IAAI,SAAS,KAAK,IAAI,GAAG;YACrB,OAAO,KAAK,IAAI;QACpB,OACK,IAAI,SAAS,KAAK,KAAK,GAAG;YAC3B,OAAO,KAAK,KAAK;QACrB,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC,MAAM;QACxB,MAAM,MAAM,EAAE;QACd,SAAS,IAAI,CAAC,MAAM,CAAC;YACjB,MAAM,OAAO,QAAQ;YACrB,IAAI,KAAK,IAAI,KAAK,WAAW;gBACzB,MAAM,QAAQ,aAAa,KAAK,IAAI,EAAE;gBACtC,IAAI,IAAI,CAAC;oBAAE;oBAAM;gBAAM,IAAI,aAAa;YAC5C,OACK;gBACD,MAAM,QAAQ,aAAa;gBAC3B,IAAI,IAAI,CAAC;oBAAE;oBAAM;gBAAM,IAAI,aAAa;YAC5C;QACJ;QACA,OAAO;IACX;IACA,MAAM,eAAe;YAAC,6EAAY;eAAa,CAAC,OAAS,SAAS,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,OAAS,aAAa,MAAM;;IAC9G,MAAM,WAAW,CAAC,OAAS,aAAa,UAAU;IAClD,2CAA2C;IAC3C,MAAM,WAAW,CAAC,MAAM,QAAU,CAAC,QAAU,CAAC;gBAC1C;gBACA,MAAM;gBACN;gBACA;YACJ,CAAC;IACD,MAAM,cAAc;QAChB;QACA;QACA;QACA;IACJ;IAEA,MAAM,cAAc,CAAC,OAAS,kBAAkB,MAAM;IACtD,MAAM,kBAAkB,CAAC,OAAO,UAAY,QAAQ,SAAS,CAAC;YAC1D,IAAI,YAAY,OAAO;gBACnB,OAAO,gBAAgB,OAAO,KAAK,KAAK;YAC5C,OACK;gBACD,OAAO,OAAO,KAAK,KAAK,KAAK,OAAO;YACxC;QACJ;IACA,MAAM,WAAW,CAAC,gBAAgB,WAAW,SAAS;QAClD,MAAM,QAAQ,IAAI,CAAC,UAAU;QAC7B,MAAM,oBAAoB,eAAe,MAAM,GAAG;QAClD,OAAO,UAAU,YAAY,gBAAgB,OAAO,SAAS,GAAG,CAAC,CAAC,IAAM,CAAC;gBACrE,KAAK;oBACD,OAAO,EAAE,KAAK;oBACd,MAAM;wBACF,MAAM,oBAAoB,iBAAiB,EAAE,IAAI;wBACjD,QAAQ;oBACZ;gBACJ;gBACA,MAAM,oBAAoB,iBAAiB,EAAE,IAAI;YACrD,CAAC,KAAK,SAAS,IAAI;IACvB;IACA,MAAM,cAAc,CAAC,UAAU;QAC3B,IAAI,cAAc,QAAQ;YACtB,OAAO,SAAS,IAAI;QACxB,OACK,IAAI,cAAc,UAAU;YAC7B,OAAO,SAAS,MAAM;QAC1B,OACK;YACD,OAAO,SAAS,IAAI;QACxB;IACJ;IACA,MAAM,OAAO,CAAC,aAAa;QACvB,MAAM,iBAAiB;YACnB,MAAM,YAAY,IAAI;YACtB,OAAO,YAAY,KAAK;QAC5B;QACA,MAAM,wBAAwB,CAAC;YAAU,IAAI;YAAI,OAAO,OAAO,eAAe,KAAK,CAAC,MAAM,IAAI,GAAG,SAAS,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,KAAK,CAAC;QAAM;QAC1L,MAAM,uBAAuB,CAAC;YAAU,IAAI;YAAI,OAAO,OAAO,eAAe,IAAI,CAAC,MAAM,IAAI,GAAG,SAAS,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK;QAAI;QAC9L,MAAM,cAAc,CAAC;YACjB,MAAM,OAAO,qBAAqB,KAAK,GAAG;YAC1C,MAAM,QAAQ,sBAAsB,KAAK,GAAG;YAC5C,8FAA8F;YAC9F,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,IAAI;gBACjC,OAAO,SAAS,IAAI,CAAC;oBACjB,GAAG,KAAK,GAAG,CAAC,CAAC,OAAS,CAAC;4BAAE;wBAAK,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE;oBAC3C,GAAG,MAAM,GAAG,CAAC,CAAC,QAAU,CAAC;4BAAE;wBAAM,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE;gBAClD;YACJ,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,MAAM,kBAAkB,CAAC,MAAM;YAC3B,MAAM,UAAU,YAAY,aAAa,QAAQ,KAAK,CAAC,EAAE;YACzD,OAAO,SAAS,eAAe,IAAI,EAAE,QAAQ,SAAS;QAC1D;QACA,MAAM,WAAW,CAAC,SAAS;YACvB,MAAM,OAAO,OAAO,IAAI;YACxB,IAAI,SAAS,OAAO;gBAChB,OAAO,YAAY;YACvB,OACK,IAAI,SAAS;gBAAC;gBAAU;aAAO,EAAE,OAAO;gBACzC,OAAO,gBAAgB,WAAW;YACtC,OACK,IAAI,SAAS,UAAU,SAAS,SAAS;gBAC1C,0EAA0E;gBAC1E,cAAc,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;gBACtC,OAAO,SAAS,IAAI;YACxB,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,OAAO;YACH;QACJ;IACJ;IACA,MAAM,gBAAgB;QAClB;QACA;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,QAAQ,SAAS;QACrC,MAAM,MAAM,OAAO,SAAS,CAAC,MAAM;QACnC,SAAS,gBAAgB,CAAC,QAAQ;YAC9B,OAAO,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC;gBACnC,OAAO,SAAS,CAAC,MAAM,CAAC;gBACxB,SAAS;YACb;QACJ;IACJ;IACA,MAAM,oBAAoB,CAAC;QACvB,MAAM,MAAM,KAAK,IAAI;QACrB,MAAM,gBAAgB,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,OAAO,CAAC,eAAe,CAAC;QACrG,OAAO,gBAAgB,SAAS,IAAI,CAAC;YACjC,SAAS;YACT,YAAY,CAAC,UAAY,CAAC;oBAAE,GAAG,OAAO;oBAAE,MAAM,YAAY;gBAAI,CAAC;QACnE,KAAK,SAAS,IAAI;IACtB;IACA,MAAM,uBAAuB,CAAC,uBAAuB,sBAAwB,CAAC;YAC1E,MAAM,MAAM,KAAK,IAAI;YACrB,MAAM,kBAAmB,0BAA0B,EAAE,8BAA8B,OAAM,CAAC,YAAY,QAClG,0BAA0B,EAAE,6BAA6B,OAAM,oBAAoB,IAAI,CAAC;YAC5F,OAAO,kBAAkB,SAAS,IAAI,CAAC;gBACnC,SAAS,AAAC,qFAAwG,OAApB,qBAAoB;gBAClH,YAAY,CAAC,UAAY,CAAC;wBAAE,GAAG,OAAO;wBAAE,MAAM,sBAAsB,QAAQ;oBAAI,CAAC;YACrF,KAAK,SAAS,IAAI;QACtB;IACA,MAAM,aAAa,CAAC,QAAQ,OAAS,QAAQ;YAAC;YAAmB,qBAAqB,sBAAsB,SAAS,uBAAuB;SAAS,EAAE,CAAC,IAAM,EAAE,OAAO,IAAI,CAAC,IAAM,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAc,IAAI,QAAQ,CAAC;gBACjO,eAAe,QAAQ,UAAU,OAAO,EAAE,CAAC;oBACvC,SAAS,QAAQ,UAAU,UAAU,CAAC,QAAQ;gBAClD;YACJ;IACA,MAAM,iBAAiB;QACnB;IACJ;IAEA,2EAA2E;IAC3E,MAAM,aAAa,CAAC;QAChB,MAAM,cAAc,OAAO,GAAG,CAAC,MAAM,CAAC;QACtC,MAAM,UAAU,KAAK,aAAa,CAAC;YAC/B,MAAM,KAAK,OAAO,IAAI,IAAI,OAAO,EAAE;YACnC,OAAO,KAAK;gBAAC;oBAAE,MAAM;oBAAI,OAAO,MAAM;gBAAG;aAAE,GAAG,EAAE;QACpD;QACA,OAAO,QAAQ,MAAM,GAAG,IAAI,SAAS,IAAI,CAAC;YAAC;gBAAE,MAAM;gBAAQ,OAAO;YAAG;SAAE,CAAC,MAAM,CAAC,YAAY,SAAS,IAAI;IAC5G;IACA,MAAM,oBAAoB;QACtB;IACJ;IAEA,0DAA0D;IAC1D,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,iBAAiB;QAC9B,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,OAAO,YAAY,QAAQ,CAAC;QAChC;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,mBAAmB;QACrB;IACJ;IAEA,MAAM,YAAY,CAAC;QACf,4BAA4B;QAC5B,IAAI;YACA,OAAO,SAAS,IAAI,CAAC,KAAK,KAAK,CAAC;QACpC,EACA,OAAO,IAAI;YACP,OAAO,SAAS,IAAI;QACxB;IACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,YAAY,CAAC,OAAS,OAAO,UAAU,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,IAAI;QAC5E,MAAM,WAAW,YAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC;YAChB,oCAAoC;YACpC,IAAI,SAAS,WAAW;gBACpB,MAAM,UACD,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,QAAQ,MAAM,IAClE,IAAI,CAAC,SAAS,IAAM,QAAQ,SAAS,IAAI;YAClD,OACK,IAAI,WAAW,WAAW;gBAC3B,SAAS,CAAC,SAAW,QAAQ,SAAS,IAAI,CAAC;YAC/C,OACK;gBACD,QAAQ,SAAS,IAAI,CAAC;YAC1B;QACJ,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,CAAC,YAAY,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC;gBAC1E,IAAI,MAAM,MAAM,GAAG,GAAG;oBAClB,MAAM,WAAW;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAG;qBAAE;oBAC9C,OAAO,SAAS,MAAM,CAAC;gBAC3B,OACK;oBACD,OAAO;gBACX;YACJ;IACJ;IACA,MAAM,kBAAkB;QACpB;IACJ;IAEA,MAAM,UAAU,CAAC,QAAQ;QACrB,MAAM,OAAO,WAAW;QACxB,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,MAAM,gBAAgB,GAAG,eAAe;YACxC,MAAM,cAAc,sBAAsB,YAAY;YACtD,MAAM,mBAAmB,CAAC,OAAS,oBAAoB,YAAY,QAAQ,CAAC,OAAO;YACnF,MAAM,YAAY,cAAc,YAAY,YAAY,CAAC,oBAAoB,YAAY,QAAQ;YACjG,OAAO,UAAU;QACrB;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,aAAa;QACf;IACJ;IAEA,mDAAmD;IACnD,qCAAqC;IACrC,MAAM,YAAY;QACd;YAAE,MAAM;YAAkB,OAAO;QAAG;QACpC;YAAE,MAAM;YAAc,OAAO;QAAS;KACzC;IACD,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,cAAc;QAC3B,IAAI,QAAQ,OAAO;YACf,OAAO,YAAY,QAAQ,CAAC,MAAM,OAAO,CAAC,IAAM,SAAS,IAAI,CAAC;QAClE,OACK,IAAI,SAAS,OAAO;YACrB,OAAO,SAAS,IAAI;QACxB;QACA,OAAO,SAAS,IAAI,CAAC;IACzB;IACA,MAAM,gBAAgB;QAClB;IACJ;IAEA,MAAM,eAAe,CAAC,KAAK,MAAM;QAC7B,MAAM,MAAM,IAAI,SAAS,CAAC,MAAM;QAChC,OAAO,QAAQ,QAAQ,IAAI,MAAM,GAAG,IAAI,SAAS,IAAI,CAAC,OAAO,SAAS,IAAI;IAC9E;IACA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,WAAW,mBAAmB;QACpC,MAAM,OAAO,WAAW,SAAS,IAAI,CAAC,cAAc,OAAO,SAAS,EAAE,WAAW,SAAS,IAAI;QAC9F,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC,YAAc,SAAS,IAAI,CAAC,IAAI,SAAS,CAAC,WAAW;QAC9E,MAAM,SAAS,OAAO,IAAI,CAAC,CAAC,YAAc,SAAS,IAAI,CAAC,IAAI,SAAS,CAAC,WAAW;QACjF,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC,YAAc,aAAa,KAAK,WAAW;QACpE,MAAM,YAAY,OAAO,IAAI,CAAC,CAAC,YAAc,aAAa,KAAK,WAAW;QAC1E,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAC,YAAc,aAAa,KAAK,WAAW;QACtE,OAAO;YACH;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,UAAU,CAAC,QAAQ,WAAa,gBAAgB,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;YACzE,MAAM,SAAS,kBAAkB,QAAQ;YACzC,OAAO;gBACH;gBACA,UAAU;oBACN,SAAS,cAAc,UAAU,CAAC;oBAClC,wDAAwD;oBACxD,MAAM,WAAW,OAAO,CAAC,QAAQ,OAAO,MAAM;oBAC9C,SAAS,iBAAiB,UAAU,CAAC;oBACrC,QAAQ,kBAAkB,UAAU,CAAC;oBACrC,MAAM;gBACV;gBACA,SAAS;gBACT,OAAO;oBACH,cAAc,oBAAoB;gBACtC;YACJ;QACJ;IACA,MAAM,aAAa;QACf;IACJ;IAEA,MAAM,eAAe,CAAC,QAAQ,OAAS,CAAC;YACpC,MAAM,OAAO,IAAI,OAAO;YACxB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE;gBACjB,OAAO;gBACP,iCAAiC;gBACjC,IAAI,KAAK;gBACT;YACJ;YACA,6EAA6E;YAC7E,qEAAqE;YACrE,MAAM,kBAAkB,CAAC,MAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAU,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,EAAE;YAClG,MAAM,cAAc;gBAChB,MAAM,KAAK,GAAG,CAAC,KAAK;gBACpB,MAAM,gBAAgB;gBACtB,QAAQ,gBAAgB;gBACxB,KAAK,gBAAgB;gBACrB,OAAO,gBAAgB;gBACvB,OAAO,gBAAgB;YAC3B;YACA,MAAM,cAAc;gBAChB,MAAM,KAAK,GAAG,CAAC,KAAK;gBACpB,QAAQ,KAAK,GAAG,CAAC,IAAI,KAAK,aAAa,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;YACzF;YACA,mEAAmE;YACnE,eAAe,UAAU,CAAC,QAAQ,aAAa,IAAI,CAAC,CAAC;gBACjD,KAAK,QAAQ,aAAa;YAC9B;YACA,IAAI,KAAK;QACb;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,aAAa,iBAAiB;QACpC,OAAO,WAAW,OAAO,CAAC,QAAQ;IACtC;IACA,MAAM,iBAAiB,CAAC,MAAM;QAC1B,MAAM,SAAS,KAAK,MAAM;QAC1B,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC;QAC7B,OAAO;YACH,KAAK;gBACD,OAAO;gBACP,MAAM;oBACF,UAAU;wBACN,OAAO;oBACX;gBACJ;YACJ;YACA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC;YACxB,OAAO,OAAO,KAAK,CAAC,KAAK,CAAC;YAC1B,QAAQ;YACR,MAAM;YACN,KAAK,OAAO,GAAG,CAAC,KAAK,CAAC;YACtB,QAAQ,OAAO,MAAM,CAAC,EAAE,CAAC,eAAe,KAAK,CAAC;YAC9C,WAAW,OAAO,SAAS,CAAC,KAAK,CAAC;QACtC;IACJ;IACA,MAAM,aAAa,CAAC,UAAU,UAAU;QACpC,MAAM,WAAW;YACb;gBACI,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACjB;SACH;QACD,MAAM,cAAc,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAM,CAAC;gBAChD,MAAM;gBACN,MAAM;gBACN,OAAO;YACX,CAAC,GAAG,OAAO;QACX,MAAM,YAAY,SAAS,KAAK,CAAC,YAAY,GAAG;YAC5C;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;SACH,GAAG,EAAE;QACN,MAAM,gBAAgB,SAAS,IAAI,CAAC,qBAAqB;QACzD,MAAM,cAAc,eAAe,UAAU;QAC7C,MAAM,WAAW,SAAS,QAAQ;QAClC,MAAM,cAAc,cAAc,IAAI,CAAC,aAAa;QACpD,MAAM,OAAO;YACT,MAAM;YACN,OAAO,QAAQ;gBACX;gBACA;gBACA;gBACA,IAAI;oBACA,SAAS,MAAM,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,UAAU;oBACnD,SAAS,IAAI,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,OAAO;oBAC9C,SAAS,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,UAAU;oBACpD,SAAS,IAAI,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,QAAQ;oBAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,aAAa;iBAC1D;aACJ;QACL;QACA,OAAO;YACH,OAAO;YACP,MAAM;YACN;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD;YACA,UAAU,CAAC;oBAAK,EAAE,IAAI,EAAE;gBACpB,YAAY,QAAQ,CAAC,IAAI,OAAO,EAAE;oBAAE;gBAAK,GAAG,IAAI,CAAC,CAAC;oBAC9C,IAAI,OAAO,CAAC;gBAChB;YACJ;YACA;QACJ;IACJ;IACA,MAAM,OAAO,CAAC;QACV,MAAM,OAAO,YAAY;QACzB,mEAAmE;QACnE,KAAK,IAAI,CAAC,CAAC;YACP,MAAM,WAAW,aAAa,QAAQ;YACtC,OAAO,WAAW,MAAM,UAAU;QACtC,GAAG,IAAI,CAAC,CAAC;YACL,OAAO,aAAa,CAAC,IAAI,CAAC;QAC9B;IACJ;IAEA,MAAM,WAAW,CAAC;QACd,OAAO,UAAU,CAAC,WAAW,CAAC,KAAK;YAC/B,IAAI,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,QAAQ,CAAC,aAAa,SAAS;gBAChG,KAAK;YACT,OACK;gBACD,OAAO,QAAQ,CAAC,uBAAuB;oBACnC,YAAY;gBAChB;YACJ;QACJ;IACJ;IAEA,MAAM,UAAU,CAAC;QACb,OAAO,WAAW,CAAC,UAAU,IAAI;YAC7B,OAAO,WAAW,CAAC;QACvB;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,oBAAoB,CAAC,MAAM;QAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,aAAa,CAAC;QACnB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC9B;IACA,MAAM,WAAW,CAAC;QACd,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,MAAM,GAAG;QACd,KAAK,IAAI,GAAG;QACZ,KAAK,GAAG,GAAG;QACX,MAAM,MAAM,IAAI,WAAW,SAAS;YAChC,SAAS;YACT,YAAY;YACZ,MAAM;QACV;QACA,SAAS,aAAa,CAAC;QACvB,kBAAkB,MAAM;IAC5B;IACA,MAAM,qBAAqB,CAAC;QACxB,OAAO,EAAE,MAAM,KAAK,QAAQ,EAAE,QAAQ,KAAK,SAAS,EAAE,OAAO,KAAK,SAAS,EAAE,OAAO,KAAK;IAC7F;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,IAAI,GAAG;YACH,MAAM,OAAO,QAAQ;YACrB,IAAI,KAAK,IAAI,CAAC,OAAO;gBACjB,MAAM,WAAW,OAAO,GAAG,CAAC,MAAM,CAAC,AAAC,GAAiB,OAAf,MAAK,YAAmC,OAAzB,cAAc,MAAM,MAAK;gBAC9E,IAAI,SAAS,MAAM,EAAE;oBACjB,OAAO,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACjD;YACJ,OACK;gBACD,SAAS,EAAE,IAAI;YACnB;QACJ;IACJ;IACA,MAAM,qCAAqC,CAAC;QACxC,MAAM,MAAM,OAAO,SAAS,CAAC,MAAM;QACnC,MAAM,OAAO,IAAI,cAAc;QAC/B,gEAAgE;QAChE,OAAO,OAAO,SAAS,IAAI,cAAc,KAAK,IAAI,YAAY,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,MAAM,MAAM,KAAK;IAChH;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,QAAQ,WAAW,OAAO,GAAG,CAAC,UAAU,CAAC;QAC/C,OAAO,OAAO,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE;IAC9C;IACA,MAAM,qBAAqB,CAAC;QACxB,MAAM,QAAQ,oBAAoB,OAAO,SAAS,CAAC,MAAM;QACzD,OAAO,OAAO,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,mBAAmB,QAAQ,OAAO,SAAS,CAAC,OAAO;IACpG;IACA,MAAM,uBAAuB,CAAC,SAAW,OAAO,SAAS,CAAC,WAAW,MAAM,mCAAmC,UACxG,mBAAmB,QAAQ,OAAO,SAAS,CAAC,QAAQ,MACpD,mBAAmB;IACzB,MAAM,UAAU,CAAC;QACb,MAAM,eAAe;QACrB,MAAM,kBAAkB,IAAM,aAAa,GAAG,GAAG,EAAE,CAAC,qBAAqB;QACzE,MAAM,mBAAmB,IAAM,kBAAkB,IAAI,CAAC,CAAC,OAAS,SAAS,QAAQ;QACjF,OAAO,EAAE,CAAC,eAAe,CAAC;YACtB,mBAAmB,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,GAAG;QAC9D;QACA,OAAO,EAAE,CAAC,mBAAmB;YACzB,IAAI,CAAC,aAAa,KAAK,IAAI;gBACvB,qBAAqB,QAAQ,IAAI,CAAC,aAAa,GAAG;YACtD;QACJ;QACA,OAAO,EAAE,CAAC,SAAS,CAAC;YAChB,aAAa,KAAK;YAClB,MAAM,QAAQ,WAAW,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,MAAM;YACvD,IAAI,MAAM,MAAM,KAAK,KAAK,OAAO,cAAc,CAAC,IAAI;gBAChD,EAAE,cAAc;gBAChB,SAAS,QAAQ,KAAK,CAAC,EAAE;YAC7B;QACJ;QACA,OAAO,EAAE,CAAC,WAAW,CAAC;YAClB,aAAa,KAAK;YAClB,IAAI,CAAC,EAAE,kBAAkB,MAAM,EAAE,OAAO,KAAK,MAAM,mBAAmB,IAAI;gBACtE,kBAAkB,IAAI,CAAC,CAAC;oBACpB,EAAE,cAAc;oBAChB,SAAS,QAAQ;gBACrB;YACJ;QACJ;QACA,OAAO;YACH;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC,SAAW;YAC3B,OAAO,WAAW,CAAC,WAAW,OAAO;gBAAE,QAAQ;YAAK;QACxD;IACA,MAAM,cAAc,CAAC,QAAQ;QACzB,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,IAAM,OAAO,GAAG,CAAC,cAAc;IAC1C;IACA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,SAAS,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,CAAC,OAAO;gBACtF,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA;YACA,OAAO,YAAY,QAAQ;QAC/B;IACA,MAAM,sBAAsB,CAAC,SAAW,CAAC;YACrC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA;YACA,OAAO,YAAY,QAAQ;QAC/B;IACA,MAAM,0BAA0B,CAAC,SAAW,CAAC;YACzC,MAAM,aAAa,CAAC,UAAY,SAAS,YAAY,oBAAoB,OAAO,SAAS,CAAC,MAAM;YAChG,MAAM,UAAU,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,SAAS,CAAC,QAAQ;YAC/D,MAAM,gBAAgB,CAAC;gBACnB,IAAI,UAAU,CAAC,WAAW,YAAY,OAAO,SAAS,CAAC,UAAU;YACrE;YACA,cAAc;YACd,OAAO,YAAY,QAAQ,CAAC,IAAM,cAAc,EAAE,OAAO;QAC7D;IACA,MAAM,eAAe,CAAC,QAAQ;QAC1B,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ;YACvC,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU,WAAW;YACrB,SAAS,gBAAgB;QAC7B;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY;YACrC,MAAM;YACN,SAAS;YACT,UAAU,SAAS,gBAAgB;YACnC,SAAS,wBAAwB;QACrC;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YACnC,MAAM;YACN,SAAS;YACT,UAAU,IAAM,OAAO;YACvB,SAAS,wBAAwB;QACrC;IACJ;IACA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,YAAY;YACvC,MAAM;YACN,MAAM;YACN,UAAU,SAAS,gBAAgB;YACnC,SAAS,wBAAwB;QACrC;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ;YACnC,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU,WAAW;YACrB,SAAS,oBAAoB;QACjC;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU;YACrC,MAAM;YACN,MAAM;YACN,UAAU,IAAM,OAAO;YACvB,SAAS,wBAAwB;QACrC;IACJ;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,SAAS;QACf,MAAM,SAAS;QACf,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ;YACtC,QAAQ,CAAC;gBACL,MAAM,aAAa,OAAO,GAAG,CAAC,UAAU,CAAC;gBACzC,IAAI,CAAC,YAAY;oBACb,OAAO;gBACX;gBACA,OAAO,SAAS,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,QAAQ,SAAS;YACpE;QACJ;IACJ;IACA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,MAAM,yBAAyB,CAAC;YAC5B,OAAO,SAAS,CAAC,QAAQ,CAAC;QAC9B;QACA,MAAM,cAAc,CAAC;YACjB,MAAM,OAAO,OAAO,SAAS,CAAC,OAAO;YACrC,UAAU,UAAU,CAAC,WAAW,QAAQ,SAAS,OAAO,SAAS,CAAC,UAAU;YAC5E,OAAO;QACX;QACA;;;;;SAKC,GACD,MAAM,cAAc,CAAC;YACjB,MAAM,SAAS,iBAAiB;YAChC,MAAM,WAAW,mBAAmB;YACpC,IAAI,OAAO,MAAM,MAAM,UAAU;gBAC7B,MAAM,OAAO,cAAc,OAAO,SAAS,EAAE;gBAC7C,OAAO,OAAO,KAAK,MAAM,KAAK,GAAG;YACrC,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa;YAC3C,QAAQ;gBACJ,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,gBAAgB;YAC7B;YACA,OAAO;YACP,WAAW,CAAC,OAAS,kBAAkB,WAAW,WAAW,QAAQ;YACrE,WAAW;gBACP,MAAM,MAAM,iBAAiB;gBAC7B,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK;YAClC;YACA,UAAU;gBACN;oBACI,MAAM;oBACN,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,SAAS,CAAC;wBACN,MAAM,OAAO,OAAO,SAAS,CAAC,OAAO;wBACrC,oCAAoC;wBACpC,UAAU,SAAS,CAAC,WAAW,QAAQ;wBACvC,OAAO,gBAAgB,QAAQ;oBACnC;oBACA,UAAU,CAAC;wBACP,MAAM,QAAQ,QAAQ,QAAQ;wBAC9B,MAAM,OAAO,YAAY;wBACzB,MAAM,cAAc;4BAAE,MAAM;4BAAO,QAAQ;wBAAK;wBAChD,KAAK,QAAQ,aAAa;4BACtB,MAAM;4BACN;4BACA,OAAO,SAAS,IAAI;4BACpB,KAAK,SAAS,IAAI;4BAClB,QAAQ,SAAS,IAAI,CAAC,qBAAqB;4BAC3C,OAAO,SAAS,IAAI;wBACxB;wBACA,uBAAuB;wBACvB,QAAQ,IAAI;oBAChB;gBACJ;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,0FAA0F;oBAC1F,UAAU,CAAC;wBACP,OAAO;wBACP,QAAQ,IAAI;oBAChB;gBACJ;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,UAAU,CAAC;wBACP,SAAS,gBAAgB;wBACzB,QAAQ,IAAI;oBAChB;gBACJ;aACH;QACL;IACJ;IACA,MAAM,QAAQ,CAAC;QACX,MAAM,WAAW,QAAQ;QACzB,aAAa,QAAQ;QACrB,eAAe,QAAQ;QACvB,iBAAiB;QACjB,qBAAqB,QAAQ;IACjC;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,QAAQ,CAAC;YAClB,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;QACZ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/link/index.js"], "sourcesContent": ["// Exports the \"link\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/link')\n//   ES2015:\n//     import 'tinymce/plugins/link'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,SAAS;AACT,cAAc;AACd,sCAAsC;AACtC,YAAY;AACZ,oCAAoC", "ignoreList": [0], "debugId": null}}]}