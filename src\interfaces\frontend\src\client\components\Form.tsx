"use client";

import { useState } from "react";

export default function Form({ onGenerate }: { onGenerate: (theme: string, description: string) => Promise<void> }) {
  const [theme, setTheme] = useState("");
  const [description, setDescription] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    await onGenerate(theme, description);
    setIsLoading(false);
  };

  return (
    <div>
      <input
        type="text"
        placeholder="Thème"
        value={theme}
        onChange={(e) => setTheme(e.target.value)}
        className="w-full border border-gray-300 rounded-md p-3 mb-3 text-gray-900 placeholder-gray-500"
      />
      <textarea
        placeholder="Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        rows={4}
        className="w-full border border-gray-300 rounded-md p-3 mb-3 resize-none text-gray-900 placeholder-gray-500"
      />
      <button
        onClick={handleClick}
        disabled={isLoading}
        className={`w-full py-3 rounded-md transition ${
          isLoading
            ? "bg-purple-400 cursor-not-allowed"
            : "bg-purple-600 hover:bg-purple-700 text-white"
        }`}
      >
        {isLoading ? "Génération en cours..." : "Générer"}
      </button>
    </div>
  );
}
