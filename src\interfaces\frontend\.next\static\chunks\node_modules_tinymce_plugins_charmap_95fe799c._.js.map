{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/charmap/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const fireInsertCustomChar = (editor, chr) => {\n        return editor.dispatch('insertCustomChar', { chr });\n    };\n\n    const insertChar = (editor, chr) => {\n        const evtChr = fireInsertCustomChar(editor, chr).chr;\n        editor.execCommand('mceInsertContent', false, evtChr);\n    };\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq = (t) => (a) => t === a;\n    const isArray$1 = isType('array');\n    const isNull = eq(null);\n    const isUndefined = eq(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const never = constant(false);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray$1(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    const contains = (str, substr, start = 0, end) => {\n        const idx = str.indexOf(substr, start);\n        if (idx !== -1) {\n            return isUndefined(end) ? true : idx + substr.length <= end;\n        }\n        else {\n            return false;\n        }\n    };\n    const fromCodePoint = String.fromCodePoint;\n\n    // Run a function fn after rate ms. If another invocation occurs\n    // during the time it is waiting, reschedule the function again\n    // with the new arguments.\n    const last = (fn, rate) => {\n        let timer = null;\n        const cancel = () => {\n            if (!isNull(timer)) {\n                clearTimeout(timer);\n                timer = null;\n            }\n        };\n        const throttle = (...args) => {\n            cancel();\n            timer = setTimeout(() => {\n                timer = null;\n                fn.apply(null, args);\n            }, rate);\n        };\n        return {\n            cancel,\n            throttle\n        };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        const charMapProcessor = (value) => isFunction(value) || isArray$1(value);\n        registerOption('charmap', {\n            processor: charMapProcessor,\n        });\n        registerOption('charmap_append', {\n            processor: charMapProcessor\n        });\n    };\n    const getCharMap$1 = option('charmap');\n    const getCharMapAppend = option('charmap_append');\n\n    const isArray = global.isArray;\n    const UserDefined = 'User Defined';\n    const getDefaultCharMap = () => {\n        return [\n            // TODO: Merge categories with TBIO\n            // {\n            //   name: 'Unknown',\n            //   characters : [\n            //     [160, 'no-break space'],\n            //     [173, 'soft hyphen'],\n            //     [34, 'quotation mark']\n            //   ]\n            // },\n            { name: 'Currency',\n                characters: [\n                    [36, 'dollar sign'],\n                    [162, 'cent sign'],\n                    [8364, 'euro sign'],\n                    [163, 'pound sign'],\n                    [165, 'yen sign'],\n                    [164, 'currency sign'],\n                    [8352, 'euro-currency sign'],\n                    [8353, 'colon sign'],\n                    [8354, 'cruzeiro sign'],\n                    [8355, 'french franc sign'],\n                    [8356, 'lira sign'],\n                    [8357, 'mill sign'],\n                    [8358, 'naira sign'],\n                    [8359, 'peseta sign'],\n                    [8360, 'rupee sign'],\n                    [8361, 'won sign'],\n                    [8362, 'new sheqel sign'],\n                    [8363, 'dong sign'],\n                    [8365, 'kip sign'],\n                    [8366, 'tugrik sign'],\n                    [8367, 'drachma sign'],\n                    [8368, 'german penny symbol'],\n                    [8369, 'peso sign'],\n                    [8370, 'guarani sign'],\n                    [8371, 'austral sign'],\n                    [8372, 'hryvnia sign'],\n                    [8373, 'cedi sign'],\n                    [8374, 'livre tournois sign'],\n                    [8375, 'spesmilo sign'],\n                    [8376, 'tenge sign'],\n                    [8377, 'indian rupee sign'],\n                    [8378, 'turkish lira sign'],\n                    [8379, 'nordic mark sign'],\n                    [8380, 'manat sign'],\n                    [8381, 'ruble sign'],\n                    [20870, 'yen character'],\n                    [20803, 'yuan character'],\n                    [22291, 'yuan character, in hong kong and taiwan'],\n                    [22278, 'yen/yuan character variant one']\n                ]\n            },\n            { name: 'Text',\n                characters: [\n                    [169, 'copyright sign'],\n                    [174, 'registered sign'],\n                    [8482, 'trade mark sign'],\n                    [8240, 'per mille sign'],\n                    [181, 'micro sign'],\n                    [183, 'middle dot'],\n                    [8226, 'bullet'],\n                    [8230, 'three dot leader'],\n                    [8242, 'minutes / feet'],\n                    [8243, 'seconds / inches'],\n                    [167, 'section sign'],\n                    [182, 'paragraph sign'],\n                    [223, 'sharp s / ess-zed']\n                ]\n            },\n            { name: 'Quotations',\n                characters: [\n                    [8249, 'single left-pointing angle quotation mark'],\n                    [8250, 'single right-pointing angle quotation mark'],\n                    [171, 'left pointing guillemet'],\n                    [187, 'right pointing guillemet'],\n                    [8216, 'left single quotation mark'],\n                    [8217, 'right single quotation mark'],\n                    [8220, 'left double quotation mark'],\n                    [8221, 'right double quotation mark'],\n                    [8218, 'single low-9 quotation mark'],\n                    [8222, 'double low-9 quotation mark'],\n                    [60, 'less-than sign'],\n                    [62, 'greater-than sign'],\n                    [8804, 'less-than or equal to'],\n                    [8805, 'greater-than or equal to'],\n                    [8211, 'en dash'],\n                    [8212, 'em dash'],\n                    [175, 'macron'],\n                    [8254, 'overline'],\n                    [164, 'currency sign'],\n                    [166, 'broken bar'],\n                    [168, 'diaeresis'],\n                    [161, 'inverted exclamation mark'],\n                    [191, 'turned question mark'],\n                    [710, 'circumflex accent'],\n                    [732, 'small tilde'],\n                    [176, 'degree sign'],\n                    [8722, 'minus sign'],\n                    [177, 'plus-minus sign'],\n                    [247, 'division sign'],\n                    [8260, 'fraction slash'],\n                    [215, 'multiplication sign'],\n                    [185, 'superscript one'],\n                    [178, 'superscript two'],\n                    [179, 'superscript three'],\n                    [188, 'fraction one quarter'],\n                    [189, 'fraction one half'],\n                    [190, 'fraction three quarters']\n                ]\n            },\n            {\n                name: 'Mathematical',\n                characters: [\n                    [402, 'function / florin'],\n                    [8747, 'integral'],\n                    [8721, 'n-ary sumation'],\n                    [8734, 'infinity'],\n                    [8730, 'square root'],\n                    [8764, 'similar to'],\n                    [8773, 'approximately equal to'],\n                    [8776, 'almost equal to'],\n                    [8800, 'not equal to'],\n                    [8801, 'identical to'],\n                    [8712, 'element of'],\n                    [8713, 'not an element of'],\n                    [8715, 'contains as member'],\n                    [8719, 'n-ary product'],\n                    [8743, 'logical and'],\n                    [8744, 'logical or'],\n                    [172, 'not sign'],\n                    [8745, 'intersection'],\n                    [8746, 'union'],\n                    [8706, 'partial differential'],\n                    [8704, 'for all'],\n                    [8707, 'there exists'],\n                    [8709, 'diameter'],\n                    [8711, 'backward difference'],\n                    [8727, 'asterisk operator'],\n                    [8733, 'proportional to'],\n                    [8736, 'angle']\n                ]\n            },\n            // TODO: Merge categories with TBIO\n            // {\n            //   name: 'Undefined',\n            //   characters: [\n            //     [180, 'acute accent'],\n            //     [184, 'cedilla'],\n            //     [170, 'feminine ordinal indicator'],\n            //     [186, 'masculine ordinal indicator'],\n            //     [8224, 'dagger'],\n            //     [8225, 'double dagger']\n            //   ]\n            // },\n            {\n                name: 'Extended Latin',\n                characters: [\n                    [192, 'A - grave'],\n                    [193, 'A - acute'],\n                    [194, 'A - circumflex'],\n                    [195, 'A - tilde'],\n                    [196, 'A - diaeresis'],\n                    [197, 'A - ring above'],\n                    [256, 'A - macron'],\n                    [198, 'ligature AE'],\n                    [199, 'C - cedilla'],\n                    [200, 'E - grave'],\n                    [201, 'E - acute'],\n                    [202, 'E - circumflex'],\n                    [203, 'E - diaeresis'],\n                    [274, 'E - macron'],\n                    [204, 'I - grave'],\n                    [205, 'I - acute'],\n                    [206, 'I - circumflex'],\n                    [207, 'I - diaeresis'],\n                    [298, 'I - macron'],\n                    [208, 'ETH'],\n                    [209, 'N - tilde'],\n                    [210, 'O - grave'],\n                    [211, 'O - acute'],\n                    [212, 'O - circumflex'],\n                    [213, 'O - tilde'],\n                    [214, 'O - diaeresis'],\n                    [216, 'O - slash'],\n                    [332, 'O - macron'],\n                    [338, 'ligature OE'],\n                    [352, 'S - caron'],\n                    [217, 'U - grave'],\n                    [218, 'U - acute'],\n                    [219, 'U - circumflex'],\n                    [220, 'U - diaeresis'],\n                    [362, 'U - macron'],\n                    [221, 'Y - acute'],\n                    [376, 'Y - diaeresis'],\n                    [562, 'Y - macron'],\n                    [222, 'THORN'],\n                    [224, 'a - grave'],\n                    [225, 'a - acute'],\n                    [226, 'a - circumflex'],\n                    [227, 'a - tilde'],\n                    [228, 'a - diaeresis'],\n                    [229, 'a - ring above'],\n                    [257, 'a - macron'],\n                    [230, 'ligature ae'],\n                    [231, 'c - cedilla'],\n                    [232, 'e - grave'],\n                    [233, 'e - acute'],\n                    [234, 'e - circumflex'],\n                    [235, 'e - diaeresis'],\n                    [275, 'e - macron'],\n                    [236, 'i - grave'],\n                    [237, 'i - acute'],\n                    [238, 'i - circumflex'],\n                    [239, 'i - diaeresis'],\n                    [299, 'i - macron'],\n                    [240, 'eth'],\n                    [241, 'n - tilde'],\n                    [242, 'o - grave'],\n                    [243, 'o - acute'],\n                    [244, 'o - circumflex'],\n                    [245, 'o - tilde'],\n                    [246, 'o - diaeresis'],\n                    [248, 'o slash'],\n                    [333, 'o macron'],\n                    [339, 'ligature oe'],\n                    [353, 's - caron'],\n                    [249, 'u - grave'],\n                    [250, 'u - acute'],\n                    [251, 'u - circumflex'],\n                    [252, 'u - diaeresis'],\n                    [363, 'u - macron'],\n                    [253, 'y - acute'],\n                    [254, 'thorn'],\n                    [255, 'y - diaeresis'],\n                    [563, 'y - macron'],\n                    [913, 'Alpha'],\n                    [914, 'Beta'],\n                    [915, 'Gamma'],\n                    [916, 'Delta'],\n                    [917, 'Epsilon'],\n                    [918, 'Zeta'],\n                    [919, 'Eta'],\n                    [920, 'Theta'],\n                    [921, 'Iota'],\n                    [922, 'Kappa'],\n                    [923, 'Lambda'],\n                    [924, 'Mu'],\n                    [925, 'Nu'],\n                    [926, 'Xi'],\n                    [927, 'Omicron'],\n                    [928, 'Pi'],\n                    [929, 'Rho'],\n                    [931, 'Sigma'],\n                    [932, 'Tau'],\n                    [933, 'Upsilon'],\n                    [934, 'Phi'],\n                    [935, 'Chi'],\n                    [936, 'Psi'],\n                    [937, 'Omega'],\n                    [945, 'alpha'],\n                    [946, 'beta'],\n                    [947, 'gamma'],\n                    [948, 'delta'],\n                    [949, 'epsilon'],\n                    [950, 'zeta'],\n                    [951, 'eta'],\n                    [952, 'theta'],\n                    [953, 'iota'],\n                    [954, 'kappa'],\n                    [955, 'lambda'],\n                    [956, 'mu'],\n                    [957, 'nu'],\n                    [958, 'xi'],\n                    [959, 'omicron'],\n                    [960, 'pi'],\n                    [961, 'rho'],\n                    [962, 'final sigma'],\n                    [963, 'sigma'],\n                    [964, 'tau'],\n                    [965, 'upsilon'],\n                    [966, 'phi'],\n                    [967, 'chi'],\n                    [968, 'psi'],\n                    [969, 'omega']\n                ]\n            },\n            {\n                name: 'Symbols',\n                characters: [\n                    [8501, 'alef symbol'],\n                    [982, 'pi symbol'],\n                    [8476, 'real part symbol'],\n                    [978, 'upsilon - hook symbol'],\n                    [8472, 'Weierstrass p'],\n                    [8465, 'imaginary part']\n                ]\n            },\n            {\n                name: 'Arrows',\n                characters: [\n                    [8592, 'leftwards arrow'],\n                    [8593, 'upwards arrow'],\n                    [8594, 'rightwards arrow'],\n                    [8595, 'downwards arrow'],\n                    [8596, 'left right arrow'],\n                    [8629, 'carriage return'],\n                    [8656, 'leftwards double arrow'],\n                    [8657, 'upwards double arrow'],\n                    [8658, 'rightwards double arrow'],\n                    [8659, 'downwards double arrow'],\n                    [8660, 'left right double arrow'],\n                    [8756, 'therefore'],\n                    [8834, 'subset of'],\n                    [8835, 'superset of'],\n                    [8836, 'not a subset of'],\n                    [8838, 'subset of or equal to'],\n                    [8839, 'superset of or equal to'],\n                    [8853, 'circled plus'],\n                    [8855, 'circled times'],\n                    [8869, 'perpendicular'],\n                    [8901, 'dot operator'],\n                    [8968, 'left ceiling'],\n                    [8969, 'right ceiling'],\n                    [8970, 'left floor'],\n                    [8971, 'right floor'],\n                    [9001, 'left-pointing angle bracket'],\n                    [9002, 'right-pointing angle bracket'],\n                    [9674, 'lozenge'],\n                    [9824, 'black spade suit'],\n                    [9827, 'black club suit'],\n                    [9829, 'black heart suit'],\n                    [9830, 'black diamond suit'],\n                    [8194, 'en space'],\n                    [8195, 'em space'],\n                    [8201, 'thin space'],\n                    [8204, 'zero width non-joiner'],\n                    [8205, 'zero width joiner'],\n                    [8206, 'left-to-right mark'],\n                    [8207, 'right-to-left mark']\n                ]\n            }\n        ];\n    };\n    const charmapFilter = (charmap) => {\n        return global.grep(charmap, (item) => {\n            return isArray(item) && item.length === 2;\n        });\n    };\n    const getCharsFromOption = (optionValue) => {\n        if (isArray(optionValue)) {\n            return charmapFilter(optionValue);\n        }\n        if (typeof optionValue === 'function') {\n            return optionValue();\n        }\n        return [];\n    };\n    const extendCharMap = (editor, charmap) => {\n        const userCharMap = getCharMap$1(editor);\n        if (userCharMap) {\n            charmap = [{ name: UserDefined, characters: getCharsFromOption(userCharMap) }];\n        }\n        const userCharMapAppend = getCharMapAppend(editor);\n        if (userCharMapAppend) {\n            const userDefinedGroup = global.grep(charmap, (cg) => cg.name === UserDefined);\n            if (userDefinedGroup.length) {\n                userDefinedGroup[0].characters = [...userDefinedGroup[0].characters, ...getCharsFromOption(userCharMapAppend)];\n                return charmap;\n            }\n            return charmap.concat({ name: UserDefined, characters: getCharsFromOption(userCharMapAppend) });\n        }\n        return charmap;\n    };\n    const getCharMap = (editor) => {\n        const groups = extendCharMap(editor, getDefaultCharMap());\n        return groups.length > 1 ? [\n            {\n                name: 'All',\n                characters: bind(groups, (g) => g.characters)\n            }\n        ].concat(groups) : groups;\n    };\n\n    const get = (editor) => {\n        const getCharMap$1 = () => {\n            return getCharMap(editor);\n        };\n        const insertChar$1 = (chr) => {\n            insertChar(editor, chr);\n        };\n        return {\n            getCharMap: getCharMap$1,\n            insertChar: insertChar$1\n        };\n    };\n\n    const charMatches = (charCode, name, lowerCasePattern) => {\n        if (contains(fromCodePoint(charCode).toLowerCase(), lowerCasePattern)) {\n            return true;\n        }\n        else {\n            return contains(name.toLowerCase(), lowerCasePattern) || contains(name.toLowerCase().replace(/\\s+/g, ''), lowerCasePattern);\n        }\n    };\n    const scan = (group, pattern) => {\n        const matches = [];\n        const lowerCasePattern = pattern.toLowerCase();\n        each(group.characters, (g) => {\n            if (charMatches(g[0], g[1], lowerCasePattern)) {\n                matches.push(g);\n            }\n        });\n        return map(matches, (m) => ({\n            text: m[1],\n            value: fromCodePoint(m[0]),\n            icon: fromCodePoint(m[0])\n        }));\n    };\n\n    const patternName = 'pattern';\n    const open = (editor, charMap) => {\n        const makeGroupItems = () => [\n            {\n                label: 'Search',\n                type: 'input',\n                name: patternName\n            },\n            {\n                type: 'collection',\n                name: 'results'\n                // TODO TINY-3229 implement collection columns properly\n                // columns: 'auto'\n            }\n        ];\n        const makeTabs = () => map(charMap, (charGroup) => ({\n            title: charGroup.name,\n            name: charGroup.name,\n            items: makeGroupItems()\n        }));\n        const makePanel = () => ({ type: 'panel', items: makeGroupItems() });\n        const makeTabPanel = () => ({ type: 'tabpanel', tabs: makeTabs() });\n        const currentTab = charMap.length === 1 ? Cell(UserDefined) : Cell('All');\n        const scanAndSet = (dialogApi, pattern) => {\n            find(charMap, (group) => group.name === currentTab.get()).each((f) => {\n                const items = scan(f, pattern);\n                dialogApi.setData({\n                    results: items\n                });\n            });\n        };\n        const SEARCH_DELAY = 40;\n        const updateFilter = last((dialogApi) => {\n            const pattern = dialogApi.getData().pattern;\n            scanAndSet(dialogApi, pattern);\n        }, SEARCH_DELAY);\n        const body = charMap.length === 1 ? makePanel() : makeTabPanel();\n        const initialData = {\n            pattern: '',\n            results: scan(charMap[0], '')\n        };\n        const bridgeSpec = {\n            title: 'Special Character',\n            size: 'normal',\n            body,\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'close',\n                    text: 'Close',\n                    primary: true\n                }\n            ],\n            initialData,\n            onAction: (api, details) => {\n                if (details.name === 'results') {\n                    insertChar(editor, details.value);\n                    api.close();\n                }\n            },\n            onTabChange: (dialogApi, details) => {\n                currentTab.set(details.newTabName);\n                updateFilter.throttle(dialogApi);\n            },\n            onChange: (dialogApi, changeData) => {\n                if (changeData.name === patternName) {\n                    updateFilter.throttle(dialogApi);\n                }\n            }\n        };\n        const dialogApi = editor.windowManager.open(bridgeSpec);\n        dialogApi.focus(patternName);\n    };\n\n    const register$1 = (editor, charMap) => {\n        editor.addCommand('mceShowCharmap', () => {\n            open(editor, charMap);\n        });\n    };\n\n    const init = (editor, all) => {\n        editor.ui.registry.addAutocompleter('charmap', {\n            trigger: ':',\n            columns: 'auto',\n            minChars: 2,\n            fetch: (pattern, _maxResults) => new Promise((resolve, _reject) => {\n                resolve(scan(all, pattern));\n            }),\n            onAction: (autocompleteApi, rng, value) => {\n                editor.selection.setRng(rng);\n                editor.insertContent(value);\n                autocompleteApi.hide();\n            }\n        });\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceShowCharmap');\n        editor.ui.registry.addButton('charmap', {\n            icon: 'insert-character',\n            tooltip: 'Special character',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n        editor.ui.registry.addMenuItem('charmap', {\n            icon: 'insert-character',\n            text: 'Special character...',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    var Plugin = () => {\n        global$1.add('charmap', (editor) => {\n            register$2(editor);\n            const charMap = getCharMap(editor);\n            register$1(editor, charMap);\n            register(editor);\n            init(editor, charMap[0]);\n            return get(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,uBAAuB,CAAC,QAAQ;QAClC,OAAO,OAAO,QAAQ,CAAC,oBAAoB;YAAE;QAAI;IACrD;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,MAAM,SAAS,qBAAqB,QAAQ,KAAK,GAAG;QACpD,OAAO,WAAW,CAAC,oBAAoB,OAAO;IAClD;IAEA,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,YAAY,OAAO;IACzB,MAAM,SAAS,GAAG;IAClB,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAEhC,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,SAAS;IAEvB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,OAAO,CAAC,IAAI;QACd,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,OAAO,CAAC,IAAI;QACd,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG;gBACnB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,IAAI,IAAM,QAAQ,IAAI,IAAI;IACxC,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAE9D,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,WAAW,SAAC,KAAK;YAAQ,yEAAQ,GAAG;QACtC,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ;QAChC,IAAI,QAAQ,CAAC,GAAG;YACZ,OAAO,YAAY,OAAO,OAAO,MAAM,OAAO,MAAM,IAAI;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,gBAAgB,OAAO,aAAa;IAE1C,gEAAgE;IAChE,+DAA+D;IAC/D,0BAA0B;IAC1B,MAAM,OAAO,CAAC,IAAI;QACd,IAAI,QAAQ;QACZ,MAAM,SAAS;YACX,IAAI,CAAC,OAAO,QAAQ;gBAChB,aAAa;gBACb,QAAQ;YACZ;QACJ;QACA,MAAM,WAAW;6CAAI;gBAAA;;YACjB;YACA,QAAQ,WAAW;gBACf,QAAQ;gBACR,GAAG,KAAK,CAAC,MAAM;YACnB,GAAG;QACP;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,MAAM,mBAAmB,CAAC,QAAU,WAAW,UAAU,UAAU;QACnE,eAAe,WAAW;YACtB,WAAW;QACf;QACA,eAAe,kBAAkB;YAC7B,WAAW;QACf;IACJ;IACA,MAAM,eAAe,OAAO;IAC5B,MAAM,mBAAmB,OAAO;IAEhC,MAAM,UAAU,OAAO,OAAO;IAC9B,MAAM,cAAc;IACpB,MAAM,oBAAoB;QACtB,OAAO;YACH,mCAAmC;YACnC,IAAI;YACJ,qBAAqB;YACrB,mBAAmB;YACnB,+BAA+B;YAC/B,4BAA4B;YAC5B,6BAA6B;YAC7B,MAAM;YACN,KAAK;YACL;gBAAE,MAAM;gBACJ,YAAY;oBACR;wBAAC;wBAAI;qBAAc;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAW;oBACjB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAM;qBAAqB;oBAC5B;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAsB;oBAC7B;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAsB;oBAC7B;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAO;qBAAgB;oBACxB;wBAAC;wBAAO;qBAAiB;oBACzB;wBAAC;wBAAO;qBAA0C;oBAClD;wBAAC;wBAAO;qBAAiC;iBAC5C;YACL;YACA;gBAAE,MAAM;gBACJ,YAAY;oBACR;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAkB;oBACxB;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAiB;oBACxB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAM;qBAAS;oBAChB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAiB;oBACxB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAK;qBAAe;oBACrB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAoB;iBAC7B;YACL;YACA;gBAAE,MAAM;gBACJ,YAAY;oBACR;wBAAC;wBAAM;qBAA4C;oBACnD;wBAAC;wBAAM;qBAA6C;oBACpD;wBAAC;wBAAK;qBAA0B;oBAChC;wBAAC;wBAAK;qBAA2B;oBACjC;wBAAC;wBAAM;qBAA6B;oBACpC;wBAAC;wBAAM;qBAA8B;oBACrC;wBAAC;wBAAM;qBAA6B;oBACpC;wBAAC;wBAAM;qBAA8B;oBACrC;wBAAC;wBAAM;qBAA8B;oBACrC;wBAAC;wBAAM;qBAA8B;oBACrC;wBAAC;wBAAI;qBAAiB;oBACtB;wBAAC;wBAAI;qBAAoB;oBACzB;wBAAC;wBAAM;qBAAwB;oBAC/B;wBAAC;wBAAM;qBAA2B;oBAClC;wBAAC;wBAAM;qBAAU;oBACjB;wBAAC;wBAAM;qBAAU;oBACjB;wBAAC;wBAAK;qBAAS;oBACf;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAA4B;oBAClC;wBAAC;wBAAK;qBAAuB;oBAC7B;wBAAC;wBAAK;qBAAoB;oBAC1B;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAK;qBAAkB;oBACxB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAM;qBAAiB;oBACxB;wBAAC;wBAAK;qBAAsB;oBAC5B;wBAAC;wBAAK;qBAAkB;oBACxB;wBAAC;wBAAK;qBAAkB;oBACxB;wBAAC;wBAAK;qBAAoB;oBAC1B;wBAAC;wBAAK;qBAAuB;oBAC7B;wBAAC;wBAAK;qBAAoB;oBAC1B;wBAAC;wBAAK;qBAA0B;iBACnC;YACL;YACA;gBACI,MAAM;gBACN,YAAY;oBACR;wBAAC;wBAAK;qBAAoB;oBAC1B;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAiB;oBACxB;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAyB;oBAChC;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAqB;oBAC5B;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAK;qBAAW;oBACjB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAQ;oBACf;wBAAC;wBAAM;qBAAuB;oBAC9B;wBAAC;wBAAM;qBAAU;oBACjB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAsB;oBAC7B;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAQ;iBAClB;YACL;YACA,mCAAmC;YACnC,IAAI;YACJ,uBAAuB;YACvB,kBAAkB;YAClB,6BAA6B;YAC7B,wBAAwB;YACxB,2CAA2C;YAC3C,4CAA4C;YAC5C,wBAAwB;YACxB,8BAA8B;YAC9B,MAAM;YACN,KAAK;YACL;gBACI,MAAM;gBACN,YAAY;oBACR;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAW;oBACjB;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAiB;oBACvB;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAgB;oBACtB;wBAAC;wBAAK;qBAAa;oBACnB;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAS;oBACf;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAO;oBACb;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAS;oBACf;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAK;oBACX;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAc;oBACpB;wBAAC;wBAAK;qBAAQ;oBACd;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAU;oBAChB;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAM;oBACZ;wBAAC;wBAAK;qBAAQ;iBACjB;YACL;YACA;gBACI,MAAM;gBACN,YAAY;oBACR;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAK;qBAAY;oBAClB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAK;qBAAwB;oBAC9B;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAiB;iBAC3B;YACL;YACA;gBACI,MAAM;gBACN,YAAY;oBACR;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAyB;oBAChC;wBAAC;wBAAM;qBAAuB;oBAC9B;wBAAC;wBAAM;qBAA0B;oBACjC;wBAAC;wBAAM;qBAAyB;oBAChC;wBAAC;wBAAM;qBAA0B;oBACjC;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAY;oBACnB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAwB;oBAC/B;wBAAC;wBAAM;qBAA0B;oBACjC;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAe;oBACtB;wBAAC;wBAAM;qBAAgB;oBACvB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAc;oBACrB;wBAAC;wBAAM;qBAA8B;oBACrC;wBAAC;wBAAM;qBAA+B;oBACtC;wBAAC;wBAAM;qBAAU;oBACjB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAkB;oBACzB;wBAAC;wBAAM;qBAAmB;oBAC1B;wBAAC;wBAAM;qBAAqB;oBAC5B;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAW;oBAClB;wBAAC;wBAAM;qBAAa;oBACpB;wBAAC;wBAAM;qBAAwB;oBAC/B;wBAAC;wBAAM;qBAAoB;oBAC3B;wBAAC;wBAAM;qBAAqB;oBAC5B;wBAAC;wBAAM;qBAAqB;iBAC/B;YACL;SACH;IACL;IACA,MAAM,gBAAgB,CAAC;QACnB,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;YACzB,OAAO,QAAQ,SAAS,KAAK,MAAM,KAAK;QAC5C;IACJ;IACA,MAAM,qBAAqB,CAAC;QACxB,IAAI,QAAQ,cAAc;YACtB,OAAO,cAAc;QACzB;QACA,IAAI,OAAO,gBAAgB,YAAY;YACnC,OAAO;QACX;QACA,OAAO,EAAE;IACb;IACA,MAAM,gBAAgB,CAAC,QAAQ;QAC3B,MAAM,cAAc,aAAa;QACjC,IAAI,aAAa;YACb,UAAU;gBAAC;oBAAE,MAAM;oBAAa,YAAY,mBAAmB;gBAAa;aAAE;QAClF;QACA,MAAM,oBAAoB,iBAAiB;QAC3C,IAAI,mBAAmB;YACnB,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAO,GAAG,IAAI,KAAK;YAClE,IAAI,iBAAiB,MAAM,EAAE;gBACzB,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG;uBAAI,gBAAgB,CAAC,EAAE,CAAC,UAAU;uBAAK,mBAAmB;iBAAmB;gBAC9G,OAAO;YACX;YACA,OAAO,QAAQ,MAAM,CAAC;gBAAE,MAAM;gBAAa,YAAY,mBAAmB;YAAmB;QACjG;QACA,OAAO;IACX;IACA,MAAM,aAAa,CAAC;QAChB,MAAM,SAAS,cAAc,QAAQ;QACrC,OAAO,OAAO,MAAM,GAAG,IAAI;YACvB;gBACI,MAAM;gBACN,YAAY,KAAK,QAAQ,CAAC,IAAM,EAAE,UAAU;YAChD;SACH,CAAC,MAAM,CAAC,UAAU;IACvB;IAEA,MAAM,MAAM,CAAC;QACT,MAAM,eAAe;YACjB,OAAO,WAAW;QACtB;QACA,MAAM,eAAe,CAAC;YAClB,WAAW,QAAQ;QACvB;QACA,OAAO;YACH,YAAY;YACZ,YAAY;QAChB;IACJ;IAEA,MAAM,cAAc,CAAC,UAAU,MAAM;QACjC,IAAI,SAAS,cAAc,UAAU,WAAW,IAAI,mBAAmB;YACnE,OAAO;QACX,OACK;YACD,OAAO,SAAS,KAAK,WAAW,IAAI,qBAAqB,SAAS,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK;QAC9G;IACJ;IACA,MAAM,OAAO,CAAC,OAAO;QACjB,MAAM,UAAU,EAAE;QAClB,MAAM,mBAAmB,QAAQ,WAAW;QAC5C,KAAK,MAAM,UAAU,EAAE,CAAC;YACpB,IAAI,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,mBAAmB;gBAC3C,QAAQ,IAAI,CAAC;YACjB;QACJ;QACA,OAAO,IAAI,SAAS,CAAC,IAAM,CAAC;gBACxB,MAAM,CAAC,CAAC,EAAE;gBACV,OAAO,cAAc,CAAC,CAAC,EAAE;gBACzB,MAAM,cAAc,CAAC,CAAC,EAAE;YAC5B,CAAC;IACL;IAEA,MAAM,cAAc;IACpB,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,iBAAiB,IAAM;gBACzB;oBACI,OAAO;oBACP,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;gBAGV;aACH;QACD,MAAM,WAAW,IAAM,IAAI,SAAS,CAAC,YAAc,CAAC;oBAChD,OAAO,UAAU,IAAI;oBACrB,MAAM,UAAU,IAAI;oBACpB,OAAO;gBACX,CAAC;QACD,MAAM,YAAY,IAAM,CAAC;gBAAE,MAAM;gBAAS,OAAO;YAAiB,CAAC;QACnE,MAAM,eAAe,IAAM,CAAC;gBAAE,MAAM;gBAAY,MAAM;YAAW,CAAC;QAClE,MAAM,aAAa,QAAQ,MAAM,KAAK,IAAI,KAAK,eAAe,KAAK;QACnE,MAAM,aAAa,CAAC,WAAW;YAC3B,KAAK,SAAS,CAAC,QAAU,MAAM,IAAI,KAAK,WAAW,GAAG,IAAI,IAAI,CAAC,CAAC;gBAC5D,MAAM,QAAQ,KAAK,GAAG;gBACtB,UAAU,OAAO,CAAC;oBACd,SAAS;gBACb;YACJ;QACJ;QACA,MAAM,eAAe;QACrB,MAAM,eAAe,KAAK,CAAC;YACvB,MAAM,UAAU,UAAU,OAAO,GAAG,OAAO;YAC3C,WAAW,WAAW;QAC1B,GAAG;QACH,MAAM,OAAO,QAAQ,MAAM,KAAK,IAAI,cAAc;QAClD,MAAM,cAAc;YAChB,SAAS;YACT,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE;QAC9B;QACA,MAAM,aAAa;YACf,OAAO;YACP,MAAM;YACN;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD;YACA,UAAU,CAAC,KAAK;gBACZ,IAAI,QAAQ,IAAI,KAAK,WAAW;oBAC5B,WAAW,QAAQ,QAAQ,KAAK;oBAChC,IAAI,KAAK;gBACb;YACJ;YACA,aAAa,CAAC,WAAW;gBACrB,WAAW,GAAG,CAAC,QAAQ,UAAU;gBACjC,aAAa,QAAQ,CAAC;YAC1B;YACA,UAAU,CAAC,WAAW;gBAClB,IAAI,WAAW,IAAI,KAAK,aAAa;oBACjC,aAAa,QAAQ,CAAC;gBAC1B;YACJ;QACJ;QACA,MAAM,YAAY,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5C,UAAU,KAAK,CAAC;IACpB;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,kBAAkB;YAChC,KAAK,QAAQ;QACjB;IACJ;IAEA,MAAM,OAAO,CAAC,QAAQ;QAClB,OAAO,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW;YAC3C,SAAS;YACT,SAAS;YACT,UAAU;YACV,OAAO,CAAC,SAAS,cAAgB,IAAI,QAAQ,CAAC,SAAS;oBACnD,QAAQ,KAAK,KAAK;gBACtB;YACA,UAAU,CAAC,iBAAiB,KAAK;gBAC7B,OAAO,SAAS,CAAC,MAAM,CAAC;gBACxB,OAAO,aAAa,CAAC;gBACrB,gBAAgB,IAAI;YACxB;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW;YACpC,MAAM;YACN,SAAS;YACT;YACA,SAAS,gBAAgB;QAC7B;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW;YACtC,MAAM;YACN,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,WAAW,CAAC;YACrB,WAAW;YACX,MAAM,UAAU,WAAW;YAC3B,WAAW,QAAQ;YACnB,SAAS;YACT,KAAK,QAAQ,OAAO,CAAC,EAAE;YACvB,OAAO,IAAI;QACf;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/charmap/index.js"], "sourcesContent": ["// Exports the \"charmap\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/charmap')\n//   ES2015:\n//     import 'tinymce/plugins/charmap'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,SAAS;AACT,cAAc;AACd,yCAAyC;AACzC,YAAY;AACZ,uCAAuC", "ignoreList": [0], "debugId": null}}]}