import csv
import smtplib
import os
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from dotenv import load_dotenv


# Chargement depuis fichier .env si souhaité
dotenv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'configs', '.env'))
load_dotenv(dotenv_path)


def load_emails_from_csv(path: str):
    """
    Charge une liste d'emails depuis un fichier CSV (1 email par ligne).
    """
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        return [row[0].strip() for row in reader if row and "@" in row[0]]


def send_newsletter_to_csv(newsletter_path: str, csv_path: str) -> int:
    """
    Envoie une newsletter HTML à tous les emails listés dans un fichier CSV.
    """
    SMTP_SERVER = os.getenv("SMTP_SERVER")
    SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
    SMTP_USER = os.getenv("SMTP_USER")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")

    if not all([SMTP_SERVER, SMTP_PORT, SMTP_USER, SMTP_PASSWORD]):
        raise ValueError("Configuration SMTP incomplète (variables d'environnement manquantes).")

    with open(newsletter_path, "r", encoding="utf-8") as f:
        content = f.read()

    recipients = load_emails_from_csv(csv_path)
    if not recipients:
        raise ValueError("Aucun email valide trouvé dans le fichier CSV.")

    success_count = 0

    with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
        server.starttls()
        server.login(SMTP_USER, SMTP_PASSWORD)

        for email in recipients:
            try:
                msg = MIMEMultipart("alternative")
                msg["From"] = SMTP_USER
                msg["To"] = email
                msg["Subject"] = " Votre Newsletter Holokia"

                msg.attach(MIMEText(content, "html", "utf-8"))
                server.sendmail(SMTP_USER, email, msg.as_string())
                success_count += 1
            except Exception as e:
                logging.warning(f"Échec d'envoi à {email} : {str(e)}")

    return success_count
