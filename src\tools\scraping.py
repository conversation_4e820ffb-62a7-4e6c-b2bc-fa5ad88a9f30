import requests
from bs4 import BeautifulSoup
import feedparser
import re
import time
from duckduckgo_search import DDGS
from src.tools.preprocessing import clean_text


def get_duckduckgo_links(theme, description, max_results=3):
    query = f"{theme} {description} actualités"
    links = []
    try:
        with DDGS() as ddgs:
            results = ddgs.text(query, region='fr-fr', max_results=max_results)
            for r in results:
                if "href" in r:
                    links.append(r["href"])
            time.sleep(2)  #  pause anti-ratelimit augmentée
    except Exception as e:
        print(f"[DDG] Erreur lors de la récupération des liens : {e}")
    return links


def get_duckduckgo_rss_links(theme, description, max_results=3):
    query = f"{theme} {description} rss"
    links = []
    try:
        with DDGS() as ddgs:
            results = ddgs.text(query, region='fr-fr', max_results=max_results)
            for r in results:
                if "rss" in r["href"].lower() or "feed" in r["href"].lower():
                    links.append(r["href"])
            time.sleep(2)  #  pause augmentée
    except Exception as e:
        print(f"[DDG RSS] Erreur lors de la récupération des flux RSS : {e}")
    return links


def scrape_website(url, keywords):
    try:
        headers = {'User-Agent': 'Mozilla/5.0'}
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        paragraphs = [p.get_text() for p in soup.find_all('p')]
        headings = [h.get_text() for h in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])]
        all_text = " ".join(paragraphs + headings)

        relevant_snippets = []
        for keyword in keywords:
            for sentence in re.split(r'[.!?]', all_text):
                if keyword.lower() in sentence.lower() and len(sentence.strip()) > 20:
                    relevant_snippets.append(f" [{url}] {sentence.strip()}...")
        return relevant_snippets
    except requests.exceptions.RequestException as e:
        print(f"Error scraping {url}: {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred while scraping {url}: {e}")
        return []


def scrape_rss_feed(url, keywords):
    try:
        feed = feedparser.parse(url)
        relevant_entries = []
        for entry in feed.entries:
            title = getattr(entry, 'title', '')
            link = getattr(entry, 'link', url)
            summary = getattr(entry, 'summary', '')
            if any(keyword.lower() in title.lower() or keyword.lower() in summary.lower() for keyword in keywords):
                relevant_entries.append(f" [{title}] {link}")
        return relevant_entries
    except Exception as e:
        print(f"Error scraping RSS feed {url}: {e}")
        return []


# def scrape_twitter(theme, description, max_results=5):
#     try:
#         import snscrape.modules.twitter as sntwitter
#         query = f"{theme} {description} lang:fr"
#         tweets = []
#         for i, tweet in enumerate(sntwitter.TwitterSearchScraper(query).get_items()):
#             if i >= max_results:
#                 break
#             tweets.append(f"{tweet.date.date()} - @{tweet.user.username} : {tweet.content}")
#         return tweets
#     except Exception as e:
#         print(f"Erreur lors du scraping Twitter : {e}")
#         return []

def scrape_content(theme, description):
    keywords = list(set(theme.lower().split() + description.lower().split()))
    scraped_results = []

    #  Scraping sites dynamiques
    dynamic_websites = get_duckduckgo_links(theme, description)

    if not dynamic_websites:
        print("[SCRAPER] Aucun lien trouvé via DuckDuckGo. Fallback activé.")
        return [f"{theme} {description}"]  #  Fallback simple garanti

    for url in dynamic_websites:
        raw = scrape_website(url, keywords)
        if raw:
            cleaned = [str(clean_text(item)) for item in raw]  #  cast explicite en str
            scraped_results.extend(cleaned)
        time.sleep(3)

    #  Scraping flux RSS
    dynamic_rss_feeds = get_duckduckgo_rss_links(theme, description)
    for url in dynamic_rss_feeds:
        raw = scrape_rss_feed(url, keywords)
        if raw:
            cleaned = [str(clean_text(item)) for item in raw]  #  cast explicite en str
            scraped_results.extend(cleaned)
        time.sleep(2)

    
        #  Toujours retourner au moins une entrée
    if not scraped_results:
        return [{"content": f"{theme} {description} (contenu générique généré)"}]

    #  Forcer chaque élément à un dict MCP-compatible
    return [{"content": str(item)} for item in scraped_results]

# def scrape_content(theme, description):
#     keywords = list(set(theme.lower().split() + description.lower().split()))
#     scraped_results = []

#     # 🌍 Scraping sites dynamiques
#     dynamic_websites = get_duckduckgo_links(theme, description)

#     if not dynamic_websites:
#         print("[SCRAPER] Aucun lien trouvé via DuckDuckGo. Fallback activé.")
#         return [f"{theme} {description}"]  # ✅ Fallback simple garanti

#     for url in dynamic_websites:
#         raw = scrape_website(url, keywords)
#         if raw:
#             cleaned = [clean_text(item) for item in raw]
#             scraped_results.extend(cleaned)
#         time.sleep(3)

#     # 📰 Scraping flux RSS
#     dynamic_rss_feeds = get_duckduckgo_rss_links(theme, description)
#     for url in dynamic_rss_feeds:
#         raw = scrape_rss_feed(url, keywords)
#         if raw:
#             cleaned = [clean_text(item) for item in raw]
#             scraped_results.extend(cleaned)
#         time.sleep(2)

#     # 🐦 Scraping Twitter
#     # raw = scrape_twitter(theme, description)
#     # if raw:
#     #     cleaned = [clean_text(item) for item in raw]
#     #     scraped_results.extend(cleaned)

#     # ✅ Toujours retourner au moins une entrée
#     if not scraped_results:
#         return [f"{theme} {description} (contenu générique généré)"]

#     return scraped_results


# def scrape_content(theme, description):
#     keywords = list(set(theme.lower().split() + description.lower().split()))
#     scraped_results = []

#     # 🌍 Scraping sites dynamiques
#     dynamic_websites = get_duckduckgo_links(theme, description)

#     if not dynamic_websites:
#         print("[SCRAPER] Aucun lien trouvé via DuckDuckGo. Fallback activé.")
#         return [f"{theme} {description}"]  # Fallback simple

#     for url in dynamic_websites:
#         raw = scrape_website(url, keywords)
#         cleaned = [clean_text(item) for item in raw]
#         scraped_results.extend(cleaned)
#         time.sleep(3)  # ✅ pause entre chaque site

#     # 📰 Scraping flux RSS
#     dynamic_rss_feeds = get_duckduckgo_rss_links(theme, description)
#     for url in dynamic_rss_feeds:
#         raw = scrape_rss_feed(url, keywords)
#         cleaned = [clean_text(item) for item in raw]
#         scraped_results.extend(cleaned)
#         time.sleep(2)

#     # # Scraping Twitter
#     # raw = scrape_twitter(theme, description)
#     # cleaned = [clean_text(item) for item in raw]
#     # scraped_results.extend(cleaned)

#     return scraped_results

