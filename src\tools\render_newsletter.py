import os
import re
from typing import List
from jinja2 import Environment, FileSystemLoader
from markupsafe import Markup, escape

def extract_urls(texts: List[str]) -> List[str]:
    """Extrait tous les liens HTTP(S) depuis une liste de textes."""
    urls = []
    for text in texts:
        urls += re.findall(r'https?://[^\s")]+', text)
    return list(set(urls))

# transforme les URLs en balises <a>
def linkify(text: str) -> str:
    url_pattern = re.compile(r'(https?://[^\s<>()]+)')
    return re.sub(url_pattern, r'<a href="\1" target="_blank" rel="noopener noreferrer">\1</a>', text)

# ✅ Déclare le filtre AVANT de charger le template
# def nl2br(value: str) -> str:
#     paragraphs = value.split("\n\n")
#     html_paragraphs = [f"<p>{escape(p).replace('\n', '<br>')}</p>" for p in paragraphs]
#     return Markup("".join(html_paragraphs))
def nl2br(value: str) -> str:
    paragraphs = value.split("\n\n")
    html_paragraphs = [
        f"<p>{linkify(escape(p).replace('\n', '<br>'))}</p>"
        for p in paragraphs
    ]
    return Markup("".join(html_paragraphs))


def get_template():
    """Charge dynamiquement le template HTML Jinja2 avec le filtre nl2br."""
    current_dir = os.path.dirname(__file__)
    template_dir = os.path.abspath(os.path.join(current_dir, "..", "template"))

    env = Environment(loader=FileSystemLoader(template_dir))
    env.filters["nl2br"] = nl2br  # ✅ Enregistre ici le filtre après instanciation

    return env.get_template("newsletter_template.html")

def render_newsletter_html(
    title: str,
    introduction: str,
    section_title: str,
    bullet_points: List[str],
    chunks: List[str],
    full_text: str
) -> str:
    """Injecte les contenus dynamiquement dans le template HTML."""
    sources = extract_urls(chunks)
    template = get_template()
    return template.render(
        title=title,
        introduction=introduction,
        section_title=section_title,
        bullet_points=bullet_points,
        sources=sources,
        full_text=full_text
    )








# import os
# import re
# from typing import List
# from jinja2 import Environment, FileSystemLoader

# #  Base absolue vers le dossier "template"
# TEMPLATE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "template"))

# #  Jinja2 configuration
# env = Environment(loader=FileSystemLoader(TEMPLATE_DIR))
# template = env.get_template("newsletter_template.html")  #  NE PAS OUBLIER

# def extract_urls(texts: List[str]) -> List[str]:
#     """Extrait tous les liens HTTP(S) depuis une liste de textes."""
#     urls = []
#     for text in texts:
#         urls += re.findall(r'https?://[^\s")]+', text)
#     return list(set(urls))

# def render_newsletter_html(
#     title: str,
#     introduction: str,
#     section_title: str,
#     bullet_points: List[str],
#     chunks: List[str]
# ) -> str:
#     """Injecte le contenu dans le template HTML et retourne le rendu final."""
#     sources = extract_urls(chunks)
#     return template.render(
#         title=title,
#         introduction=introduction,
#         section_title=section_title,
#         bullet_points=bullet_points,
#         sources=sources
#     )
