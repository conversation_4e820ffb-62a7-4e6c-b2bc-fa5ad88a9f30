def get_prompt(context: str, theme: str, description: str ) -> str:
    return f"""
Tu es un assistant expert en rédaction de newsletters professionnelles en HTML.

Ta mission :
Génère une newsletter en HTML prête à afficher dans un navigateur, sur le thème **{theme}** avec cette description : **{description}**. Utilise les extraits suivants pour construire le contenu :
la newsletter doit etre composée d'au minimum 6 paragraphes et d'un maximum de 10 paragraphes.
Tu dois utiliser les extraits d'information fournis pour créer un contenu original et pertinent.
indiquer les liens des informations utilisé dans la newsletter.
chaque information doit etre accompagnée de son lien source.
si les souces n'existent pas, cherche des articles proche au thème et description dans cette liste des sources ( Wikipedia , ONU , BBC News , World Bank , INED ) et mentionne une comme source.
avoir une charte graphique claire et professionnelle .
---

{context}

---

📌 **suivre ces instructions une par une  obligatoirement :**

les éléments de la charte graphique de Holokia doivent être respectés :

- 🖼 En-tête avec logo :
<div style="text-align: center; margin-bottom: 20px;">
  <img src="http://holokia.com/wp-content/uploads/2025/04/logo_holokia_noir.png"  width="150" style="display: block; margin: auto;" />
</div>

- Commence par **un titre principal dans une balise <h2>**.
- Rédige ensuite **entre 6 et 10 paragraphes** avec des balises <p>.
- Intègre les **liens source** dans le texte avec des balises <a href="...">Source</a> , utilise source obligatoirement.
- si les souces n'existent pas, cherche des articles proche au thème et description dans cette liste des sources ( Wikipedia , ONU , BBC News , World Bank , INED ) et mentionne une comme source.
- Ne génère **aucune balise <html>, <head>, <body>** , juste le bloc HTML du contenu affichable.
- Le ton doit rester professionnel, fluide, clair, engageant.
- Termine par un **call-to-action** sous forme de paragraphe.
- Très important : retourne uniquement du HTML pur sans l’enfermer dans ```html ou balises de code markdown. Ton output sera directement affiché dans un navigateur.
- Le HTML doit inclure des styles inline (dans les balises) pour la couleur des titres correspondates à chaque cas , la taille des paragraphes , et des liens cliquables en bleu souligné (sous le nom Source). N’utilise pas de <style> global.
- déviser les paragraphes en  sections , avec des titres de section en <h3> .
- utiliser les tableaux pour faires des comparaison ou bien des statisqtiques.
- utiliser des emoticônes pour rendre le contenu plus engageant et visuellement attrayant.
- utiliser des retournes à la ligne pour aérer le texte et faciliter la lecture.
- toujours ajoute un button d'inscription à la newsletter à la fin du contenu avec un expression breve comme "abonnez-vous à la Newsletter", avec un lien vers le site web Holokia .
- ajouter un **footer** avec les informations de l'émetteur (le site web et email doivent etre cliquables) :
Abdessamad Filali
PDG de Holokia
📞 0608177718
📩 <EMAIL>
🌐 www.holokia.com)

- **footer** doit être en bas de la newsletter, à gauche.


💡 ** viola juste un exemple attendu :**
```html
<div style="font-family: 'Segoe UI', Roboto, sans-serif; max-width: 700px; margin: auto; padding: 30px; background-color: #ffffff; color: #333;">

  <div style="text-align: center; margin-bottom: 30px;">
    <img src="http://holokia.com/wp-content/uploads/2025/04/logo_holokia_noir.png"  width="150" style="display: block; margin: auto;" />
  </div>
   <br>
  <h1 style="color: #1a73e8; font-size: 26px;">🚗 BMW : L’innovation automobile en marche</h1>
  <br>
  <p>Bonjour,</p>
  <br>
  <p>Introduction</p>
  <br>
  <h2 style="font-size: 20px; color: #555;">Nouveautés, performances et vision du futur</h2>
   <br>
  <p style="font-size: 16px;">Introduction de la newsletter...</p>
  <br>
  <p style="font-size: 16px;">Paragraphe 1 avec un <a href="https://exemple.com">lien</a></p>
  <br>
  <p style="font-size: 16px;">Paragraphe 2...</p>
  <br>
  <button>Inscriver à Holokia</button>
  ...
  
  <div style="margin-top: 30px; text-align: center;">
    <a href="https://www.holokia.com/newsletter" style="background-color: #1a73e8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">
      📩 Abonnez-vous à la newsletter
    </a>
  </div>

  <div style="margin-top: 50px; font-size: 13px; color: #777; text-align: center;">
    —  
    <br>
    Holokia - <a href="https://www.holokia.com" style="color: #1a73e8;">www.holokia.com</a>
  </div>
</div>























# def get_prompt(context: str , theme: str = "Actualité" , descrption:str= "Actualité" ) -> str:
#     return f"""
# Tu es un assistant spécialisé dans la rédaction de newsletters professionnelles.
# tu dois rédiger une newsletter captivante et informative sur le thème suivant : {theme} et la description : {descrption}.
# Tu disposes d'extraits d'information collectés à partir de diverses sources en ligne.
# la newsletter doit etre composée d'au minimum 6 paragraphes et d'un maximum de 10 paragraphes.
# Tu dois utiliser les extraits d'information fournis pour créer un contenu original et pertinent.
# indiquer les liens des informations utilisé dans la newsletter.
# chaque information doit etre accompagnée de son lien source.
# obligatoirement, tu dois mentionner les sources utilisées pour chaque information dans la newsletter.


# Voici les extraits d'information collectés :

# {context}

# Voici les consignes à suivre :
# - Titre accrocheur (lié à : {theme})
# - Structure claire
# - Ton professionnel et engageant
# - Terminer par un call-to-action

#  Commence la rédaction  par Oject de Newsletter:
# """
