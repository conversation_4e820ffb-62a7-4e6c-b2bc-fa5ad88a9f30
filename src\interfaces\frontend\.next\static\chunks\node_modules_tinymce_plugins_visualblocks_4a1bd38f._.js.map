{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/visualblocks/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const fireVisualBlocks = (editor, state) => {\n        editor.dispatch('VisualBlocks', { state });\n    };\n\n    const toggleVisualBlocks = (editor, pluginUrl, enabledState) => {\n        const dom = editor.dom;\n        dom.toggleClass(editor.getBody(), 'mce-visualblocks');\n        enabledState.set(!enabledState.get());\n        fireVisualBlocks(editor, enabledState.get());\n    };\n\n    const register$2 = (editor, pluginUrl, enabledState) => {\n        editor.addCommand('mceVisualBlocks', () => {\n            toggleVisualBlocks(editor, pluginUrl, enabledState);\n        });\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$1 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('visualblocks_default_state', {\n            processor: 'boolean',\n            default: false\n        });\n    };\n    const isEnabledByDefault = option('visualblocks_default_state');\n\n    const setup = (editor, pluginUrl, enabledState) => {\n        // Prevents the visualblocks from being presented in the preview of formats when that is computed\n        editor.on('PreviewFormats AfterPreviewFormats', (e) => {\n            if (enabledState.get()) {\n                editor.dom.toggleClass(editor.getBody(), 'mce-visualblocks', e.type === 'afterpreviewformats');\n            }\n        });\n        editor.on('init', () => {\n            if (isEnabledByDefault(editor)) {\n                toggleVisualBlocks(editor, pluginUrl, enabledState);\n            }\n        });\n    };\n\n    const toggleActiveState = (editor, enabledState) => (api) => {\n        api.setActive(enabledState.get());\n        const editorEventCallback = (e) => api.setActive(e.state);\n        editor.on('VisualBlocks', editorEventCallback);\n        return () => editor.off('VisualBlocks', editorEventCallback);\n    };\n    const register = (editor, enabledState) => {\n        const onAction = () => editor.execCommand('mceVisualBlocks');\n        editor.ui.registry.addToggleButton('visualblocks', {\n            icon: 'visualblocks',\n            tooltip: 'Show blocks',\n            onAction,\n            onSetup: toggleActiveState(editor, enabledState),\n            context: 'any'\n        });\n        editor.ui.registry.addToggleMenuItem('visualblocks', {\n            text: 'Show blocks',\n            icon: 'visualblocks',\n            onAction,\n            onSetup: toggleActiveState(editor, enabledState),\n            context: 'any'\n        });\n    };\n\n    var Plugin = () => {\n        global.add('visualblocks', (editor, pluginUrl) => {\n            register$1(editor);\n            const enabledState = Cell(false);\n            register$2(editor, pluginUrl, enabledState);\n            register(editor, enabledState);\n            setup(editor, pluginUrl, enabledState);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,OAAO,QAAQ,CAAC,gBAAgB;YAAE;QAAM;IAC5C;IAEA,MAAM,qBAAqB,CAAC,QAAQ,WAAW;QAC3C,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI,WAAW,CAAC,OAAO,OAAO,IAAI;QAClC,aAAa,GAAG,CAAC,CAAC,aAAa,GAAG;QAClC,iBAAiB,QAAQ,aAAa,GAAG;IAC7C;IAEA,MAAM,aAAa,CAAC,QAAQ,WAAW;QACnC,OAAO,UAAU,CAAC,mBAAmB;YACjC,mBAAmB,QAAQ,WAAW;QAC1C;IACJ;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,8BAA8B;YACzC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,qBAAqB,OAAO;IAElC,MAAM,QAAQ,CAAC,QAAQ,WAAW;QAC9B,iGAAiG;QACjG,OAAO,EAAE,CAAC,sCAAsC,CAAC;YAC7C,IAAI,aAAa,GAAG,IAAI;gBACpB,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,OAAO,IAAI,oBAAoB,EAAE,IAAI,KAAK;YAC5E;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ;YACd,IAAI,mBAAmB,SAAS;gBAC5B,mBAAmB,QAAQ,WAAW;YAC1C;QACJ;IACJ;IAEA,MAAM,oBAAoB,CAAC,QAAQ,eAAiB,CAAC;YACjD,IAAI,SAAS,CAAC,aAAa,GAAG;YAC9B,MAAM,sBAAsB,CAAC,IAAM,IAAI,SAAS,CAAC,EAAE,KAAK;YACxD,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,IAAM,OAAO,GAAG,CAAC,gBAAgB;QAC5C;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB;YAC/C,MAAM;YACN,SAAS;YACT;YACA,SAAS,kBAAkB,QAAQ;YACnC,SAAS;QACb;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,gBAAgB;YACjD,MAAM;YACN,MAAM;YACN;YACA,SAAS,kBAAkB,QAAQ;YACnC,SAAS;QACb;IACJ;IAEA,IAAI,SAAS;QACT,OAAO,GAAG,CAAC,gBAAgB,CAAC,QAAQ;YAChC,WAAW;YACX,MAAM,eAAe,KAAK;YAC1B,WAAW,QAAQ,WAAW;YAC9B,SAAS,QAAQ;YACjB,MAAM,QAAQ,WAAW;QAC7B;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/visualblocks/index.js"], "sourcesContent": ["// Exports the \"visualblocks\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/visualblocks')\n//   ES2015:\n//     import 'tinymce/plugins/visualblocks'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,SAAS;AACT,cAAc;AACd,8CAA8C;AAC9C,YAAY;AACZ,4CAA4C", "ignoreList": [0], "debugId": null}}]}