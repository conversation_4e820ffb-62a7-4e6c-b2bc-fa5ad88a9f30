'use client';

import { useEffect, useRef } from 'react';

declare global {
  interface Window {
    tinymce: any;
  }
}

interface TinyMCEEditorProps {
  content: string;
  onChange: (content: string) => void;
  height?: number;
}

export default function TinyMCEEditorCDN({ content, onChange, height = 500 }: TinyMCEEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const editorInitialized = useRef(false);

  useEffect(() => {
    // Charger TinyMCE depuis CDN
    if (!window.tinymce && !document.querySelector('script[src*="tinymce"]')) {
      const script = document.createElement('script');
      script.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js';
      script.referrerPolicy = 'origin';
      document.head.appendChild(script);
      
      script.onload = () => {
        initializeTinyMCE();
      };
    } else if (window.tinymce && !editorInitialized.current) {
      initializeTinyMCE();
    }

    return () => {
      if (window.tinymce && textareaRef.current) {
        window.tinymce.remove(`#${textareaRef.current.id}`);
      }
    };
  }, []);

  const initializeTinyMCE = () => {
    if (!textareaRef.current || editorInitialized.current) return;
    
    editorInitialized.current = true;
    
    // Masquer l'avertissement de la clé API
    const hideApiWarning = () => {
      const warningElements = document.querySelectorAll('[class*="tox-notification"], [class*="tox-notification-container"]');
      warningElements.forEach(el => {
        if (el.textContent?.includes('API key') || el.textContent?.includes('valid API key')) {
          (el as HTMLElement).style.display = 'none';
        }
      });
    };

    window.tinymce.init({
      target: textareaRef.current,
      height: height,
      menubar: true,
      plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',
        'emoticons', 'template', 'codesample', 'textcolor', 'colorpicker'
      ],
      toolbar: [
        'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table',
        'align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat | code fullscreen preview'
      ],
      font_family_formats: 
        'Arial=Arial,Helvetica,sans-serif; ' +
        'Georgia=Georgia,serif; ' +
        'Helvetica=Helvetica,Arial,sans-serif; ' +
        'Times New Roman=Times,Times New Roman,serif; ' +
        'Verdana=Verdana,Geneva,sans-serif',
      font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 36pt 48pt 72pt',
      
      // Configuration spécifique pour les emails
      content_style: `
        body { 
          font-family: 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; 
          font-size: 16px; 
          line-height: 1.6;
          color: #333;
          max-width: 700px;
          margin: 0 auto;
          padding: 20px;
        }
        h1 { color: #1a73e8; font-size: 28px; font-weight: bold; }
        h2 { color: #2563eb; font-size: 22px; font-weight: 600; }
        h3 { color: #333; font-size: 18px; font-weight: 600; }
        a { color: #1a73e8; }
        img { max-width: 100%; height: auto; }
        .cta-button { 
          display: inline-block; 
          padding: 12px 24px; 
          background: #1a73e8; 
          color: #fff !important; 
          text-decoration: none; 
          border-radius: 6px; 
          font-weight: 600;
          margin: 10px 0;
        }
      `,
      
      // Templates prédéfinis pour newsletters
      templates: [
        {
          title: 'Titre + Texte + CTA',
          description: 'Section avec titre, paragraphe et bouton',
          content: `
            <h2>Votre titre ici</h2>
            <p>Votre contenu principal ici. Décrivez les points importants de votre newsletter.</p>
            <div style="text-align: center; margin: 20px 0;">
              <a href="#" class="cta-button">Votre bouton d'action</a>
            </div>
          `
        },
        {
          title: 'Article avec image',
          description: 'Section article avec image à gauche',
          content: `
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="width: 150px; vertical-align: top; padding-right: 20px;">
                  <img src="https://via.placeholder.com/150x100" alt="Image" style="width: 100%; height: auto; border-radius: 8px;">
                </td>
                <td style="vertical-align: top;">
                  <h3>Titre de l'article</h3>
                  <p>Description de votre article ou actualité. Ajoutez ici les détails importants.</p>
                  <a href="#" style="color: #1a73e8;">Lire la suite →</a>
                </td>
              </tr>
            </table>
          `
        },
        {
          title: 'Liste de liens',
          description: 'Liste de liens utiles',
          content: `
            <h3>📌 Liens utiles</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                <a href="#" style="font-weight: 600;">Titre du lien 1</a><br>
                <span style="color: #666; font-size: 14px;">Description courte du lien</span>
              </li>
              <li style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                <a href="#" style="font-weight: 600;">Titre du lien 2</a><br>
                <span style="color: #666; font-size: 14px;">Description courte du lien</span>
              </li>
            </ul>
          `
        }
      ],
      
      // Configuration pour les emails (styles inline)
      cleanup: true,
      verify_html: false,
      convert_urls: false,
      remove_script_host: false,
      relative_urls: false,
      
      // Événements
      setup: function(editor: any) {
        editor.on('init', function() {
          editor.setContent(content);
          // Masquer l'avertissement après initialisation
          setTimeout(hideApiWarning, 1000);
          setTimeout(hideApiWarning, 3000);
        });
        
        editor.on('change keyup undo redo', function() {
          const content = editor.getContent();
          onChange(content);
        });
        
        // Bouton personnalisé pour CTA
        editor.ui.registry.addButton('cta', {
          text: 'CTA',
          tooltip: 'Insérer un bouton Call-to-Action',
          onAction: function() {
            const text = prompt('Texte du bouton :') || 'Cliquez ici';
            const url = prompt('URL du lien :') || '#';
            editor.insertContent(`
              <div style="text-align: center; margin: 20px 0;">
                <a href="${url}" style="display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600;">${text}</a>
              </div>
            `);
          }
        });
        
        // Bouton pour convertir en styles inline
        editor.ui.registry.addButton('inlinestyles', {
          text: 'Email',
          tooltip: 'Optimiser pour email (styles inline)',
          onAction: function() {
            const content = editor.getContent();
            const optimized = optimizeForEmail(content);
            editor.setContent(optimized);
          }
        });
      },
      
      // Ajouter nos boutons personnalisés à la toolbar
      toolbar1: 'undo redo | blocks fontfamily fontsize | bold italic underline | forecolor backcolor | link image cta inlinestyles',
      toolbar2: 'align lineheight | checklist numlist bullist indent outdent | template | removeformat | code fullscreen preview'
    });
  };

  // Fonction pour optimiser le HTML pour les emails
  const optimizeForEmail = (html: string): string => {
    let optimized = html;
    
    // Convertir les classes en styles inline
    const styleConversions = [
      [/class="cta-button"/g, 'style="display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0;"'],
      [/<h1>/g, '<h1 style="color: #1a73e8; font-size: 28px; font-weight: bold; margin: 20px 0 10px 0;">'],
      [/<h2>/g, '<h2 style="color: #2563eb; font-size: 22px; font-weight: 600; margin: 15px 0 8px 0;">'],
      [/<h3>/g, '<h3 style="color: #333; font-size: 18px; font-weight: 600; margin: 12px 0 6px 0;">'],
      [/<p>/g, '<p style="color: #333; font-size: 16px; line-height: 1.6; margin: 10px 0;">'],
      [/<img([^>]*)>/g, '<img$1 style="max-width: 100%; height: auto; border-radius: 8px;">'],
    ];
    
    styleConversions.forEach(([regex, replacement]) => {
      optimized = optimized.replace(regex as RegExp, replacement as string);
    });
    
    return optimized;
  };

  return (
    <div className="tinymce-container">
      <textarea
        ref={textareaRef}
        id={`tinymce-${Date.now()}`}
        defaultValue={content}
        style={{ width: '100%', height: `${height}px` }}
      />
      
      <div className="mt-4 text-sm text-gray-600">
        <p><strong>💡 Conseils d'utilisation :</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Utilisez les <strong>Templates</strong> pour des mises en page prêtes</li>
          <li>Le bouton <strong>CTA</strong> insère des boutons d'action optimisés</li>
          <li>Le bouton <strong>Email</strong> convertit automatiquement en styles inline</li>
          <li>Prévisualisez avec <strong>Preview</strong> avant d'envoyer</li>
        </ul>
      </div>
      
      {/* CSS pour masquer l'avertissement API */}
      <style jsx>{`
        :global(.tox-notification--warning),
        :global(.tox-notification-container .tox-notification--warning) {
          display: none !important;
        }
        :global(.tox-notification[data-alloy-id*="warning"]) {
          display: none !important;
        }
      `}</style>
    </div>
  );
}
