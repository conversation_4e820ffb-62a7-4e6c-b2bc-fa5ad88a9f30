const fs = require('fs');
const path = require('path');

// Chemins source et destination
const sourcePath = path.join(__dirname, 'node_modules', 'tinymce');
const destPath = path.join(__dirname, 'public', 'tinymce');

// Fonction pour copier récursivement
function copyRecursive(src, dest) {
  if (!fs.existsSync(src)) {
    console.error(`Source path does not exist: ${src}`);
    return;
  }

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (let entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyRecursive(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Copier TinyMCE
console.log('Copying TinyMCE files...');
try {
  copyRecursive(sourcePath, destPath);
  console.log('✅ TinyMCE files copied successfully to public/tinymce/');
} catch (error) {
  console.error('❌ Error copying TinyMCE files:', error);
}
