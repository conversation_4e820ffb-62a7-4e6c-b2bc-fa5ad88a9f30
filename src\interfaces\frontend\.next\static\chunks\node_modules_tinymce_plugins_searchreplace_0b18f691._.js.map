{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/searchreplace/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType$1 = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isNumber = isSimpleType('number');\n\n    const noop = () => { };\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const always = constant(true);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const eachr = (xs, f) => {\n        for (let i = xs.length - 1; i >= 0; i--) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    /*\n     * Groups an array into contiguous arrays of like elements. Whether an element is like or not depends on f.\n     *\n     * f is a function that derives a value from an element - e.g. true or false, or a string.\n     * Elements are like if this function generates the same value for them (according to ===).\n     *\n     *\n     * Order of the elements is preserved. Arr.flatten() on the result will return the original list, as with Haskell groupBy function.\n     *  For a good explanation, see the group function (which is a special case of groupBy)\n     *  http://hackage.haskell.org/package/base-4.7.0.0/docs/Data-List.html#v:group\n     */\n    const groupBy = (xs, f) => {\n        if (xs.length === 0) {\n            return [];\n        }\n        else {\n            let wasType = f(xs[0]); // initial case for matching\n            const r = [];\n            let group = [];\n            for (let i = 0, len = xs.length; i < len; i++) {\n                const x = xs[i];\n                const type = f(x);\n                if (type !== wasType) {\n                    r.push(group);\n                    group = [];\n                }\n                wasType = type;\n                group.push(x);\n            }\n            if (group.length !== 0) {\n                r.push(group);\n            }\n            return r;\n        }\n    };\n    const foldl = (xs, f, acc) => {\n        each(xs, (x, i) => {\n            acc = f(acc, x, i);\n        });\n        return acc;\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const sort = (xs, comparator) => {\n        const copy = nativeSlice.call(xs, 0);\n        copy.sort(comparator);\n        return copy;\n    };\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    const singleton = (doRevoke) => {\n        const subject = Cell(Optional.none());\n        const revoke = () => subject.get().each(doRevoke);\n        const clear = () => {\n            revoke();\n            subject.set(Optional.none());\n        };\n        const isSet = () => subject.get().isSome();\n        const get = () => subject.get();\n        const set = (s) => {\n            revoke();\n            subject.set(Optional.some(s));\n        };\n        return {\n            clear,\n            isSet,\n            get,\n            set\n        };\n    };\n    const value = () => {\n        const subject = singleton(noop);\n        const on = (f) => subject.get().each(f);\n        return {\n            ...subject,\n            on\n        };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable max-len */\n    const punctuationStr = `[~№|!-*+-\\\\/:;?@\\\\[-\\`{}\\u00A1\\u00AB\\u00B7\\u00BB\\u00BF;\\u00B7\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1361-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u3008\\u3009\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30\\u2E31\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]`;\n\n    const punctuation$1 = constant(punctuationStr);\n\n    // tslint:disable-next-line:variable-name\n    const Custom = (regex, prefix, suffix, flags) => {\n        const term = () => {\n            return new RegExp(regex, flags.getOr('g'));\n        };\n        return {\n            term,\n            prefix,\n            suffix\n        };\n    };\n\n    Custom;\n    const punctuation = punctuation$1;\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const fromHtml = (html, scope) => {\n        const doc = scope || document;\n        const div = doc.createElement('div');\n        div.innerHTML = html;\n        if (!div.hasChildNodes() || div.childNodes.length > 1) {\n            const message = 'HTML does not have a single root node';\n            // eslint-disable-next-line no-console\n            console.error(message, html);\n            throw new Error(message);\n        }\n        return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n        const doc = scope || document;\n        const node = doc.createElement(tag);\n        return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n        const doc = scope || document;\n        const node = doc.createTextNode(text);\n        return fromDom(node);\n    };\n    const fromDom = (node) => {\n        // TODO: Consider removing this check, but left atm for safety\n        if (node === null || node === undefined) {\n            throw new Error('Node cannot be null or undefined');\n        }\n        return {\n            dom: node\n        };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    // tslint:disable-next-line:variable-name\n    const SugarElement = {\n        fromHtml,\n        fromTag,\n        fromText,\n        fromDom,\n        fromPoint\n    };\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const bypassSelector = (dom) => \n    // Only elements, documents and shadow roots support querySelector\n    // shadow root element type is DOCUMENT_FRAGMENT\n    dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT ||\n        // IE fix for complex queries on empty nodes: http://jsfiddle.net/spyder/fv9ptr5L/\n        dom.childElementCount === 0;\n    const all = (selector, scope) => {\n        const base = scope === undefined ? document : scope.dom;\n        return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    /*\n     * Most of sand doesn't alter the methods on the object.\n     * We're making an exception for Node, because bitwise and is so easy to get wrong.\n     *\n     * Might be nice to ADT this at some point instead of having individual methods.\n     */\n    const compareDocumentPosition = (a, b, match) => {\n        // Returns: 0 if e1 and e2 are the same node, or a bitmask comparing the positions\n        // of nodes e1 and e2 in their documents. See the URL below for bitmask interpretation\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        // eslint-disable-next-line no-bitwise\n        return (a.compareDocumentPosition(b) & match) !== 0;\n    };\n    const documentPositionPreceding = (a, b) => {\n        return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_PRECEDING);\n    };\n\n    const type = (element) => element.dom.nodeType;\n    const isType = (t) => (element) => type(element) === t;\n    const isText$1 = isType(TEXT);\n\n    const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const children = (element) => map(element.dom.childNodes, SugarElement.fromDom);\n    const spot = (element, offset) => ({\n        element,\n        offset\n    });\n    const leaf = (element, offset) => {\n        const cs = children(element);\n        return cs.length > 0 && offset < cs.length ? spot(cs[offset], 0) : spot(element, offset);\n    };\n\n    const before = (marker, element) => {\n        const parent$1 = parent(marker);\n        parent$1.each((v) => {\n            v.dom.insertBefore(element.dom, marker.dom);\n        });\n    };\n    const append = (parent, element) => {\n        parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n        before(element, wrapper);\n        append(wrapper, element);\n    };\n\n    const rawSet = (dom, key, value) => {\n        /*\n         * JQuery coerced everything to a string, and silently did nothing on text node/null/undefined.\n         *\n         * We fail on those invalid cases, only allowing numbers and booleans.\n         */\n        if (isString(value) || isBoolean(value) || isNumber(value)) {\n            dom.setAttribute(key, value + '');\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n            throw new Error('Attribute value was not simple');\n        }\n    };\n    const set = (element, key, value) => {\n        rawSet(element.dom, key, value);\n    };\n\n    const NodeValue = (is, name) => {\n        const get = (element) => {\n            if (!is(element)) {\n                throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n            }\n            return getOption(element).getOr('');\n        };\n        const getOption = (element) => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n        const set = (element, value) => {\n            if (!is(element)) {\n                throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n            }\n            element.dom.nodeValue = value;\n        };\n        return {\n            get,\n            getOption,\n            set\n        };\n    };\n\n    const api = NodeValue(isText$1, 'text');\n    const get$1 = (element) => api.get(element);\n\n    const descendants = (scope, selector) => all(selector, scope);\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    const isSimpleBoundary = (dom, node) => dom.isBlock(node) || has(dom.schema.getVoidElements(), node.nodeName);\n    const isContentEditableFalse = (dom, node) => !dom.isEditable(node);\n    const isContentEditableTrueInCef = (dom, node) => dom.getContentEditable(node) === 'true' && node.parentNode && !dom.isEditable(node.parentNode);\n    const isHidden = (dom, node) => !dom.isBlock(node) && has(dom.schema.getWhitespaceElements(), node.nodeName);\n    const isBoundary = (dom, node) => isSimpleBoundary(dom, node) || isContentEditableFalse(dom, node) || isHidden(dom, node) || isContentEditableTrueInCef(dom, node);\n    const isText = (node) => node.nodeType === 3;\n    const nuSection = () => ({\n        sOffset: 0,\n        fOffset: 0,\n        elements: []\n    });\n    const toLeaf = (node, offset) => leaf(SugarElement.fromDom(node), offset);\n    const walk = (dom, walkerFn, startNode, callbacks, endNode, skipStart = true) => {\n        let next = skipStart ? walkerFn(false) : startNode;\n        while (next) {\n            // Walk over content editable or hidden elements\n            const isCefNode = isContentEditableFalse(dom, next);\n            if (isCefNode || isHidden(dom, next)) {\n                const stopWalking = isCefNode ? callbacks.cef(next) : callbacks.boundary(next);\n                if (stopWalking) {\n                    break;\n                }\n                else {\n                    next = walkerFn(true);\n                    continue;\n                }\n            }\n            else if (isSimpleBoundary(dom, next)) {\n                if (callbacks.boundary(next)) {\n                    break;\n                }\n            }\n            else if (isText(next)) {\n                callbacks.text(next);\n            }\n            if (next === endNode) {\n                break;\n            }\n            else {\n                next = walkerFn(false);\n            }\n        }\n    };\n    const collectTextToBoundary = (dom, section, node, rootNode, forwards) => {\n        var _a;\n        // Don't bother collecting text nodes if we're already at a boundary\n        if (isBoundary(dom, node)) {\n            return;\n        }\n        const rootBlock = (_a = dom.getParent(rootNode, dom.isBlock)) !== null && _a !== void 0 ? _a : dom.getRoot();\n        const walker = new global(node, rootBlock);\n        const walkerFn = forwards ? walker.next.bind(walker) : walker.prev.bind(walker);\n        // Walk over and add text nodes to the section and increase the offsets\n        // so we know to ignore the additional text when matching\n        walk(dom, walkerFn, node, {\n            boundary: always,\n            cef: always,\n            text: (next) => {\n                if (forwards) {\n                    section.fOffset += next.length;\n                }\n                else {\n                    section.sOffset += next.length;\n                }\n                section.elements.push(SugarElement.fromDom(next));\n            }\n        });\n    };\n    const collect = (dom, rootNode, startNode, endNode, callbacks, skipStart = true) => {\n        const walker = new global(startNode, rootNode);\n        const sections = [];\n        let current = nuSection();\n        // Find any text between the start node and the closest boundary\n        collectTextToBoundary(dom, current, startNode, rootNode, false);\n        const finishSection = () => {\n            if (current.elements.length > 0) {\n                sections.push(current);\n                current = nuSection();\n            }\n            return false;\n        };\n        // Collect all the text nodes in the specified range and create sections from the\n        // boundaries within the range\n        walk(dom, walker.next.bind(walker), startNode, {\n            boundary: finishSection,\n            cef: (node) => {\n                finishSection();\n                // Collect additional nested contenteditable true content\n                if (callbacks) {\n                    sections.push(...callbacks.cef(node));\n                }\n                return false;\n            },\n            text: (next) => {\n                current.elements.push(SugarElement.fromDom(next));\n                if (callbacks) {\n                    callbacks.text(next, current);\n                }\n            }\n        }, endNode, skipStart);\n        // Find any text between the end node and the closest boundary, then finalise the section\n        if (endNode) {\n            collectTextToBoundary(dom, current, endNode, rootNode, true);\n        }\n        finishSection();\n        return sections;\n    };\n    const collectRangeSections = (dom, rng) => {\n        const start = toLeaf(rng.startContainer, rng.startOffset);\n        const startNode = start.element.dom;\n        const end = toLeaf(rng.endContainer, rng.endOffset);\n        const endNode = end.element.dom;\n        return collect(dom, rng.commonAncestorContainer, startNode, endNode, {\n            text: (node, section) => {\n                // Set the start/end offset of the section\n                if (node === endNode) {\n                    section.fOffset += node.length - end.offset;\n                }\n                else if (node === startNode) {\n                    section.sOffset += start.offset;\n                }\n            },\n            cef: (node) => {\n                // Collect the sections and then order them appropriately, as nested sections maybe out of order\n                // TODO: See if we can improve this to avoid the sort overhead\n                const sections = bind(descendants(SugarElement.fromDom(node), '*[contenteditable=true]'), (e) => {\n                    const ceTrueNode = e.dom;\n                    return collect(dom, ceTrueNode, ceTrueNode);\n                });\n                return sort(sections, (a, b) => (documentPositionPreceding(a.elements[0].dom, b.elements[0].dom)) ? 1 : -1);\n            }\n        }, false);\n    };\n    const fromRng = (dom, rng) => rng.collapsed ? [] : collectRangeSections(dom, rng);\n    const fromNode = (dom, node) => {\n        const rng = dom.createRng();\n        rng.selectNode(node);\n        return fromRng(dom, rng);\n    };\n    const fromNodes = (dom, nodes) => bind(nodes, (node) => fromNode(dom, node));\n\n    const find$2 = (text, pattern, start = 0, finish = text.length) => {\n        const regex = pattern.regex;\n        regex.lastIndex = start;\n        const results = [];\n        let match;\n        while ((match = regex.exec(text))) {\n            const matchedText = match[pattern.matchIndex];\n            const matchStart = match.index + match[0].indexOf(matchedText);\n            const matchFinish = matchStart + matchedText.length;\n            // Stop finding matches if we've hit the finish mark\n            if (matchFinish > finish) {\n                break;\n            }\n            results.push({\n                start: matchStart,\n                finish: matchFinish\n            });\n            regex.lastIndex = matchFinish;\n        }\n        return results;\n    };\n    const extract = (elements, matches) => {\n        // Walk over each text node and compare with the matches\n        const nodePositions = foldl(elements, (acc, element) => {\n            const content = get$1(element);\n            const start = acc.last;\n            const finish = start + content.length;\n            // Find positions for any matches in the current text node\n            const positions = bind(matches, (match, matchIdx) => {\n                // Check to see if the match overlaps with the text position\n                if (match.start < finish && match.finish > start) {\n                    return [{\n                            element,\n                            start: Math.max(start, match.start) - start,\n                            finish: Math.min(finish, match.finish) - start,\n                            matchId: matchIdx\n                        }];\n                }\n                else {\n                    return [];\n                }\n            });\n            return {\n                results: acc.results.concat(positions),\n                last: finish\n            };\n        }, { results: [], last: 0 }).results;\n        // Group the positions by the match id\n        return groupBy(nodePositions, (position) => position.matchId);\n    };\n\n    const find$1 = (pattern, sections) => bind(sections, (section) => {\n        const elements = section.elements;\n        const content = map(elements, get$1).join('');\n        const positions = find$2(content, pattern, section.sOffset, content.length - section.fOffset);\n        return extract(elements, positions);\n    });\n    const mark = (matches, replacementNode) => {\n        // Walk backwards and mark the positions\n        // Note: We need to walk backwards so the position indexes don't change\n        eachr(matches, (match, idx) => {\n            eachr(match, (pos) => {\n                const wrapper = SugarElement.fromDom(replacementNode.cloneNode(false));\n                set(wrapper, 'data-mce-index', idx);\n                const textNode = pos.element.dom;\n                if (textNode.length === pos.finish && pos.start === 0) {\n                    wrap(pos.element, wrapper);\n                }\n                else {\n                    if (textNode.length !== pos.finish) {\n                        textNode.splitText(pos.finish);\n                    }\n                    const matchNode = textNode.splitText(pos.start);\n                    wrap(SugarElement.fromDom(matchNode), wrapper);\n                }\n            });\n        });\n    };\n    const findAndMark = (dom, pattern, node, replacementNode) => {\n        const textSections = fromNode(dom, node);\n        const matches = find$1(pattern, textSections);\n        mark(matches, replacementNode);\n        return matches.length;\n    };\n    const findAndMarkInSelection = (dom, pattern, selection, replacementNode) => {\n        const bookmark = selection.getBookmark();\n        // Handle table cell selection as the table plugin enables\n        // you to fake select table cells and perform actions on them\n        const nodes = dom.select('td[data-mce-selected],th[data-mce-selected]');\n        const textSections = nodes.length > 0 ? fromNodes(dom, nodes) : fromRng(dom, selection.getRng());\n        // Find and mark matches\n        const matches = find$1(pattern, textSections);\n        mark(matches, replacementNode);\n        // Restore the selection\n        selection.moveToBookmark(bookmark);\n        return matches.length;\n    };\n\n    const getElmIndex = (elm) => {\n        return elm.getAttribute('data-mce-index');\n    };\n    const markAllMatches = (editor, currentSearchState, pattern, inSelection) => {\n        const marker = editor.dom.create('span', {\n            'data-mce-bogus': 1\n        });\n        marker.className = 'mce-match-marker';\n        const node = editor.getBody();\n        done(editor, currentSearchState, false);\n        if (inSelection) {\n            return findAndMarkInSelection(editor.dom, pattern, editor.selection, marker);\n        }\n        else {\n            return findAndMark(editor.dom, pattern, node, marker);\n        }\n    };\n    const unwrap = (node) => {\n        var _a;\n        const parentNode = node.parentNode;\n        if (node.firstChild) {\n            parentNode.insertBefore(node.firstChild, node);\n        }\n        (_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(node);\n    };\n    const findSpansByIndex = (editor, index) => {\n        const spans = [];\n        const nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n        if (nodes.length) {\n            for (let i = 0; i < nodes.length; i++) {\n                const nodeIndex = getElmIndex(nodes[i]);\n                if (nodeIndex === null || !nodeIndex.length) {\n                    continue;\n                }\n                if (nodeIndex === index.toString()) {\n                    spans.push(nodes[i]);\n                }\n            }\n        }\n        return spans;\n    };\n    const moveSelection = (editor, currentSearchState, forward) => {\n        const searchState = currentSearchState.get();\n        let testIndex = searchState.index;\n        const dom = editor.dom;\n        if (forward) {\n            if (testIndex + 1 === searchState.count) {\n                testIndex = 0;\n            }\n            else {\n                testIndex++;\n            }\n        }\n        else {\n            if (testIndex - 1 === -1) {\n                testIndex = searchState.count - 1;\n            }\n            else {\n                testIndex--;\n            }\n        }\n        dom.removeClass(findSpansByIndex(editor, searchState.index), 'mce-match-marker-selected');\n        const spans = findSpansByIndex(editor, testIndex);\n        if (spans.length) {\n            dom.addClass(findSpansByIndex(editor, testIndex), 'mce-match-marker-selected');\n            editor.selection.scrollIntoView(spans[0]);\n            return testIndex;\n        }\n        return -1;\n    };\n    const removeNode = (dom, node) => {\n        const parent = node.parentNode;\n        dom.remove(node);\n        if (parent && dom.isEmpty(parent)) {\n            dom.remove(parent);\n        }\n    };\n    const escapeSearchText = (text, wholeWord) => {\n        const escapedText = text.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&').replace(/\\s/g, '[^\\\\S\\\\r\\\\n\\\\uFEFF]');\n        const wordRegex = '(' + escapedText + ')';\n        return wholeWord ? `(?:^|\\\\s|${punctuation()})` + wordRegex + `(?=$|\\\\s|${punctuation()})` : wordRegex;\n    };\n    const find = (editor, currentSearchState, text, matchCase, wholeWord, inSelection) => {\n        const selection = editor.selection;\n        const escapedText = escapeSearchText(text, wholeWord);\n        const isForwardSelection = selection.isForward();\n        const pattern = {\n            regex: new RegExp(escapedText, matchCase ? 'g' : 'gi'),\n            matchIndex: 1\n        };\n        const count = markAllMatches(editor, currentSearchState, pattern, inSelection);\n        // Safari has a bug whereby splitting text nodes breaks the selection (which is done when marking matches).\n        // As such we need to manually reset it after doing a find action. See https://bugs.webkit.org/show_bug.cgi?id=230594\n        if (global$2.browser.isSafari()) {\n            selection.setRng(selection.getRng(), isForwardSelection);\n        }\n        if (count) {\n            const newIndex = moveSelection(editor, currentSearchState, true);\n            currentSearchState.set({\n                index: newIndex,\n                count,\n                text,\n                matchCase,\n                wholeWord,\n                inSelection\n            });\n        }\n        return count;\n    };\n    const next = (editor, currentSearchState) => {\n        const index = moveSelection(editor, currentSearchState, true);\n        currentSearchState.set({ ...currentSearchState.get(), index });\n    };\n    const prev = (editor, currentSearchState) => {\n        const index = moveSelection(editor, currentSearchState, false);\n        currentSearchState.set({ ...currentSearchState.get(), index });\n    };\n    const isMatchSpan = (node) => {\n        const matchIndex = getElmIndex(node);\n        return matchIndex !== null && matchIndex.length > 0;\n    };\n    const replace = (editor, currentSearchState, text, forward, all) => {\n        const searchState = currentSearchState.get();\n        const currentIndex = searchState.index;\n        let currentMatchIndex, nextIndex = currentIndex;\n        forward = forward !== false;\n        const node = editor.getBody();\n        const nodes = global$1.grep(global$1.toArray(node.getElementsByTagName('span')), isMatchSpan);\n        for (let i = 0; i < nodes.length; i++) {\n            const nodeIndex = getElmIndex(nodes[i]);\n            let matchIndex = currentMatchIndex = parseInt(nodeIndex, 10);\n            if (all || matchIndex === searchState.index) {\n                if (text.length) {\n                    nodes[i].innerText = text;\n                    unwrap(nodes[i]);\n                }\n                else {\n                    removeNode(editor.dom, nodes[i]);\n                }\n                while (nodes[++i]) {\n                    matchIndex = parseInt(getElmIndex(nodes[i]), 10);\n                    if (matchIndex === currentMatchIndex) {\n                        removeNode(editor.dom, nodes[i]);\n                    }\n                    else {\n                        i--;\n                        break;\n                    }\n                }\n                if (forward) {\n                    nextIndex--;\n                }\n            }\n            else if (currentMatchIndex > currentIndex) {\n                nodes[i].setAttribute('data-mce-index', String(currentMatchIndex - 1));\n            }\n        }\n        currentSearchState.set({\n            ...searchState,\n            count: all ? 0 : searchState.count - 1,\n            index: nextIndex\n        });\n        if (forward) {\n            next(editor, currentSearchState);\n        }\n        else {\n            prev(editor, currentSearchState);\n        }\n        return !all && currentSearchState.get().count > 0;\n    };\n    const done = (editor, currentSearchState, keepEditorSelection) => {\n        let startContainer;\n        let endContainer;\n        const searchState = currentSearchState.get();\n        const nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n        for (let i = 0; i < nodes.length; i++) {\n            const nodeIndex = getElmIndex(nodes[i]);\n            if (nodeIndex !== null && nodeIndex.length) {\n                if (nodeIndex === searchState.index.toString()) {\n                    // Note: The first child of the span node will be the highlighted text node\n                    if (!startContainer) {\n                        startContainer = nodes[i].firstChild;\n                    }\n                    endContainer = nodes[i].firstChild;\n                }\n                unwrap(nodes[i]);\n            }\n        }\n        // Reset the search state\n        currentSearchState.set({\n            ...searchState,\n            index: -1,\n            count: 0,\n            text: ''\n        });\n        if (startContainer && endContainer) {\n            const rng = editor.dom.createRng();\n            rng.setStart(startContainer, 0);\n            rng.setEnd(endContainer, endContainer.data.length);\n            if (keepEditorSelection !== false) {\n                editor.selection.setRng(rng);\n            }\n            return rng;\n        }\n        else {\n            return undefined;\n        }\n    };\n    const hasNext = (editor, currentSearchState) => currentSearchState.get().count > 1;\n    const hasPrev = (editor, currentSearchState) => currentSearchState.get().count > 1;\n\n    const get = (editor, currentState) => {\n        const done$1 = (keepEditorSelection) => {\n            return done(editor, currentState, keepEditorSelection);\n        };\n        const find$1 = (text, matchCase, wholeWord, inSelection = false) => {\n            return find(editor, currentState, text, matchCase, wholeWord, inSelection);\n        };\n        const next$1 = () => {\n            return next(editor, currentState);\n        };\n        const prev$1 = () => {\n            return prev(editor, currentState);\n        };\n        const replace$1 = (text, forward, all) => {\n            return replace(editor, currentState, text, forward, all);\n        };\n        return {\n            done: done$1,\n            find: find$1,\n            next: next$1,\n            prev: prev$1,\n            replace: replace$1\n        };\n    };\n\n    const open = (editor, currentSearchState) => {\n        const dialogApi = value();\n        editor.undoManager.add();\n        const selectedText = global$1.trim(editor.selection.getContent({ format: 'text' }));\n        const updateButtonStates = (api) => {\n            api.setEnabled('next', hasNext(editor, currentSearchState));\n            api.setEnabled('prev', hasPrev(editor, currentSearchState));\n        };\n        const updateSearchState = (api) => {\n            const data = api.getData();\n            const current = currentSearchState.get();\n            currentSearchState.set({\n                ...current,\n                matchCase: data.matchcase,\n                wholeWord: data.wholewords,\n                inSelection: data.inselection\n            });\n        };\n        const disableAll = (api, disable) => {\n            const buttons = ['replace', 'replaceall', 'prev', 'next'];\n            const toggle = (name) => api.setEnabled(name, !disable);\n            each(buttons, toggle);\n        };\n        const toggleNotFoundAlert = (isVisible, api) => {\n            api.redial(getDialogSpec(isVisible, api.getData()));\n        };\n        // Temporarily workaround for iOS/iPadOS dialog placement to hide the keyboard\n        // TODO: Remove in 5.2 once iOS fixed positioning is fixed. See TINY-4441\n        const focusButtonIfRequired = (api, name) => {\n            if (global$2.browser.isSafari() && global$2.deviceType.isTouch() && (name === 'find' || name === 'replace' || name === 'replaceall')) {\n                api.focus(name);\n            }\n        };\n        const reset = (api) => {\n            // Clean up the markers if required\n            done(editor, currentSearchState, false);\n            // Disable the buttons\n            disableAll(api, true);\n            updateButtonStates(api);\n        };\n        const doFind = (api) => {\n            const data = api.getData();\n            const last = currentSearchState.get();\n            if (!data.findtext.length) {\n                reset(api);\n                return;\n            }\n            // Same search text, so treat the find as a next click instead\n            if (last.text === data.findtext && last.matchCase === data.matchcase && last.wholeWord === data.wholewords) {\n                next(editor, currentSearchState);\n            }\n            else {\n                // Find new matches\n                const count = find(editor, currentSearchState, data.findtext, data.matchcase, data.wholewords, data.inselection);\n                if (count <= 0) {\n                    toggleNotFoundAlert(true, api);\n                }\n                disableAll(api, count === 0);\n            }\n            updateButtonStates(api);\n        };\n        const initialState = currentSearchState.get();\n        const initialData = {\n            findtext: selectedText,\n            replacetext: '',\n            wholewords: initialState.wholeWord,\n            matchcase: initialState.matchCase,\n            inselection: initialState.inSelection\n        };\n        const getPanelItems = (error) => {\n            const items = [\n                {\n                    type: 'label',\n                    label: 'Find',\n                    for: 'findtext',\n                    items: [\n                        {\n                            type: 'bar',\n                            items: [\n                                {\n                                    type: 'input',\n                                    name: 'findtext',\n                                    maximized: true,\n                                    inputMode: 'search'\n                                },\n                                {\n                                    type: 'button',\n                                    name: 'prev',\n                                    text: 'Previous',\n                                    icon: 'action-prev',\n                                    enabled: false,\n                                    borderless: true\n                                },\n                                {\n                                    type: 'button',\n                                    name: 'next',\n                                    text: 'Next',\n                                    icon: 'action-next',\n                                    enabled: false,\n                                    borderless: true\n                                }\n                            ]\n                        }\n                    ]\n                },\n                {\n                    type: 'input',\n                    name: 'replacetext',\n                    label: 'Replace with',\n                    inputMode: 'search'\n                },\n            ];\n            if (error) {\n                items.push({\n                    type: 'alertbanner',\n                    level: 'error',\n                    text: 'Could not find the specified string.',\n                    icon: 'warning',\n                });\n            }\n            return items;\n        };\n        const getDialogSpec = (showNoMatchesAlertBanner, initialData) => ({\n            title: 'Find and Replace',\n            size: 'normal',\n            body: {\n                type: 'panel',\n                items: getPanelItems(showNoMatchesAlertBanner)\n            },\n            buttons: [\n                {\n                    type: 'menu',\n                    name: 'options',\n                    icon: 'preferences',\n                    tooltip: 'Preferences',\n                    align: 'start',\n                    items: [\n                        {\n                            type: 'togglemenuitem',\n                            name: 'matchcase',\n                            text: 'Match case'\n                        }, {\n                            type: 'togglemenuitem',\n                            name: 'wholewords',\n                            text: 'Find whole words only'\n                        },\n                        {\n                            type: 'togglemenuitem',\n                            name: 'inselection',\n                            text: 'Find in selection'\n                        }\n                    ]\n                },\n                {\n                    type: 'custom',\n                    name: 'find',\n                    text: 'Find',\n                    primary: true\n                },\n                {\n                    type: 'custom',\n                    name: 'replace',\n                    text: 'Replace',\n                    enabled: false\n                },\n                {\n                    type: 'custom',\n                    name: 'replaceall',\n                    text: 'Replace all',\n                    enabled: false,\n                }\n            ],\n            initialData,\n            onChange: (api, details) => {\n                if (showNoMatchesAlertBanner) {\n                    toggleNotFoundAlert(false, api);\n                }\n                if (details.name === 'findtext' && currentSearchState.get().count > 0) {\n                    reset(api);\n                }\n            },\n            onAction: (api, details) => {\n                const data = api.getData();\n                switch (details.name) {\n                    case 'find':\n                        doFind(api);\n                        break;\n                    case 'replace':\n                        if (!replace(editor, currentSearchState, data.replacetext)) {\n                            reset(api);\n                        }\n                        else {\n                            updateButtonStates(api);\n                        }\n                        break;\n                    case 'replaceall':\n                        replace(editor, currentSearchState, data.replacetext, true, true);\n                        reset(api);\n                        break;\n                    case 'prev':\n                        prev(editor, currentSearchState);\n                        updateButtonStates(api);\n                        break;\n                    case 'next':\n                        next(editor, currentSearchState);\n                        updateButtonStates(api);\n                        break;\n                    case 'matchcase':\n                    case 'wholewords':\n                    case 'inselection':\n                        toggleNotFoundAlert(false, api);\n                        updateSearchState(api);\n                        reset(api);\n                        break;\n                }\n                focusButtonIfRequired(api, details.name);\n            },\n            onSubmit: (api) => {\n                doFind(api);\n                focusButtonIfRequired(api, 'find');\n            },\n            onClose: () => {\n                editor.focus();\n                done(editor, currentSearchState);\n                editor.undoManager.add();\n            }\n        });\n        dialogApi.set(editor.windowManager.open(getDialogSpec(false, initialData), { inline: 'toolbar' }));\n    };\n\n    const register$1 = (editor, currentSearchState) => {\n        editor.addCommand('SearchReplace', () => {\n            open(editor, currentSearchState);\n        });\n    };\n\n    const showDialog = (editor, currentSearchState) => () => {\n        open(editor, currentSearchState);\n    };\n    const register = (editor, currentSearchState) => {\n        editor.ui.registry.addMenuItem('searchreplace', {\n            text: 'Find and replace...',\n            shortcut: 'Meta+F',\n            onAction: showDialog(editor, currentSearchState),\n            icon: 'search'\n        });\n        editor.ui.registry.addButton('searchreplace', {\n            tooltip: 'Find and replace',\n            onAction: showDialog(editor, currentSearchState),\n            icon: 'search',\n            shortcut: 'Meta+F'\n        });\n        editor.shortcuts.add('Meta+F', '', showDialog(editor, currentSearchState));\n    };\n\n    var Plugin = () => {\n        global$3.add('searchreplace', (editor) => {\n            const currentSearchState = Cell({\n                index: -1,\n                count: 0,\n                text: '',\n                matchCase: false,\n                wholeWord: false,\n                inSelection: false\n            });\n            register$1(editor, currentSearchState);\n            register(editor, currentSearchState);\n            return get(editor, currentSearchState);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,WAAW,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACxD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,WAAW,SAAS;IAC1B,MAAM,UAAU,SAAS;IACzB,MAAM,YAAY,aAAa;IAC/B,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,WAAW,aAAa;IAE9B,MAAM,OAAO,KAAQ;IACrB,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,SAAS,SAAS;IAExB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,OAAO,CAAC,IAAI;QACd,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,QAAQ,CAAC,IAAI;QACf,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACrC,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA;;;;;;;;;;KAUC,GACD,MAAM,UAAU,CAAC,IAAI;QACjB,IAAI,GAAG,MAAM,KAAK,GAAG;YACjB,OAAO,EAAE;QACb,OACK;YACD,IAAI,UAAU,EAAE,EAAE,CAAC,EAAE,GAAG,4BAA4B;YACpD,MAAM,IAAI,EAAE;YACZ,IAAI,QAAQ,EAAE;YACd,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;gBACf,MAAM,OAAO,EAAE;gBACf,IAAI,SAAS,SAAS;oBAClB,EAAE,IAAI,CAAC;oBACP,QAAQ,EAAE;gBACd;gBACA,UAAU;gBACV,MAAM,IAAI,CAAC;YACf;YACA,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB,EAAE,IAAI,CAAC;YACX;YACA,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,CAAC,IAAI,GAAG;QAClB,KAAK,IAAI,CAAC,GAAG;YACT,MAAM,EAAE,KAAK,GAAG;QACpB;QACA,OAAO;IACX;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,IAAI,IAAM,QAAQ,IAAI,IAAI;IACxC,MAAM,OAAO,CAAC,IAAI;QACd,MAAM,OAAO,YAAY,IAAI,CAAC,IAAI;QAClC,KAAK,IAAI,CAAC;QACV,OAAO;IACX;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IAEnD,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,YAAY,CAAC;QACf,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,SAAS,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC7B;QACA,MAAM,QAAQ,IAAM,QAAQ,GAAG,GAAG,MAAM;QACxC,MAAM,MAAM,IAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,CAAC;YACT;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC;QAC9B;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,QAAQ;QACV,MAAM,UAAU,UAAU;QAC1B,MAAM,KAAK,CAAC,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,GAAG,OAAO;YACV;QACJ;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,0BAA0B,GAC1B,MAAM,iBAAkB;IAExB,MAAM,gBAAgB,SAAS;IAE/B,yCAAyC;IACzC,MAAM,SAAS,CAAC,OAAO,QAAQ,QAAQ;QACnC,MAAM,OAAO;YACT,OAAO,IAAI,OAAO,OAAO,MAAM,KAAK,CAAC;QACzC;QACA,OAAO;YACH;YACA;YACA;QACJ;IACJ;IAEA;IACA,MAAM,cAAc;IAEpB,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,IAAI,aAAa,CAAC;QAC9B,IAAI,SAAS,GAAG;QAChB,IAAI,CAAC,IAAI,aAAa,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,UAAU;YAChB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,QAAQ,IAAI,UAAU,CAAC,EAAE;IACpC;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,aAAa,CAAC;QAC/B,OAAO,QAAQ;IACnB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,cAAc,CAAC;QAChC,OAAO,QAAQ;IACnB;IACA,MAAM,UAAU,CAAC;QACb,8DAA8D;QAC9D,IAAI,SAAS,QAAQ,SAAS,WAAW;YACrC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;YACH,KAAK;QACT;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,GAAG,IAAM,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,CAAC;IACzF,yCAAyC;IACzC,MAAM,eAAe;QACjB;QACA;QACA;QACA;QACA;IACJ;IAEA,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAC1B,MAAM,UAAU;IAChB,MAAM,OAAO;IAEb,MAAM,iBAAiB,CAAC,MACxB,kEAAkE;QAClE,gDAAgD;QAChD,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,qBACtE,kFAAkF;QAClF,IAAI,iBAAiB,KAAK;IAC9B,MAAM,MAAM,CAAC,UAAU;QACnB,MAAM,OAAO,UAAU,YAAY,WAAW,MAAM,GAAG;QACvD,OAAO,eAAe,QAAQ,EAAE,GAAG,IAAI,KAAK,gBAAgB,CAAC,WAAW,aAAa,OAAO;IAChG;IAEA;;;;;KAKC,GACD,MAAM,0BAA0B,CAAC,GAAG,GAAG;QACnC,kFAAkF;QAClF,sFAAsF;QACtF,gFAAgF;QAChF,sCAAsC;QACtC,OAAO,CAAC,EAAE,uBAAuB,CAAC,KAAK,KAAK,MAAM;IACtD;IACA,MAAM,4BAA4B,CAAC,GAAG;QAClC,OAAO,wBAAwB,GAAG,GAAG,KAAK,2BAA2B;IACzE;IAEA,MAAM,OAAO,CAAC,UAAY,QAAQ,GAAG,CAAC,QAAQ;IAC9C,MAAM,SAAS,CAAC,IAAM,CAAC,UAAY,KAAK,aAAa;IACrD,MAAM,WAAW,OAAO;IAExB,MAAM,SAAS,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,OAAO;IAC1F,MAAM,WAAW,CAAC,UAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,aAAa,OAAO;IAC9E,MAAM,OAAO,CAAC,SAAS,SAAW,CAAC;YAC/B;YACA;QACJ,CAAC;IACD,MAAM,OAAO,CAAC,SAAS;QACnB,MAAM,KAAK,SAAS;QACpB,OAAO,GAAG,MAAM,GAAG,KAAK,SAAS,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAK,SAAS;IACrF;IAEA,MAAM,SAAS,CAAC,QAAQ;QACpB,MAAM,WAAW,OAAO;QACxB,SAAS,IAAI,CAAC,CAAC;YACX,EAAE,GAAG,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,OAAO,GAAG;QAC9C;IACJ;IACA,MAAM,SAAS,CAAC,QAAQ;QACpB,OAAO,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG;IACtC;IACA,MAAM,OAAO,CAAC,SAAS;QACnB,OAAO,SAAS;QAChB,OAAO,SAAS;IACpB;IAEA,MAAM,SAAS,CAAC,KAAK,KAAK;QACtB;;;;SAIC,GACD,IAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;YACxD,IAAI,YAAY,CAAC,KAAK,QAAQ;QAClC,OACK;YACD,sCAAsC;YACtC,QAAQ,KAAK,CAAC,uCAAuC,KAAK,aAAa,OAAO,eAAe;YAC7F,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,MAAM,CAAC,SAAS,KAAK;QACvB,OAAO,QAAQ,GAAG,EAAE,KAAK;IAC7B;IAEA,MAAM,YAAY,CAAC,IAAI;QACnB,MAAM,MAAM,CAAC;YACT,IAAI,CAAC,GAAG,UAAU;gBACd,MAAM,IAAI,MAAM,kBAAkB,OAAO,iBAAiB,OAAO;YACrE;YACA,OAAO,UAAU,SAAS,KAAK,CAAC;QACpC;QACA,MAAM,YAAY,CAAC,UAAY,GAAG,WAAW,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAAI,SAAS,IAAI;QACjG,MAAM,MAAM,CAAC,SAAS;YAClB,IAAI,CAAC,GAAG,UAAU;gBACd,MAAM,IAAI,MAAM,sBAAsB,OAAO,iBAAiB,OAAO;YACzE;YACA,QAAQ,GAAG,CAAC,SAAS,GAAG;QAC5B;QACA,OAAO;YACH;YACA;YACA;QACJ;IACJ;IAEA,MAAM,MAAM,UAAU,UAAU;IAChC,MAAM,QAAQ,CAAC,UAAY,IAAI,GAAG,CAAC;IAEnC,MAAM,cAAc,CAAC,OAAO,WAAa,IAAI,UAAU;IAEvD,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,mBAAmB,CAAC,KAAK,OAAS,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,MAAM,CAAC,eAAe,IAAI,KAAK,QAAQ;IAC5G,MAAM,yBAAyB,CAAC,KAAK,OAAS,CAAC,IAAI,UAAU,CAAC;IAC9D,MAAM,6BAA6B,CAAC,KAAK,OAAS,IAAI,kBAAkB,CAAC,UAAU,UAAU,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,UAAU;IAC/I,MAAM,WAAW,CAAC,KAAK,OAAS,CAAC,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,MAAM,CAAC,qBAAqB,IAAI,KAAK,QAAQ;IAC3G,MAAM,aAAa,CAAC,KAAK,OAAS,iBAAiB,KAAK,SAAS,uBAAuB,KAAK,SAAS,SAAS,KAAK,SAAS,2BAA2B,KAAK;IAC7J,MAAM,SAAS,CAAC,OAAS,KAAK,QAAQ,KAAK;IAC3C,MAAM,YAAY,IAAM,CAAC;YACrB,SAAS;YACT,SAAS;YACT,UAAU,EAAE;QAChB,CAAC;IACD,MAAM,SAAS,CAAC,MAAM,SAAW,KAAK,aAAa,OAAO,CAAC,OAAO;IAClE,MAAM,OAAO,SAAC,KAAK,UAAU,WAAW,WAAW;YAAS,6EAAY;QACpE,IAAI,OAAO,YAAY,SAAS,SAAS;QACzC,MAAO,KAAM;YACT,gDAAgD;YAChD,MAAM,YAAY,uBAAuB,KAAK;YAC9C,IAAI,aAAa,SAAS,KAAK,OAAO;gBAClC,MAAM,cAAc,YAAY,UAAU,GAAG,CAAC,QAAQ,UAAU,QAAQ,CAAC;gBACzE,IAAI,aAAa;oBACb;gBACJ,OACK;oBACD,OAAO,SAAS;oBAChB;gBACJ;YACJ,OACK,IAAI,iBAAiB,KAAK,OAAO;gBAClC,IAAI,UAAU,QAAQ,CAAC,OAAO;oBAC1B;gBACJ;YACJ,OACK,IAAI,OAAO,OAAO;gBACnB,UAAU,IAAI,CAAC;YACnB;YACA,IAAI,SAAS,SAAS;gBAClB;YACJ,OACK;gBACD,OAAO,SAAS;YACpB;QACJ;IACJ;IACA,MAAM,wBAAwB,CAAC,KAAK,SAAS,MAAM,UAAU;QACzD,IAAI;QACJ,oEAAoE;QACpE,IAAI,WAAW,KAAK,OAAO;YACvB;QACJ;QACA,MAAM,YAAY,CAAC,KAAK,IAAI,SAAS,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO;QAC1G,MAAM,SAAS,IAAI,OAAO,MAAM;QAChC,MAAM,WAAW,WAAW,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC;QACxE,uEAAuE;QACvE,yDAAyD;QACzD,KAAK,KAAK,UAAU,MAAM;YACtB,UAAU;YACV,KAAK;YACL,MAAM,CAAC;gBACH,IAAI,UAAU;oBACV,QAAQ,OAAO,IAAI,KAAK,MAAM;gBAClC,OACK;oBACD,QAAQ,OAAO,IAAI,KAAK,MAAM;gBAClC;gBACA,QAAQ,QAAQ,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC;YAC/C;QACJ;IACJ;IACA,MAAM,UAAU,SAAC,KAAK,UAAU,WAAW,SAAS;YAAW,6EAAY;QACvE,MAAM,SAAS,IAAI,OAAO,WAAW;QACrC,MAAM,WAAW,EAAE;QACnB,IAAI,UAAU;QACd,gEAAgE;QAChE,sBAAsB,KAAK,SAAS,WAAW,UAAU;QACzD,MAAM,gBAAgB;YAClB,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7B,SAAS,IAAI,CAAC;gBACd,UAAU;YACd;YACA,OAAO;QACX;QACA,iFAAiF;QACjF,8BAA8B;QAC9B,KAAK,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,WAAW;YAC3C,UAAU;YACV,KAAK,CAAC;gBACF;gBACA,yDAAyD;gBACzD,IAAI,WAAW;oBACX,SAAS,IAAI,IAAI,UAAU,GAAG,CAAC;gBACnC;gBACA,OAAO;YACX;YACA,MAAM,CAAC;gBACH,QAAQ,QAAQ,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC;gBAC3C,IAAI,WAAW;oBACX,UAAU,IAAI,CAAC,MAAM;gBACzB;YACJ;QACJ,GAAG,SAAS;QACZ,yFAAyF;QACzF,IAAI,SAAS;YACT,sBAAsB,KAAK,SAAS,SAAS,UAAU;QAC3D;QACA;QACA,OAAO;IACX;IACA,MAAM,uBAAuB,CAAC,KAAK;QAC/B,MAAM,QAAQ,OAAO,IAAI,cAAc,EAAE,IAAI,WAAW;QACxD,MAAM,YAAY,MAAM,OAAO,CAAC,GAAG;QACnC,MAAM,MAAM,OAAO,IAAI,YAAY,EAAE,IAAI,SAAS;QAClD,MAAM,UAAU,IAAI,OAAO,CAAC,GAAG;QAC/B,OAAO,QAAQ,KAAK,IAAI,uBAAuB,EAAE,WAAW,SAAS;YACjE,MAAM,CAAC,MAAM;gBACT,0CAA0C;gBAC1C,IAAI,SAAS,SAAS;oBAClB,QAAQ,OAAO,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM;gBAC/C,OACK,IAAI,SAAS,WAAW;oBACzB,QAAQ,OAAO,IAAI,MAAM,MAAM;gBACnC;YACJ;YACA,KAAK,CAAC;gBACF,gGAAgG;gBAChG,8DAA8D;gBAC9D,MAAM,WAAW,KAAK,YAAY,aAAa,OAAO,CAAC,OAAO,4BAA4B,CAAC;oBACvF,MAAM,aAAa,EAAE,GAAG;oBACxB,OAAO,QAAQ,KAAK,YAAY;gBACpC;gBACA,OAAO,KAAK,UAAU,CAAC,GAAG,IAAM,AAAC,0BAA0B,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAK,IAAI,CAAC;YAC7G;QACJ,GAAG;IACP;IACA,MAAM,UAAU,CAAC,KAAK,MAAQ,IAAI,SAAS,GAAG,EAAE,GAAG,qBAAqB,KAAK;IAC7E,MAAM,WAAW,CAAC,KAAK;QACnB,MAAM,MAAM,IAAI,SAAS;QACzB,IAAI,UAAU,CAAC;QACf,OAAO,QAAQ,KAAK;IACxB;IACA,MAAM,YAAY,CAAC,KAAK,QAAU,KAAK,OAAO,CAAC,OAAS,SAAS,KAAK;IAEtE,MAAM,SAAS,SAAC,MAAM;YAAS,yEAAQ,GAAG,0EAAS,KAAK,MAAM;QAC1D,MAAM,QAAQ,QAAQ,KAAK;QAC3B,MAAM,SAAS,GAAG;QAClB,MAAM,UAAU,EAAE;QAClB,IAAI;QACJ,MAAQ,QAAQ,MAAM,IAAI,CAAC,MAAQ;YAC/B,MAAM,cAAc,KAAK,CAAC,QAAQ,UAAU,CAAC;YAC7C,MAAM,aAAa,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC;YAClD,MAAM,cAAc,aAAa,YAAY,MAAM;YACnD,oDAAoD;YACpD,IAAI,cAAc,QAAQ;gBACtB;YACJ;YACA,QAAQ,IAAI,CAAC;gBACT,OAAO;gBACP,QAAQ;YACZ;YACA,MAAM,SAAS,GAAG;QACtB;QACA,OAAO;IACX;IACA,MAAM,UAAU,CAAC,UAAU;QACvB,wDAAwD;QACxD,MAAM,gBAAgB,MAAM,UAAU,CAAC,KAAK;YACxC,MAAM,UAAU,MAAM;YACtB,MAAM,QAAQ,IAAI,IAAI;YACtB,MAAM,SAAS,QAAQ,QAAQ,MAAM;YACrC,0DAA0D;YAC1D,MAAM,YAAY,KAAK,SAAS,CAAC,OAAO;gBACpC,4DAA4D;gBAC5D,IAAI,MAAM,KAAK,GAAG,UAAU,MAAM,MAAM,GAAG,OAAO;oBAC9C,OAAO;wBAAC;4BACA;4BACA,OAAO,KAAK,GAAG,CAAC,OAAO,MAAM,KAAK,IAAI;4BACtC,QAAQ,KAAK,GAAG,CAAC,QAAQ,MAAM,MAAM,IAAI;4BACzC,SAAS;wBACb;qBAAE;gBACV,OACK;oBACD,OAAO,EAAE;gBACb;YACJ;YACA,OAAO;gBACH,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;gBAC5B,MAAM;YACV;QACJ,GAAG;YAAE,SAAS,EAAE;YAAE,MAAM;QAAE,GAAG,OAAO;QACpC,sCAAsC;QACtC,OAAO,QAAQ,eAAe,CAAC,WAAa,SAAS,OAAO;IAChE;IAEA,MAAM,SAAS,CAAC,SAAS,WAAa,KAAK,UAAU,CAAC;YAClD,MAAM,WAAW,QAAQ,QAAQ;YACjC,MAAM,UAAU,IAAI,UAAU,OAAO,IAAI,CAAC;YAC1C,MAAM,YAAY,OAAO,SAAS,SAAS,QAAQ,OAAO,EAAE,QAAQ,MAAM,GAAG,QAAQ,OAAO;YAC5F,OAAO,QAAQ,UAAU;QAC7B;IACA,MAAM,OAAO,CAAC,SAAS;QACnB,wCAAwC;QACxC,uEAAuE;QACvE,MAAM,SAAS,CAAC,OAAO;YACnB,MAAM,OAAO,CAAC;gBACV,MAAM,UAAU,aAAa,OAAO,CAAC,gBAAgB,SAAS,CAAC;gBAC/D,IAAI,SAAS,kBAAkB;gBAC/B,MAAM,WAAW,IAAI,OAAO,CAAC,GAAG;gBAChC,IAAI,SAAS,MAAM,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,GAAG;oBACnD,KAAK,IAAI,OAAO,EAAE;gBACtB,OACK;oBACD,IAAI,SAAS,MAAM,KAAK,IAAI,MAAM,EAAE;wBAChC,SAAS,SAAS,CAAC,IAAI,MAAM;oBACjC;oBACA,MAAM,YAAY,SAAS,SAAS,CAAC,IAAI,KAAK;oBAC9C,KAAK,aAAa,OAAO,CAAC,YAAY;gBAC1C;YACJ;QACJ;IACJ;IACA,MAAM,cAAc,CAAC,KAAK,SAAS,MAAM;QACrC,MAAM,eAAe,SAAS,KAAK;QACnC,MAAM,UAAU,OAAO,SAAS;QAChC,KAAK,SAAS;QACd,OAAO,QAAQ,MAAM;IACzB;IACA,MAAM,yBAAyB,CAAC,KAAK,SAAS,WAAW;QACrD,MAAM,WAAW,UAAU,WAAW;QACtC,0DAA0D;QAC1D,6DAA6D;QAC7D,MAAM,QAAQ,IAAI,MAAM,CAAC;QACzB,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,UAAU,KAAK,SAAS,QAAQ,KAAK,UAAU,MAAM;QAC7F,wBAAwB;QACxB,MAAM,UAAU,OAAO,SAAS;QAChC,KAAK,SAAS;QACd,wBAAwB;QACxB,UAAU,cAAc,CAAC;QACzB,OAAO,QAAQ,MAAM;IACzB;IAEA,MAAM,cAAc,CAAC;QACjB,OAAO,IAAI,YAAY,CAAC;IAC5B;IACA,MAAM,iBAAiB,CAAC,QAAQ,oBAAoB,SAAS;QACzD,MAAM,SAAS,OAAO,GAAG,CAAC,MAAM,CAAC,QAAQ;YACrC,kBAAkB;QACtB;QACA,OAAO,SAAS,GAAG;QACnB,MAAM,OAAO,OAAO,OAAO;QAC3B,KAAK,QAAQ,oBAAoB;QACjC,IAAI,aAAa;YACb,OAAO,uBAAuB,OAAO,GAAG,EAAE,SAAS,OAAO,SAAS,EAAE;QACzE,OACK;YACD,OAAO,YAAY,OAAO,GAAG,EAAE,SAAS,MAAM;QAClD;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,IAAI;QACJ,MAAM,aAAa,KAAK,UAAU;QAClC,IAAI,KAAK,UAAU,EAAE;YACjB,WAAW,YAAY,CAAC,KAAK,UAAU,EAAE;QAC7C;QACA,CAAC,KAAK,KAAK,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC;IAC/E;IACA,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,MAAM,QAAQ,EAAE;QAChB,MAAM,QAAQ,SAAS,OAAO,CAAC,OAAO,OAAO,GAAG,oBAAoB,CAAC;QACrE,IAAI,MAAM,MAAM,EAAE;YACd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,YAAY,YAAY,KAAK,CAAC,EAAE;gBACtC,IAAI,cAAc,QAAQ,CAAC,UAAU,MAAM,EAAE;oBACzC;gBACJ;gBACA,IAAI,cAAc,MAAM,QAAQ,IAAI;oBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gBACvB;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,gBAAgB,CAAC,QAAQ,oBAAoB;QAC/C,MAAM,cAAc,mBAAmB,GAAG;QAC1C,IAAI,YAAY,YAAY,KAAK;QACjC,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI,SAAS;YACT,IAAI,YAAY,MAAM,YAAY,KAAK,EAAE;gBACrC,YAAY;YAChB,OACK;gBACD;YACJ;QACJ,OACK;YACD,IAAI,YAAY,MAAM,CAAC,GAAG;gBACtB,YAAY,YAAY,KAAK,GAAG;YACpC,OACK;gBACD;YACJ;QACJ;QACA,IAAI,WAAW,CAAC,iBAAiB,QAAQ,YAAY,KAAK,GAAG;QAC7D,MAAM,QAAQ,iBAAiB,QAAQ;QACvC,IAAI,MAAM,MAAM,EAAE;YACd,IAAI,QAAQ,CAAC,iBAAiB,QAAQ,YAAY;YAClD,OAAO,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACxC,OAAO;QACX;QACA,OAAO,CAAC;IACZ;IACA,MAAM,aAAa,CAAC,KAAK;QACrB,MAAM,SAAS,KAAK,UAAU;QAC9B,IAAI,MAAM,CAAC;QACX,IAAI,UAAU,IAAI,OAAO,CAAC,SAAS;YAC/B,IAAI,MAAM,CAAC;QACf;IACJ;IACA,MAAM,mBAAmB,CAAC,MAAM;QAC5B,MAAM,cAAc,KAAK,OAAO,CAAC,uCAAuC,QAAQ,OAAO,CAAC,OAAO;QAC/F,MAAM,YAAY,MAAM,cAAc;QACtC,OAAO,YAAY,AAAC,YAAyB,OAAd,eAAc,OAAK,YAAY,AAAC,YAAyB,OAAd,eAAc,OAAK;IACjG;IACA,MAAM,OAAO,CAAC,QAAQ,oBAAoB,MAAM,WAAW,WAAW;QAClE,MAAM,YAAY,OAAO,SAAS;QAClC,MAAM,cAAc,iBAAiB,MAAM;QAC3C,MAAM,qBAAqB,UAAU,SAAS;QAC9C,MAAM,UAAU;YACZ,OAAO,IAAI,OAAO,aAAa,YAAY,MAAM;YACjD,YAAY;QAChB;QACA,MAAM,QAAQ,eAAe,QAAQ,oBAAoB,SAAS;QAClE,2GAA2G;QAC3G,qHAAqH;QACrH,IAAI,SAAS,OAAO,CAAC,QAAQ,IAAI;YAC7B,UAAU,MAAM,CAAC,UAAU,MAAM,IAAI;QACzC;QACA,IAAI,OAAO;YACP,MAAM,WAAW,cAAc,QAAQ,oBAAoB;YAC3D,mBAAmB,GAAG,CAAC;gBACnB,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,QAAQ,cAAc,QAAQ,oBAAoB;QACxD,mBAAmB,GAAG,CAAC;YAAE,GAAG,mBAAmB,GAAG,EAAE;YAAE;QAAM;IAChE;IACA,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,QAAQ,cAAc,QAAQ,oBAAoB;QACxD,mBAAmB,GAAG,CAAC;YAAE,GAAG,mBAAmB,GAAG,EAAE;YAAE;QAAM;IAChE;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,aAAa,YAAY;QAC/B,OAAO,eAAe,QAAQ,WAAW,MAAM,GAAG;IACtD;IACA,MAAM,UAAU,CAAC,QAAQ,oBAAoB,MAAM,SAAS;QACxD,MAAM,cAAc,mBAAmB,GAAG;QAC1C,MAAM,eAAe,YAAY,KAAK;QACtC,IAAI,mBAAmB,YAAY;QACnC,UAAU,YAAY;QACtB,MAAM,OAAO,OAAO,OAAO;QAC3B,MAAM,QAAQ,SAAS,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,oBAAoB,CAAC,UAAU;QACjF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,MAAM,YAAY,YAAY,KAAK,CAAC,EAAE;YACtC,IAAI,aAAa,oBAAoB,SAAS,WAAW;YACzD,IAAI,OAAO,eAAe,YAAY,KAAK,EAAE;gBACzC,IAAI,KAAK,MAAM,EAAE;oBACb,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG;oBACrB,OAAO,KAAK,CAAC,EAAE;gBACnB,OACK;oBACD,WAAW,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE;gBACnC;gBACA,MAAO,KAAK,CAAC,EAAE,EAAE,CAAE;oBACf,aAAa,SAAS,YAAY,KAAK,CAAC,EAAE,GAAG;oBAC7C,IAAI,eAAe,mBAAmB;wBAClC,WAAW,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE;oBACnC,OACK;wBACD;wBACA;oBACJ;gBACJ;gBACA,IAAI,SAAS;oBACT;gBACJ;YACJ,OACK,IAAI,oBAAoB,cAAc;gBACvC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,OAAO,oBAAoB;YACvE;QACJ;QACA,mBAAmB,GAAG,CAAC;YACnB,GAAG,WAAW;YACd,OAAO,MAAM,IAAI,YAAY,KAAK,GAAG;YACrC,OAAO;QACX;QACA,IAAI,SAAS;YACT,KAAK,QAAQ;QACjB,OACK;YACD,KAAK,QAAQ;QACjB;QACA,OAAO,CAAC,OAAO,mBAAmB,GAAG,GAAG,KAAK,GAAG;IACpD;IACA,MAAM,OAAO,CAAC,QAAQ,oBAAoB;QACtC,IAAI;QACJ,IAAI;QACJ,MAAM,cAAc,mBAAmB,GAAG;QAC1C,MAAM,QAAQ,SAAS,OAAO,CAAC,OAAO,OAAO,GAAG,oBAAoB,CAAC;QACrE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,MAAM,YAAY,YAAY,KAAK,CAAC,EAAE;YACtC,IAAI,cAAc,QAAQ,UAAU,MAAM,EAAE;gBACxC,IAAI,cAAc,YAAY,KAAK,CAAC,QAAQ,IAAI;oBAC5C,2EAA2E;oBAC3E,IAAI,CAAC,gBAAgB;wBACjB,iBAAiB,KAAK,CAAC,EAAE,CAAC,UAAU;oBACxC;oBACA,eAAe,KAAK,CAAC,EAAE,CAAC,UAAU;gBACtC;gBACA,OAAO,KAAK,CAAC,EAAE;YACnB;QACJ;QACA,yBAAyB;QACzB,mBAAmB,GAAG,CAAC;YACnB,GAAG,WAAW;YACd,OAAO,CAAC;YACR,OAAO;YACP,MAAM;QACV;QACA,IAAI,kBAAkB,cAAc;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,SAAS;YAChC,IAAI,QAAQ,CAAC,gBAAgB;YAC7B,IAAI,MAAM,CAAC,cAAc,aAAa,IAAI,CAAC,MAAM;YACjD,IAAI,wBAAwB,OAAO;gBAC/B,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B;YACA,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,UAAU,CAAC,QAAQ,qBAAuB,mBAAmB,GAAG,GAAG,KAAK,GAAG;IACjF,MAAM,UAAU,CAAC,QAAQ,qBAAuB,mBAAmB,GAAG,GAAG,KAAK,GAAG;IAEjF,MAAM,MAAM,CAAC,QAAQ;QACjB,MAAM,SAAS,CAAC;YACZ,OAAO,KAAK,QAAQ,cAAc;QACtC;QACA,MAAM,SAAS,SAAC,MAAM,WAAW;gBAAW,+EAAc;YACtD,OAAO,KAAK,QAAQ,cAAc,MAAM,WAAW,WAAW;QAClE;QACA,MAAM,SAAS;YACX,OAAO,KAAK,QAAQ;QACxB;QACA,MAAM,SAAS;YACX,OAAO,KAAK,QAAQ;QACxB;QACA,MAAM,YAAY,CAAC,MAAM,SAAS;YAC9B,OAAO,QAAQ,QAAQ,cAAc,MAAM,SAAS;QACxD;QACA,OAAO;YACH,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,SAAS;QACb;IACJ;IAEA,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,YAAY;QAClB,OAAO,WAAW,CAAC,GAAG;QACtB,MAAM,eAAe,SAAS,IAAI,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC;YAAE,QAAQ;QAAO;QAChF,MAAM,qBAAqB,CAAC;YACxB,IAAI,UAAU,CAAC,QAAQ,QAAQ,QAAQ;YACvC,IAAI,UAAU,CAAC,QAAQ,QAAQ,QAAQ;QAC3C;QACA,MAAM,oBAAoB,CAAC;YACvB,MAAM,OAAO,IAAI,OAAO;YACxB,MAAM,UAAU,mBAAmB,GAAG;YACtC,mBAAmB,GAAG,CAAC;gBACnB,GAAG,OAAO;gBACV,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,UAAU;gBAC1B,aAAa,KAAK,WAAW;YACjC;QACJ;QACA,MAAM,aAAa,CAAC,KAAK;YACrB,MAAM,UAAU;gBAAC;gBAAW;gBAAc;gBAAQ;aAAO;YACzD,MAAM,SAAS,CAAC,OAAS,IAAI,UAAU,CAAC,MAAM,CAAC;YAC/C,KAAK,SAAS;QAClB;QACA,MAAM,sBAAsB,CAAC,WAAW;YACpC,IAAI,MAAM,CAAC,cAAc,WAAW,IAAI,OAAO;QACnD;QACA,8EAA8E;QAC9E,yEAAyE;QACzE,MAAM,wBAAwB,CAAC,KAAK;YAChC,IAAI,SAAS,OAAO,CAAC,QAAQ,MAAM,SAAS,UAAU,CAAC,OAAO,MAAM,CAAC,SAAS,UAAU,SAAS,aAAa,SAAS,YAAY,GAAG;gBAClI,IAAI,KAAK,CAAC;YACd;QACJ;QACA,MAAM,QAAQ,CAAC;YACX,mCAAmC;YACnC,KAAK,QAAQ,oBAAoB;YACjC,sBAAsB;YACtB,WAAW,KAAK;YAChB,mBAAmB;QACvB;QACA,MAAM,SAAS,CAAC;YACZ,MAAM,OAAO,IAAI,OAAO;YACxB,MAAM,OAAO,mBAAmB,GAAG;YACnC,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,EAAE;gBACvB,MAAM;gBACN;YACJ;YACA,8DAA8D;YAC9D,IAAI,KAAK,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,SAAS,KAAK,KAAK,SAAS,IAAI,KAAK,SAAS,KAAK,KAAK,UAAU,EAAE;gBACxG,KAAK,QAAQ;YACjB,OACK;gBACD,mBAAmB;gBACnB,MAAM,QAAQ,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,EAAE,KAAK,SAAS,EAAE,KAAK,UAAU,EAAE,KAAK,WAAW;gBAC/G,IAAI,SAAS,GAAG;oBACZ,oBAAoB,MAAM;gBAC9B;gBACA,WAAW,KAAK,UAAU;YAC9B;YACA,mBAAmB;QACvB;QACA,MAAM,eAAe,mBAAmB,GAAG;QAC3C,MAAM,cAAc;YAChB,UAAU;YACV,aAAa;YACb,YAAY,aAAa,SAAS;YAClC,WAAW,aAAa,SAAS;YACjC,aAAa,aAAa,WAAW;QACzC;QACA,MAAM,gBAAgB,CAAC;YACnB,MAAM,QAAQ;gBACV;oBACI,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,OAAO;wBACH;4BACI,MAAM;4BACN,OAAO;gCACH;oCACI,MAAM;oCACN,MAAM;oCACN,WAAW;oCACX,WAAW;gCACf;gCACA;oCACI,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,YAAY;gCAChB;gCACA;oCACI,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,YAAY;gCAChB;6BACH;wBACL;qBACH;gBACL;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,OAAO;oBACP,WAAW;gBACf;aACH;YACD,IAAI,OAAO;gBACP,MAAM,IAAI,CAAC;oBACP,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,MAAM;gBACV;YACJ;YACA,OAAO;QACX;QACA,MAAM,gBAAgB,CAAC,0BAA0B,cAAgB,CAAC;gBAC9D,OAAO;gBACP,MAAM;gBACN,MAAM;oBACF,MAAM;oBACN,OAAO,cAAc;gBACzB;gBACA,SAAS;oBACL;wBACI,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,OAAO;4BACH;gCACI,MAAM;gCACN,MAAM;gCACN,MAAM;4BACV;4BAAG;gCACC,MAAM;gCACN,MAAM;gCACN,MAAM;4BACV;4BACA;gCACI,MAAM;gCACN,MAAM;gCACN,MAAM;4BACV;yBACH;oBACL;oBACA;wBACI,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,SAAS;oBACb;oBACA;wBACI,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,SAAS;oBACb;oBACA;wBACI,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,SAAS;oBACb;iBACH;gBACD;gBACA,UAAU,CAAC,KAAK;oBACZ,IAAI,0BAA0B;wBAC1B,oBAAoB,OAAO;oBAC/B;oBACA,IAAI,QAAQ,IAAI,KAAK,cAAc,mBAAmB,GAAG,GAAG,KAAK,GAAG,GAAG;wBACnE,MAAM;oBACV;gBACJ;gBACA,UAAU,CAAC,KAAK;oBACZ,MAAM,OAAO,IAAI,OAAO;oBACxB,OAAQ,QAAQ,IAAI;wBAChB,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,IAAI,CAAC,QAAQ,QAAQ,oBAAoB,KAAK,WAAW,GAAG;gCACxD,MAAM;4BACV,OACK;gCACD,mBAAmB;4BACvB;4BACA;wBACJ,KAAK;4BACD,QAAQ,QAAQ,oBAAoB,KAAK,WAAW,EAAE,MAAM;4BAC5D,MAAM;4BACN;wBACJ,KAAK;4BACD,KAAK,QAAQ;4BACb,mBAAmB;4BACnB;wBACJ,KAAK;4BACD,KAAK,QAAQ;4BACb,mBAAmB;4BACnB;wBACJ,KAAK;wBACL,KAAK;wBACL,KAAK;4BACD,oBAAoB,OAAO;4BAC3B,kBAAkB;4BAClB,MAAM;4BACN;oBACR;oBACA,sBAAsB,KAAK,QAAQ,IAAI;gBAC3C;gBACA,UAAU,CAAC;oBACP,OAAO;oBACP,sBAAsB,KAAK;gBAC/B;gBACA,SAAS;oBACL,OAAO,KAAK;oBACZ,KAAK,QAAQ;oBACb,OAAO,WAAW,CAAC,GAAG;gBAC1B;YACJ,CAAC;QACD,UAAU,GAAG,CAAC,OAAO,aAAa,CAAC,IAAI,CAAC,cAAc,OAAO,cAAc;YAAE,QAAQ;QAAU;IACnG;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,iBAAiB;YAC/B,KAAK,QAAQ;QACjB;IACJ;IAEA,MAAM,aAAa,CAAC,QAAQ,qBAAuB;YAC/C,KAAK,QAAQ;QACjB;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,iBAAiB;YAC5C,MAAM;YACN,UAAU;YACV,UAAU,WAAW,QAAQ;YAC7B,MAAM;QACV;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,iBAAiB;YAC1C,SAAS;YACT,UAAU,WAAW,QAAQ;YAC7B,MAAM;YACN,UAAU;QACd;QACA,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW,QAAQ;IAC1D;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,iBAAiB,CAAC;YAC3B,MAAM,qBAAqB,KAAK;gBAC5B,OAAO,CAAC;gBACR,OAAO;gBACP,MAAM;gBACN,WAAW;gBACX,WAAW;gBACX,aAAa;YACjB;YACA,WAAW,QAAQ;YACnB,SAAS,QAAQ;YACjB,OAAO,IAAI,QAAQ;QACvB;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/searchreplace/index.js"], "sourcesContent": ["// Exports the \"searchreplace\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/searchreplace')\n//   ES2015:\n//     import 'tinymce/plugins/searchreplace'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,SAAS;AACT,cAAc;AACd,+CAA+C;AAC/C,YAAY;AACZ,6CAA6C", "ignoreList": [0], "debugId": null}}]}