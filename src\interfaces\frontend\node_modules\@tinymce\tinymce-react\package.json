{"description": "Official TinyMCE React Component", "repository": {"url": "https://github.com/tinymce/tinymce-react"}, "files": ["lib", "README.md", "CHANGELOG.md", "LICENSE.txt"], "main": "lib/cjs/main/ts/index.js", "module": "lib/es2015/main/ts/index.js", "scripts": {"lint": "eslint 'src/**/*.ts?(x)'", "clean": "<PERSON><PERSON><PERSON> lib", "test": "bedrock-auto -b chrome-headless -f src/test/ts/**/*Test.ts", "test-manual": "bedrock -f src/test/ts/**/*Test.ts", "build": "yarn run clean && tsc -p ./tsconfig.es2015.json && tsc -p ./tsconfig.cjs.json", "watch": "tsc -w -p ./tsconfig.es2015.json", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "deploy-storybook": "storybook build && gh-pages -d ./storybook-static -u 'tiny-bot <<EMAIL>>' --noje<PERSON>ll"}, "keywords": [], "author": "Ephox Corporation DBA Tiny Technologies, Inc.", "license": "MIT", "dependencies": {"prop-types": "^15.6.2", "tinymce": "^7.0.0 || ^6.0.0 || ^5.5.1"}, "peerDependencies": {"react": "^18.0.0 || ^17.0.1 || ^16.7.0", "react-dom": "^18.0.0 || ^17.0.1 || ^16.7.0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@ephox/agar": "^8.0.0-alpha.0", "@ephox/bedrock-client": "^14.1.1", "@ephox/bedrock-server": "^14.1.3", "@ephox/katamari": "^9.1.5", "@ephox/mcagar": "^9.0.0-alpha.0", "@ephox/sand": "^6.0.9", "@ephox/sugar": "^9.2.1", "@storybook/addon-essentials": "^8.0.2", "@storybook/addon-interactions": "^8.0.2", "@storybook/addon-links": "^8.0.2", "@storybook/blocks": "^8.0.2", "@storybook/react": "^8.0.2", "@storybook/react-vite": "^8.0.2", "@tinymce/beehive-flow": "^0.19.0", "@tinymce/eslint-plugin": "^2.3.1", "@tinymce/miniature": "^6.0.0", "@types/node": "^20.9.0", "@types/prop-types": "^15.7.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "gh-pages": "^6.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^5.0.5", "storybook": "^8.0.2", "tinymce-4": "npm:tinymce@^4", "tinymce-5": "npm:tinymce@^5", "tinymce-6": "npm:tinymce@^6", "tinymce-7": "npm:tinymce@^7", "typescript": "~5.4.3", "vite": "^5.2.2"}, "version": "5.1.1", "name": "@tinymce/tinymce-react"}