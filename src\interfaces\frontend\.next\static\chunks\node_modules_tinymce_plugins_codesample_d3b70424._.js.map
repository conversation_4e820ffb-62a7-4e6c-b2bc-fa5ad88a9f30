{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/codesample/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n\n    const noop = () => { };\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    const get$1 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = (xs) => get$1(xs, 0);\n\n    // Use window object as the global if it's available since CSP will block script evals\n    // eslint-disable-next-line @typescript-eslint/no-implied-eval\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const blank = (r) => (s) => s.replace(r, '');\n    /** removes all leading and trailing spaces */\n    const trim = blank(/^\\s+|\\s+$/g);\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    const prismjs = function(global, module, exports) {\n    // preserve the global if it has already been loaded\n    const oldprism = window.Prism;\n    window.Prism = { manual: true };\n    /// <reference lib=\"WebWorker\"/>\n\n    var _self = (typeof window !== 'undefined')\n    \t? window   // if in browser\n    \t: (\n    \t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n    \t\t\t? self // if in worker\n    \t\t\t: {}   // if in node js\n    \t);\n\n    /**\n     * Prism: Lightweight, robust, elegant syntax highlighting\n     *\n     * @license MIT <https://opensource.org/licenses/MIT>\n     * <AUTHOR> Verou <https://lea.verou.me>\n     * @namespace\n     * @public\n     */\n    var Prism = (function (_self) {\n\n    \t// Private helper vars\n    \tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n    \tvar uniqueId = 0;\n\n    \t// The grammar object for plaintext\n    \tvar plainTextGrammar = {};\n\n\n    \tvar _ = {\n    \t\t/**\n    \t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n    \t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n    \t\t * additional languages or plugins yourself.\n    \t\t *\n    \t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n    \t\t *\n    \t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n    \t\t * empty Prism object into the global scope before loading the Prism script like this:\n    \t\t *\n    \t\t * ```js\n    \t\t * window.Prism = window.Prism || {};\n    \t\t * Prism.manual = true;\n    \t\t * // add a new <script> to load Prism's script\n    \t\t * ```\n    \t\t *\n    \t\t * @default false\n    \t\t * @type {boolean}\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\tmanual: _self.Prism && _self.Prism.manual,\n    \t\t/**\n    \t\t * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n    \t\t * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n    \t\t * own worker, you don't want it to do this.\n    \t\t *\n    \t\t * By setting this value to `true`, Prism will not add its own listeners to the worker.\n    \t\t *\n    \t\t * You obviously have to change this value before Prism executes. To do this, you can add an\n    \t\t * empty Prism object into the global scope before loading the Prism script like this:\n    \t\t *\n    \t\t * ```js\n    \t\t * window.Prism = window.Prism || {};\n    \t\t * Prism.disableWorkerMessageHandler = true;\n    \t\t * // Load Prism's script\n    \t\t * ```\n    \t\t *\n    \t\t * @default false\n    \t\t * @type {boolean}\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n    \t\t/**\n    \t\t * A namespace for utility methods.\n    \t\t *\n    \t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n    \t\t * change or disappear at any time.\n    \t\t *\n    \t\t * @namespace\n    \t\t * @memberof Prism\n    \t\t */\n    \t\tutil: {\n    \t\t\tencode: function encode(tokens) {\n    \t\t\t\tif (tokens instanceof Token) {\n    \t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n    \t\t\t\t} else if (Array.isArray(tokens)) {\n    \t\t\t\t\treturn tokens.map(encode);\n    \t\t\t\t} else {\n    \t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n    \t\t\t\t}\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Returns the name of the type of the given value.\n    \t\t\t *\n    \t\t\t * @param {any} o\n    \t\t\t * @returns {string}\n    \t\t\t * @example\n    \t\t\t * type(null)      === 'Null'\n    \t\t\t * type(undefined) === 'Undefined'\n    \t\t\t * type(123)       === 'Number'\n    \t\t\t * type('foo')     === 'String'\n    \t\t\t * type(true)      === 'Boolean'\n    \t\t\t * type([1, 2])    === 'Array'\n    \t\t\t * type({})        === 'Object'\n    \t\t\t * type(String)    === 'Function'\n    \t\t\t * type(/abc+/)    === 'RegExp'\n    \t\t\t */\n    \t\t\ttype: function (o) {\n    \t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n    \t\t\t *\n    \t\t\t * @param {Object} obj\n    \t\t\t * @returns {number}\n    \t\t\t */\n    \t\t\tobjId: function (obj) {\n    \t\t\t\tif (!obj['__id']) {\n    \t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n    \t\t\t\t}\n    \t\t\t\treturn obj['__id'];\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Creates a deep clone of the given object.\n    \t\t\t *\n    \t\t\t * The main intended use of this function is to clone language definitions.\n    \t\t\t *\n    \t\t\t * @param {T} o\n    \t\t\t * @param {Record<number, any>} [visited]\n    \t\t\t * @returns {T}\n    \t\t\t * @template T\n    \t\t\t */\n    \t\t\tclone: function deepClone(o, visited) {\n    \t\t\t\tvisited = visited || {};\n\n    \t\t\t\tvar clone; var id;\n    \t\t\t\tswitch (_.util.type(o)) {\n    \t\t\t\t\tcase 'Object':\n    \t\t\t\t\t\tid = _.util.objId(o);\n    \t\t\t\t\t\tif (visited[id]) {\n    \t\t\t\t\t\t\treturn visited[id];\n    \t\t\t\t\t\t}\n    \t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n    \t\t\t\t\t\tvisited[id] = clone;\n\n    \t\t\t\t\t\tfor (var key in o) {\n    \t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n    \t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n    \t\t\t\t\t\t\t}\n    \t\t\t\t\t\t}\n\n    \t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n    \t\t\t\t\tcase 'Array':\n    \t\t\t\t\t\tid = _.util.objId(o);\n    \t\t\t\t\t\tif (visited[id]) {\n    \t\t\t\t\t\t\treturn visited[id];\n    \t\t\t\t\t\t}\n    \t\t\t\t\t\tclone = [];\n    \t\t\t\t\t\tvisited[id] = clone;\n\n    \t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n    \t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n    \t\t\t\t\t\t});\n\n    \t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n    \t\t\t\t\tdefault:\n    \t\t\t\t\t\treturn o;\n    \t\t\t\t}\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n    \t\t\t *\n    \t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n    \t\t\t *\n    \t\t\t * @param {Element} element\n    \t\t\t * @returns {string}\n    \t\t\t */\n    \t\t\tgetLanguage: function (element) {\n    \t\t\t\twhile (element) {\n    \t\t\t\t\tvar m = lang.exec(element.className);\n    \t\t\t\t\tif (m) {\n    \t\t\t\t\t\treturn m[1].toLowerCase();\n    \t\t\t\t\t}\n    \t\t\t\t\telement = element.parentElement;\n    \t\t\t\t}\n    \t\t\t\treturn 'none';\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Sets the Prism `language-xxxx` class of the given element.\n    \t\t\t *\n    \t\t\t * @param {Element} element\n    \t\t\t * @param {string} language\n    \t\t\t * @returns {void}\n    \t\t\t */\n    \t\t\tsetLanguage: function (element, language) {\n    \t\t\t\t// remove all `language-xxxx` classes\n    \t\t\t\t// (this might leave behind a leading space)\n    \t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n    \t\t\t\t// add the new `language-xxxx` class\n    \t\t\t\t// (using `classList` will automatically clean up spaces for us)\n    \t\t\t\telement.classList.add('language-' + language);\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Returns the script element that is currently executing.\n    \t\t\t *\n    \t\t\t * This does __not__ work for line script element.\n    \t\t\t *\n    \t\t\t * @returns {HTMLScriptElement | null}\n    \t\t\t */\n    \t\t\tcurrentScript: function () {\n    \t\t\t\tif (typeof document === 'undefined') {\n    \t\t\t\t\treturn null;\n    \t\t\t\t}\n    \t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n    \t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n    \t\t\t\t}\n\n    \t\t\t\t// IE11 workaround\n    \t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n    \t\t\t\t// this will not work for inline scripts\n\n    \t\t\t\ttry {\n    \t\t\t\t\tthrow new Error();\n    \t\t\t\t} catch (err) {\n    \t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n    \t\t\t\t\t// A stack will look like this:\n    \t\t\t\t\t//\n    \t\t\t\t\t// Error\n    \t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n    \t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n    \t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n    \t\t\t\t\tif (src) {\n    \t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n    \t\t\t\t\t\tfor (var i in scripts) {\n    \t\t\t\t\t\t\tif (scripts[i].src == src) {\n    \t\t\t\t\t\t\t\treturn scripts[i];\n    \t\t\t\t\t\t\t}\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n    \t\t\t\t\treturn null;\n    \t\t\t\t}\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Returns whether a given class is active for `element`.\n    \t\t\t *\n    \t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n    \t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n    \t\t\t * given class is just the given class with a `no-` prefix.\n    \t\t\t *\n    \t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n    \t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n    \t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n    \t\t\t *\n    \t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n    \t\t\t * version of it, the class is considered active.\n    \t\t\t *\n    \t\t\t * @param {Element} element\n    \t\t\t * @param {string} className\n    \t\t\t * @param {boolean} [defaultActivation=false]\n    \t\t\t * @returns {boolean}\n    \t\t\t */\n    \t\t\tisActive: function (element, className, defaultActivation) {\n    \t\t\t\tvar no = 'no-' + className;\n\n    \t\t\t\twhile (element) {\n    \t\t\t\t\tvar classList = element.classList;\n    \t\t\t\t\tif (classList.contains(className)) {\n    \t\t\t\t\t\treturn true;\n    \t\t\t\t\t}\n    \t\t\t\t\tif (classList.contains(no)) {\n    \t\t\t\t\t\treturn false;\n    \t\t\t\t\t}\n    \t\t\t\t\telement = element.parentElement;\n    \t\t\t\t}\n    \t\t\t\treturn !!defaultActivation;\n    \t\t\t}\n    \t\t},\n\n    \t\t/**\n    \t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n    \t\t *\n    \t\t * @namespace\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\tlanguages: {\n    \t\t\t/**\n    \t\t\t * The grammar for plain, unformatted text.\n    \t\t\t */\n    \t\t\tplain: plainTextGrammar,\n    \t\t\tplaintext: plainTextGrammar,\n    \t\t\ttext: plainTextGrammar,\n    \t\t\ttxt: plainTextGrammar,\n\n    \t\t\t/**\n    \t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n    \t\t\t *\n    \t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n    \t\t\t * will be overwritten at its original position.\n    \t\t\t *\n    \t\t\t * ## Best practices\n    \t\t\t *\n    \t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n    \t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n    \t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n    \t\t\t *\n    \t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n    \t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n    \t\t\t *\n    \t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n    \t\t\t * @param {Grammar} redef The new tokens to append.\n    \t\t\t * @returns {Grammar} The new language created.\n    \t\t\t * @public\n    \t\t\t * @example\n    \t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n    \t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n    \t\t\t *     // at its original position\n    \t\t\t *     'comment': { ... },\n    \t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n    \t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n    \t\t\t * });\n    \t\t\t */\n    \t\t\textend: function (id, redef) {\n    \t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n    \t\t\t\tfor (var key in redef) {\n    \t\t\t\t\tlang[key] = redef[key];\n    \t\t\t\t}\n\n    \t\t\t\treturn lang;\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n    \t\t\t *\n    \t\t\t * ## Usage\n    \t\t\t *\n    \t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n    \t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n    \t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n    \t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n    \t\t\t * this:\n    \t\t\t *\n    \t\t\t * ```js\n    \t\t\t * Prism.languages.markup.style = {\n    \t\t\t *     // token\n    \t\t\t * };\n    \t\t\t * ```\n    \t\t\t *\n    \t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n    \t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n    \t\t\t *\n    \t\t\t * ```js\n    \t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n    \t\t\t *     'style': {\n    \t\t\t *         // token\n    \t\t\t *     }\n    \t\t\t * });\n    \t\t\t * ```\n    \t\t\t *\n    \t\t\t * ## Special cases\n    \t\t\t *\n    \t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n    \t\t\t * will be ignored.\n    \t\t\t *\n    \t\t\t * This behavior can be used to insert tokens after `before`:\n    \t\t\t *\n    \t\t\t * ```js\n    \t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n    \t\t\t *     'comment': Prism.languages.markup.comment,\n    \t\t\t *     // tokens after 'comment'\n    \t\t\t * });\n    \t\t\t * ```\n    \t\t\t *\n    \t\t\t * ## Limitations\n    \t\t\t *\n    \t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n    \t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n    \t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n    \t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n    \t\t\t *\n    \t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n    \t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n    \t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n    \t\t\t *\n    \t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n    \t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n    \t\t\t *\n    \t\t\t * ```js\n    \t\t\t * var oldMarkup = Prism.languages.markup;\n    \t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n    \t\t\t *\n    \t\t\t * assert(oldMarkup !== Prism.languages.markup);\n    \t\t\t * assert(newMarkup === Prism.languages.markup);\n    \t\t\t * ```\n    \t\t\t *\n    \t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n    \t\t\t * object to be modified.\n    \t\t\t * @param {string} before The key to insert before.\n    \t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n    \t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n    \t\t\t * object to be modified.\n    \t\t\t *\n    \t\t\t * Defaults to `Prism.languages`.\n    \t\t\t * @returns {Grammar} The new grammar object.\n    \t\t\t * @public\n    \t\t\t */\n    \t\t\tinsertBefore: function (inside, before, insert, root) {\n    \t\t\t\troot = root || /** @type {any} */ (_.languages);\n    \t\t\t\tvar grammar = root[inside];\n    \t\t\t\t/** @type {Grammar} */\n    \t\t\t\tvar ret = {};\n\n    \t\t\t\tfor (var token in grammar) {\n    \t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n    \t\t\t\t\t\tif (token == before) {\n    \t\t\t\t\t\t\tfor (var newToken in insert) {\n    \t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n    \t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n    \t\t\t\t\t\t\t\t}\n    \t\t\t\t\t\t\t}\n    \t\t\t\t\t\t}\n\n    \t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n    \t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n    \t\t\t\t\t\t\tret[token] = grammar[token];\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n    \t\t\t\t}\n\n    \t\t\t\tvar old = root[inside];\n    \t\t\t\troot[inside] = ret;\n\n    \t\t\t\t// Update references in other language definitions\n    \t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n    \t\t\t\t\tif (value === old && key != inside) {\n    \t\t\t\t\t\tthis[key] = ret;\n    \t\t\t\t\t}\n    \t\t\t\t});\n\n    \t\t\t\treturn ret;\n    \t\t\t},\n\n    \t\t\t// Traverse a language definition with Depth First Search\n    \t\t\tDFS: function DFS(o, callback, type, visited) {\n    \t\t\t\tvisited = visited || {};\n\n    \t\t\t\tvar objId = _.util.objId;\n\n    \t\t\t\tfor (var i in o) {\n    \t\t\t\t\tif (o.hasOwnProperty(i)) {\n    \t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n    \t\t\t\t\t\tvar property = o[i];\n    \t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n    \t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n    \t\t\t\t\t\t\tvisited[objId(property)] = true;\n    \t\t\t\t\t\t\tDFS(property, callback, null, visited);\n    \t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n    \t\t\t\t\t\t\tvisited[objId(property)] = true;\n    \t\t\t\t\t\t\tDFS(property, callback, i, visited);\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n\n    \t\tplugins: {},\n\n    \t\t/**\n    \t\t * This is the most high-level function in Prism’s API.\n    \t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n    \t\t * each one of them.\n    \t\t *\n    \t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n    \t\t *\n    \t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n    \t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\thighlightAll: function (async, callback) {\n    \t\t\t_.highlightAllUnder(document, async, callback);\n    \t\t},\n\n    \t\t/**\n    \t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n    \t\t * {@link Prism.highlightElement} on each one of them.\n    \t\t *\n    \t\t * The following hooks will be run:\n    \t\t * 1. `before-highlightall`\n    \t\t * 2. `before-all-elements-highlight`\n    \t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n    \t\t *\n    \t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n    \t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n    \t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\thighlightAllUnder: function (container, async, callback) {\n    \t\t\tvar env = {\n    \t\t\t\tcallback: callback,\n    \t\t\t\tcontainer: container,\n    \t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n    \t\t\t};\n\n    \t\t\t_.hooks.run('before-highlightall', env);\n\n    \t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n    \t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n    \t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n    \t\t\t\t_.highlightElement(element, async === true, env.callback);\n    \t\t\t}\n    \t\t},\n\n    \t\t/**\n    \t\t * Highlights the code inside a single element.\n    \t\t *\n    \t\t * The following hooks will be run:\n    \t\t * 1. `before-sanity-check`\n    \t\t * 2. `before-highlight`\n    \t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n    \t\t * 4. `before-insert`\n    \t\t * 5. `after-highlight`\n    \t\t * 6. `complete`\n    \t\t *\n    \t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n    \t\t * the element's language.\n    \t\t *\n    \t\t * @param {Element} element The element containing the code.\n    \t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n    \t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n    \t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n    \t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n    \t\t *\n    \t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n    \t\t * asynchronous highlighting to work. You can build your own bundle on the\n    \t\t * [Download page](https://prismjs.com/download.html).\n    \t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n    \t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\thighlightElement: function (element, async, callback) {\n    \t\t\t// Find language\n    \t\t\tvar language = _.util.getLanguage(element);\n    \t\t\tvar grammar = _.languages[language];\n\n    \t\t\t// Set language on the element, if not present\n    \t\t\t_.util.setLanguage(element, language);\n\n    \t\t\t// Set language on the parent, for styling\n    \t\t\tvar parent = element.parentElement;\n    \t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n    \t\t\t\t_.util.setLanguage(parent, language);\n    \t\t\t}\n\n    \t\t\tvar code = element.textContent;\n\n    \t\t\tvar env = {\n    \t\t\t\telement: element,\n    \t\t\t\tlanguage: language,\n    \t\t\t\tgrammar: grammar,\n    \t\t\t\tcode: code\n    \t\t\t};\n\n    \t\t\tfunction insertHighlightedCode(highlightedCode) {\n    \t\t\t\tenv.highlightedCode = highlightedCode;\n\n    \t\t\t\t_.hooks.run('before-insert', env);\n\n    \t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n    \t\t\t\t_.hooks.run('after-highlight', env);\n    \t\t\t\t_.hooks.run('complete', env);\n    \t\t\t\tcallback && callback.call(env.element);\n    \t\t\t}\n\n    \t\t\t_.hooks.run('before-sanity-check', env);\n\n    \t\t\t// plugins may change/add the parent/element\n    \t\t\tparent = env.element.parentElement;\n    \t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n    \t\t\t\tparent.setAttribute('tabindex', '0');\n    \t\t\t}\n\n    \t\t\tif (!env.code) {\n    \t\t\t\t_.hooks.run('complete', env);\n    \t\t\t\tcallback && callback.call(env.element);\n    \t\t\t\treturn;\n    \t\t\t}\n\n    \t\t\t_.hooks.run('before-highlight', env);\n\n    \t\t\tif (!env.grammar) {\n    \t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n    \t\t\t\treturn;\n    \t\t\t}\n\n    \t\t\tif (async && _self.Worker) {\n    \t\t\t\tvar worker = new Worker(_.filename);\n\n    \t\t\t\tworker.onmessage = function (evt) {\n    \t\t\t\t\tinsertHighlightedCode(evt.data);\n    \t\t\t\t};\n\n    \t\t\t\tworker.postMessage(JSON.stringify({\n    \t\t\t\t\tlanguage: env.language,\n    \t\t\t\t\tcode: env.code,\n    \t\t\t\t\timmediateClose: true\n    \t\t\t\t}));\n    \t\t\t} else {\n    \t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n    \t\t\t}\n    \t\t},\n\n    \t\t/**\n    \t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n    \t\t * and the language definitions to use, and returns a string with the HTML produced.\n    \t\t *\n    \t\t * The following hooks will be run:\n    \t\t * 1. `before-tokenize`\n    \t\t * 2. `after-tokenize`\n    \t\t * 3. `wrap`: On each {@link Token}.\n    \t\t *\n    \t\t * @param {string} text A string with the code to be highlighted.\n    \t\t * @param {Grammar} grammar An object containing the tokens to use.\n    \t\t *\n    \t\t * Usually a language definition like `Prism.languages.markup`.\n    \t\t * @param {string} language The name of the language definition passed to `grammar`.\n    \t\t * @returns {string} The highlighted HTML.\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t * @example\n    \t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n    \t\t */\n    \t\thighlight: function (text, grammar, language) {\n    \t\t\tvar env = {\n    \t\t\t\tcode: text,\n    \t\t\t\tgrammar: grammar,\n    \t\t\t\tlanguage: language\n    \t\t\t};\n    \t\t\t_.hooks.run('before-tokenize', env);\n    \t\t\tif (!env.grammar) {\n    \t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n    \t\t\t}\n    \t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n    \t\t\t_.hooks.run('after-tokenize', env);\n    \t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n    \t\t},\n\n    \t\t/**\n    \t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n    \t\t * and the language definitions to use, and returns an array with the tokenized code.\n    \t\t *\n    \t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n    \t\t *\n    \t\t * This method could be useful in other contexts as well, as a very crude parser.\n    \t\t *\n    \t\t * @param {string} text A string with the code to be highlighted.\n    \t\t * @param {Grammar} grammar An object containing the tokens to use.\n    \t\t *\n    \t\t * Usually a language definition like `Prism.languages.markup`.\n    \t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t * @example\n    \t\t * let code = `var foo = 0;`;\n    \t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n    \t\t * tokens.forEach(token => {\n    \t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n    \t\t *         console.log(`Found numeric literal: ${token.content}`);\n    \t\t *     }\n    \t\t * });\n    \t\t */\n    \t\ttokenize: function (text, grammar) {\n    \t\t\tvar rest = grammar.rest;\n    \t\t\tif (rest) {\n    \t\t\t\tfor (var token in rest) {\n    \t\t\t\t\tgrammar[token] = rest[token];\n    \t\t\t\t}\n\n    \t\t\t\tdelete grammar.rest;\n    \t\t\t}\n\n    \t\t\tvar tokenList = new LinkedList();\n    \t\t\taddAfter(tokenList, tokenList.head, text);\n\n    \t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n    \t\t\treturn toArray(tokenList);\n    \t\t},\n\n    \t\t/**\n    \t\t * @namespace\n    \t\t * @memberof Prism\n    \t\t * @public\n    \t\t */\n    \t\thooks: {\n    \t\t\tall: {},\n\n    \t\t\t/**\n    \t\t\t * Adds the given callback to the list of callbacks for the given hook.\n    \t\t\t *\n    \t\t\t * The callback will be invoked when the hook it is registered for is run.\n    \t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n    \t\t\t *\n    \t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n    \t\t\t *\n    \t\t\t * @param {string} name The name of the hook.\n    \t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n    \t\t\t * @public\n    \t\t\t */\n    \t\t\tadd: function (name, callback) {\n    \t\t\t\tvar hooks = _.hooks.all;\n\n    \t\t\t\thooks[name] = hooks[name] || [];\n\n    \t\t\t\thooks[name].push(callback);\n    \t\t\t},\n\n    \t\t\t/**\n    \t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n    \t\t\t *\n    \t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n    \t\t\t *\n    \t\t\t * @param {string} name The name of the hook.\n    \t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n    \t\t\t * @public\n    \t\t\t */\n    \t\t\trun: function (name, env) {\n    \t\t\t\tvar callbacks = _.hooks.all[name];\n\n    \t\t\t\tif (!callbacks || !callbacks.length) {\n    \t\t\t\t\treturn;\n    \t\t\t\t}\n\n    \t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n    \t\t\t\t\tcallback(env);\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n\n    \t\tToken: Token\n    \t};\n    \t_self.Prism = _;\n\n\n    \t// Typescript note:\n    \t// The following can be used to import the Token type in JSDoc:\n    \t//\n    \t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n    \t/**\n    \t * Creates a new token.\n    \t *\n    \t * @param {string} type See {@link Token#type type}\n    \t * @param {string | TokenStream} content See {@link Token#content content}\n    \t * @param {string|string[]} [alias] The alias(es) of the token.\n    \t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n    \t * @class\n    \t * @global\n    \t * @public\n    \t */\n    \tfunction Token(type, content, alias, matchedStr) {\n    \t\t/**\n    \t\t * The type of the token.\n    \t\t *\n    \t\t * This is usually the key of a pattern in a {@link Grammar}.\n    \t\t *\n    \t\t * @type {string}\n    \t\t * @see GrammarToken\n    \t\t * @public\n    \t\t */\n    \t\tthis.type = type;\n    \t\t/**\n    \t\t * The strings or tokens contained by this token.\n    \t\t *\n    \t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n    \t\t *\n    \t\t * @type {string | TokenStream}\n    \t\t * @public\n    \t\t */\n    \t\tthis.content = content;\n    \t\t/**\n    \t\t * The alias(es) of the token.\n    \t\t *\n    \t\t * @type {string|string[]}\n    \t\t * @see GrammarToken\n    \t\t * @public\n    \t\t */\n    \t\tthis.alias = alias;\n    \t\t// Copy of the full string this token was created from\n    \t\tthis.length = (matchedStr || '').length | 0;\n    \t}\n\n    \t/**\n    \t * A token stream is an array of strings and {@link Token Token} objects.\n    \t *\n    \t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n    \t * them.\n    \t *\n    \t * 1. No adjacent strings.\n    \t * 2. No empty strings.\n    \t *\n    \t *    The only exception here is the token stream that only contains the empty string and nothing else.\n    \t *\n    \t * @typedef {Array<string | Token>} TokenStream\n    \t * @global\n    \t * @public\n    \t */\n\n    \t/**\n    \t * Converts the given token or token stream to an HTML representation.\n    \t *\n    \t * The following hooks will be run:\n    \t * 1. `wrap`: On each {@link Token}.\n    \t *\n    \t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n    \t * @param {string} language The name of current language.\n    \t * @returns {string} The HTML representation of the token or token stream.\n    \t * @memberof Token\n    \t * @static\n    \t */\n    \tToken.stringify = function stringify(o, language) {\n    \t\tif (typeof o == 'string') {\n    \t\t\treturn o;\n    \t\t}\n    \t\tif (Array.isArray(o)) {\n    \t\t\tvar s = '';\n    \t\t\to.forEach(function (e) {\n    \t\t\t\ts += stringify(e, language);\n    \t\t\t});\n    \t\t\treturn s;\n    \t\t}\n\n    \t\tvar env = {\n    \t\t\ttype: o.type,\n    \t\t\tcontent: stringify(o.content, language),\n    \t\t\ttag: 'span',\n    \t\t\tclasses: ['token', o.type],\n    \t\t\tattributes: {},\n    \t\t\tlanguage: language\n    \t\t};\n\n    \t\tvar aliases = o.alias;\n    \t\tif (aliases) {\n    \t\t\tif (Array.isArray(aliases)) {\n    \t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n    \t\t\t} else {\n    \t\t\t\tenv.classes.push(aliases);\n    \t\t\t}\n    \t\t}\n\n    \t\t_.hooks.run('wrap', env);\n\n    \t\tvar attributes = '';\n    \t\tfor (var name in env.attributes) {\n    \t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n    \t\t}\n\n    \t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n    \t};\n\n    \t/**\n    \t * @param {RegExp} pattern\n    \t * @param {number} pos\n    \t * @param {string} text\n    \t * @param {boolean} lookbehind\n    \t * @returns {RegExpExecArray | null}\n    \t */\n    \tfunction matchPattern(pattern, pos, text, lookbehind) {\n    \t\tpattern.lastIndex = pos;\n    \t\tvar match = pattern.exec(text);\n    \t\tif (match && lookbehind && match[1]) {\n    \t\t\t// change the match to remove the text matched by the Prism lookbehind group\n    \t\t\tvar lookbehindLength = match[1].length;\n    \t\t\tmatch.index += lookbehindLength;\n    \t\t\tmatch[0] = match[0].slice(lookbehindLength);\n    \t\t}\n    \t\treturn match;\n    \t}\n\n    \t/**\n    \t * @param {string} text\n    \t * @param {LinkedList<string | Token>} tokenList\n    \t * @param {any} grammar\n    \t * @param {LinkedListNode<string | Token>} startNode\n    \t * @param {number} startPos\n    \t * @param {RematchOptions} [rematch]\n    \t * @returns {void}\n    \t * @private\n    \t *\n    \t * @typedef RematchOptions\n    \t * @property {string} cause\n    \t * @property {number} reach\n    \t */\n    \tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n    \t\tfor (var token in grammar) {\n    \t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n    \t\t\t\tcontinue;\n    \t\t\t}\n\n    \t\t\tvar patterns = grammar[token];\n    \t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n    \t\t\tfor (var j = 0; j < patterns.length; ++j) {\n    \t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n    \t\t\t\t\treturn;\n    \t\t\t\t}\n\n    \t\t\t\tvar patternObj = patterns[j];\n    \t\t\t\tvar inside = patternObj.inside;\n    \t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n    \t\t\t\tvar greedy = !!patternObj.greedy;\n    \t\t\t\tvar alias = patternObj.alias;\n\n    \t\t\t\tif (greedy && !patternObj.pattern.global) {\n    \t\t\t\t\t// Without the global flag, lastIndex won't work\n    \t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n    \t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n    \t\t\t\t}\n\n    \t\t\t\t/** @type {RegExp} */\n    \t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n    \t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n    \t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n    \t\t\t\t\tcurrentNode !== tokenList.tail;\n    \t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n    \t\t\t\t) {\n\n    \t\t\t\t\tif (rematch && pos >= rematch.reach) {\n    \t\t\t\t\t\tbreak;\n    \t\t\t\t\t}\n\n    \t\t\t\t\tvar str = currentNode.value;\n\n    \t\t\t\t\tif (tokenList.length > text.length) {\n    \t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n    \t\t\t\t\t\treturn;\n    \t\t\t\t\t}\n\n    \t\t\t\t\tif (str instanceof Token) {\n    \t\t\t\t\t\tcontinue;\n    \t\t\t\t\t}\n\n    \t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n    \t\t\t\t\tvar match;\n\n    \t\t\t\t\tif (greedy) {\n    \t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n    \t\t\t\t\t\tif (!match || match.index >= text.length) {\n    \t\t\t\t\t\t\tbreak;\n    \t\t\t\t\t\t}\n\n    \t\t\t\t\t\tvar from = match.index;\n    \t\t\t\t\t\tvar to = match.index + match[0].length;\n    \t\t\t\t\t\tvar p = pos;\n\n    \t\t\t\t\t\t// find the node that contains the match\n    \t\t\t\t\t\tp += currentNode.value.length;\n    \t\t\t\t\t\twhile (from >= p) {\n    \t\t\t\t\t\t\tcurrentNode = currentNode.next;\n    \t\t\t\t\t\t\tp += currentNode.value.length;\n    \t\t\t\t\t\t}\n    \t\t\t\t\t\t// adjust pos (and p)\n    \t\t\t\t\t\tp -= currentNode.value.length;\n    \t\t\t\t\t\tpos = p;\n\n    \t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n    \t\t\t\t\t\tif (currentNode.value instanceof Token) {\n    \t\t\t\t\t\t\tcontinue;\n    \t\t\t\t\t\t}\n\n    \t\t\t\t\t\t// find the last node which is affected by this match\n    \t\t\t\t\t\tfor (\n    \t\t\t\t\t\t\tvar k = currentNode;\n    \t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n    \t\t\t\t\t\t\tk = k.next\n    \t\t\t\t\t\t) {\n    \t\t\t\t\t\t\tremoveCount++;\n    \t\t\t\t\t\t\tp += k.value.length;\n    \t\t\t\t\t\t}\n    \t\t\t\t\t\tremoveCount--;\n\n    \t\t\t\t\t\t// replace with the new match\n    \t\t\t\t\t\tstr = text.slice(pos, p);\n    \t\t\t\t\t\tmatch.index -= pos;\n    \t\t\t\t\t} else {\n    \t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n    \t\t\t\t\t\tif (!match) {\n    \t\t\t\t\t\t\tcontinue;\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n\n    \t\t\t\t\t// eslint-disable-next-line no-redeclare\n    \t\t\t\t\tvar from = match.index;\n    \t\t\t\t\tvar matchStr = match[0];\n    \t\t\t\t\tvar before = str.slice(0, from);\n    \t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n    \t\t\t\t\tvar reach = pos + str.length;\n    \t\t\t\t\tif (rematch && reach > rematch.reach) {\n    \t\t\t\t\t\trematch.reach = reach;\n    \t\t\t\t\t}\n\n    \t\t\t\t\tvar removeFrom = currentNode.prev;\n\n    \t\t\t\t\tif (before) {\n    \t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n    \t\t\t\t\t\tpos += before.length;\n    \t\t\t\t\t}\n\n    \t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n    \t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n    \t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n    \t\t\t\t\tif (after) {\n    \t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n    \t\t\t\t\t}\n\n    \t\t\t\t\tif (removeCount > 1) {\n    \t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n    \t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n    \t\t\t\t\t\t/** @type {RematchOptions} */\n    \t\t\t\t\t\tvar nestedRematch = {\n    \t\t\t\t\t\t\tcause: token + ',' + j,\n    \t\t\t\t\t\t\treach: reach\n    \t\t\t\t\t\t};\n    \t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n    \t\t\t\t\t\t// the reach might have been extended because of the rematching\n    \t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n    \t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t}\n\n    \t/**\n    \t * @typedef LinkedListNode\n    \t * @property {T} value\n    \t * @property {LinkedListNode<T> | null} prev The previous node.\n    \t * @property {LinkedListNode<T> | null} next The next node.\n    \t * @template T\n    \t * @private\n    \t */\n\n    \t/**\n    \t * @template T\n    \t * @private\n    \t */\n    \tfunction LinkedList() {\n    \t\t/** @type {LinkedListNode<T>} */\n    \t\tvar head = { value: null, prev: null, next: null };\n    \t\t/** @type {LinkedListNode<T>} */\n    \t\tvar tail = { value: null, prev: head, next: null };\n    \t\thead.next = tail;\n\n    \t\t/** @type {LinkedListNode<T>} */\n    \t\tthis.head = head;\n    \t\t/** @type {LinkedListNode<T>} */\n    \t\tthis.tail = tail;\n    \t\tthis.length = 0;\n    \t}\n\n    \t/**\n    \t * Adds a new node with the given value to the list.\n    \t *\n    \t * @param {LinkedList<T>} list\n    \t * @param {LinkedListNode<T>} node\n    \t * @param {T} value\n    \t * @returns {LinkedListNode<T>} The added node.\n    \t * @template T\n    \t */\n    \tfunction addAfter(list, node, value) {\n    \t\t// assumes that node != list.tail && values.length >= 0\n    \t\tvar next = node.next;\n\n    \t\tvar newNode = { value: value, prev: node, next: next };\n    \t\tnode.next = newNode;\n    \t\tnext.prev = newNode;\n    \t\tlist.length++;\n\n    \t\treturn newNode;\n    \t}\n    \t/**\n    \t * Removes `count` nodes after the given node. The given node will not be removed.\n    \t *\n    \t * @param {LinkedList<T>} list\n    \t * @param {LinkedListNode<T>} node\n    \t * @param {number} count\n    \t * @template T\n    \t */\n    \tfunction removeRange(list, node, count) {\n    \t\tvar next = node.next;\n    \t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n    \t\t\tnext = next.next;\n    \t\t}\n    \t\tnode.next = next;\n    \t\tnext.prev = node;\n    \t\tlist.length -= i;\n    \t}\n    \t/**\n    \t * @param {LinkedList<T>} list\n    \t * @returns {T[]}\n    \t * @template T\n    \t */\n    \tfunction toArray(list) {\n    \t\tvar array = [];\n    \t\tvar node = list.head.next;\n    \t\twhile (node !== list.tail) {\n    \t\t\tarray.push(node.value);\n    \t\t\tnode = node.next;\n    \t\t}\n    \t\treturn array;\n    \t}\n\n\n    \tif (!_self.document) {\n    \t\tif (!_self.addEventListener) {\n    \t\t\t// in Node.js\n    \t\t\treturn _;\n    \t\t}\n\n    \t\tif (!_.disableWorkerMessageHandler) {\n    \t\t\t// In worker\n    \t\t\t_self.addEventListener('message', function (evt) {\n    \t\t\t\tvar message = JSON.parse(evt.data);\n    \t\t\t\tvar lang = message.language;\n    \t\t\t\tvar code = message.code;\n    \t\t\t\tvar immediateClose = message.immediateClose;\n\n    \t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n    \t\t\t\tif (immediateClose) {\n    \t\t\t\t\t_self.close();\n    \t\t\t\t}\n    \t\t\t}, false);\n    \t\t}\n\n    \t\treturn _;\n    \t}\n\n    \t// Get current script and highlight\n    \tvar script = _.util.currentScript();\n\n    \tif (script) {\n    \t\t_.filename = script.src;\n\n    \t\tif (script.hasAttribute('data-manual')) {\n    \t\t\t_.manual = true;\n    \t\t}\n    \t}\n\n    \tfunction highlightAutomaticallyCallback() {\n    \t\tif (!_.manual) {\n    \t\t\t_.highlightAll();\n    \t\t}\n    \t}\n\n    \tif (!_.manual) {\n    \t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n    \t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n    \t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n    \t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n    \t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n    \t\t// See https://github.com/PrismJS/prism/issues/2102\n    \t\tvar readyState = document.readyState;\n    \t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n    \t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n    \t\t} else {\n    \t\t\tif (window.requestAnimationFrame) {\n    \t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n    \t\t\t} else {\n    \t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n    \t\t\t}\n    \t\t}\n    \t}\n\n    \treturn _;\n\n    }(_self));\n\n    if (typeof module !== 'undefined' && module.exports) {\n    \tmodule.exports = Prism;\n    }\n\n    // hack for components to work correctly in node.js\n    if (typeof global !== 'undefined') {\n    \tglobal.Prism = Prism;\n    }\n\n    // some additional documentation/types\n\n    /**\n     * The expansion of a simple `RegExp` literal to support additional properties.\n     *\n     * @typedef GrammarToken\n     * @property {RegExp} pattern The regular expression of the token.\n     * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n     * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n     * @property {boolean} [greedy=false] Whether the token is greedy.\n     * @property {string|string[]} [alias] An optional alias or list of aliases.\n     * @property {Grammar} [inside] The nested grammar of this token.\n     *\n     * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n     *\n     * This can be used to make nested and even recursive language definitions.\n     *\n     * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n     * each another.\n     * @global\n     * @public\n     */\n\n    /**\n     * @typedef Grammar\n     * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n     * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n     * @global\n     * @public\n     */\n\n    /**\n     * A function which will invoked after an element was successfully highlighted.\n     *\n     * @callback HighlightCallback\n     * @param {Element} element The element successfully highlighted.\n     * @returns {void}\n     * @global\n     * @public\n     */\n\n    /**\n     * @callback HookCallback\n     * @param {Object<string, any>} env The environment variables of the hook.\n     * @returns {void}\n     * @global\n     * @public\n     */\n\n    Prism.languages.clike = {\n    \t'comment': [\n    \t\t{\n    \t\t\tpattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(^|[^\\\\:])\\/\\/.*/,\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true\n    \t\t}\n    \t],\n    \t'string': {\n    \t\tpattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    \t\tgreedy: true\n    \t},\n    \t'class-name': {\n    \t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n    \t\tlookbehind: true,\n    \t\tinside: {\n    \t\t\t'punctuation': /[.\\\\]/\n    \t\t}\n    \t},\n    \t'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n    \t'boolean': /\\b(?:false|true)\\b/,\n    \t'function': /\\b\\w+(?=\\()/,\n    \t'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    \t'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n    \t'punctuation': /[{}[\\];(),.:]/\n    };\n\n    (function (Prism) {\n\n    \t/**\n    \t * Returns the placeholder for the given language id and index.\n    \t *\n    \t * @param {string} language\n    \t * @param {string|number} index\n    \t * @returns {string}\n    \t */\n    \tfunction getPlaceholder(language, index) {\n    \t\treturn '___' + language.toUpperCase() + index + '___';\n    \t}\n\n    \tObject.defineProperties(Prism.languages['markup-templating'] = {}, {\n    \t\tbuildPlaceholders: {\n    \t\t\t/**\n    \t\t\t * Tokenize all inline templating expressions matching `placeholderPattern`.\n    \t\t\t *\n    \t\t\t * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n    \t\t\t * `true` will be replaced.\n    \t\t\t *\n    \t\t\t * @param {object} env The environment of the `before-tokenize` hook.\n    \t\t\t * @param {string} language The language id.\n    \t\t\t * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n    \t\t\t * @param {(match: string) => boolean} [replaceFilter]\n    \t\t\t */\n    \t\t\tvalue: function (env, language, placeholderPattern, replaceFilter) {\n    \t\t\t\tif (env.language !== language) {\n    \t\t\t\t\treturn;\n    \t\t\t\t}\n\n    \t\t\t\tvar tokenStack = env.tokenStack = [];\n\n    \t\t\t\tenv.code = env.code.replace(placeholderPattern, function (match) {\n    \t\t\t\t\tif (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n    \t\t\t\t\t\treturn match;\n    \t\t\t\t\t}\n    \t\t\t\t\tvar i = tokenStack.length;\n    \t\t\t\t\tvar placeholder;\n\n    \t\t\t\t\t// Check for existing strings\n    \t\t\t\t\twhile (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n    \t\t\t\t\t\t++i;\n    \t\t\t\t\t}\n\n    \t\t\t\t\t// Create a sparse array\n    \t\t\t\t\ttokenStack[i] = match;\n\n    \t\t\t\t\treturn placeholder;\n    \t\t\t\t});\n\n    \t\t\t\t// Switch the grammar to markup\n    \t\t\t\tenv.grammar = Prism.languages.markup;\n    \t\t\t}\n    \t\t},\n    \t\ttokenizePlaceholders: {\n    \t\t\t/**\n    \t\t\t * Replace placeholders with proper tokens after tokenizing.\n    \t\t\t *\n    \t\t\t * @param {object} env The environment of the `after-tokenize` hook.\n    \t\t\t * @param {string} language The language id.\n    \t\t\t */\n    \t\t\tvalue: function (env, language) {\n    \t\t\t\tif (env.language !== language || !env.tokenStack) {\n    \t\t\t\t\treturn;\n    \t\t\t\t}\n\n    \t\t\t\t// Switch the grammar back\n    \t\t\t\tenv.grammar = Prism.languages[language];\n\n    \t\t\t\tvar j = 0;\n    \t\t\t\tvar keys = Object.keys(env.tokenStack);\n\n    \t\t\t\tfunction walkTokens(tokens) {\n    \t\t\t\t\tfor (var i = 0; i < tokens.length; i++) {\n    \t\t\t\t\t\t// all placeholders are replaced already\n    \t\t\t\t\t\tif (j >= keys.length) {\n    \t\t\t\t\t\t\tbreak;\n    \t\t\t\t\t\t}\n\n    \t\t\t\t\t\tvar token = tokens[i];\n    \t\t\t\t\t\tif (typeof token === 'string' || (token.content && typeof token.content === 'string')) {\n    \t\t\t\t\t\t\tvar k = keys[j];\n    \t\t\t\t\t\t\tvar t = env.tokenStack[k];\n    \t\t\t\t\t\t\tvar s = typeof token === 'string' ? token : token.content;\n    \t\t\t\t\t\t\tvar placeholder = getPlaceholder(language, k);\n\n    \t\t\t\t\t\t\tvar index = s.indexOf(placeholder);\n    \t\t\t\t\t\t\tif (index > -1) {\n    \t\t\t\t\t\t\t\t++j;\n\n    \t\t\t\t\t\t\t\tvar before = s.substring(0, index);\n    \t\t\t\t\t\t\t\tvar middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n    \t\t\t\t\t\t\t\tvar after = s.substring(index + placeholder.length);\n\n    \t\t\t\t\t\t\t\tvar replacement = [];\n    \t\t\t\t\t\t\t\tif (before) {\n    \t\t\t\t\t\t\t\t\treplacement.push.apply(replacement, walkTokens([before]));\n    \t\t\t\t\t\t\t\t}\n    \t\t\t\t\t\t\t\treplacement.push(middle);\n    \t\t\t\t\t\t\t\tif (after) {\n    \t\t\t\t\t\t\t\t\treplacement.push.apply(replacement, walkTokens([after]));\n    \t\t\t\t\t\t\t\t}\n\n    \t\t\t\t\t\t\t\tif (typeof token === 'string') {\n    \t\t\t\t\t\t\t\t\ttokens.splice.apply(tokens, [i, 1].concat(replacement));\n    \t\t\t\t\t\t\t\t} else {\n    \t\t\t\t\t\t\t\t\ttoken.content = replacement;\n    \t\t\t\t\t\t\t\t}\n    \t\t\t\t\t\t\t}\n    \t\t\t\t\t\t} else if (token.content /* && typeof token.content !== 'string' */) {\n    \t\t\t\t\t\t\twalkTokens(token.content);\n    \t\t\t\t\t\t}\n    \t\t\t\t\t}\n\n    \t\t\t\t\treturn tokens;\n    \t\t\t\t}\n\n    \t\t\t\twalkTokens(env.tokens);\n    \t\t\t}\n    \t\t}\n    \t});\n\n    }(Prism));\n\n    Prism.languages.c = Prism.languages.extend('clike', {\n    \t'comment': {\n    \t\tpattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    \t\tgreedy: true\n    \t},\n    \t'string': {\n    \t\t// https://en.cppreference.com/w/c/language/string_literal\n    \t\tpattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n    \t\tgreedy: true\n    \t},\n    \t'class-name': {\n    \t\tpattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n    \t\tlookbehind: true\n    \t},\n    \t'keyword': /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n    \t'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    \t'number': /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n    \t'operator': />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n    });\n\n    Prism.languages.insertBefore('c', 'string', {\n    \t'char': {\n    \t\t// https://en.cppreference.com/w/c/language/character_constant\n    \t\tpattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n    \t\tgreedy: true\n    \t}\n    });\n\n    Prism.languages.insertBefore('c', 'string', {\n    \t'macro': {\n    \t\t// allow for multiline macro definitions\n    \t\t// spaces after the # character compile fine with gcc\n    \t\tpattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n    \t\tlookbehind: true,\n    \t\tgreedy: true,\n    \t\talias: 'property',\n    \t\tinside: {\n    \t\t\t'string': [\n    \t\t\t\t{\n    \t\t\t\t\t// highlight the path of the include statement as a string\n    \t\t\t\t\tpattern: /^(#\\s*include\\s*)<[^>]+>/,\n    \t\t\t\t\tlookbehind: true\n    \t\t\t\t},\n    \t\t\t\tPrism.languages.c['string']\n    \t\t\t],\n    \t\t\t'char': Prism.languages.c['char'],\n    \t\t\t'comment': Prism.languages.c['comment'],\n    \t\t\t'macro-name': [\n    \t\t\t\t{\n    \t\t\t\t\tpattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n    \t\t\t\t\tlookbehind: true\n    \t\t\t\t},\n    \t\t\t\t{\n    \t\t\t\t\tpattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n    \t\t\t\t\tlookbehind: true,\n    \t\t\t\t\talias: 'function'\n    \t\t\t\t}\n    \t\t\t],\n    \t\t\t// highlight macro directives as keywords\n    \t\t\t'directive': {\n    \t\t\t\tpattern: /^(#\\s*)[a-z]+/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\talias: 'keyword'\n    \t\t\t},\n    \t\t\t'directive-hash': /^#/,\n    \t\t\t'punctuation': /##|\\\\(?=[\\r\\n])/,\n    \t\t\t'expression': {\n    \t\t\t\tpattern: /\\S[\\s\\S]*/,\n    \t\t\t\tinside: Prism.languages.c\n    \t\t\t}\n    \t\t}\n    \t}\n    });\n\n    Prism.languages.insertBefore('c', 'function', {\n    \t// highlight predefined macros as constants\n    \t'constant': /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n    });\n\n    delete Prism.languages.c['boolean'];\n\n    (function (Prism) {\n\n    \tvar keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n    \tvar modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () { return keyword.source; });\n\n    \tPrism.languages.cpp = Prism.languages.extend('c', {\n    \t\t'class-name': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source\n    \t\t\t\t\t.replace(/<keyword>/g, function () { return keyword.source; })),\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t// This is intended to capture the class name of method implementations like:\n    \t\t\t//   void foo::bar() const {}\n    \t\t\t// However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n    \t\t\t// it starts with an uppercase letter. This approximation should give decent results.\n    \t\t\t/\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,\n    \t\t\t// This will capture the class name before destructors like:\n    \t\t\t//   Foo::~Foo() {}\n    \t\t\t/\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,\n    \t\t\t// This also intends to capture the class name of method implementations but here the class has template\n    \t\t\t// parameters, so it can't be a namespace (until C++ adds generic namespaces).\n    \t\t\t/\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n    \t\t],\n    \t\t'keyword': keyword,\n    \t\t'number': {\n    \t\t\tpattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t'operator': />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n    \t\t'boolean': /\\b(?:false|true)\\b/\n    \t});\n\n    \tPrism.languages.insertBefore('cpp', 'string', {\n    \t\t'module': {\n    \t\t\t// https://en.cppreference.com/w/cpp/language/modules\n    \t\t\tpattern: RegExp(\n    \t\t\t\t/(\\b(?:import|module)\\s+)/.source +\n    \t\t\t\t'(?:' +\n    \t\t\t\t// header-name\n    \t\t\t\t/\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// module name or partition or both\n    \t\t\t\t/<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () { return modName; }) +\n    \t\t\t\t')'\n    \t\t\t),\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'string': /^[<\"][\\s\\S]+/,\n    \t\t\t\t'operator': /:/,\n    \t\t\t\t'punctuation': /\\./\n    \t\t\t}\n    \t\t},\n    \t\t'raw-string': {\n    \t\t\tpattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n    \t\t\talias: 'string',\n    \t\t\tgreedy: true\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('cpp', 'keyword', {\n    \t\t'generic-function': {\n    \t\t\tpattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n    \t\t\tinside: {\n    \t\t\t\t'function': /^\\w+/,\n    \t\t\t\t'generic': {\n    \t\t\t\t\tpattern: /<[\\s\\S]+/,\n    \t\t\t\t\talias: 'class-name',\n    \t\t\t\t\tinside: Prism.languages.cpp\n    \t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('cpp', 'operator', {\n    \t\t'double-colon': {\n    \t\t\tpattern: /::/,\n    \t\t\talias: 'punctuation'\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('cpp', 'class-name', {\n    \t\t// the base clause is an optional list of parent classes\n    \t\t// https://en.cppreference.com/w/cpp/language/class\n    \t\t'base-clause': {\n    \t\t\tpattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true,\n    \t\t\tinside: Prism.languages.extend('cpp', {})\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('inside', 'double-colon', {\n    \t\t// All untokenized words that are not namespaces should be class names\n    \t\t'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n    \t}, Prism.languages.cpp['base-clause']);\n\n    }(Prism));\n\n    (function (Prism) {\n\n    \t/**\n    \t * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n    \t *\n    \t * Note: This is a simple text based replacement. Be careful when using backreferences!\n    \t *\n    \t * @param {string} pattern the given pattern.\n    \t * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n    \t * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n    \t * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n    \t */\n    \tfunction replace(pattern, replacements) {\n    \t\treturn pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n    \t\t\treturn '(?:' + replacements[+index] + ')';\n    \t\t});\n    \t}\n    \t/**\n    \t * @param {string} pattern\n    \t * @param {string[]} replacements\n    \t * @param {string} [flags]\n    \t * @returns {RegExp}\n    \t */\n    \tfunction re(pattern, replacements, flags) {\n    \t\treturn RegExp(replace(pattern, replacements), flags || '');\n    \t}\n\n    \t/**\n    \t * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n    \t *\n    \t * @param {string} pattern\n    \t * @param {number} depthLog2\n    \t * @returns {string}\n    \t */\n    \tfunction nested(pattern, depthLog2) {\n    \t\tfor (var i = 0; i < depthLog2; i++) {\n    \t\t\tpattern = pattern.replace(/<<self>>/g, function () { return '(?:' + pattern + ')'; });\n    \t\t}\n    \t\treturn pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n    \t}\n\n    \t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n    \tvar keywordKinds = {\n    \t\t// keywords which represent a return or variable type\n    \t\ttype: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n    \t\t// keywords which are used to declare a type\n    \t\ttypeDeclaration: 'class enum interface record struct',\n    \t\t// contextual keywords\n    \t\t// (\"var\" and \"dynamic\" are missing because they are used like types)\n    \t\tcontextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n    \t\t// all other keywords\n    \t\tother: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n    \t};\n\n    \t// keywords\n    \tfunction keywordsToPattern(words) {\n    \t\treturn '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n    \t}\n    \tvar typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n    \tvar keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n    \tvar nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n    \tvar nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other);\n\n    \t// types\n    \tvar generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2); // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n    \tvar nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n    \tvar name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n    \tvar genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic]);\n    \tvar identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [nonTypeKeywords, genericName]);\n    \tvar array = /\\[\\s*(?:,\\s*)*\\]/.source;\n    \tvar typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [identifier, array]);\n    \tvar tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [generic, nestedRound, array]);\n    \tvar tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n    \tvar typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [tuple, identifier, array]);\n\n    \tvar typeInside = {\n    \t\t'keyword': keywords,\n    \t\t'punctuation': /[<>()?,.:[\\]]/\n    \t};\n\n    \t// strings & characters\n    \t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n    \t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n    \tvar character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source; // simplified pattern\n    \tvar regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n    \tvar verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n\n\n    \tPrism.languages.csharp = Prism.languages.extend('clike', {\n    \t\t'string': [\n    \t\t\t{\n    \t\t\t\tpattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true\n    \t\t\t}\n    \t\t],\n    \t\t'class-name': [\n    \t\t\t{\n    \t\t\t\t// Using static\n    \t\t\t\t// using static System.Math;\n    \t\t\t\tpattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: typeInside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Using alias (type)\n    \t\t\t\t// using Project = PC.MyCompany.Project;\n    \t\t\t\tpattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [name, typeExpression]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: typeInside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Using alias (alias)\n    \t\t\t\t// using Project = PC.MyCompany.Project;\n    \t\t\t\tpattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Type declarations\n    \t\t\t\t// class Foo<A, B>\n    \t\t\t\t// interface Foo<out A, B>\n    \t\t\t\tpattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [typeDeclarationKeywords, genericName]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: typeInside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Single catch exception declaration\n    \t\t\t\t// catch(Foo)\n    \t\t\t\t// (things like catch(Foo e) is covered by variable declaration)\n    \t\t\t\tpattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: typeInside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Name of the type parameter of generic constraints\n    \t\t\t\t// where Foo : class\n    \t\t\t\tpattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Casts and checks via as and is.\n    \t\t\t\t// as Foo<A>, is Bar<B>\n    \t\t\t\t// (things like if(a is Foo b) is covered by variable declaration)\n    \t\t\t\tpattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: typeInside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// Variable, field and parameter declaration\n    \t\t\t\t// (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n    \t\t\t\tpattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [typeExpression, nonContextualKeywords, name]),\n    \t\t\t\tinside: typeInside\n    \t\t\t}\n    \t\t],\n    \t\t'keyword': keywords,\n    \t\t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n    \t\t'number': /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n    \t\t'operator': />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n    \t\t'punctuation': /\\?\\.?|::|[{}[\\];(),.:]/\n    \t});\n\n    \tPrism.languages.insertBefore('csharp', 'number', {\n    \t\t'range': {\n    \t\t\tpattern: /\\.\\./,\n    \t\t\talias: 'operator'\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('csharp', 'punctuation', {\n    \t\t'named-parameter': {\n    \t\t\tpattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'punctuation'\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('csharp', 'class-name', {\n    \t\t'namespace': {\n    \t\t\t// namespace Foo.Bar {}\n    \t\t\t// using Foo.Bar;\n    \t\t\tpattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'punctuation': /\\./\n    \t\t\t}\n    \t\t},\n    \t\t'type-expression': {\n    \t\t\t// default(Foo), typeof(Foo<Bar>), sizeof(int)\n    \t\t\tpattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'class-name',\n    \t\t\tinside: typeInside\n    \t\t},\n    \t\t'return-type': {\n    \t\t\t// Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n    \t\t\t// int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n    \t\t\t// int Foo => 0; int Foo { get; set } = 0;\n    \t\t\tpattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [typeExpression, identifier]),\n    \t\t\tinside: typeInside,\n    \t\t\talias: 'class-name'\n    \t\t},\n    \t\t'constructor-invocation': {\n    \t\t\t// new List<Foo<Bar[]>> { }\n    \t\t\tpattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n    \t\t\tlookbehind: true,\n    \t\t\tinside: typeInside,\n    \t\t\talias: 'class-name'\n    \t\t},\n    \t\t/*'explicit-implementation': {\n    \t\t\t// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\n    \t\t\tpattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\n    \t\t\tinside: classNameInside,\n    \t\t\talias: 'class-name'\n    \t\t},*/\n    \t\t'generic-method': {\n    \t\t\t// foo<Bar>()\n    \t\t\tpattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n    \t\t\tinside: {\n    \t\t\t\t'function': re(/^<<0>>/.source, [name]),\n    \t\t\t\t'generic': {\n    \t\t\t\t\tpattern: RegExp(generic),\n    \t\t\t\t\talias: 'class-name',\n    \t\t\t\t\tinside: typeInside\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n    \t\t'type-list': {\n    \t\t\t// The list of types inherited or of generic constraints\n    \t\t\t// class Foo<F> : Bar, IList<FooBar>\n    \t\t\t// where F : Bar, IList<int>\n    \t\t\tpattern: re(\n    \t\t\t\t/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source,\n    \t\t\t\t[typeDeclarationKeywords, genericName, name, typeExpression, keywords.source, nestedRound, /\\bnew\\s*\\(\\s*\\)/.source]\n    \t\t\t),\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'record-arguments': {\n    \t\t\t\t\tpattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [genericName, nestedRound]),\n    \t\t\t\t\tlookbehind: true,\n    \t\t\t\t\tgreedy: true,\n    \t\t\t\t\tinside: Prism.languages.csharp\n    \t\t\t\t},\n    \t\t\t\t'keyword': keywords,\n    \t\t\t\t'class-name': {\n    \t\t\t\t\tpattern: RegExp(typeExpression),\n    \t\t\t\t\tgreedy: true,\n    \t\t\t\t\tinside: typeInside\n    \t\t\t\t},\n    \t\t\t\t'punctuation': /[,()]/\n    \t\t\t}\n    \t\t},\n    \t\t'preprocessor': {\n    \t\t\tpattern: /(^[\\t ]*)#.*/m,\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'property',\n    \t\t\tinside: {\n    \t\t\t\t// highlight preprocessor directives as keywords\n    \t\t\t\t'directive': {\n    \t\t\t\t\tpattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n    \t\t\t\t\tlookbehind: true,\n    \t\t\t\t\talias: 'keyword'\n    \t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t});\n\n    \t// attributes\n    \tvar regularStringOrCharacter = regularString + '|' + character;\n    \tvar regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n    \tvar roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n\n    \t// https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n    \tvar attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n    \tvar attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [identifier, roundExpression]);\n\n    \tPrism.languages.insertBefore('csharp', 'class-name', {\n    \t\t'attribute': {\n    \t\t\t// Attributes\n    \t\t\t// [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n    \t\t\tpattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [attrTarget, attr]),\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'target': {\n    \t\t\t\t\tpattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n    \t\t\t\t\talias: 'keyword'\n    \t\t\t\t},\n    \t\t\t\t'attribute-arguments': {\n    \t\t\t\t\tpattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n    \t\t\t\t\tinside: Prism.languages.csharp\n    \t\t\t\t},\n    \t\t\t\t'class-name': {\n    \t\t\t\t\tpattern: RegExp(identifier),\n    \t\t\t\t\tinside: {\n    \t\t\t\t\t\t'punctuation': /\\./\n    \t\t\t\t\t}\n    \t\t\t\t},\n    \t\t\t\t'punctuation': /[:,]/\n    \t\t\t}\n    \t\t}\n    \t});\n\n\n    \t// string interpolation\n    \tvar formatString = /:[^}\\r\\n]+/.source;\n    \t// multi line\n    \tvar mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n    \tvar mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [mInterpolationRound, formatString]);\n    \t// single line\n    \tvar sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n    \tvar sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [sInterpolationRound, formatString]);\n\n    \tfunction createInterpolationInside(interpolation, interpolationRound) {\n    \t\treturn {\n    \t\t\t'interpolation': {\n    \t\t\t\tpattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'format-string': {\n    \t\t\t\t\t\tpattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [interpolationRound, formatString]),\n    \t\t\t\t\t\tlookbehind: true,\n    \t\t\t\t\t\tinside: {\n    \t\t\t\t\t\t\t'punctuation': /^:/\n    \t\t\t\t\t\t}\n    \t\t\t\t\t},\n    \t\t\t\t\t'punctuation': /^\\{|\\}$/,\n    \t\t\t\t\t'expression': {\n    \t\t\t\t\t\tpattern: /[\\s\\S]+/,\n    \t\t\t\t\t\talias: 'language-csharp',\n    \t\t\t\t\t\tinside: Prism.languages.csharp\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'string': /[\\s\\S]+/\n    \t\t};\n    \t}\n\n    \tPrism.languages.insertBefore('csharp', 'string', {\n    \t\t'interpolation-string': [\n    \t\t\t{\n    \t\t\t\tpattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: createInterpolationInside(mInterpolation, mInterpolationRound),\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: createInterpolationInside(sInterpolation, sInterpolationRound),\n    \t\t\t}\n    \t\t],\n    \t\t'char': {\n    \t\t\tpattern: RegExp(character),\n    \t\t\tgreedy: true\n    \t\t}\n    \t});\n\n    \tPrism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n\n    }(Prism));\n\n    (function (Prism) {\n\n    \tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n    \tPrism.languages.css = {\n    \t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n    \t\t'atrule': {\n    \t\t\tpattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n    \t\t\tinside: {\n    \t\t\t\t'rule': /^@[\\w-]+/,\n    \t\t\t\t'selector-function-argument': {\n    \t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n    \t\t\t\t\tlookbehind: true,\n    \t\t\t\t\talias: 'selector'\n    \t\t\t\t},\n    \t\t\t\t'keyword': {\n    \t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n    \t\t\t\t\tlookbehind: true\n    \t\t\t\t}\n    \t\t\t\t// See rest below\n    \t\t\t}\n    \t\t},\n    \t\t'url': {\n    \t\t\t// https://drafts.csswg.org/css-values-3/#urls\n    \t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'function': /^url/i,\n    \t\t\t\t'punctuation': /^\\(|\\)$/,\n    \t\t\t\t'string': {\n    \t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n    \t\t\t\t\talias: 'url'\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n    \t\t'selector': {\n    \t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'string': {\n    \t\t\tpattern: string,\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t'property': {\n    \t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'important': /!important\\b/i,\n    \t\t'function': {\n    \t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'punctuation': /[(){};:,]/\n    \t};\n\n    \tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n    \tvar markup = Prism.languages.markup;\n    \tif (markup) {\n    \t\tmarkup.tag.addInlined('style', 'css');\n    \t\tmarkup.tag.addAttribute('style', 'css');\n    \t}\n\n    }(Prism));\n\n    (function (Prism) {\n\n    \tvar keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\\s*[(){}[\\]<>=%~.:,;?+\\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/;\n\n    \t// full package (optional) + parent classes (optional)\n    \tvar classNamePrefix = /(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source;\n\n    \t// based on the java naming conventions\n    \tvar className = {\n    \t\tpattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n    \t\tlookbehind: true,\n    \t\tinside: {\n    \t\t\t'namespace': {\n    \t\t\t\tpattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\./\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'punctuation': /\\./\n    \t\t}\n    \t};\n\n    \tPrism.languages.java = Prism.languages.extend('clike', {\n    \t\t'string': {\n    \t\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t'class-name': [\n    \t\t\tclassName,\n    \t\t\t{\n    \t\t\t\t// variables, parameters, and constructor references\n    \t\t\t\t// this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n    \t\t\t\tpattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()]|\\s*(?:\\[[\\s,]*\\]\\s*)?::\\s*new\\b)/.source),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: className.inside\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// class names based on keyword\n    \t\t\t\t// this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n    \t\t\t\tpattern: RegExp(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\\s+)/.source + classNamePrefix + /[A-Z]\\w*\\b/.source),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: className.inside\n    \t\t\t}\n    \t\t],\n    \t\t'keyword': keywords,\n    \t\t'function': [\n    \t\t\tPrism.languages.clike.function,\n    \t\t\t{\n    \t\t\t\tpattern: /(::\\s*)[a-z_]\\w*/,\n    \t\t\t\tlookbehind: true\n    \t\t\t}\n    \t\t],\n    \t\t'number': /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n    \t\t'operator': {\n    \t\t\tpattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'constant': /\\b[A-Z][A-Z_\\d]+\\b/\n    \t});\n\n    \tPrism.languages.insertBefore('java', 'string', {\n    \t\t'triple-quoted-string': {\n    \t\t\t// http://openjdk.java.net/jeps/355#Description\n    \t\t\tpattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n    \t\t\tgreedy: true,\n    \t\t\talias: 'string'\n    \t\t},\n    \t\t'char': {\n    \t\t\tpattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n    \t\t\tgreedy: true\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('java', 'class-name', {\n    \t\t'annotation': {\n    \t\t\tpattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'punctuation'\n    \t\t},\n    \t\t'generics': {\n    \t\t\tpattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n    \t\t\tinside: {\n    \t\t\t\t'class-name': className,\n    \t\t\t\t'keyword': keywords,\n    \t\t\t\t'punctuation': /[<>(),.:]/,\n    \t\t\t\t'operator': /[?&|]/\n    \t\t\t}\n    \t\t},\n    \t\t'import': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/(\\bimport\\s+)/.source + classNamePrefix + /(?:[A-Z]\\w*|\\*)(?=\\s*;)/.source),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'namespace': className.inside.namespace,\n    \t\t\t\t\t'punctuation': /\\./,\n    \t\t\t\t\t'operator': /\\*/,\n    \t\t\t\t\t'class-name': /\\w+/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/(\\bimport\\s+static\\s+)/.source + classNamePrefix + /(?:\\w+|\\*)(?=\\s*;)/.source),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\talias: 'static',\n    \t\t\t\tinside: {\n    \t\t\t\t\t'namespace': className.inside.namespace,\n    \t\t\t\t\t'static': /\\b\\w+$/,\n    \t\t\t\t\t'punctuation': /\\./,\n    \t\t\t\t\t'operator': /\\*/,\n    \t\t\t\t\t'class-name': /\\w+/\n    \t\t\t\t}\n    \t\t\t}\n    \t\t],\n    \t\t'namespace': {\n    \t\t\tpattern: RegExp(\n    \t\t\t\t/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/\n    \t\t\t\t\t.source.replace(/<keyword>/g, function () { return keywords.source; })),\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'punctuation': /\\./,\n    \t\t\t}\n    \t\t}\n    \t});\n    }(Prism));\n\n    Prism.languages.javascript = Prism.languages.extend('clike', {\n    \t'class-name': [\n    \t\tPrism.languages.clike['class-name'],\n    \t\t{\n    \t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n    \t\t\tlookbehind: true\n    \t\t}\n    \t],\n    \t'keyword': [\n    \t\t{\n    \t\t\tpattern: /((?:^|\\})\\s*)catch\\b/,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t],\n    \t// Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n    \t'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n    \t'number': {\n    \t\tpattern: RegExp(\n    \t\t\t/(^|[^\\w$])/.source +\n    \t\t\t'(?:' +\n    \t\t\t(\n    \t\t\t\t// constant\n    \t\t\t\t/NaN|Infinity/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// binary integer\n    \t\t\t\t/0[bB][01]+(?:_[01]+)*n?/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// octal integer\n    \t\t\t\t/0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// hexadecimal integer\n    \t\t\t\t/0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// decimal bigint\n    \t\t\t\t/\\d+(?:_\\d+)*n/.source +\n    \t\t\t\t'|' +\n    \t\t\t\t// decimal number (integer or float) but no bigint\n    \t\t\t\t/(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source\n    \t\t\t) +\n    \t\t\t')' +\n    \t\t\t/(?![\\w$])/.source\n    \t\t),\n    \t\tlookbehind: true\n    \t},\n    \t'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n    });\n\n    Prism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n\n    Prism.languages.insertBefore('javascript', 'keyword', {\n    \t'regex': {\n    \t\tpattern: RegExp(\n    \t\t\t// lookbehind\n    \t\t\t// eslint-disable-next-line regexp/no-dupe-characters-character-class\n    \t\t\t/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source +\n    \t\t\t// Regex pattern:\n    \t\t\t// There are 2 regex patterns here. The RegExp set notation proposal added support for nested character\n    \t\t\t// classes if the `v` flag is present. Unfortunately, nested CCs are both context-free and incompatible\n    \t\t\t// with the only syntax, so we have to define 2 different regex patterns.\n    \t\t\t/\\//.source +\n    \t\t\t'(?:' +\n    \t\t\t/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source +\n    \t\t\t'|' +\n    \t\t\t// `v` flag syntax. This supports 3 levels of nested character classes.\n    \t\t\t/(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source +\n    \t\t\t')' +\n    \t\t\t// lookahead\n    \t\t\t/(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source\n    \t\t),\n    \t\tlookbehind: true,\n    \t\tgreedy: true,\n    \t\tinside: {\n    \t\t\t'regex-source': {\n    \t\t\t\tpattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\talias: 'language-regex',\n    \t\t\t\tinside: Prism.languages.regex\n    \t\t\t},\n    \t\t\t'regex-delimiter': /^\\/|\\/$/,\n    \t\t\t'regex-flags': /^[a-z]+$/,\n    \t\t}\n    \t},\n    \t// This must be declared before keyword because we use \"function\" inside the look-forward\n    \t'function-variable': {\n    \t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n    \t\talias: 'function'\n    \t},\n    \t'parameter': [\n    \t\t{\n    \t\t\tpattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: Prism.languages.javascript\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: Prism.languages.javascript\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: Prism.languages.javascript\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: Prism.languages.javascript\n    \t\t}\n    \t],\n    \t'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n    });\n\n    Prism.languages.insertBefore('javascript', 'string', {\n    \t'hashbang': {\n    \t\tpattern: /^#!.*/,\n    \t\tgreedy: true,\n    \t\talias: 'comment'\n    \t},\n    \t'template-string': {\n    \t\tpattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n    \t\tgreedy: true,\n    \t\tinside: {\n    \t\t\t'template-punctuation': {\n    \t\t\t\tpattern: /^`|`$/,\n    \t\t\t\talias: 'string'\n    \t\t\t},\n    \t\t\t'interpolation': {\n    \t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation-punctuation': {\n    \t\t\t\t\t\tpattern: /^\\$\\{|\\}$/,\n    \t\t\t\t\t\talias: 'punctuation'\n    \t\t\t\t\t},\n    \t\t\t\t\trest: Prism.languages.javascript\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'string': /[\\s\\S]+/\n    \t\t}\n    \t},\n    \t'string-property': {\n    \t\tpattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n    \t\tlookbehind: true,\n    \t\tgreedy: true,\n    \t\talias: 'property'\n    \t}\n    });\n\n    Prism.languages.insertBefore('javascript', 'operator', {\n    \t'literal-property': {\n    \t\tpattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n    \t\tlookbehind: true,\n    \t\talias: 'property'\n    \t},\n    });\n\n    if (Prism.languages.markup) {\n    \tPrism.languages.markup.tag.addInlined('script', 'javascript');\n\n    \t// add attribute support for all DOM events.\n    \t// https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n    \tPrism.languages.markup.tag.addAttribute(\n    \t\t/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\n    \t\t'javascript'\n    \t);\n    }\n\n    Prism.languages.js = Prism.languages.javascript;\n\n    Prism.languages.markup = {\n    \t'comment': {\n    \t\tpattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n    \t\tgreedy: true\n    \t},\n    \t'prolog': {\n    \t\tpattern: /<\\?[\\s\\S]+?\\?>/,\n    \t\tgreedy: true\n    \t},\n    \t'doctype': {\n    \t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n    \t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n    \t\tgreedy: true,\n    \t\tinside: {\n    \t\t\t'internal-subset': {\n    \t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: null // see below\n    \t\t\t},\n    \t\t\t'string': {\n    \t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t'punctuation': /^<!|>$|[[\\]]/,\n    \t\t\t'doctype-tag': /^DOCTYPE/i,\n    \t\t\t'name': /[^\\s<>'\"]+/\n    \t\t}\n    \t},\n    \t'cdata': {\n    \t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n    \t\tgreedy: true\n    \t},\n    \t'tag': {\n    \t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n    \t\tgreedy: true,\n    \t\tinside: {\n    \t\t\t'tag': {\n    \t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /^<\\/?/,\n    \t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'special-attr': [],\n    \t\t\t'attr-value': {\n    \t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': [\n    \t\t\t\t\t\t{\n    \t\t\t\t\t\t\tpattern: /^=/,\n    \t\t\t\t\t\t\talias: 'attr-equals'\n    \t\t\t\t\t\t},\n    \t\t\t\t\t\t{\n    \t\t\t\t\t\t\tpattern: /^(\\s*)[\"']|[\"']$/,\n    \t\t\t\t\t\t\tlookbehind: true\n    \t\t\t\t\t\t}\n    \t\t\t\t\t]\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'punctuation': /\\/?>/,\n    \t\t\t'attr-name': {\n    \t\t\t\tpattern: /[^\\s>\\/]+/,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n    \t\t\t\t}\n    \t\t\t}\n\n    \t\t}\n    \t},\n    \t'entity': [\n    \t\t{\n    \t\t\tpattern: /&[\\da-z]{1,8};/i,\n    \t\t\talias: 'named-entity'\n    \t\t},\n    \t\t/&#x?[\\da-f]{1,8};/i\n    \t]\n    };\n\n    Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n    \tPrism.languages.markup['entity'];\n    Prism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n    // Plugin to make entity title show the real entity, idea by Roman Komarov\n    Prism.hooks.add('wrap', function (env) {\n\n    \tif (env.type === 'entity') {\n    \t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n    \t}\n    });\n\n    Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    \t/**\n    \t * Adds an inlined language to markup.\n    \t *\n    \t * An example of an inlined language is CSS with `<style>` tags.\n    \t *\n    \t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n    \t * case insensitive.\n    \t * @param {string} lang The language key.\n    \t * @example\n    \t * addInlined('style', 'css');\n    \t */\n    \tvalue: function addInlined(tagName, lang) {\n    \t\tvar includedCdataInside = {};\n    \t\tincludedCdataInside['language-' + lang] = {\n    \t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: Prism.languages[lang]\n    \t\t};\n    \t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n    \t\tvar inside = {\n    \t\t\t'included-cdata': {\n    \t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n    \t\t\t\tinside: includedCdataInside\n    \t\t\t}\n    \t\t};\n    \t\tinside['language-' + lang] = {\n    \t\t\tpattern: /[\\s\\S]+/,\n    \t\t\tinside: Prism.languages[lang]\n    \t\t};\n\n    \t\tvar def = {};\n    \t\tdef[tagName] = {\n    \t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n    \t\t\tlookbehind: true,\n    \t\t\tgreedy: true,\n    \t\t\tinside: inside\n    \t\t};\n\n    \t\tPrism.languages.insertBefore('markup', 'cdata', def);\n    \t}\n    });\n    Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    \t/**\n    \t * Adds an pattern to highlight languages embedded in HTML attributes.\n    \t *\n    \t * An example of an inlined language is CSS with `style` attributes.\n    \t *\n    \t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n    \t * case insensitive.\n    \t * @param {string} lang The language key.\n    \t * @example\n    \t * addAttribute('style', 'css');\n    \t */\n    \tvalue: function (attrName, lang) {\n    \t\tPrism.languages.markup.tag.inside['special-attr'].push({\n    \t\t\tpattern: RegExp(\n    \t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n    \t\t\t\t'i'\n    \t\t\t),\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'attr-name': /^[^\\s=]+/,\n    \t\t\t\t'attr-value': {\n    \t\t\t\t\tpattern: /=[\\s\\S]+/,\n    \t\t\t\t\tinside: {\n    \t\t\t\t\t\t'value': {\n    \t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n    \t\t\t\t\t\t\tlookbehind: true,\n    \t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n    \t\t\t\t\t\t\tinside: Prism.languages[lang]\n    \t\t\t\t\t\t},\n    \t\t\t\t\t\t'punctuation': [\n    \t\t\t\t\t\t\t{\n    \t\t\t\t\t\t\t\tpattern: /^=/,\n    \t\t\t\t\t\t\t\talias: 'attr-equals'\n    \t\t\t\t\t\t\t},\n    \t\t\t\t\t\t\t/\"|'/\n    \t\t\t\t\t\t]\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t}\n    \t\t});\n    \t}\n    });\n\n    Prism.languages.html = Prism.languages.markup;\n    Prism.languages.mathml = Prism.languages.markup;\n    Prism.languages.svg = Prism.languages.markup;\n\n    Prism.languages.xml = Prism.languages.extend('markup', {});\n    Prism.languages.ssml = Prism.languages.xml;\n    Prism.languages.atom = Prism.languages.xml;\n    Prism.languages.rss = Prism.languages.xml;\n\n    /**\n     * Original by Aaron Harun: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n     * Modified by Miles Johnson: http://milesj.me\n     * Rewritten by Tom Pavelec\n     *\n     * Supports PHP 5.3 - 8.0\n     */\n    (function (Prism) {\n    \tvar comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n    \tvar constant = [\n    \t\t{\n    \t\t\tpattern: /\\b(?:false|true)\\b/i,\n    \t\t\talias: 'boolean'\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n    \t\t\tgreedy: true,\n    \t\t\tlookbehind: true,\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n    \t\t\tgreedy: true,\n    \t\t\tlookbehind: true,\n    \t\t},\n    \t\t/\\b(?:null)\\b/i,\n    \t\t/\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/,\n    \t];\n    \tvar number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n    \tvar operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n    \tvar punctuation = /[{}\\[\\](),:;]/;\n\n    \tPrism.languages.php = {\n    \t\t'delimiter': {\n    \t\t\tpattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n    \t\t\talias: 'important'\n    \t\t},\n    \t\t'comment': comment,\n    \t\t'variable': /\\$+(?:\\w+\\b|(?=\\{))/,\n    \t\t'package': {\n    \t\t\tpattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'punctuation': /\\\\/\n    \t\t\t}\n    \t\t},\n    \t\t'class-name-definition': {\n    \t\t\tpattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'class-name'\n    \t\t},\n    \t\t'function-definition': {\n    \t\t\tpattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n    \t\t\tlookbehind: true,\n    \t\t\talias: 'function'\n    \t\t},\n    \t\t'keyword': [\n    \t\t\t{\n    \t\t\t\tpattern: /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n    \t\t\t\talias: 'type-casting',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n    \t\t\t\talias: 'type-hint',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|never|object|self|static|string|void)\\b/i,\n    \t\t\t\talias: 'return-type',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n    \t\t\t\talias: 'type-declaration',\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n    \t\t\t\talias: 'type-declaration',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n    \t\t\t\talias: 'static-context',\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t// yield from\n    \t\t\t\tpattern: /(\\byield\\s+)from\\b/i,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t// `class` is always a keyword unlike other keywords\n    \t\t\t/\\bclass\\b/i,\n    \t\t\t{\n    \t\t\t\t// https://www.php.net/manual/en/reserved.keywords.php\n    \t\t\t\t//\n    \t\t\t\t// keywords cannot be preceded by \"->\"\n    \t\t\t\t// the complex lookbehind means `(?<!(?:->|::)\\s*)`\n    \t\t\t\tpattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n    \t\t\t\tlookbehind: true\n    \t\t\t}\n    \t\t],\n    \t\t'argument-name': {\n    \t\t\tpattern: /([(,]\\s*)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'class-name': [\n    \t\t\t{\n    \t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n    \t\t\t\talias: 'class-name-fully-qualified',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n    \t\t\t\talias: 'class-name-fully-qualified',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n    \t\t\t\talias: 'class-name-fully-qualified',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n    \t\t\t\talias: 'type-declaration',\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n    \t\t\t\talias: ['class-name-fully-qualified', 'type-declaration'],\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n    \t\t\t\talias: 'static-context',\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n    \t\t\t\talias: ['class-name-fully-qualified', 'static-context'],\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n    \t\t\t\talias: 'type-hint',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n    \t\t\t\talias: ['class-name-fully-qualified', 'type-hint'],\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n    \t\t\t\talias: 'return-type',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n    \t\t\t\talias: ['class-name-fully-qualified', 'return-type'],\n    \t\t\t\tgreedy: true,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t}\n    \t\t\t}\n    \t\t],\n    \t\t'constant': constant,\n    \t\t'function': {\n    \t\t\tpattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'punctuation': /\\\\/\n    \t\t\t}\n    \t\t},\n    \t\t'property': {\n    \t\t\tpattern: /(->\\s*)\\w+/,\n    \t\t\tlookbehind: true\n    \t\t},\n    \t\t'number': number,\n    \t\t'operator': operator,\n    \t\t'punctuation': punctuation\n    \t};\n\n    \tvar string_interpolation = {\n    \t\tpattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n    \t\tlookbehind: true,\n    \t\tinside: Prism.languages.php\n    \t};\n\n    \tvar string = [\n    \t\t{\n    \t\t\tpattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n    \t\t\talias: 'nowdoc-string',\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'delimiter': {\n    \t\t\t\t\tpattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n    \t\t\t\t\talias: 'symbol',\n    \t\t\t\t\tinside: {\n    \t\t\t\t\t\t'punctuation': /^<<<'?|[';]$/\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n    \t\t\talias: 'heredoc-string',\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'delimiter': {\n    \t\t\t\t\tpattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n    \t\t\t\t\talias: 'symbol',\n    \t\t\t\t\tinside: {\n    \t\t\t\t\t\t'punctuation': /^<<<\"?|[\";]$/\n    \t\t\t\t\t}\n    \t\t\t\t},\n    \t\t\t\t'interpolation': string_interpolation\n    \t\t\t}\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n    \t\t\talias: 'backtick-quoted-string',\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n    \t\t\talias: 'single-quoted-string',\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t{\n    \t\t\tpattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n    \t\t\talias: 'double-quoted-string',\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'interpolation': string_interpolation\n    \t\t\t}\n    \t\t}\n    \t];\n\n    \tPrism.languages.insertBefore('php', 'variable', {\n    \t\t'string': string,\n    \t\t'attribute': {\n    \t\t\tpattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n    \t\t\tgreedy: true,\n    \t\t\tinside: {\n    \t\t\t\t'attribute-content': {\n    \t\t\t\t\tpattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n    \t\t\t\t\tlookbehind: true,\n    \t\t\t\t\t// inside can appear subset of php\n    \t\t\t\t\tinside: {\n    \t\t\t\t\t\t'comment': comment,\n    \t\t\t\t\t\t'string': string,\n    \t\t\t\t\t\t'attribute-class-name': [\n    \t\t\t\t\t\t\t{\n    \t\t\t\t\t\t\t\tpattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n    \t\t\t\t\t\t\t\talias: 'class-name',\n    \t\t\t\t\t\t\t\tgreedy: true,\n    \t\t\t\t\t\t\t\tlookbehind: true\n    \t\t\t\t\t\t\t},\n    \t\t\t\t\t\t\t{\n    \t\t\t\t\t\t\t\tpattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n    \t\t\t\t\t\t\t\talias: [\n    \t\t\t\t\t\t\t\t\t'class-name',\n    \t\t\t\t\t\t\t\t\t'class-name-fully-qualified'\n    \t\t\t\t\t\t\t\t],\n    \t\t\t\t\t\t\t\tgreedy: true,\n    \t\t\t\t\t\t\t\tlookbehind: true,\n    \t\t\t\t\t\t\t\tinside: {\n    \t\t\t\t\t\t\t\t\t'punctuation': /\\\\/\n    \t\t\t\t\t\t\t\t}\n    \t\t\t\t\t\t\t}\n    \t\t\t\t\t\t],\n    \t\t\t\t\t\t'constant': constant,\n    \t\t\t\t\t\t'number': number,\n    \t\t\t\t\t\t'operator': operator,\n    \t\t\t\t\t\t'punctuation': punctuation\n    \t\t\t\t\t}\n    \t\t\t\t},\n    \t\t\t\t'delimiter': {\n    \t\t\t\t\tpattern: /^#\\[|\\]$/,\n    \t\t\t\t\talias: 'punctuation'\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n    \t});\n\n    \tPrism.hooks.add('before-tokenize', function (env) {\n    \t\tif (!/<\\?/.test(env.code)) {\n    \t\t\treturn;\n    \t\t}\n\n    \t\tvar phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g;\n    \t\tPrism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n    \t});\n\n    \tPrism.hooks.add('after-tokenize', function (env) {\n    \t\tPrism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n    \t});\n\n    }(Prism));\n\n    Prism.languages.python = {\n    \t'comment': {\n    \t\tpattern: /(^|[^\\\\])#.*/,\n    \t\tlookbehind: true,\n    \t\tgreedy: true\n    \t},\n    \t'string-interpolation': {\n    \t\tpattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n    \t\tgreedy: true,\n    \t\tinside: {\n    \t\t\t'interpolation': {\n    \t\t\t\t// \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n    \t\t\t\tpattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'format-spec': {\n    \t\t\t\t\t\tpattern: /(:)[^:(){}]+(?=\\}$)/,\n    \t\t\t\t\t\tlookbehind: true\n    \t\t\t\t\t},\n    \t\t\t\t\t'conversion-option': {\n    \t\t\t\t\t\tpattern: /![sra](?=[:}]$)/,\n    \t\t\t\t\t\talias: 'punctuation'\n    \t\t\t\t\t},\n    \t\t\t\t\trest: null\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t'string': /[\\s\\S]+/\n    \t\t}\n    \t},\n    \t'triple-quoted-string': {\n    \t\tpattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n    \t\tgreedy: true,\n    \t\talias: 'string'\n    \t},\n    \t'string': {\n    \t\tpattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n    \t\tgreedy: true\n    \t},\n    \t'function': {\n    \t\tpattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n    \t\tlookbehind: true\n    \t},\n    \t'class-name': {\n    \t\tpattern: /(\\bclass\\s+)\\w+/i,\n    \t\tlookbehind: true\n    \t},\n    \t'decorator': {\n    \t\tpattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n    \t\tlookbehind: true,\n    \t\talias: ['annotation', 'punctuation'],\n    \t\tinside: {\n    \t\t\t'punctuation': /\\./\n    \t\t}\n    \t},\n    \t'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n    \t'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n    \t'boolean': /\\b(?:False|None|True)\\b/,\n    \t'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n    \t'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    \t'punctuation': /[{}[\\];(),.:]/\n    };\n\n    Prism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n\n    Prism.languages.py = Prism.languages.python;\n\n    /**\n     * Original by Samuel Flores\n     *\n     * Adds the following new token classes:\n     *     constant, builtin, variable, symbol, regex\n     */\n    (function (Prism) {\n    \tPrism.languages.ruby = Prism.languages.extend('clike', {\n    \t\t'comment': {\n    \t\t\tpattern: /#.*|^=begin\\s[\\s\\S]*?^=end/m,\n    \t\t\tgreedy: true\n    \t\t},\n    \t\t'class-name': {\n    \t\t\tpattern: /(\\b(?:class|module)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+|\\b[A-Z_]\\w*(?=\\s*\\.\\s*new\\b)/,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'punctuation': /[.\\\\]/\n    \t\t\t}\n    \t\t},\n    \t\t'keyword': /\\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\\b/,\n    \t\t'operator': /\\.{2,3}|&\\.|===|<?=>|[!=]?~|(?:&&|\\|\\||<<|>>|\\*\\*|[+\\-*/%<>!^&|=])=?|[?:]/,\n    \t\t'punctuation': /[(){}[\\].,;]/,\n    \t});\n\n    \tPrism.languages.insertBefore('ruby', 'operator', {\n    \t\t'double-colon': {\n    \t\t\tpattern: /::/,\n    \t\t\talias: 'punctuation'\n    \t\t},\n    \t});\n\n    \tvar interpolation = {\n    \t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)#\\{(?:[^{}]|\\{[^{}]*\\})*\\}/,\n    \t\tlookbehind: true,\n    \t\tinside: {\n    \t\t\t'content': {\n    \t\t\t\tpattern: /^(#\\{)[\\s\\S]+(?=\\}$)/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tinside: Prism.languages.ruby\n    \t\t\t},\n    \t\t\t'delimiter': {\n    \t\t\t\tpattern: /^#\\{|\\}$/,\n    \t\t\t\talias: 'punctuation'\n    \t\t\t}\n    \t\t}\n    \t};\n\n    \tdelete Prism.languages.ruby.function;\n\n    \tvar percentExpression = '(?:' + [\n    \t\t/([^a-zA-Z0-9\\s{(\\[<=])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n    \t\t/\\((?:[^()\\\\]|\\\\[\\s\\S]|\\((?:[^()\\\\]|\\\\[\\s\\S])*\\))*\\)/.source,\n    \t\t/\\{(?:[^{}\\\\]|\\\\[\\s\\S]|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\})*\\}/.source,\n    \t\t/\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S]|\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S])*\\])*\\]/.source,\n    \t\t/<(?:[^<>\\\\]|\\\\[\\s\\S]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)*>/.source\n    \t].join('|') + ')';\n\n    \tvar symbolName = /(?:\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|(?:\\b[a-zA-Z_]\\w*|[^\\s\\0-\\x7F]+)[?!]?|\\$.)/.source;\n\n    \tPrism.languages.insertBefore('ruby', 'keyword', {\n    \t\t'regex-literal': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/%r/.source + percentExpression + /[egimnosux]{0,6}/.source),\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'regex': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(^|[^/])\\/(?!\\/)(?:\\[[^\\r\\n\\]]+\\]|\\\\.|[^[/\\\\\\r\\n])+\\/[egimnosux]{0,6}(?=\\s*(?:$|[\\r\\n,.;})#]))/,\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'regex': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t}\n    \t\t],\n    \t\t'variable': /[@$]+[a-zA-Z_]\\w*(?:[?!]|\\b)/,\n    \t\t'symbol': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/(^|[^:]):/.source + symbolName),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/([\\r\\n{(,][ \\t]*)/.source + symbolName + /(?=:(?!:))/.source),\n    \t\t\t\tlookbehind: true,\n    \t\t\t\tgreedy: true\n    \t\t\t},\n    \t\t],\n    \t\t'method-definition': {\n    \t\t\tpattern: /(\\bdef\\s+)\\w+(?:\\s*\\.\\s*\\w+)?/,\n    \t\t\tlookbehind: true,\n    \t\t\tinside: {\n    \t\t\t\t'function': /\\b\\w+$/,\n    \t\t\t\t'keyword': /^self\\b/,\n    \t\t\t\t'class-name': /^\\w+/,\n    \t\t\t\t'punctuation': /\\./\n    \t\t\t}\n    \t\t}\n    \t});\n\n    \tPrism.languages.insertBefore('ruby', 'string', {\n    \t\t'string-literal': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'string': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /(\"|')(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\#\\r\\n])*\\1/,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'string': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /<<[-~]?([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n    \t\t\t\talias: 'heredoc-string',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'delimiter': {\n    \t\t\t\t\t\tpattern: /^<<[-~]?[a-z_]\\w*|\\b[a-z_]\\w*$/i,\n    \t\t\t\t\t\tinside: {\n    \t\t\t\t\t\t\t'symbol': /\\b\\w+/,\n    \t\t\t\t\t\t\t'punctuation': /^<<[-~]?/\n    \t\t\t\t\t\t}\n    \t\t\t\t\t},\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'string': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /<<[-~]?'([a-z_]\\w*)'[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n    \t\t\t\talias: 'heredoc-string',\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'delimiter': {\n    \t\t\t\t\t\tpattern: /^<<[-~]?'[a-z_]\\w*'|\\b[a-z_]\\w*$/i,\n    \t\t\t\t\t\tinside: {\n    \t\t\t\t\t\t\t'symbol': /\\b\\w+/,\n    \t\t\t\t\t\t\t'punctuation': /^<<[-~]?'|'$/,\n    \t\t\t\t\t\t}\n    \t\t\t\t\t},\n    \t\t\t\t\t'string': /[\\s\\S]+/\n    \t\t\t\t}\n    \t\t\t}\n    \t\t],\n    \t\t'command-literal': [\n    \t\t\t{\n    \t\t\t\tpattern: RegExp(/%x/.source + percentExpression),\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'command': {\n    \t\t\t\t\t\tpattern: /[\\s\\S]+/,\n    \t\t\t\t\t\talias: 'string'\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\tpattern: /`(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|[^\\\\`#\\r\\n])*`/,\n    \t\t\t\tgreedy: true,\n    \t\t\t\tinside: {\n    \t\t\t\t\t'interpolation': interpolation,\n    \t\t\t\t\t'command': {\n    \t\t\t\t\t\tpattern: /[\\s\\S]+/,\n    \t\t\t\t\t\talias: 'string'\n    \t\t\t\t\t}\n    \t\t\t\t}\n    \t\t\t}\n    \t\t]\n    \t});\n\n    \tdelete Prism.languages.ruby.string;\n\n    \tPrism.languages.insertBefore('ruby', 'number', {\n    \t\t'builtin': /\\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\\b/,\n    \t\t'constant': /\\b[A-Z][A-Z0-9_]*(?:[?!]|\\b)/\n    \t});\n\n    \tPrism.languages.rb = Prism.languages.ruby;\n    }(Prism));\n\n    // restore the original Prism reference\n    window.Prism = oldprism;\n    return Prism;\n    }(undefined, undefined);\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('codesample_languages', {\n            processor: 'object[]'\n        });\n        registerOption('codesample_global_prismjs', {\n            processor: 'boolean',\n            default: false\n        });\n    };\n    const getLanguages$1 = option('codesample_languages');\n    const useGlobalPrismJS = option('codesample_global_prismjs');\n\n    const get = (editor) => Global.Prism && useGlobalPrismJS(editor) ? Global.Prism : prismjs;\n\n    const isCodeSample = (elm) => {\n        return isNonNullable(elm) && elm.nodeName === 'PRE' && elm.className.indexOf('language-') !== -1;\n    };\n\n    const getSelectedCodeSample = (editor) => {\n        const node = editor.selection ? editor.selection.getNode() : null;\n        return isCodeSample(node) ? Optional.some(node) : Optional.none();\n    };\n    const insertCodeSample = (editor, language, code) => {\n        const dom = editor.dom;\n        editor.undoManager.transact(() => {\n            const node = getSelectedCodeSample(editor);\n            code = global$1.DOM.encode(code);\n            return node.fold(() => {\n                editor.insertContent('<pre id=\"__new\" class=\"language-' + language + '\">' + code + '</pre>');\n                const newPre = dom.select('#__new')[0];\n                dom.setAttrib(newPre, 'id', null);\n                editor.selection.select(newPre);\n            }, (n) => {\n                dom.setAttrib(n, 'class', 'language-' + language);\n                n.innerHTML = code;\n                get(editor).highlightElement(n);\n                editor.selection.select(n);\n            });\n        });\n    };\n    const getCurrentCode = (editor) => {\n        const node = getSelectedCodeSample(editor);\n        return node.bind((n) => Optional.from(n.textContent)).getOr('');\n    };\n\n    const getLanguages = (editor) => {\n        const defaultLanguages = [\n            { text: 'HTML/XML', value: 'markup' },\n            { text: 'JavaScript', value: 'javascript' },\n            { text: 'CSS', value: 'css' },\n            { text: 'PHP', value: 'php' },\n            { text: 'Ruby', value: 'ruby' },\n            { text: 'Python', value: 'python' },\n            { text: 'Java', value: 'java' },\n            { text: 'C', value: 'c' },\n            { text: 'C#', value: 'csharp' },\n            { text: 'C++', value: 'cpp' }\n        ];\n        const customLanguages = getLanguages$1(editor);\n        return customLanguages ? customLanguages : defaultLanguages;\n    };\n    const getCurrentLanguage = (editor, fallback) => {\n        const node = getSelectedCodeSample(editor);\n        return node.fold(() => fallback, (n) => {\n            const matches = n.className.match(/language-(\\w+)/);\n            return matches ? matches[1] : fallback;\n        });\n    };\n\n    const open = (editor) => {\n        const languages = getLanguages(editor);\n        const defaultLanguage = head(languages).fold(constant(''), (l) => l.value);\n        const currentLanguage = getCurrentLanguage(editor, defaultLanguage);\n        const currentCode = getCurrentCode(editor);\n        editor.windowManager.open({\n            title: 'Insert/Edit Code Sample',\n            size: 'large',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        type: 'listbox',\n                        name: 'language',\n                        label: 'Language',\n                        items: languages\n                    },\n                    {\n                        type: 'textarea',\n                        name: 'code',\n                        label: 'Code view'\n                    }\n                ]\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: {\n                language: currentLanguage,\n                code: currentCode\n            },\n            onSubmit: (api) => {\n                const data = api.getData();\n                insertCodeSample(editor, data.language, data.code);\n                api.close();\n            }\n        });\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('codesample', () => {\n            const node = editor.selection.getNode();\n            if (editor.selection.isCollapsed() || isCodeSample(node)) {\n                open(editor);\n            }\n            else {\n                editor.formatter.toggle('code');\n            }\n        });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const setup = (editor) => {\n        editor.on('PreProcess', (e) => {\n            const dom = editor.dom;\n            const pres = dom.select('pre[contenteditable=false]', e.node);\n            global.each(global.grep(pres, isCodeSample), (elm) => {\n                const code = elm.textContent;\n                dom.setAttrib(elm, 'class', trim(dom.getAttrib(elm, 'class')));\n                dom.setAttrib(elm, 'contentEditable', null);\n                dom.setAttrib(elm, 'data-mce-highlighted', null);\n                // Empty the pre element\n                let child;\n                while ((child = elm.firstChild)) {\n                    elm.removeChild(child);\n                }\n                const codeElm = dom.add(elm, 'code');\n                // Needs to be textContent since innerText produces BR:s\n                codeElm.textContent = code;\n            });\n        });\n        editor.on('SetContent', () => {\n            const dom = editor.dom;\n            const unprocessedCodeSamples = global.grep(dom.select('pre'), (elm) => {\n                return isCodeSample(elm) && dom.getAttrib(elm, 'data-mce-highlighted') !== 'true';\n            });\n            if (unprocessedCodeSamples.length) {\n                editor.undoManager.transact(() => {\n                    global.each(unprocessedCodeSamples, (elm) => {\n                        var _a;\n                        global.each(dom.select('br', elm), (elm) => {\n                            dom.replace(editor.getDoc().createTextNode('\\n'), elm);\n                        });\n                        elm.innerHTML = dom.encode((_a = elm.textContent) !== null && _a !== void 0 ? _a : '');\n                        get(editor).highlightElement(elm);\n                        dom.setAttrib(elm, 'data-mce-highlighted', true);\n                        elm.className = trim(elm.className);\n                    });\n                });\n            }\n        });\n        editor.on('PreInit', () => {\n            editor.parser.addNodeFilter('pre', (nodes) => {\n                var _a;\n                for (let i = 0, l = nodes.length; i < l; i++) {\n                    const node = nodes[i];\n                    const isCodeSample = ((_a = node.attr('class')) !== null && _a !== void 0 ? _a : '').indexOf('language-') !== -1;\n                    if (isCodeSample) {\n                        node.attr('contenteditable', 'false');\n                        node.attr('data-mce-highlighted', 'false');\n                    }\n                }\n            });\n        });\n    };\n\n    const onSetupEditable = (editor, onChanged = noop) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n            onChanged(api);\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const isCodeSampleSelection = (editor) => {\n        const node = editor.selection.getStart();\n        return editor.dom.is(node, 'pre[class*=\"language-\"]');\n    };\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('codesample');\n        editor.ui.registry.addToggleButton('codesample', {\n            icon: 'code-sample',\n            tooltip: 'Insert/edit code sample',\n            onAction,\n            onSetup: onSetupEditable(editor, (api) => {\n                api.setActive(isCodeSampleSelection(editor));\n            })\n        });\n        editor.ui.registry.addMenuItem('codesample', {\n            text: 'Code sample...',\n            icon: 'code-sample',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    var Plugin = () => {\n        global$2.add('codesample', (editor) => {\n            register$2(editor);\n            setup(editor);\n            register(editor);\n            register$1(editor);\n            editor.on('dblclick', (ev) => {\n                if (isCodeSample(ev.target)) {\n                    open(editor);\n                }\n            });\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IAEzC,MAAM,OAAO,KAAQ;IACrB,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IAEA;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,MAAM,QAAQ,CAAC,IAAI,IAAM,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI;IACvF,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI;IAE/B,sFAAsF;IACtF,8DAA8D;IAC9D,MAAM,SAAS,uCAAgC,SAAS;IAExD,MAAM,QAAQ,CAAC,IAAM,CAAC,IAAM,EAAE,OAAO,CAAC,GAAG;IACzC,4CAA4C,GAC5C,MAAM,OAAO,MAAM;IAEnB,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,UAAU,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO;QAChD,oDAAoD;QACpD,MAAM,WAAW,OAAO,KAAK;QAC7B,OAAO,KAAK,GAAG;YAAE,QAAQ;QAAK;QAC9B,gCAAgC;QAEhC,IAAI,QAAQ,uCACT,OAAS,gBAAgB;WACzB;QAMH;;;;;;;KAOC,GACD,IAAI,QAAS,SAAU,KAAK;YAE3B,sBAAsB;YACtB,IAAI,OAAO;YACX,IAAI,WAAW;YAEf,mCAAmC;YACnC,IAAI,mBAAmB,CAAC;YAGxB,IAAI,IAAI;gBACP;;;;;;;;;;;;;;;;;;;;OAoBC,GACD,QAAQ,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM;gBACzC;;;;;;;;;;;;;;;;;;;;OAoBC,GACD,6BAA6B,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,2BAA2B;gBAEnF;;;;;;;;OAQC,GACD,MAAM;oBACL,QAAQ,SAAS,OAAO,MAAM;wBAC7B,IAAI,kBAAkB,OAAO;4BAC5B,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;wBACnE,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;4BACjC,OAAO,OAAO,GAAG,CAAC;wBACnB,OAAO;4BACN,OAAO,OAAO,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,WAAW;wBAC/E;oBACD;oBAEA;;;;;;;;;;;;;;;QAeC,GACD,MAAM,SAAU,CAAC;wBAChB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;oBACpD;oBAEA;;;;;QAKC,GACD,OAAO,SAAU,GAAG;wBACnB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;4BACjB,OAAO,cAAc,CAAC,KAAK,QAAQ;gCAAE,OAAO,EAAE;4BAAS;wBACxD;wBACA,OAAO,GAAG,CAAC,OAAO;oBACnB;oBAEA;;;;;;;;;QASC,GACD,OAAO,SAAS,UAAU,CAAC,EAAE,OAAO;wBACnC,UAAU,WAAW,CAAC;wBAEtB,IAAI;wBAAO,IAAI;wBACf,OAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;4BACnB,KAAK;gCACJ,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;gCAClB,IAAI,OAAO,CAAC,GAAG,EAAE;oCAChB,OAAO,OAAO,CAAC,GAAG;gCACnB;gCACA,QAA4C,CAAC;gCAC7C,OAAO,CAAC,GAAG,GAAG;gCAEd,IAAK,IAAI,OAAO,EAAG;oCAClB,IAAI,EAAE,cAAc,CAAC,MAAM;wCAC1B,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE;oCAChC;gCACD;gCAEA,OAA2B;4BAE5B,KAAK;gCACJ,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;gCAClB,IAAI,OAAO,CAAC,GAAG,EAAE;oCAChB,OAAO,OAAO,CAAC,GAAG;gCACnB;gCACA,QAAQ,EAAE;gCACV,OAAO,CAAC,GAAG,GAAG;gCAE2B,EAAK,OAAO,CAAC,SAAU,CAAC,EAAE,CAAC;oCACnE,KAAK,CAAC,EAAE,GAAG,UAAU,GAAG;gCACzB;gCAEA,OAA2B;4BAE5B;gCACC,OAAO;wBACT;oBACD;oBAEA;;;;;;;QAOC,GACD,aAAa,SAAU,OAAO;wBAC7B,MAAO,QAAS;4BACf,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,SAAS;4BACnC,IAAI,GAAG;gCACN,OAAO,CAAC,CAAC,EAAE,CAAC,WAAW;4BACxB;4BACA,UAAU,QAAQ,aAAa;wBAChC;wBACA,OAAO;oBACR;oBAEA;;;;;;QAMC,GACD,aAAa,SAAU,OAAO,EAAE,QAAQ;wBACvC,qCAAqC;wBACrC,4CAA4C;wBAC5C,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,OAAO,CAAC,OAAO,MAAM,OAAO;wBAElE,oCAAoC;wBACpC,gEAAgE;wBAChE,QAAQ,SAAS,CAAC,GAAG,CAAC,cAAc;oBACrC;oBAEA;;;;;;QAMC,GACD,eAAe;wBACd,IAAI,OAAO,aAAa,aAAa;4BACpC,OAAO;wBACR;wBACA,IAAI,mBAAmB,YAAY,IAAI,EAAE,kCAAkC,KAAI;4BAC9E,OAA2B,SAAS,aAAa;wBAClD;wBAEA,kBAAkB;wBAClB,8EAA8E;wBAC9E,wCAAwC;wBAExC,IAAI;4BACH,MAAM,IAAI;wBACX,EAAE,OAAO,KAAK;4BACb,yFAAyF;4BACzF,+BAA+B;4BAC/B,EAAE;4BACF,QAAQ;4BACR,+EAA+E;4BAC/E,sEAAsE;4BAEtE,IAAI,MAAM,CAAC,qCAAqC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE;4BACzE,IAAI,KAAK;gCACR,IAAI,UAAU,SAAS,oBAAoB,CAAC;gCAC5C,IAAK,IAAI,KAAK,QAAS;oCACtB,IAAI,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK;wCAC1B,OAAO,OAAO,CAAC,EAAE;oCAClB;gCACD;4BACD;4BACA,OAAO;wBACR;oBACD;oBAEA;;;;;;;;;;;;;;;;;;QAkBC,GACD,UAAU,SAAU,OAAO,EAAE,SAAS,EAAE,iBAAiB;wBACxD,IAAI,KAAK,QAAQ;wBAEjB,MAAO,QAAS;4BACf,IAAI,YAAY,QAAQ,SAAS;4BACjC,IAAI,UAAU,QAAQ,CAAC,YAAY;gCAClC,OAAO;4BACR;4BACA,IAAI,UAAU,QAAQ,CAAC,KAAK;gCAC3B,OAAO;4BACR;4BACA,UAAU,QAAQ,aAAa;wBAChC;wBACA,OAAO,CAAC,CAAC;oBACV;gBACD;gBAEA;;;;;;OAMC,GACD,WAAW;oBACV;;QAEC,GACD,OAAO;oBACP,WAAW;oBACX,MAAM;oBACN,KAAK;oBAEL;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2BC,GACD,QAAQ,SAAU,EAAE,EAAE,KAAK;wBAC1B,IAAI,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,GAAG;wBAEvC,IAAK,IAAI,OAAO,MAAO;4BACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;wBACvB;wBAEA,OAAO;oBACR;oBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA0EC,GACD,cAAc,SAAU,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI;wBACnD,OAAO,QAA4B,EAAE,SAAS;wBAC9C,IAAI,UAAU,IAAI,CAAC,OAAO;wBAC1B,oBAAoB,GACpB,IAAI,MAAM,CAAC;wBAEX,IAAK,IAAI,SAAS,QAAS;4BAC1B,IAAI,QAAQ,cAAc,CAAC,QAAQ;gCAElC,IAAI,SAAS,QAAQ;oCACpB,IAAK,IAAI,YAAY,OAAQ;wCAC5B,IAAI,OAAO,cAAc,CAAC,WAAW;4CACpC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;wCACjC;oCACD;gCACD;gCAEA,4DAA4D;gCAC5D,IAAI,CAAC,OAAO,cAAc,CAAC,QAAQ;oCAClC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;gCAC5B;4BACD;wBACD;wBAEA,IAAI,MAAM,IAAI,CAAC,OAAO;wBACtB,IAAI,CAAC,OAAO,GAAG;wBAEf,kDAAkD;wBAClD,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,SAAU,GAAG,EAAE,KAAK;4BAChD,IAAI,UAAU,OAAO,OAAO,QAAQ;gCACnC,IAAI,CAAC,IAAI,GAAG;4BACb;wBACD;wBAEA,OAAO;oBACR;oBAEA,yDAAyD;oBACzD,KAAK,SAAS,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;wBAC3C,UAAU,WAAW,CAAC;wBAEtB,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK;wBAExB,IAAK,IAAI,KAAK,EAAG;4BAChB,IAAI,EAAE,cAAc,CAAC,IAAI;gCACxB,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ;gCAElC,IAAI,WAAW,CAAC,CAAC,EAAE;gCACnB,IAAI,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC;gCAE/B,IAAI,iBAAiB,YAAY,CAAC,OAAO,CAAC,MAAM,UAAU,EAAE;oCAC3D,OAAO,CAAC,MAAM,UAAU,GAAG;oCAC3B,IAAI,UAAU,UAAU,MAAM;gCAC/B,OAAO,IAAI,iBAAiB,WAAW,CAAC,OAAO,CAAC,MAAM,UAAU,EAAE;oCACjE,OAAO,CAAC,MAAM,UAAU,GAAG;oCAC3B,IAAI,UAAU,UAAU,GAAG;gCAC5B;4BACD;wBACD;oBACD;gBACD;gBAEA,SAAS,CAAC;gBAEV;;;;;;;;;;;OAWC,GACD,cAAc,SAAU,KAAK,EAAE,QAAQ;oBACtC,EAAE,iBAAiB,CAAC,UAAU,OAAO;gBACtC;gBAEA;;;;;;;;;;;;;;OAcC,GACD,mBAAmB,SAAU,SAAS,EAAE,KAAK,EAAE,QAAQ;oBACtD,IAAI,MAAM;wBACT,UAAU;wBACV,WAAW;wBACX,UAAU;oBACX;oBAEA,EAAE,KAAK,CAAC,GAAG,CAAC,uBAAuB;oBAEnC,IAAI,QAAQ,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,QAAQ;oBAEtF,EAAE,KAAK,CAAC,GAAG,CAAC,iCAAiC;oBAE7C,IAAK,IAAI,IAAI,GAAG,SAAU,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAI;wBACxD,EAAE,gBAAgB,CAAC,SAAS,UAAU,MAAM,IAAI,QAAQ;oBACzD;gBACD;gBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BC,GACD,kBAAkB,SAAU,OAAO,EAAE,KAAK,EAAE,QAAQ;oBACnD,gBAAgB;oBAChB,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC;oBAClC,IAAI,UAAU,EAAE,SAAS,CAAC,SAAS;oBAEnC,8CAA8C;oBAC9C,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;oBAE5B,0CAA0C;oBAC1C,IAAI,SAAS,QAAQ,aAAa;oBAClC,IAAI,UAAU,OAAO,QAAQ,CAAC,WAAW,OAAO,OAAO;wBACtD,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ;oBAC5B;oBAEA,IAAI,OAAO,QAAQ,WAAW;oBAE9B,IAAI,MAAM;wBACT,SAAS;wBACT,UAAU;wBACV,SAAS;wBACT,MAAM;oBACP;oBAEA,SAAS,sBAAsB,eAAe;wBAC7C,IAAI,eAAe,GAAG;wBAEtB,EAAE,KAAK,CAAC,GAAG,CAAC,iBAAiB;wBAE7B,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,eAAe;wBAE3C,EAAE,KAAK,CAAC,GAAG,CAAC,mBAAmB;wBAC/B,EAAE,KAAK,CAAC,GAAG,CAAC,YAAY;wBACxB,YAAY,SAAS,IAAI,CAAC,IAAI,OAAO;oBACtC;oBAEA,EAAE,KAAK,CAAC,GAAG,CAAC,uBAAuB;oBAEnC,4CAA4C;oBAC5C,SAAS,IAAI,OAAO,CAAC,aAAa;oBAClC,IAAI,UAAU,OAAO,QAAQ,CAAC,WAAW,OAAO,SAAS,CAAC,OAAO,YAAY,CAAC,aAAa;wBAC1F,OAAO,YAAY,CAAC,YAAY;oBACjC;oBAEA,IAAI,CAAC,IAAI,IAAI,EAAE;wBACd,EAAE,KAAK,CAAC,GAAG,CAAC,YAAY;wBACxB,YAAY,SAAS,IAAI,CAAC,IAAI,OAAO;wBACrC;oBACD;oBAEA,EAAE,KAAK,CAAC,GAAG,CAAC,oBAAoB;oBAEhC,IAAI,CAAC,IAAI,OAAO,EAAE;wBACjB,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI;wBAC5C;oBACD;oBAEA,IAAI,SAAS,MAAM,MAAM,EAAE;wBAC1B,IAAI,SAAS,IAAI,OAAO,EAAE,QAAQ;wBAElC,OAAO,SAAS,GAAG,SAAU,GAAG;4BAC/B,sBAAsB,IAAI,IAAI;wBAC/B;wBAEA,OAAO,WAAW,CAAC,KAAK,SAAS,CAAC;4BACjC,UAAU,IAAI,QAAQ;4BACtB,MAAM,IAAI,IAAI;4BACd,gBAAgB;wBACjB;oBACD,OAAO;wBACN,sBAAsB,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO,EAAE,IAAI,QAAQ;oBACtE;gBACD;gBAEA;;;;;;;;;;;;;;;;;;;OAmBC,GACD,WAAW,SAAU,IAAI,EAAE,OAAO,EAAE,QAAQ;oBAC3C,IAAI,MAAM;wBACT,MAAM;wBACN,SAAS;wBACT,UAAU;oBACX;oBACA,EAAE,KAAK,CAAC,GAAG,CAAC,mBAAmB;oBAC/B,IAAI,CAAC,IAAI,OAAO,EAAE;wBACjB,MAAM,IAAI,MAAM,mBAAmB,IAAI,QAAQ,GAAG;oBACnD;oBACA,IAAI,MAAM,GAAG,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO;oBAC7C,EAAE,KAAK,CAAC,GAAG,CAAC,kBAAkB;oBAC9B,OAAO,MAAM,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,IAAI,QAAQ;gBAC/D;gBAEA;;;;;;;;;;;;;;;;;;;;;;;OAuBC,GACD,UAAU,SAAU,IAAI,EAAE,OAAO;oBAChC,IAAI,OAAO,QAAQ,IAAI;oBACvB,IAAI,MAAM;wBACT,IAAK,IAAI,SAAS,KAAM;4BACvB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;wBAC7B;wBAEA,OAAO,QAAQ,IAAI;oBACpB;oBAEA,IAAI,YAAY,IAAI;oBACpB,SAAS,WAAW,UAAU,IAAI,EAAE;oBAEpC,aAAa,MAAM,WAAW,SAAS,UAAU,IAAI,EAAE;oBAEvD,OAAO,QAAQ;gBAChB;gBAEA;;;;OAIC,GACD,OAAO;oBACN,KAAK,CAAC;oBAEN;;;;;;;;;;;QAWC,GACD,KAAK,SAAU,IAAI,EAAE,QAAQ;wBAC5B,IAAI,QAAQ,EAAE,KAAK,CAAC,GAAG;wBAEvB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;wBAE/B,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;oBAClB;oBAEA;;;;;;;;QAQC,GACD,KAAK,SAAU,IAAI,EAAE,GAAG;wBACvB,IAAI,YAAY,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK;wBAEjC,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM,EAAE;4BACpC;wBACD;wBAEA,IAAK,IAAI,IAAI,GAAG,UAAW,WAAW,SAAS,CAAC,IAAI,EAAI;4BACvD,SAAS;wBACV;oBACD;gBACD;gBAEA,OAAO;YACR;YACA,MAAM,KAAK,GAAG;YAGd,mBAAmB;YACnB,+DAA+D;YAC/D,EAAE;YACF,mEAAmE;YAEnE;;;;;;;;;;MAUC,GACD,SAAS,MAAM,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU;gBAC9C;;;;;;;;OAQC,GACD,IAAI,CAAC,IAAI,GAAG;gBACZ;;;;;;;OAOC,GACD,IAAI,CAAC,OAAO,GAAG;gBACf;;;;;;OAMC,GACD,IAAI,CAAC,KAAK,GAAG;gBACb,sDAAsD;gBACtD,IAAI,CAAC,MAAM,GAAG,CAAC,cAAc,EAAE,EAAE,MAAM,GAAG;YAC3C;YAEA;;;;;;;;;;;;;;MAcC,GAED;;;;;;;;;;;MAWC,GACD,MAAM,SAAS,GAAG,SAAS,UAAU,CAAC,EAAE,QAAQ;gBAC/C,IAAI,OAAO,KAAK,UAAU;oBACzB,OAAO;gBACR;gBACA,IAAI,MAAM,OAAO,CAAC,IAAI;oBACrB,IAAI,IAAI;oBACR,EAAE,OAAO,CAAC,SAAU,CAAC;wBACpB,KAAK,UAAU,GAAG;oBACnB;oBACA,OAAO;gBACR;gBAEA,IAAI,MAAM;oBACT,MAAM,EAAE,IAAI;oBACZ,SAAS,UAAU,EAAE,OAAO,EAAE;oBAC9B,KAAK;oBACL,SAAS;wBAAC;wBAAS,EAAE,IAAI;qBAAC;oBAC1B,YAAY,CAAC;oBACb,UAAU;gBACX;gBAEA,IAAI,UAAU,EAAE,KAAK;gBACrB,IAAI,SAAS;oBACZ,IAAI,MAAM,OAAO,CAAC,UAAU;wBAC3B,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;oBACzC,OAAO;wBACN,IAAI,OAAO,CAAC,IAAI,CAAC;oBAClB;gBACD;gBAEA,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ;gBAEpB,IAAI,aAAa;gBACjB,IAAK,IAAI,QAAQ,IAAI,UAAU,CAAE;oBAChC,cAAc,MAAM,OAAO,OAAO,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,YAAY;gBAC1F;gBAEA,OAAO,MAAM,IAAI,GAAG,GAAG,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM,aAAa,MAAM,IAAI,OAAO,GAAG,OAAO,IAAI,GAAG,GAAG;YACrH;YAEA;;;;;;MAMC,GACD,SAAS,aAAa,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU;gBACnD,QAAQ,SAAS,GAAG;gBACpB,IAAI,QAAQ,QAAQ,IAAI,CAAC;gBACzB,IAAI,SAAS,cAAc,KAAK,CAAC,EAAE,EAAE;oBACpC,4EAA4E;oBAC5E,IAAI,mBAAmB,KAAK,CAAC,EAAE,CAAC,MAAM;oBACtC,MAAM,KAAK,IAAI;oBACf,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;gBAC3B;gBACA,OAAO;YACR;YAEA;;;;;;;;;;;;;MAaC,GACD,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO;gBAC3E,IAAK,IAAI,SAAS,QAAS;oBAC1B,IAAI,CAAC,QAAQ,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE;wBACtD;oBACD;oBAEA,IAAI,WAAW,OAAO,CAAC,MAAM;oBAC7B,WAAW,MAAM,OAAO,CAAC,YAAY,WAAW;wBAAC;qBAAS;oBAE1D,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EAAG;wBACzC,IAAI,WAAW,QAAQ,KAAK,IAAI,QAAQ,MAAM,GAAG;4BAChD;wBACD;wBAEA,IAAI,aAAa,QAAQ,CAAC,EAAE;wBAC5B,IAAI,SAAS,WAAW,MAAM;wBAC9B,IAAI,aAAa,CAAC,CAAC,WAAW,UAAU;wBACxC,IAAI,SAAS,CAAC,CAAC,WAAW,MAAM;wBAChC,IAAI,QAAQ,WAAW,KAAK;wBAE5B,IAAI,UAAU,CAAC,WAAW,OAAO,CAAC,MAAM,EAAE;4BACzC,gDAAgD;4BAChD,IAAI,QAAQ,WAAW,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE;4BAC/D,WAAW,OAAO,GAAG,OAAO,WAAW,OAAO,CAAC,MAAM,EAAE,QAAQ;wBAChE;wBAEA,mBAAmB,GACnB,IAAI,UAAU,WAAW,OAAO,IAAI;wBAEpC,IACC,IAAI,cAAc,UAAU,IAAI,EAAE,MAAM,UACxC,gBAAgB,UAAU,IAAI,EAC9B,OAAO,YAAY,KAAK,CAAC,MAAM,EAAE,cAAc,YAAY,IAAI,CAC9D;4BAED,IAAI,WAAW,OAAO,QAAQ,KAAK,EAAE;gCACpC;4BACD;4BAEA,IAAI,MAAM,YAAY,KAAK;4BAE3B,IAAI,UAAU,MAAM,GAAG,KAAK,MAAM,EAAE;gCACnC,+CAA+C;gCAC/C;4BACD;4BAEA,IAAI,eAAe,OAAO;gCACzB;4BACD;4BAEA,IAAI,cAAc,GAAG,4CAA4C;4BACjE,IAAI;4BAEJ,IAAI,QAAQ;gCACX,QAAQ,aAAa,SAAS,KAAK,MAAM;gCACzC,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,KAAK,MAAM,EAAE;oCACzC;gCACD;gCAEA,IAAI,OAAO,MAAM,KAAK;gCACtB,IAAI,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gCACtC,IAAI,IAAI;gCAER,wCAAwC;gCACxC,KAAK,YAAY,KAAK,CAAC,MAAM;gCAC7B,MAAO,QAAQ,EAAG;oCACjB,cAAc,YAAY,IAAI;oCAC9B,KAAK,YAAY,KAAK,CAAC,MAAM;gCAC9B;gCACA,qBAAqB;gCACrB,KAAK,YAAY,KAAK,CAAC,MAAM;gCAC7B,MAAM;gCAEN,4FAA4F;gCAC5F,IAAI,YAAY,KAAK,YAAY,OAAO;oCACvC;gCACD;gCAEA,qDAAqD;gCACrD,IACC,IAAI,IAAI,aACR,MAAM,UAAU,IAAI,IAAI,CAAC,IAAI,MAAM,OAAO,EAAE,KAAK,KAAK,QAAQ,GAC9D,IAAI,EAAE,IAAI,CACT;oCACD;oCACA,KAAK,EAAE,KAAK,CAAC,MAAM;gCACpB;gCACA;gCAEA,6BAA6B;gCAC7B,MAAM,KAAK,KAAK,CAAC,KAAK;gCACtB,MAAM,KAAK,IAAI;4BAChB,OAAO;gCACN,QAAQ,aAAa,SAAS,GAAG,KAAK;gCACtC,IAAI,CAAC,OAAO;oCACX;gCACD;4BACD;4BAEA,wCAAwC;4BACxC,IAAI,OAAO,MAAM,KAAK;4BACtB,IAAI,WAAW,KAAK,CAAC,EAAE;4BACvB,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG;4BAC1B,IAAI,QAAQ,IAAI,KAAK,CAAC,OAAO,SAAS,MAAM;4BAE5C,IAAI,QAAQ,MAAM,IAAI,MAAM;4BAC5B,IAAI,WAAW,QAAQ,QAAQ,KAAK,EAAE;gCACrC,QAAQ,KAAK,GAAG;4BACjB;4BAEA,IAAI,aAAa,YAAY,IAAI;4BAEjC,IAAI,QAAQ;gCACX,aAAa,SAAS,WAAW,YAAY;gCAC7C,OAAO,OAAO,MAAM;4BACrB;4BAEA,YAAY,WAAW,YAAY;4BAEnC,IAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,QAAQ,CAAC,UAAU,UAAU,UAAU,OAAO;4BACxF,cAAc,SAAS,WAAW,YAAY;4BAE9C,IAAI,OAAO;gCACV,SAAS,WAAW,aAAa;4BAClC;4BAEA,IAAI,cAAc,GAAG;gCACpB,0EAA0E;gCAC1E,wDAAwD;gCAExD,2BAA2B,GAC3B,IAAI,gBAAgB;oCACnB,OAAO,QAAQ,MAAM;oCACrB,OAAO;gCACR;gCACA,aAAa,MAAM,WAAW,SAAS,YAAY,IAAI,EAAE,KAAK;gCAE9D,+DAA+D;gCAC/D,IAAI,WAAW,cAAc,KAAK,GAAG,QAAQ,KAAK,EAAE;oCACnD,QAAQ,KAAK,GAAG,cAAc,KAAK;gCACpC;4BACD;wBACD;oBACD;gBACD;YACD;YAEA;;;;;;;MAOC,GAED;;;MAGC,GACD,SAAS;gBACR,8BAA8B,GAC9B,IAAI,OAAO;oBAAE,OAAO;oBAAM,MAAM;oBAAM,MAAM;gBAAK;gBACjD,8BAA8B,GAC9B,IAAI,OAAO;oBAAE,OAAO;oBAAM,MAAM;oBAAM,MAAM;gBAAK;gBACjD,KAAK,IAAI,GAAG;gBAEZ,8BAA8B,GAC9B,IAAI,CAAC,IAAI,GAAG;gBACZ,8BAA8B,GAC9B,IAAI,CAAC,IAAI,GAAG;gBACZ,IAAI,CAAC,MAAM,GAAG;YACf;YAEA;;;;;;;;MAQC,GACD,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,KAAK;gBAClC,uDAAuD;gBACvD,IAAI,OAAO,KAAK,IAAI;gBAEpB,IAAI,UAAU;oBAAE,OAAO;oBAAO,MAAM;oBAAM,MAAM;gBAAK;gBACrD,KAAK,IAAI,GAAG;gBACZ,KAAK,IAAI,GAAG;gBACZ,KAAK,MAAM;gBAEX,OAAO;YACR;YACA;;;;;;;MAOC,GACD,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,KAAK;gBACrC,IAAI,OAAO,KAAK,IAAI;gBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,IAAI,EAAE,IAAK;oBACrD,OAAO,KAAK,IAAI;gBACjB;gBACA,KAAK,IAAI,GAAG;gBACZ,KAAK,IAAI,GAAG;gBACZ,KAAK,MAAM,IAAI;YAChB;YACA;;;;MAIC,GACD,SAAS,QAAQ,IAAI;gBACpB,IAAI,QAAQ,EAAE;gBACd,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI;gBACzB,MAAO,SAAS,KAAK,IAAI,CAAE;oBAC1B,MAAM,IAAI,CAAC,KAAK,KAAK;oBACrB,OAAO,KAAK,IAAI;gBACjB;gBACA,OAAO;YACR;YAGA,IAAI,CAAC,MAAM,QAAQ,EAAE;gBACpB,IAAI,CAAC,MAAM,gBAAgB,EAAE;oBAC5B,aAAa;oBACb,OAAO;gBACR;gBAEA,IAAI,CAAC,EAAE,2BAA2B,EAAE;oBACnC,YAAY;oBACZ,MAAM,gBAAgB,CAAC,WAAW,SAAU,GAAG;wBAC9C,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI,IAAI;wBACjC,IAAI,OAAO,QAAQ,QAAQ;wBAC3B,IAAI,OAAO,QAAQ,IAAI;wBACvB,IAAI,iBAAiB,QAAQ,cAAc;wBAE3C,MAAM,WAAW,CAAC,EAAE,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,EAAE;wBACvD,IAAI,gBAAgB;4BACnB,MAAM,KAAK;wBACZ;oBACD,GAAG;gBACJ;gBAEA,OAAO;YACR;YAEA,mCAAmC;YACnC,IAAI,SAAS,EAAE,IAAI,CAAC,aAAa;YAEjC,IAAI,QAAQ;gBACX,EAAE,QAAQ,GAAG,OAAO,GAAG;gBAEvB,IAAI,OAAO,YAAY,CAAC,gBAAgB;oBACvC,EAAE,MAAM,GAAG;gBACZ;YACD;YAEA,SAAS;gBACR,IAAI,CAAC,EAAE,MAAM,EAAE;oBACd,EAAE,YAAY;gBACf;YACD;YAEA,IAAI,CAAC,EAAE,MAAM,EAAE;gBACd,uEAAuE;gBACvE,sGAAsG;gBACtG,iHAAiH;gBACjH,kHAAkH;gBAClH,iGAAiG;gBACjG,mDAAmD;gBACnD,IAAI,aAAa,SAAS,UAAU;gBACpC,IAAI,eAAe,aAAa,eAAe,iBAAiB,UAAU,OAAO,KAAK,EAAE;oBACvF,SAAS,gBAAgB,CAAC,oBAAoB;gBAC/C,OAAO;oBACN,IAAI,OAAO,qBAAqB,EAAE;wBACjC,OAAO,qBAAqB,CAAC;oBAC9B,OAAO;wBACN,OAAO,UAAU,CAAC,gCAAgC;oBACnD;gBACD;YACD;YAEA,OAAO;QAER,EAAE;QAEF,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,EAAE;YACpD,OAAO,OAAO,GAAG;QAClB;QAEA,mDAAmD;QACnD,IAAI,OAAO,WAAW,aAAa;YAClC,OAAO,KAAK,GAAG;QAChB;QAEA,sCAAsC;QAEtC;;;;;;;;;;;;;;;;;;;KAmBC,GAED;;;;;;KAMC,GAED;;;;;;;;KAQC,GAED;;;;;;KAMC,GAED,MAAM,SAAS,CAAC,KAAK,GAAG;YACvB,WAAW;gBACV;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ;gBACT;gBACA;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ;gBACT;aACA;YACD,UAAU;gBACT,SAAS;gBACT,QAAQ;YACT;YACA,cAAc;gBACb,SAAS;gBACT,YAAY;gBACZ,QAAQ;oBACP,eAAe;gBAChB;YACD;YACA,WAAW;YACX,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,eAAe;QAChB;QAEC,CAAA,SAAU,KAAK;YAEf;;;;;;MAMC,GACD,SAAS,eAAe,QAAQ,EAAE,KAAK;gBACtC,OAAO,QAAQ,SAAS,WAAW,KAAK,QAAQ;YACjD;YAEA,OAAO,gBAAgB,CAAC,MAAM,SAAS,CAAC,oBAAoB,GAAG,CAAC,GAAG;gBAClE,mBAAmB;oBAClB;;;;;;;;;;QAUC,GACD,OAAO,SAAU,GAAG,EAAE,QAAQ,EAAE,kBAAkB,EAAE,aAAa;wBAChE,IAAI,IAAI,QAAQ,KAAK,UAAU;4BAC9B;wBACD;wBAEA,IAAI,aAAa,IAAI,UAAU,GAAG,EAAE;wBAEpC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,SAAU,KAAK;4BAC9D,IAAI,OAAO,kBAAkB,cAAc,CAAC,cAAc,QAAQ;gCACjE,OAAO;4BACR;4BACA,IAAI,IAAI,WAAW,MAAM;4BACzB,IAAI;4BAEJ,6BAA6B;4BAC7B,MAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,eAAe,UAAU,QAAQ,CAAC,EAAG;gCAC1E,EAAE;4BACH;4BAEA,wBAAwB;4BACxB,UAAU,CAAC,EAAE,GAAG;4BAEhB,OAAO;wBACR;wBAEA,+BAA+B;wBAC/B,IAAI,OAAO,GAAG,MAAM,SAAS,CAAC,MAAM;oBACrC;gBACD;gBACA,sBAAsB;oBACrB;;;;;QAKC,GACD,OAAO,SAAU,GAAG,EAAE,QAAQ;wBAC7B,IAAI,IAAI,QAAQ,KAAK,YAAY,CAAC,IAAI,UAAU,EAAE;4BACjD;wBACD;wBAEA,0BAA0B;wBAC1B,IAAI,OAAO,GAAG,MAAM,SAAS,CAAC,SAAS;wBAEvC,IAAI,IAAI;wBACR,IAAI,OAAO,OAAO,IAAI,CAAC,IAAI,UAAU;wBAErC,SAAS,WAAW,MAAM;4BACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gCACvC,wCAAwC;gCACxC,IAAI,KAAK,KAAK,MAAM,EAAE;oCACrB;gCACD;gCAEA,IAAI,QAAQ,MAAM,CAAC,EAAE;gCACrB,IAAI,OAAO,UAAU,YAAa,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK,UAAW;oCACtF,IAAI,IAAI,IAAI,CAAC,EAAE;oCACf,IAAI,IAAI,IAAI,UAAU,CAAC,EAAE;oCACzB,IAAI,IAAI,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO;oCACzD,IAAI,cAAc,eAAe,UAAU;oCAE3C,IAAI,QAAQ,EAAE,OAAO,CAAC;oCACtB,IAAI,QAAQ,CAAC,GAAG;wCACf,EAAE;wCAEF,IAAI,SAAS,EAAE,SAAS,CAAC,GAAG;wCAC5B,IAAI,SAAS,IAAI,MAAM,KAAK,CAAC,UAAU,MAAM,QAAQ,CAAC,GAAG,IAAI,OAAO,GAAG,cAAc,UAAU;wCAC/F,IAAI,QAAQ,EAAE,SAAS,CAAC,QAAQ,YAAY,MAAM;wCAElD,IAAI,cAAc,EAAE;wCACpB,IAAI,QAAQ;4CACX,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,WAAW;gDAAC;6CAAO;wCACxD;wCACA,YAAY,IAAI,CAAC;wCACjB,IAAI,OAAO;4CACV,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,WAAW;gDAAC;6CAAM;wCACvD;wCAEA,IAAI,OAAO,UAAU,UAAU;4CAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ;gDAAC;gDAAG;6CAAE,CAAC,MAAM,CAAC;wCAC3C,OAAO;4CACN,MAAM,OAAO,GAAG;wCACjB;oCACD;gCACD,OAAO,IAAI,MAAM,OAAO,CAAC,wCAAwC,KAAI;oCACpE,WAAW,MAAM,OAAO;gCACzB;4BACD;4BAEA,OAAO;wBACR;wBAEA,WAAW,IAAI,MAAM;oBACtB;gBACD;YACD;QAED,CAAA,EAAE;QAEF,MAAM,SAAS,CAAC,CAAC,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;YACnD,WAAW;gBACV,SAAS;gBACT,QAAQ;YACT;YACA,UAAU;gBACT,0DAA0D;gBAC1D,SAAS;gBACT,QAAQ;YACT;YACA,cAAc;gBACb,SAAS;gBACT,YAAY;YACb;YACA,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;QACb;QAEA,MAAM,SAAS,CAAC,YAAY,CAAC,KAAK,UAAU;YAC3C,QAAQ;gBACP,8DAA8D;gBAC9D,SAAS;gBACT,QAAQ;YACT;QACD;QAEA,MAAM,SAAS,CAAC,YAAY,CAAC,KAAK,UAAU;YAC3C,SAAS;gBACR,wCAAwC;gBACxC,qDAAqD;gBACrD,SAAS;gBACT,YAAY;gBACZ,QAAQ;gBACR,OAAO;gBACP,QAAQ;oBACP,UAAU;wBACT;4BACC,0DAA0D;4BAC1D,SAAS;4BACT,YAAY;wBACb;wBACA,MAAM,SAAS,CAAC,CAAC,CAAC,SAAS;qBAC3B;oBACD,QAAQ,MAAM,SAAS,CAAC,CAAC,CAAC,OAAO;oBACjC,WAAW,MAAM,SAAS,CAAC,CAAC,CAAC,UAAU;oBACvC,cAAc;wBACb;4BACC,SAAS;4BACT,YAAY;wBACb;wBACA;4BACC,SAAS;4BACT,YAAY;4BACZ,OAAO;wBACR;qBACA;oBACD,yCAAyC;oBACzC,aAAa;wBACZ,SAAS;wBACT,YAAY;wBACZ,OAAO;oBACR;oBACA,kBAAkB;oBAClB,eAAe;oBACf,cAAc;wBACb,SAAS;wBACT,QAAQ,MAAM,SAAS,CAAC,CAAC;oBAC1B;gBACD;YACD;QACD;QAEA,MAAM,SAAS,CAAC,YAAY,CAAC,KAAK,YAAY;YAC7C,2CAA2C;YAC3C,YAAY;QACb;QAEA,OAAO,MAAM,SAAS,CAAC,CAAC,CAAC,UAAU;QAElC,CAAA,SAAU,KAAK;YAEf,IAAI,UAAU;YACd,IAAI,UAAU,uCAAuC,MAAM,CAAC,OAAO,CAAC,cAAc;gBAAc,OAAO,QAAQ,MAAM;YAAE;YAEvH,MAAM,SAAS,CAAC,GAAG,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK;gBACjD,cAAc;oBACb;wBACC,SAAS,OAAO,gEAAgE,MAAM,CACpF,OAAO,CAAC,cAAc;4BAAc,OAAO,QAAQ,MAAM;wBAAE;wBAC7D,YAAY;oBACb;oBACA,6EAA6E;oBAC7E,6BAA6B;oBAC7B,0GAA0G;oBAC1G,qFAAqF;oBACrF;oBACA,4DAA4D;oBAC5D,mBAAmB;oBACnB;oBACA,wGAAwG;oBACxG,8EAA8E;oBAC9E;iBACA;gBACD,WAAW;gBACX,UAAU;oBACT,SAAS;oBACT,QAAQ;gBACT;gBACA,YAAY;gBACZ,WAAW;YACZ;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,OAAO,UAAU;gBAC7C,UAAU;oBACT,qDAAqD;oBACrD,SAAS,OACR,2BAA2B,MAAM,GACjC,QACA,cAAc;oBACd,mDAAmD,MAAM,GACzD,MACA,mCAAmC;oBACnC,kDAAkD,MAAM,CAAC,OAAO,CAAC,eAAe;wBAAc,OAAO;oBAAS,KAC9G;oBAED,YAAY;oBACZ,QAAQ;oBACR,QAAQ;wBACP,UAAU;wBACV,YAAY;wBACZ,eAAe;oBAChB;gBACD;gBACA,cAAc;oBACb,SAAS;oBACT,OAAO;oBACP,QAAQ;gBACT;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,OAAO,WAAW;gBAC9C,oBAAoB;oBACnB,SAAS;oBACT,QAAQ;wBACP,YAAY;wBACZ,WAAW;4BACV,SAAS;4BACT,OAAO;4BACP,QAAQ,MAAM,SAAS,CAAC,GAAG;wBAC5B;oBACD;gBACD;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,OAAO,YAAY;gBAC/C,gBAAgB;oBACf,SAAS;oBACT,OAAO;gBACR;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,OAAO,cAAc;gBACjD,wDAAwD;gBACxD,mDAAmD;gBACnD,eAAe;oBACd,SAAS;oBACT,YAAY;oBACZ,QAAQ;oBACR,QAAQ,MAAM,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;gBACxC;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,gBAAgB;gBACtD,sEAAsE;gBACtE,cAAc;YACf,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,cAAc;QAEtC,CAAA,EAAE;QAED,CAAA,SAAU,KAAK;YAEf;;;;;;;;;MASC,GACD,SAAS,QAAQ,OAAO,EAAE,YAAY;gBACrC,OAAO,QAAQ,OAAO,CAAC,cAAc,SAAU,CAAC,EAAE,KAAK;oBACtD,OAAO,QAAQ,YAAY,CAAC,CAAC,MAAM,GAAG;gBACvC;YACD;YACA;;;;;MAKC,GACD,SAAS,GAAG,OAAO,EAAE,YAAY,EAAE,KAAK;gBACvC,OAAO,OAAO,QAAQ,SAAS,eAAe,SAAS;YACxD;YAEA;;;;;;MAMC,GACD,SAAS,OAAO,OAAO,EAAE,SAAS;gBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;oBACnC,UAAU,QAAQ,OAAO,CAAC,aAAa;wBAAc,OAAO,QAAQ,UAAU;oBAAK;gBACpF;gBACA,OAAO,QAAQ,OAAO,CAAC,aAAa;YACrC;YAEA,8EAA8E;YAC9E,IAAI,eAAe;gBAClB,qDAAqD;gBACrD,MAAM;gBACN,4CAA4C;gBAC5C,iBAAiB;gBACjB,sBAAsB;gBACtB,qEAAqE;gBACrE,YAAY;gBACZ,qBAAqB;gBACrB,OAAO;YACR;YAEA,WAAW;YACX,SAAS,kBAAkB,KAAK;gBAC/B,OAAO,WAAW,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,OAAO;YACrD;YACA,IAAI,0BAA0B,kBAAkB,aAAa,eAAe;YAC5E,IAAI,WAAW,OAAO,kBAAkB,aAAa,IAAI,GAAG,MAAM,aAAa,eAAe,GAAG,MAAM,aAAa,UAAU,GAAG,MAAM,aAAa,KAAK;YACzJ,IAAI,kBAAkB,kBAAkB,aAAa,eAAe,GAAG,MAAM,aAAa,UAAU,GAAG,MAAM,aAAa,KAAK;YAC/H,IAAI,wBAAwB,kBAAkB,aAAa,IAAI,GAAG,MAAM,aAAa,eAAe,GAAG,MAAM,aAAa,KAAK;YAE/H,QAAQ;YACR,IAAI,UAAU,OAAO,mCAAmC,MAAM,EAAE,IAAI,uGAAuG;YAC3K,IAAI,cAAc,OAAO,0BAA0B,MAAM,EAAE;YAC3D,IAAI,OAAO,qBAAqB,MAAM;YACtC,IAAI,cAAc,QAAQ,qBAAqB,MAAM,EAAE;gBAAC;gBAAM;aAAQ;YACtE,IAAI,aAAa,QAAQ,mCAAmC,MAAM,EAAE;gBAAC;gBAAiB;aAAY;YAClG,IAAI,QAAQ,mBAAmB,MAAM;YACrC,IAAI,6BAA6B,QAAQ,yCAAyC,MAAM,EAAE;gBAAC;gBAAY;aAAM;YAC7G,IAAI,eAAe,QAAQ,2CAA2C,MAAM,EAAE;gBAAC;gBAAS;gBAAa;aAAM;YAC3G,IAAI,QAAQ,QAAQ,yBAAyB,MAAM,EAAE;gBAAC;aAAa;YACnE,IAAI,iBAAiB,QAAQ,mDAAmD,MAAM,EAAE;gBAAC;gBAAO;gBAAY;aAAM;YAElH,IAAI,aAAa;gBAChB,WAAW;gBACX,eAAe;YAChB;YAEA,uBAAuB;YACvB,gIAAgI;YAChI,6HAA6H;YAC7H,IAAI,YAAY,8CAA8C,MAAM,EAAE,qBAAqB;YAC3F,IAAI,gBAAgB,wBAAwB,MAAM;YAClD,IAAI,iBAAiB,kCAAkC,MAAM;YAG7D,MAAM,SAAS,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;gBACxD,UAAU;oBACT;wBACC,SAAS,GAAG,kBAAkB,MAAM,EAAE;4BAAC;yBAAe;wBACtD,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,SAAS,GAAG,mBAAmB,MAAM,EAAE;4BAAC;yBAAc;wBACtD,YAAY;wBACZ,QAAQ;oBACT;iBACA;gBACD,cAAc;oBACb;wBACC,eAAe;wBACf,4BAA4B;wBAC5B,SAAS,GAAG,qCAAqC,MAAM,EAAE;4BAAC;yBAAW;wBACrE,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,qBAAqB;wBACrB,wCAAwC;wBACxC,SAAS,GAAG,wCAAwC,MAAM,EAAE;4BAAC;4BAAM;yBAAe;wBAClF,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,sBAAsB;wBACtB,wCAAwC;wBACxC,SAAS,GAAG,4BAA4B,MAAM,EAAE;4BAAC;yBAAK;wBACtD,YAAY;oBACb;oBACA;wBACC,oBAAoB;wBACpB,kBAAkB;wBAClB,0BAA0B;wBAC1B,SAAS,GAAG,oBAAoB,MAAM,EAAE;4BAAC;4BAAyB;yBAAY;wBAC9E,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,qCAAqC;wBACrC,aAAa;wBACb,gEAAgE;wBAChE,SAAS,GAAG,yBAAyB,MAAM,EAAE;4BAAC;yBAAW;wBACzD,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,oDAAoD;wBACpD,oBAAoB;wBACpB,SAAS,GAAG,oBAAoB,MAAM,EAAE;4BAAC;yBAAK;wBAC9C,YAAY;oBACb;oBACA;wBACC,kCAAkC;wBAClC,uBAAuB;wBACvB,kEAAkE;wBAClE,SAAS,GAAG,mCAAmC,MAAM,EAAE;4BAAC;yBAA2B;wBACnF,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,4CAA4C;wBAC5C,6DAA6D;wBAC7D,SAAS,GAAG,2EAA2E,MAAM,EAAE;4BAAC;4BAAgB;4BAAuB;yBAAK;wBAC5I,QAAQ;oBACT;iBACA;gBACD,WAAW;gBACX,sHAAsH;gBACtH,UAAU;gBACV,YAAY;gBACZ,eAAe;YAChB;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,UAAU;gBAChD,SAAS;oBACR,SAAS;oBACT,OAAO;gBACR;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,eAAe;gBACrD,mBAAmB;oBAClB,SAAS,GAAG,yBAAyB,MAAM,EAAE;wBAAC;qBAAK;oBACnD,YAAY;oBACZ,OAAO;gBACR;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,cAAc;gBACpD,aAAa;oBACZ,uBAAuB;oBACvB,iBAAiB;oBACjB,SAAS,GAAG,+DAA+D,MAAM,EAAE;wBAAC;qBAAK;oBACzF,YAAY;oBACZ,QAAQ;wBACP,eAAe;oBAChB;gBACD;gBACA,mBAAmB;oBAClB,8CAA8C;oBAC9C,SAAS,GAAG,kFAAkF,MAAM,EAAE;wBAAC;qBAAY;oBACnH,YAAY;oBACZ,OAAO;oBACP,QAAQ;gBACT;gBACA,eAAe;oBACd,yCAAyC;oBACzC,+EAA+E;oBAC/E,0CAA0C;oBAC1C,SAAS,GAAG,+DAA+D,MAAM,EAAE;wBAAC;wBAAgB;qBAAW;oBAC/G,QAAQ;oBACR,OAAO;gBACR;gBACA,0BAA0B;oBACzB,2BAA2B;oBAC3B,SAAS,GAAG,8BAA8B,MAAM,EAAE;wBAAC;qBAAe;oBAClE,YAAY;oBACZ,QAAQ;oBACR,OAAO;gBACR;gBACA;;;;;QAKE,GACF,kBAAkB;oBACjB,aAAa;oBACb,SAAS,GAAG,yBAAyB,MAAM,EAAE;wBAAC;wBAAM;qBAAQ;oBAC5D,QAAQ;wBACP,YAAY,GAAG,SAAS,MAAM,EAAE;4BAAC;yBAAK;wBACtC,WAAW;4BACV,SAAS,OAAO;4BAChB,OAAO;4BACP,QAAQ;wBACT;oBACD;gBACD;gBACA,aAAa;oBACZ,wDAAwD;oBACxD,oCAAoC;oBACpC,4BAA4B;oBAC5B,SAAS,GACR,kKAAkK,MAAM,EACxK;wBAAC;wBAAyB;wBAAa;wBAAM;wBAAgB,SAAS,MAAM;wBAAE;wBAAa,kBAAkB,MAAM;qBAAC;oBAErH,YAAY;oBACZ,QAAQ;wBACP,oBAAoB;4BACnB,SAAS,GAAG,+BAA+B,MAAM,EAAE;gCAAC;gCAAa;6BAAY;4BAC7E,YAAY;4BACZ,QAAQ;4BACR,QAAQ,MAAM,SAAS,CAAC,MAAM;wBAC/B;wBACA,WAAW;wBACX,cAAc;4BACb,SAAS,OAAO;4BAChB,QAAQ;4BACR,QAAQ;wBACT;wBACA,eAAe;oBAChB;gBACD;gBACA,gBAAgB;oBACf,SAAS;oBACT,YAAY;oBACZ,OAAO;oBACP,QAAQ;wBACP,gDAAgD;wBAChD,aAAa;4BACZ,SAAS;4BACT,YAAY;4BACZ,OAAO;wBACR;oBACD;gBACD;YACD;YAEA,aAAa;YACb,IAAI,2BAA2B,gBAAgB,MAAM;YACrD,IAAI,kCAAkC,QAAQ,iEAAiE,MAAM,EAAE;gBAAC;aAAyB;YACjJ,IAAI,kBAAkB,OAAO,QAAQ,+BAA+B,MAAM,EAAE;gBAAC;aAAgC,GAAG;YAEhH,0GAA0G;YAC1G,IAAI,aAAa,wEAAwE,MAAM;YAC/F,IAAI,OAAO,QAAQ,0BAA0B,MAAM,EAAE;gBAAC;gBAAY;aAAgB;YAElF,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,cAAc;gBACpD,aAAa;oBACZ,aAAa;oBACb,wFAAwF;oBACxF,SAAS,GAAG,6EAA6E,MAAM,EAAE;wBAAC;wBAAY;qBAAK;oBACnH,YAAY;oBACZ,QAAQ;oBACR,QAAQ;wBACP,UAAU;4BACT,SAAS,GAAG,iBAAiB,MAAM,EAAE;gCAAC;6BAAW;4BACjD,OAAO;wBACR;wBACA,uBAAuB;4BACtB,SAAS,GAAG,aAAa,MAAM,EAAE;gCAAC;6BAAgB;4BAClD,QAAQ,MAAM,SAAS,CAAC,MAAM;wBAC/B;wBACA,cAAc;4BACb,SAAS,OAAO;4BAChB,QAAQ;gCACP,eAAe;4BAChB;wBACD;wBACA,eAAe;oBAChB;gBACD;YACD;YAGA,uBAAuB;YACvB,IAAI,eAAe,aAAa,MAAM;YACtC,aAAa;YACb,IAAI,sBAAsB,OAAO,QAAQ,+BAA+B,MAAM,EAAE;gBAAC;aAAgC,GAAG;YACpH,IAAI,iBAAiB,QAAQ,qCAAqC,MAAM,EAAE;gBAAC;gBAAqB;aAAa;YAC7G,cAAc;YACd,IAAI,sBAAsB,OAAO,QAAQ,mEAAmE,MAAM,EAAE;gBAAC;aAAyB,GAAG;YACjJ,IAAI,iBAAiB,QAAQ,qCAAqC,MAAM,EAAE;gBAAC;gBAAqB;aAAa;YAE7G,SAAS,0BAA0B,aAAa,EAAE,kBAAkB;gBACnE,OAAO;oBACN,iBAAiB;wBAChB,SAAS,GAAG,6BAA6B,MAAM,EAAE;4BAAC;yBAAc;wBAChE,YAAY;wBACZ,QAAQ;4BACP,iBAAiB;gCAChB,SAAS,GAAG,sCAAsC,MAAM,EAAE;oCAAC;oCAAoB;iCAAa;gCAC5F,YAAY;gCACZ,QAAQ;oCACP,eAAe;gCAChB;4BACD;4BACA,eAAe;4BACf,cAAc;gCACb,SAAS;gCACT,OAAO;gCACP,QAAQ,MAAM,SAAS,CAAC,MAAM;4BAC/B;wBACD;oBACD;oBACA,UAAU;gBACX;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,UAAU;gBAChD,wBAAwB;oBACvB;wBACC,SAAS,GAAG,4DAA4D,MAAM,EAAE;4BAAC;yBAAe;wBAChG,YAAY;wBACZ,QAAQ;wBACR,QAAQ,0BAA0B,gBAAgB;oBACnD;oBACA;wBACC,SAAS,GAAG,4CAA4C,MAAM,EAAE;4BAAC;yBAAe;wBAChF,YAAY;wBACZ,QAAQ;wBACR,QAAQ,0BAA0B,gBAAgB;oBACnD;iBACA;gBACD,QAAQ;oBACP,SAAS,OAAO;oBAChB,QAAQ;gBACT;YACD;YAEA,MAAM,SAAS,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,MAAM;QAErE,CAAA,EAAE;QAED,CAAA,SAAU,KAAK;YAEf,IAAI,SAAS;YAEb,MAAM,SAAS,CAAC,GAAG,GAAG;gBACrB,WAAW;gBACX,UAAU;oBACT,SAAS,OAAO,eAAe,sBAAsB,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG,QAAQ,kBAAkB,MAAM;oBACpH,QAAQ;wBACP,QAAQ;wBACR,8BAA8B;4BAC7B,SAAS;4BACT,YAAY;4BACZ,OAAO;wBACR;wBACA,WAAW;4BACV,SAAS;4BACT,YAAY;wBACb;oBAED;gBACD;gBACA,OAAO;oBACN,8CAA8C;oBAC9C,SAAS,OAAO,iBAAiB,OAAO,MAAM,GAAG,MAAM,8BAA8B,MAAM,GAAG,QAAQ;oBACtG,QAAQ;oBACR,QAAQ;wBACP,YAAY;wBACZ,eAAe;wBACf,UAAU;4BACT,SAAS,OAAO,MAAM,OAAO,MAAM,GAAG;4BACtC,OAAO;wBACR;oBACD;gBACD;gBACA,YAAY;oBACX,SAAS,OAAO,uDAAuD,OAAO,MAAM,GAAG;oBACvF,YAAY;gBACb;gBACA,UAAU;oBACT,SAAS;oBACT,QAAQ;gBACT;gBACA,YAAY;oBACX,SAAS;oBACT,YAAY;gBACb;gBACA,aAAa;gBACb,YAAY;oBACX,SAAS;oBACT,YAAY;gBACb;gBACA,eAAe;YAChB;YAEA,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG;YAE/D,IAAI,SAAS,MAAM,SAAS,CAAC,MAAM;YACnC,IAAI,QAAQ;gBACX,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS;gBAC/B,OAAO,GAAG,CAAC,YAAY,CAAC,SAAS;YAClC;QAED,CAAA,EAAE;QAED,CAAA,SAAU,KAAK;YAEf,IAAI,WAAW;YAEf,sDAAsD;YACtD,IAAI,kBAAkB,6CAA6C,MAAM;YAEzE,uCAAuC;YACvC,IAAI,YAAY;gBACf,SAAS,OAAO,aAAa,MAAM,GAAG,kBAAkB,gCAAgC,MAAM;gBAC9F,YAAY;gBACZ,QAAQ;oBACP,aAAa;wBACZ,SAAS;wBACT,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA,eAAe;gBAChB;YACD;YAEA,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;gBACtD,UAAU;oBACT,SAAS;oBACT,YAAY;oBACZ,QAAQ;gBACT;gBACA,cAAc;oBACb;oBACA;wBACC,oDAAoD;wBACpD,wHAAwH;wBACxH,SAAS,OAAO,aAAa,MAAM,GAAG,kBAAkB,+DAA+D,MAAM;wBAC7H,YAAY;wBACZ,QAAQ,UAAU,MAAM;oBACzB;oBACA;wBACC,+BAA+B;wBAC/B,wHAAwH;wBACxH,SAAS,OAAO,kFAAkF,MAAM,GAAG,kBAAkB,aAAa,MAAM;wBAChJ,YAAY;wBACZ,QAAQ,UAAU,MAAM;oBACzB;iBACA;gBACD,WAAW;gBACX,YAAY;oBACX,MAAM,SAAS,CAAC,KAAK,CAAC,QAAQ;oBAC9B;wBACC,SAAS;wBACT,YAAY;oBACb;iBACA;gBACD,UAAU;gBACV,YAAY;oBACX,SAAS;oBACT,YAAY;gBACb;gBACA,YAAY;YACb;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,UAAU;gBAC9C,wBAAwB;oBACvB,+CAA+C;oBAC/C,SAAS;oBACT,QAAQ;oBACR,OAAO;gBACR;gBACA,QAAQ;oBACP,SAAS;oBACT,QAAQ;gBACT;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,cAAc;gBAClD,cAAc;oBACb,SAAS;oBACT,YAAY;oBACZ,OAAO;gBACR;gBACA,YAAY;oBACX,SAAS;oBACT,QAAQ;wBACP,cAAc;wBACd,WAAW;wBACX,eAAe;wBACf,YAAY;oBACb;gBACD;gBACA,UAAU;oBACT;wBACC,SAAS,OAAO,gBAAgB,MAAM,GAAG,kBAAkB,0BAA0B,MAAM;wBAC3F,YAAY;wBACZ,QAAQ;4BACP,aAAa,UAAU,MAAM,CAAC,SAAS;4BACvC,eAAe;4BACf,YAAY;4BACZ,cAAc;wBACf;oBACD;oBACA;wBACC,SAAS,OAAO,yBAAyB,MAAM,GAAG,kBAAkB,qBAAqB,MAAM;wBAC/F,YAAY;wBACZ,OAAO;wBACP,QAAQ;4BACP,aAAa,UAAU,MAAM,CAAC,SAAS;4BACvC,UAAU;4BACV,eAAe;4BACf,YAAY;4BACZ,cAAc;wBACf;oBACD;iBACA;gBACD,aAAa;oBACZ,SAAS,OACR,qJACE,MAAM,CAAC,OAAO,CAAC,cAAc;wBAAc,OAAO,SAAS,MAAM;oBAAE;oBACtE,YAAY;oBACZ,QAAQ;wBACP,eAAe;oBAChB;gBACD;YACD;QACD,CAAA,EAAE;QAEF,MAAM,SAAS,CAAC,UAAU,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;YAC5D,cAAc;gBACb,MAAM,SAAS,CAAC,KAAK,CAAC,aAAa;gBACnC;oBACC,SAAS;oBACT,YAAY;gBACb;aACA;YACD,WAAW;gBACV;oBACC,SAAS;oBACT,YAAY;gBACb;gBACA;oBACC,SAAS;oBACT,YAAY;gBACb;aACA;YACD,8EAA8E;YAC9E,YAAY;YACZ,UAAU;gBACT,SAAS,OACR,aAAa,MAAM,GACnB,QACA,CACC,WAAW;gBACX,eAAe,MAAM,GACrB,MACA,iBAAiB;gBACjB,0BAA0B,MAAM,GAChC,MACA,gBAAgB;gBAChB,4BAA4B,MAAM,GAClC,MACA,sBAAsB;gBACtB,sCAAsC,MAAM,GAC5C,MACA,iBAAiB;gBACjB,gBAAgB,MAAM,GACtB,MACA,kDAAkD;gBAClD,oFAAoF,MAAM,AAC3F,IACA,MACA,YAAY,MAAM;gBAEnB,YAAY;YACb;YACA,YAAY;QACb;QAEA,MAAM,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,GAAG;QAEtD,MAAM,SAAS,CAAC,YAAY,CAAC,cAAc,WAAW;YACrD,SAAS;gBACR,SAAS,OACR,aAAa;gBACb,qEAAqE;gBACrE,0DAA0D,MAAM,GAChE,iBAAiB;gBACjB,uGAAuG;gBACvG,uGAAuG;gBACvG,yEAAyE;gBACzE,KAAK,MAAM,GACX,QACA,iEAAiE,MAAM,GACvE,MACA,uEAAuE;gBACvE,qIAAqI,MAAM,GAC3I,MACA,YAAY;gBACZ,kEAAkE,MAAM;gBAEzE,YAAY;gBACZ,QAAQ;gBACR,QAAQ;oBACP,gBAAgB;wBACf,SAAS;wBACT,YAAY;wBACZ,OAAO;wBACP,QAAQ,MAAM,SAAS,CAAC,KAAK;oBAC9B;oBACA,mBAAmB;oBACnB,eAAe;gBAChB;YACD;YACA,yFAAyF;YACzF,qBAAqB;gBACpB,SAAS;gBACT,OAAO;YACR;YACA,aAAa;gBACZ;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ,MAAM,SAAS,CAAC,UAAU;gBACnC;gBACA;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ,MAAM,SAAS,CAAC,UAAU;gBACnC;gBACA;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ,MAAM,SAAS,CAAC,UAAU;gBACnC;gBACA;oBACC,SAAS;oBACT,YAAY;oBACZ,QAAQ,MAAM,SAAS,CAAC,UAAU;gBACnC;aACA;YACD,YAAY;QACb;QAEA,MAAM,SAAS,CAAC,YAAY,CAAC,cAAc,UAAU;YACpD,YAAY;gBACX,SAAS;gBACT,QAAQ;gBACR,OAAO;YACR;YACA,mBAAmB;gBAClB,SAAS;gBACT,QAAQ;gBACR,QAAQ;oBACP,wBAAwB;wBACvB,SAAS;wBACT,OAAO;oBACR;oBACA,iBAAiB;wBAChB,SAAS;wBACT,YAAY;wBACZ,QAAQ;4BACP,6BAA6B;gCAC5B,SAAS;gCACT,OAAO;4BACR;4BACA,MAAM,MAAM,SAAS,CAAC,UAAU;wBACjC;oBACD;oBACA,UAAU;gBACX;YACD;YACA,mBAAmB;gBAClB,SAAS;gBACT,YAAY;gBACZ,QAAQ;gBACR,OAAO;YACR;QACD;QAEA,MAAM,SAAS,CAAC,YAAY,CAAC,cAAc,YAAY;YACtD,oBAAoB;gBACnB,SAAS;gBACT,YAAY;gBACZ,OAAO;YACR;QACD;QAEA,IAAI,MAAM,SAAS,CAAC,MAAM,EAAE;YAC3B,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU;YAEhD,4CAA4C;YAC5C,sEAAsE;YACtE,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CACtC,yNAAyN,MAAM,EAC/N;QAEF;QAEA,MAAM,SAAS,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,UAAU;QAE/C,MAAM,SAAS,CAAC,MAAM,GAAG;YACxB,WAAW;gBACV,SAAS;gBACT,QAAQ;YACT;YACA,UAAU;gBACT,SAAS;gBACT,QAAQ;YACT;YACA,WAAW;gBACV,4CAA4C;gBAC5C,SAAS;gBACT,QAAQ;gBACR,QAAQ;oBACP,mBAAmB;wBAClB,SAAS;wBACT,YAAY;wBACZ,QAAQ;wBACR,QAAQ,KAAK,YAAY;oBAC1B;oBACA,UAAU;wBACT,SAAS;wBACT,QAAQ;oBACT;oBACA,eAAe;oBACf,eAAe;oBACf,QAAQ;gBACT;YACD;YACA,SAAS;gBACR,SAAS;gBACT,QAAQ;YACT;YACA,OAAO;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;oBACP,OAAO;wBACN,SAAS;wBACT,QAAQ;4BACP,eAAe;4BACf,aAAa;wBACd;oBACD;oBACA,gBAAgB,EAAE;oBAClB,cAAc;wBACb,SAAS;wBACT,QAAQ;4BACP,eAAe;gCACd;oCACC,SAAS;oCACT,OAAO;gCACR;gCACA;oCACC,SAAS;oCACT,YAAY;gCACb;6BACA;wBACF;oBACD;oBACA,eAAe;oBACf,aAAa;wBACZ,SAAS;wBACT,QAAQ;4BACP,aAAa;wBACd;oBACD;gBAED;YACD;YACA,UAAU;gBACT;oBACC,SAAS;oBACT,OAAO;gBACR;gBACA;aACA;QACF;QAEA,MAAM,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,GAClE,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;QACjC,MAAM,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,MAAM;QAE3F,0EAA0E;QAC1E,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,SAAU,GAAG;YAEpC,IAAI,IAAI,IAAI,KAAK,UAAU;gBAC1B,IAAI,UAAU,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;YACxD;QACD;QAEA,OAAO,cAAc,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,cAAc;YAC/D;;;;;;;;;;MAUC,GACD,OAAO,SAAS,WAAW,OAAO,EAAE,IAAI;gBACvC,IAAI,sBAAsB,CAAC;gBAC3B,mBAAmB,CAAC,cAAc,KAAK,GAAG;oBACzC,SAAS;oBACT,YAAY;oBACZ,QAAQ,MAAM,SAAS,CAAC,KAAK;gBAC9B;gBACA,mBAAmB,CAAC,QAAQ,GAAG;gBAE/B,IAAI,SAAS;oBACZ,kBAAkB;wBACjB,SAAS;wBACT,QAAQ;oBACT;gBACD;gBACA,MAAM,CAAC,cAAc,KAAK,GAAG;oBAC5B,SAAS;oBACT,QAAQ,MAAM,SAAS,CAAC,KAAK;gBAC9B;gBAEA,IAAI,MAAM,CAAC;gBACX,GAAG,CAAC,QAAQ,GAAG;oBACd,SAAS,OAAO,wFAAwF,MAAM,CAAC,OAAO,CAAC,OAAO;wBAAc,OAAO;oBAAS,IAAI;oBAChK,YAAY;oBACZ,QAAQ;oBACR,QAAQ;gBACT;gBAEA,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,SAAS;YACjD;QACD;QACA,OAAO,cAAc,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,gBAAgB;YACjE;;;;;;;;;;MAUC,GACD,OAAO,SAAU,QAAQ,EAAE,IAAI;gBAC9B,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;oBACtD,SAAS,OACR,aAAa,MAAM,GAAG,QAAQ,WAAW,MAAM,iDAAiD,MAAM,EACtG;oBAED,YAAY;oBACZ,QAAQ;wBACP,aAAa;wBACb,cAAc;4BACb,SAAS;4BACT,QAAQ;gCACP,SAAS;oCACR,SAAS;oCACT,YAAY;oCACZ,OAAO;wCAAC;wCAAM,cAAc;qCAAK;oCACjC,QAAQ,MAAM,SAAS,CAAC,KAAK;gCAC9B;gCACA,eAAe;oCACd;wCACC,SAAS;wCACT,OAAO;oCACR;oCACA;iCACA;4BACF;wBACD;oBACD;gBACD;YACD;QACD;QAEA,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM;QAC7C,MAAM,SAAS,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,MAAM;QAC/C,MAAM,SAAS,CAAC,GAAG,GAAG,MAAM,SAAS,CAAC,MAAM;QAE5C,MAAM,SAAS,CAAC,GAAG,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;QACxD,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG;QAC1C,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG;QAC1C,MAAM,SAAS,CAAC,GAAG,GAAG,MAAM,SAAS,CAAC,GAAG;QAEzC;;;;;;KAMC,GACA,CAAA,SAAU,KAAK;YACf,IAAI,UAAU;YACd,IAAI,WAAW;gBACd;oBACC,SAAS;oBACT,OAAO;gBACR;gBACA;oBACC,SAAS;oBACT,QAAQ;oBACR,YAAY;gBACb;gBACA;oBACC,SAAS;oBACT,QAAQ;oBACR,YAAY;gBACb;gBACA;gBACA;aACA;YACD,IAAI,SAAS;YACb,IAAI,WAAW;YACf,IAAI,cAAc;YAElB,MAAM,SAAS,CAAC,GAAG,GAAG;gBACrB,aAAa;oBACZ,SAAS;oBACT,OAAO;gBACR;gBACA,WAAW;gBACX,YAAY;gBACZ,WAAW;oBACV,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBACP,eAAe;oBAChB;gBACD;gBACA,yBAAyB;oBACxB,SAAS;oBACT,YAAY;oBACZ,OAAO;gBACR;gBACA,uBAAuB;oBACtB,SAAS;oBACT,YAAY;oBACZ,OAAO;gBACR;gBACA,WAAW;oBACV;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACT;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACT;oBACA;wBACC,aAAa;wBACb,SAAS;wBACT,YAAY;oBACb;oBACA,oDAAoD;oBACpD;oBACA;wBACC,sDAAsD;wBACtD,EAAE;wBACF,sCAAsC;wBACtC,mDAAmD;wBACnD,SAAS;wBACT,YAAY;oBACb;iBACA;gBACD,iBAAiB;oBAChB,SAAS;oBACT,YAAY;gBACb;gBACA,cAAc;oBACb;wBACC,SAAS;wBACT,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,QAAQ;oBACT;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACT;oBACA;wBACC,SAAS;wBACT,OAAO;4BAAC;4BAA8B;yBAAmB;wBACzD,QAAQ;wBACR,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACT;oBACA;wBACC,SAAS;wBACT,OAAO;4BAAC;4BAA8B;yBAAiB;wBACvD,QAAQ;wBACR,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;4BAAC;4BAA8B;yBAAY;wBAClD,QAAQ;wBACR,YAAY;wBACZ,QAAQ;4BACP,eAAe;wBAChB;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,YAAY;oBACb;oBACA;wBACC,SAAS;wBACT,OAAO;4BAAC;4BAA8B;yBAAc;wBACpD,QAAQ;wBACR,YAAY;wBACZ,QAAQ;4BACP,eAAe;wBAChB;oBACD;iBACA;gBACD,YAAY;gBACZ,YAAY;oBACX,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBACP,eAAe;oBAChB;gBACD;gBACA,YAAY;oBACX,SAAS;oBACT,YAAY;gBACb;gBACA,UAAU;gBACV,YAAY;gBACZ,eAAe;YAChB;YAEA,IAAI,uBAAuB;gBAC1B,SAAS;gBACT,YAAY;gBACZ,QAAQ,MAAM,SAAS,CAAC,GAAG;YAC5B;YAEA,IAAI,SAAS;gBACZ;oBACC,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACP,aAAa;4BACZ,SAAS;4BACT,OAAO;4BACP,QAAQ;gCACP,eAAe;4BAChB;wBACD;oBACD;gBACD;gBACA;oBACC,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACP,aAAa;4BACZ,SAAS;4BACT,OAAO;4BACP,QAAQ;gCACP,eAAe;4BAChB;wBACD;wBACA,iBAAiB;oBAClB;gBACD;gBACA;oBACC,SAAS;oBACT,OAAO;oBACP,QAAQ;gBACT;gBACA;oBACC,SAAS;oBACT,OAAO;oBACP,QAAQ;gBACT;gBACA;oBACC,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACP,iBAAiB;oBAClB;gBACD;aACA;YAED,MAAM,SAAS,CAAC,YAAY,CAAC,OAAO,YAAY;gBAC/C,UAAU;gBACV,aAAa;oBACZ,SAAS;oBACT,QAAQ;oBACR,QAAQ;wBACP,qBAAqB;4BACpB,SAAS;4BACT,YAAY;4BACZ,kCAAkC;4BAClC,QAAQ;gCACP,WAAW;gCACX,UAAU;gCACV,wBAAwB;oCACvB;wCACC,SAAS;wCACT,OAAO;wCACP,QAAQ;wCACR,YAAY;oCACb;oCACA;wCACC,SAAS;wCACT,OAAO;4CACN;4CACA;yCACA;wCACD,QAAQ;wCACR,YAAY;wCACZ,QAAQ;4CACP,eAAe;wCAChB;oCACD;iCACA;gCACD,YAAY;gCACZ,UAAU;gCACV,YAAY;gCACZ,eAAe;4BAChB;wBACD;wBACA,aAAa;4BACZ,SAAS;4BACT,OAAO;wBACR;oBACD;gBACD;YACD;YAEA,MAAM,KAAK,CAAC,GAAG,CAAC,mBAAmB,SAAU,GAAG;gBAC/C,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,GAAG;oBAC1B;gBACD;gBAEA,IAAI,aAAa;gBACjB,MAAM,SAAS,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,OAAO;YACpE;YAEA,MAAM,KAAK,CAAC,GAAG,CAAC,kBAAkB,SAAU,GAAG;gBAC9C,MAAM,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK;YAChE;QAED,CAAA,EAAE;QAEF,MAAM,SAAS,CAAC,MAAM,GAAG;YACxB,WAAW;gBACV,SAAS;gBACT,YAAY;gBACZ,QAAQ;YACT;YACA,wBAAwB;gBACvB,SAAS;gBACT,QAAQ;gBACR,QAAQ;oBACP,iBAAiB;wBAChB,sFAAsF;wBACtF,SAAS;wBACT,YAAY;wBACZ,QAAQ;4BACP,eAAe;gCACd,SAAS;gCACT,YAAY;4BACb;4BACA,qBAAqB;gCACpB,SAAS;gCACT,OAAO;4BACR;4BACA,MAAM;wBACP;oBACD;oBACA,UAAU;gBACX;YACD;YACA,wBAAwB;gBACvB,SAAS;gBACT,QAAQ;gBACR,OAAO;YACR;YACA,UAAU;gBACT,SAAS;gBACT,QAAQ;YACT;YACA,YAAY;gBACX,SAAS;gBACT,YAAY;YACb;YACA,cAAc;gBACb,SAAS;gBACT,YAAY;YACb;YACA,aAAa;gBACZ,SAAS;gBACT,YAAY;gBACZ,OAAO;oBAAC;oBAAc;iBAAc;gBACpC,QAAQ;oBACP,eAAe;gBAChB;YACD;YACA,WAAW;YACX,WAAW;YACX,WAAW;YACX,UAAU;YACV,YAAY;YACZ,eAAe;QAChB;QAEA,MAAM,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM;QAE3G,MAAM,SAAS,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,MAAM;QAE3C;;;;;KAKC,GACA,CAAA,SAAU,KAAK;YACf,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,SAAS;gBACtD,WAAW;oBACV,SAAS;oBACT,QAAQ;gBACT;gBACA,cAAc;oBACb,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBACP,eAAe;oBAChB;gBACD;gBACA,WAAW;gBACX,YAAY;gBACZ,eAAe;YAChB;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,YAAY;gBAChD,gBAAgB;oBACf,SAAS;oBACT,OAAO;gBACR;YACD;YAEA,IAAI,gBAAgB;gBACnB,SAAS;gBACT,YAAY;gBACZ,QAAQ;oBACP,WAAW;wBACV,SAAS;wBACT,YAAY;wBACZ,QAAQ,MAAM,SAAS,CAAC,IAAI;oBAC7B;oBACA,aAAa;wBACZ,SAAS;wBACT,OAAO;oBACR;gBACD;YACD;YAEA,OAAO,MAAM,SAAS,CAAC,IAAI,CAAC,QAAQ;YAEpC,IAAI,oBAAoB,QAAQ;gBAC/B,oDAAoD,MAAM;gBAC1D,sDAAsD,MAAM;gBAC5D,sDAAsD,MAAM;gBAC5D,0DAA0D,MAAM;gBAChE,kDAAkD,MAAM;aACxD,CAAC,IAAI,CAAC,OAAO;YAEd,IAAI,aAAa,sEAAsE,MAAM;YAE7F,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,WAAW;gBAC/C,iBAAiB;oBAChB;wBACC,SAAS,OAAO,KAAK,MAAM,GAAG,oBAAoB,mBAAmB,MAAM;wBAC3E,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,SAAS;wBACV;oBACD;oBACA;wBACC,SAAS;wBACT,YAAY;wBACZ,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,SAAS;wBACV;oBACD;iBACA;gBACD,YAAY;gBACZ,UAAU;oBACT;wBACC,SAAS,OAAO,YAAY,MAAM,GAAG;wBACrC,YAAY;wBACZ,QAAQ;oBACT;oBACA;wBACC,SAAS,OAAO,oBAAoB,MAAM,GAAG,aAAa,aAAa,MAAM;wBAC7E,YAAY;wBACZ,QAAQ;oBACT;iBACA;gBACD,qBAAqB;oBACpB,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBACP,YAAY;wBACZ,WAAW;wBACX,cAAc;wBACd,eAAe;oBAChB;gBACD;YACD;YAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,UAAU;gBAC9C,kBAAkB;oBACjB;wBACC,SAAS,OAAO,cAAc,MAAM,GAAG;wBACvC,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,UAAU;wBACX;oBACD;oBACA;wBACC,SAAS;wBACT,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,UAAU;wBACX;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,QAAQ;4BACP,aAAa;gCACZ,SAAS;gCACT,QAAQ;oCACP,UAAU;oCACV,eAAe;gCAChB;4BACD;4BACA,iBAAiB;4BACjB,UAAU;wBACX;oBACD;oBACA;wBACC,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,QAAQ;4BACP,aAAa;gCACZ,SAAS;gCACT,QAAQ;oCACP,UAAU;oCACV,eAAe;gCAChB;4BACD;4BACA,UAAU;wBACX;oBACD;iBACA;gBACD,mBAAmB;oBAClB;wBACC,SAAS,OAAO,KAAK,MAAM,GAAG;wBAC9B,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,WAAW;gCACV,SAAS;gCACT,OAAO;4BACR;wBACD;oBACD;oBACA;wBACC,SAAS;wBACT,QAAQ;wBACR,QAAQ;4BACP,iBAAiB;4BACjB,WAAW;gCACV,SAAS;gCACT,OAAO;4BACR;wBACD;oBACD;iBACA;YACF;YAEA,OAAO,MAAM,SAAS,CAAC,IAAI,CAAC,MAAM;YAElC,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,UAAU;gBAC9C,WAAW;gBACX,YAAY;YACb;YAEA,MAAM,SAAS,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI;QAC1C,CAAA,EAAE;QAEF,uCAAuC;QACvC,OAAO,KAAK,GAAG;QACf,OAAO;IACP,EAAE,WAAW;IAEb,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,wBAAwB;YACnC,WAAW;QACf;QACA,eAAe,6BAA6B;YACxC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,iBAAiB,OAAO;IAC9B,MAAM,mBAAmB,OAAO;IAEhC,MAAM,MAAM,CAAC,SAAW,OAAO,KAAK,IAAI,iBAAiB,UAAU,OAAO,KAAK,GAAG;IAElF,MAAM,eAAe,CAAC;QAClB,OAAO,cAAc,QAAQ,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC;IACnG;IAEA,MAAM,wBAAwB,CAAC;QAC3B,MAAM,OAAO,OAAO,SAAS,GAAG,OAAO,SAAS,CAAC,OAAO,KAAK;QAC7D,OAAO,aAAa,QAAQ,SAAS,IAAI,CAAC,QAAQ,SAAS,IAAI;IACnE;IACA,MAAM,mBAAmB,CAAC,QAAQ,UAAU;QACxC,MAAM,MAAM,OAAO,GAAG;QACtB,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,MAAM,OAAO,sBAAsB;YACnC,OAAO,SAAS,GAAG,CAAC,MAAM,CAAC;YAC3B,OAAO,KAAK,IAAI,CAAC;gBACb,OAAO,aAAa,CAAC,qCAAqC,WAAW,OAAO,OAAO;gBACnF,MAAM,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE;gBACtC,IAAI,SAAS,CAAC,QAAQ,MAAM;gBAC5B,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B,GAAG,CAAC;gBACA,IAAI,SAAS,CAAC,GAAG,SAAS,cAAc;gBACxC,EAAE,SAAS,GAAG;gBACd,IAAI,QAAQ,gBAAgB,CAAC;gBAC7B,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B;QACJ;IACJ;IACA,MAAM,iBAAiB,CAAC;QACpB,MAAM,OAAO,sBAAsB;QACnC,OAAO,KAAK,IAAI,CAAC,CAAC,IAAM,SAAS,IAAI,CAAC,EAAE,WAAW,GAAG,KAAK,CAAC;IAChE;IAEA,MAAM,eAAe,CAAC;QAClB,MAAM,mBAAmB;YACrB;gBAAE,MAAM;gBAAY,OAAO;YAAS;YACpC;gBAAE,MAAM;gBAAc,OAAO;YAAa;YAC1C;gBAAE,MAAM;gBAAO,OAAO;YAAM;YAC5B;gBAAE,MAAM;gBAAO,OAAO;YAAM;YAC5B;gBAAE,MAAM;gBAAQ,OAAO;YAAO;YAC9B;gBAAE,MAAM;gBAAU,OAAO;YAAS;YAClC;gBAAE,MAAM;gBAAQ,OAAO;YAAO;YAC9B;gBAAE,MAAM;gBAAK,OAAO;YAAI;YACxB;gBAAE,MAAM;gBAAM,OAAO;YAAS;YAC9B;gBAAE,MAAM;gBAAO,OAAO;YAAM;SAC/B;QACD,MAAM,kBAAkB,eAAe;QACvC,OAAO,kBAAkB,kBAAkB;IAC/C;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,OAAO,sBAAsB;QACnC,OAAO,KAAK,IAAI,CAAC,IAAM,UAAU,CAAC;YAC9B,MAAM,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC;YAClC,OAAO,UAAU,OAAO,CAAC,EAAE,GAAG;QAClC;IACJ;IAEA,MAAM,OAAO,CAAC;QACV,MAAM,YAAY,aAAa;QAC/B,MAAM,kBAAkB,KAAK,WAAW,IAAI,CAAC,SAAS,KAAK,CAAC,IAAM,EAAE,KAAK;QACzE,MAAM,kBAAkB,mBAAmB,QAAQ;QACnD,MAAM,cAAc,eAAe;QACnC,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,OAAO;oBACX;oBACA;wBACI,MAAM;wBACN,MAAM;wBACN,OAAO;oBACX;iBACH;YACL;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;gBACT,UAAU;gBACV,MAAM;YACV;YACA,UAAU,CAAC;gBACP,MAAM,OAAO,IAAI,OAAO;gBACxB,iBAAiB,QAAQ,KAAK,QAAQ,EAAE,KAAK,IAAI;gBACjD,IAAI,KAAK;YACb;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,cAAc;YAC5B,MAAM,OAAO,OAAO,SAAS,CAAC,OAAO;YACrC,IAAI,OAAO,SAAS,CAAC,WAAW,MAAM,aAAa,OAAO;gBACtD,KAAK;YACT,OACK;gBACD,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B;QACJ;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,cAAc,CAAC;YACrB,MAAM,MAAM,OAAO,GAAG;YACtB,MAAM,OAAO,IAAI,MAAM,CAAC,8BAA8B,EAAE,IAAI;YAC5D,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,eAAe,CAAC;gBAC1C,MAAM,OAAO,IAAI,WAAW;gBAC5B,IAAI,SAAS,CAAC,KAAK,SAAS,KAAK,IAAI,SAAS,CAAC,KAAK;gBACpD,IAAI,SAAS,CAAC,KAAK,mBAAmB;gBACtC,IAAI,SAAS,CAAC,KAAK,wBAAwB;gBAC3C,wBAAwB;gBACxB,IAAI;gBACJ,MAAQ,QAAQ,IAAI,UAAU,CAAG;oBAC7B,IAAI,WAAW,CAAC;gBACpB;gBACA,MAAM,UAAU,IAAI,GAAG,CAAC,KAAK;gBAC7B,wDAAwD;gBACxD,QAAQ,WAAW,GAAG;YAC1B;QACJ;QACA,OAAO,EAAE,CAAC,cAAc;YACpB,MAAM,MAAM,OAAO,GAAG;YACtB,MAAM,yBAAyB,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC;gBAC3D,OAAO,aAAa,QAAQ,IAAI,SAAS,CAAC,KAAK,4BAA4B;YAC/E;YACA,IAAI,uBAAuB,MAAM,EAAE;gBAC/B,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC;wBACjC,IAAI;wBACJ,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,MAAM,CAAC;4BAChC,IAAI,OAAO,CAAC,OAAO,MAAM,GAAG,cAAc,CAAC,OAAO;wBACtD;wBACA,IAAI,SAAS,GAAG,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACnF,IAAI,QAAQ,gBAAgB,CAAC;wBAC7B,IAAI,SAAS,CAAC,KAAK,wBAAwB;wBAC3C,IAAI,SAAS,GAAG,KAAK,IAAI,SAAS;oBACtC;gBACJ;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,WAAW;YACjB,OAAO,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC;gBAChC,IAAI;gBACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;oBAC1C,MAAM,OAAO,KAAK,CAAC,EAAE;oBACrB,MAAM,eAAe,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,EAAE,OAAO,CAAC,iBAAiB,CAAC;oBAC/G,IAAI,cAAc;wBACd,KAAK,IAAI,CAAC,mBAAmB;wBAC7B,KAAK,IAAI,CAAC,wBAAwB;oBACtC;gBACJ;YACJ;QACJ;IACJ;IAEA,MAAM,kBAAkB,SAAC;YAAQ,6EAAY;eAAS,CAAC;YACnD,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;gBAC1C,UAAU;YACd;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;;IACA,MAAM,wBAAwB,CAAC;QAC3B,MAAM,OAAO,OAAO,SAAS,CAAC,QAAQ;QACtC,OAAO,OAAO,GAAG,CAAC,EAAE,CAAC,MAAM;IAC/B;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc;YAC7C,MAAM;YACN,SAAS;YACT;YACA,SAAS,gBAAgB,QAAQ,CAAC;gBAC9B,IAAI,SAAS,CAAC,sBAAsB;YACxC;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,cAAc;YACzC,MAAM;YACN,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,cAAc,CAAC;YACxB,WAAW;YACX,MAAM;YACN,SAAS;YACT,WAAW;YACX,OAAO,EAAE,CAAC,YAAY,CAAC;gBACnB,IAAI,aAAa,GAAG,MAAM,GAAG;oBACzB,KAAK;gBACT;YACJ;QACJ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/codesample/index.js"], "sourcesContent": ["// Exports the \"codesample\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/codesample')\n//   ES2015:\n//     import 'tinymce/plugins/codesample'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,SAAS;AACT,cAAc;AACd,4CAA4C;AAC5C,YAAY;AACZ,0CAA0C", "ignoreList": [0], "debugId": null}}]}