import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Configuration pour TinyMCE côté client
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },
  // Permettre l'importation de TinyMCE
  transpilePackages: ['@tinymce/tinymce-react', 'tinymce'],
};

export default nextConfig;
