{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/lists/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType$1 = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => { };\n    /** Compose two unary functions. Similar to compose, but avoids using Function.prototype.apply. */\n    const compose1 = (fbc, fab) => (a) => fbc(fab(a));\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const tripleEquals = (a, b) => {\n        return a === b;\n    };\n    // eslint-disable-next-line prefer-arrow/prefer-arrow-functions\n    function curry(fn, ...initialArgs) {\n        return (...restArgs) => {\n            const all = initialArgs.concat(restArgs);\n            return fn.apply(null, all);\n        };\n    }\n    const not = (f) => (t) => !f(t);\n    const never = constant(false);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    /* eslint-enable */\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains$1 = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const filter$1 = (xs, pred) => {\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                r.push(x);\n            }\n        }\n        return r;\n    };\n    /*\n     * Groups an array into contiguous arrays of like elements. Whether an element is like or not depends on f.\n     *\n     * f is a function that derives a value from an element - e.g. true or false, or a string.\n     * Elements are like if this function generates the same value for them (according to ===).\n     *\n     *\n     * Order of the elements is preserved. Arr.flatten() on the result will return the original list, as with Haskell groupBy function.\n     *  For a good explanation, see the group function (which is a special case of groupBy)\n     *  http://hackage.haskell.org/package/base-4.7.0.0/docs/Data-List.html#v:group\n     */\n    const groupBy = (xs, f) => {\n        if (xs.length === 0) {\n            return [];\n        }\n        else {\n            let wasType = f(xs[0]); // initial case for matching\n            const r = [];\n            let group = [];\n            for (let i = 0, len = xs.length; i < len; i++) {\n                const x = xs[i];\n                const type = f(x);\n                if (type !== wasType) {\n                    r.push(group);\n                    group = [];\n                }\n                wasType = type;\n                group.push(x);\n            }\n            if (group.length !== 0) {\n                r.push(group);\n            }\n            return r;\n        }\n    };\n    const foldl = (xs, f, acc) => {\n        each$1(xs, (x, i) => {\n            acc = f(acc, x, i);\n        });\n        return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const reverse = (xs) => {\n        const r = nativeSlice.call(xs, 0);\n        r.reverse();\n        return r;\n    };\n    const get$1 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = (xs) => get$1(xs, 0);\n    const last = (xs) => get$1(xs, xs.length - 1);\n    const unique = (xs, comparator) => {\n        const r = [];\n        const isDuplicated = isFunction(comparator) ?\n            (x) => exists(r, (i) => comparator(i, x)) :\n            (x) => contains$1(r, x);\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (!isDuplicated(x)) {\n                r.push(x);\n            }\n        }\n        return r;\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const objAcc = (r) => (x, i) => {\n        r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n        each(obj, (x, i) => {\n            (pred(x, i) ? onTrue : onFalse)(x, i);\n        });\n    };\n    const filter = (obj, pred) => {\n        const t = {};\n        internalFilter(obj, pred, objAcc(t), noop);\n        return t;\n    };\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    // Use window object as the global if it's available since CSP will block script evals\n    // eslint-disable-next-line @typescript-eslint/no-implied-eval\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    /**\n     * **Is** the value stored inside this Optional object equal to `rhs`?\n     */\n    const is$2 = (lhs, rhs, comparator = tripleEquals) => lhs.exists((left) => comparator(left, rhs));\n    /**\n     * Are these two Optional objects equal? Equality here means either they're both\n     * `Some` (and the values are equal under the comparator) or they're both `None`.\n     */\n    const equals = (lhs, rhs, comparator = tripleEquals) => lift2(lhs, rhs, comparator).getOr(lhs.isNone() && rhs.isNone());\n    /*\n    Notes on the lift functions:\n    - We used to have a generic liftN, but we were concerned about its type-safety, and the below variants were faster in microbenchmarks.\n    - The getOrDie calls are partial functions, but are checked beforehand. This is faster and more convenient (but less safe) than folds.\n    - && is used instead of a loop for simplicity and performance.\n    */\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n\n    /** path :: ([String], JsObj?) -> JsObj */\n    const path = (parts, scope) => {\n        let o = scope !== undefined && scope !== null ? scope : Global;\n        for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n            o = o[parts[i]];\n        }\n        return o;\n    };\n    /** resolve :: (String, JsObj?) -> JsObj */\n    const resolve = (p, scope) => {\n        const parts = p.split('.');\n        return path(parts, scope);\n    };\n\n    const blank = (r) => (s) => s.replace(r, '');\n    /** removes all leading and trailing spaces */\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = (s) => s.length > 0;\n    const isEmpty$2 = (s) => !isNotEmpty(s);\n\n    const zeroWidth = '\\uFEFF';\n    const isZwsp = (char) => char === zeroWidth;\n\n    const fromHtml = (html, scope) => {\n        const doc = scope || document;\n        const div = doc.createElement('div');\n        div.innerHTML = html;\n        if (!div.hasChildNodes() || div.childNodes.length > 1) {\n            const message = 'HTML does not have a single root node';\n            // eslint-disable-next-line no-console\n            console.error(message, html);\n            throw new Error(message);\n        }\n        return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n        const doc = scope || document;\n        const node = doc.createElement(tag);\n        return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n        const doc = scope || document;\n        const node = doc.createTextNode(text);\n        return fromDom$1(node);\n    };\n    const fromDom$1 = (node) => {\n        // TODO: Consider removing this check, but left atm for safety\n        if (node === null || node === undefined) {\n            throw new Error('Node cannot be null or undefined');\n        }\n        return {\n            dom: node\n        };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    // tslint:disable-next-line:variable-name\n    const SugarElement = {\n        fromHtml,\n        fromTag,\n        fromText,\n        fromDom: fromDom$1,\n        fromPoint\n    };\n\n    const COMMENT = 8;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const is$1 = (element, selector) => {\n        const dom = element.dom;\n        if (dom.nodeType !== ELEMENT) {\n            return false;\n        }\n        else {\n            const elem = dom;\n            if (elem.matches !== undefined) {\n                return elem.matches(selector);\n            }\n            else if (elem.msMatchesSelector !== undefined) {\n                return elem.msMatchesSelector(selector);\n            }\n            else if (elem.webkitMatchesSelector !== undefined) {\n                return elem.webkitMatchesSelector(selector);\n            }\n            else if (elem.mozMatchesSelector !== undefined) {\n                // cast to any as mozMatchesSelector doesn't exist in TS DOM lib\n                return elem.mozMatchesSelector(selector);\n            }\n            else {\n                throw new Error('Browser lacks native selectors');\n            } // unfortunately we can't throw this on startup :(\n        }\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    // Returns: true if node e1 contains e2, otherwise false.\n    // (returns false if e1===e2: A node does not contain itself).\n    const contains = (e1, e2) => {\n        const d1 = e1.dom;\n        const d2 = e2.dom;\n        return d1 === d2 ? false : d1.contains(d2);\n    };\n    const is = is$1;\n\n    const unsafe = (name, scope) => {\n        return resolve(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n        const actual = unsafe(name, scope);\n        if (actual === undefined || actual === null) {\n            throw new Error(name + ' not available on this browser');\n        }\n        return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    /*\n     * IE9 and above\n     *\n     * MDN no use on this one, but here's the link anyway:\n     * https://developer.mozilla.org/en/docs/Web/API/HTMLElement\n     */\n    const sandHTMLElement = (scope) => {\n        return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = (x) => {\n        // use Resolve to get the window object for x and just return undefined if it can't find it.\n        // undefined scope later triggers using the global window.\n        const scope = resolve('ownerDocument.defaultView', x);\n        // TINY-7374: We can't rely on looking at the owner window HTMLElement as the element may have\n        // been constructed in a different window and then appended to the current window document.\n        return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const name = (element) => {\n        const r = element.dom.nodeName;\n        return r.toLowerCase();\n    };\n    const type = (element) => element.dom.nodeType;\n    const isType = (t) => (element) => type(element) === t;\n    const isComment = (element) => type(element) === COMMENT || name(element) === '#comment';\n    const isHTMLElement = (element) => isElement$1(element) && isPrototypeOf(element.dom);\n    const isElement$1 = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = (tag) => (e) => isElement$1(e) && name(e) === tag;\n\n    const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parentElement = (element) => Optional.from(element.dom.parentElement).map(SugarElement.fromDom);\n    const nextSibling = (element) => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children = (element) => map(element.dom.childNodes, SugarElement.fromDom);\n    const child = (element, index) => {\n        const cs = element.dom.childNodes;\n        return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = (element) => child(element, 0);\n    const lastChild = (element) => child(element, element.dom.childNodes.length - 1);\n\n    /**\n     * Is the element a ShadowRoot?\n     *\n     * Note: this is insufficient to test if any element is a shadow root, but it is sufficient to differentiate between\n     * a Document and a ShadowRoot.\n     */\n    const isShadowRoot = (dos) => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const getRootNode = (e) => SugarElement.fromDom(e.dom.getRootNode());\n    /** If this element is in a ShadowRoot, return it. */\n    const getShadowRoot = (e) => {\n        const r = getRootNode(e);\n        return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    /** Return the host of a ShadowRoot.\n     *\n     * This function will throw if Shadow DOM is unsupported in the browser, or if the host is null.\n     * If you actually have a ShadowRoot, this shouldn't happen.\n     */\n    const getShadowHost = (e) => SugarElement.fromDom(e.dom.host);\n\n    const before$1 = (marker, element) => {\n        const parent$1 = parent(marker);\n        parent$1.each((v) => {\n            v.dom.insertBefore(element.dom, marker.dom);\n        });\n    };\n    const after = (marker, element) => {\n        const sibling = nextSibling(marker);\n        sibling.fold(() => {\n            const parent$1 = parent(marker);\n            parent$1.each((v) => {\n                append$1(v, element);\n            });\n        }, (v) => {\n            before$1(v, element);\n        });\n    };\n    const prepend = (parent, element) => {\n        const firstChild$1 = firstChild(parent);\n        firstChild$1.fold(() => {\n            append$1(parent, element);\n        }, (v) => {\n            parent.dom.insertBefore(element.dom, v.dom);\n        });\n    };\n    const append$1 = (parent, element) => {\n        parent.dom.appendChild(element.dom);\n    };\n\n    const before = (marker, elements) => {\n        each$1(elements, (x) => {\n            before$1(marker, x);\n        });\n    };\n    const append = (parent, elements) => {\n        each$1(elements, (x) => {\n            append$1(parent, x);\n        });\n    };\n\n    const rawSet = (dom, key, value) => {\n        /*\n         * JQuery coerced everything to a string, and silently did nothing on text node/null/undefined.\n         *\n         * We fail on those invalid cases, only allowing numbers and booleans.\n         */\n        if (isString(value) || isBoolean(value) || isNumber(value)) {\n            dom.setAttribute(key, value + '');\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n            throw new Error('Attribute value was not simple');\n        }\n    };\n    const setAll = (element, attrs) => {\n        const dom = element.dom;\n        each(attrs, (v, k) => {\n            rawSet(dom, k, v);\n        });\n    };\n    const clone$1 = (element) => foldl(element.dom.attributes, (acc, attr) => {\n        acc[attr.name] = attr.value;\n        return acc;\n    }, {});\n\n    const empty = (element) => {\n        // shortcut \"empty node\" trick. Requires IE 9.\n        element.dom.textContent = '';\n        // If the contents was a single empty text node, the above doesn't remove it. But, it's still faster in general\n        // than removing every child node manually.\n        // The following is (probably) safe for performance as 99.9% of the time the trick works and\n        // Traverse.children will return an empty array.\n        each$1(children(element), (rogue) => {\n            remove(rogue);\n        });\n    };\n    const remove = (element) => {\n        const dom = element.dom;\n        if (dom.parentNode !== null) {\n            dom.parentNode.removeChild(dom);\n        }\n    };\n\n    const clone = (original, isDeep) => SugarElement.fromDom(original.dom.cloneNode(isDeep));\n    /** Deep clone - everything copied including children */\n    const deep = (original) => clone(original, true);\n    /** Shallow clone, with a new tag */\n    const shallowAs = (original, tag) => {\n        const nu = SugarElement.fromTag(tag);\n        const attributes = clone$1(original);\n        setAll(nu, attributes);\n        return nu;\n    };\n    /** Change the tag name, but keep all children */\n    const mutate = (original, tag) => {\n        const nu = shallowAs(original, tag);\n        after(original, nu);\n        const children$1 = children(original);\n        append(nu, children$1);\n        remove(original);\n        return nu;\n    };\n\n    const fromDom = (nodes) => map(nodes, SugarElement.fromDom);\n\n    // some elements, such as mathml, don't have style attributes\n    // others, such as angular elements, have style attributes that aren't a CSSStyleDeclaration\n    const isSupported = (dom) => \n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    // Node.contains() is very, very, very good performance\n    // http://jsperf.com/closest-vs-contains/5\n    const inBody = (element) => {\n        // Technically this is only required on IE, where contains() returns false for text nodes.\n        // But it's cheap enough to run everywhere and Sugar doesn't have platform detection (yet).\n        const dom = isText(element) ? element.dom.parentNode : element.dom;\n        // use ownerDocument.body to ensure this works inside iframes.\n        // Normally contains is bad because an element \"contains\" itself, but here we want that.\n        if (dom === undefined || dom === null || dom.ownerDocument === null) {\n            return false;\n        }\n        const doc = dom.ownerDocument;\n        return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    const internalSet = (dom, property, value) => {\n        // This is going to hurt. Apologies.\n        // JQuery coerces numbers to pixels for certain property names, and other times lets numbers through.\n        // we're going to be explicit; strings only.\n        if (!isString(value)) {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n            throw new Error('CSS value must be a string: ' + value);\n        }\n        // removed: support for dom().style[property] where prop is camel case instead of normal property name\n        if (isSupported(dom)) {\n            dom.style.setProperty(property, value);\n        }\n    };\n    const set = (element, property, value) => {\n        const dom = element.dom;\n        internalSet(dom, property, value);\n    };\n\n    const fromElements = (elements, scope) => {\n        const doc = scope || document;\n        const fragment = doc.createDocumentFragment();\n        each$1(elements, (element) => {\n            fragment.appendChild(element.dom);\n        });\n        return SugarElement.fromDom(fragment);\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n        if (is(scope, a)) {\n            return Optional.some(scope);\n        }\n        else if (isFunction(isRoot) && isRoot(scope)) {\n            return Optional.none();\n        }\n        else {\n            return ancestor(scope, a, isRoot);\n        }\n    };\n\n    const ancestor$3 = (scope, predicate, isRoot) => {\n        let element = scope.dom;\n        const stop = isFunction(isRoot) ? isRoot : never;\n        while (element.parentNode) {\n            element = element.parentNode;\n            const el = SugarElement.fromDom(element);\n            if (predicate(el)) {\n                return Optional.some(el);\n            }\n            else if (stop(el)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n        // This is required to avoid ClosestOrAncestor passing the predicate to itself\n        const is = (s, test) => test(s);\n        return ClosestOrAncestor(is, ancestor$3, scope, predicate, isRoot);\n    };\n\n    const ancestor$2 = (scope, selector, isRoot) => ancestor$3(scope, (e) => is$1(e, selector), isRoot);\n    // Returns Some(closest ancestor element (sugared)) matching 'selector' up to isRoot, or None() otherwise\n    const closest$1 = (scope, selector, isRoot) => {\n        const is = (element, selector) => is$1(element, selector);\n        return ClosestOrAncestor(is, ancestor$2, scope, selector, isRoot);\n    };\n\n    const closest = (target) => closest$1(target, '[contenteditable]');\n    const isEditable = (element, assumeEditable = false) => {\n        if (inBody(element)) {\n            return element.dom.isContentEditable;\n        }\n        else {\n            // Find the closest contenteditable element and check if it's editable\n            return closest(element).fold(constant(assumeEditable), (editable) => getRaw(editable) === 'true');\n        }\n    };\n    const getRaw = (element) => element.dom.contentEditable;\n\n    const ancestor$1 = (scope, predicate, isRoot) => ancestor$3(scope, predicate, isRoot).isSome();\n\n    const ancestor = (element, target) => ancestor$1(element, curry(eq, target));\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const matchNodeName = (name) => (node) => isNonNullable(node) && node.nodeName.toLowerCase() === name;\n    const matchNodeNames = (regex) => (node) => isNonNullable(node) && regex.test(node.nodeName);\n    const isTextNode$1 = (node) => isNonNullable(node) && node.nodeType === 3;\n    const isElement = (node) => isNonNullable(node) && node.nodeType === 1;\n    const isListNode = matchNodeNames(/^(OL|UL|DL)$/);\n    const isOlUlNode = matchNodeNames(/^(OL|UL)$/);\n    const isOlNode = matchNodeName('ol');\n    const isListItemNode = matchNodeNames(/^(LI|DT|DD)$/);\n    const isDlItemNode = matchNodeNames(/^(DT|DD)$/);\n    const isTableCellNode = matchNodeNames(/^(TH|TD)$/);\n    const isBr = matchNodeName('br');\n    const isFirstChild = (node) => { var _a; return ((_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.firstChild) === node; };\n    const isTextBlock = (editor, node) => isNonNullable(node) && node.nodeName in editor.schema.getTextBlockElements();\n    const isBlock = (node, blockElements) => isNonNullable(node) && node.nodeName in blockElements;\n    const isVoid = (editor, node) => isNonNullable(node) && node.nodeName in editor.schema.getVoidElements();\n    const isBogusBr = (dom, node) => {\n        if (!isBr(node)) {\n            return false;\n        }\n        return dom.isBlock(node.nextSibling) && !isBr(node.previousSibling);\n    };\n    const isEmpty$1 = (dom, elm, keepBookmarks) => {\n        const empty = dom.isEmpty(elm);\n        if (keepBookmarks && dom.select('span[data-mce-type=bookmark]', elm).length > 0) {\n            return false;\n        }\n        return empty;\n    };\n    const isChildOfBody = (dom, elm) => dom.isChildOf(elm, dom.getRoot());\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$3 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('lists_indent_on_tab', {\n            processor: 'boolean',\n            default: true\n        });\n    };\n    const shouldIndentOnTab = option('lists_indent_on_tab');\n    const getForcedRootBlock = option('forced_root_block');\n    const getForcedRootBlockAttrs = option('forced_root_block_attrs');\n\n    const createTextBlock = (editor, contentNode, attrs = {}) => {\n        const dom = editor.dom;\n        const blockElements = editor.schema.getBlockElements();\n        const fragment = dom.createFragment();\n        const blockName = getForcedRootBlock(editor);\n        const blockAttrs = getForcedRootBlockAttrs(editor);\n        let node;\n        let textBlock;\n        let hasContentNode = false;\n        textBlock = dom.create(blockName, {\n            ...blockAttrs,\n            ...(attrs.style ? { style: attrs.style } : {})\n        });\n        if (!isBlock(contentNode.firstChild, blockElements)) {\n            fragment.appendChild(textBlock);\n        }\n        while ((node = contentNode.firstChild)) {\n            const nodeName = node.nodeName;\n            if (!hasContentNode && (nodeName !== 'SPAN' || node.getAttribute('data-mce-type') !== 'bookmark')) {\n                hasContentNode = true;\n            }\n            if (isBlock(node, blockElements)) {\n                fragment.appendChild(node);\n                textBlock = null;\n            }\n            else {\n                if (!textBlock) {\n                    textBlock = dom.create(blockName, blockAttrs);\n                    fragment.appendChild(textBlock);\n                }\n                textBlock.appendChild(node);\n            }\n        }\n        // BR is needed in empty blocks\n        if (!hasContentNode && textBlock) {\n            textBlock.appendChild(dom.create('br', { 'data-mce-bogus': '1' }));\n        }\n        return fragment;\n    };\n\n    const DOM$2 = global$3.DOM;\n    const splitList = (editor, list, li) => {\n        const removeAndKeepBookmarks = (targetNode) => {\n            const parent = targetNode.parentNode;\n            if (parent) {\n                global$2.each(bookmarks, (node) => {\n                    parent.insertBefore(node, li.parentNode);\n                });\n            }\n            DOM$2.remove(targetNode);\n        };\n        const bookmarks = DOM$2.select('span[data-mce-type=\"bookmark\"]', list);\n        const newBlock = createTextBlock(editor, li);\n        const tmpRng = DOM$2.createRng();\n        tmpRng.setStartAfter(li);\n        tmpRng.setEndAfter(list);\n        const fragment = tmpRng.extractContents();\n        for (let node = fragment.firstChild; node; node = node.firstChild) {\n            if (node.nodeName === 'LI' && editor.dom.isEmpty(node)) {\n                DOM$2.remove(node);\n                break;\n            }\n        }\n        if (!editor.dom.isEmpty(fragment)) {\n            DOM$2.insertAfter(fragment, list);\n        }\n        DOM$2.insertAfter(newBlock, list);\n        const parent = li.parentElement;\n        if (parent && isEmpty$1(editor.dom, parent)) {\n            removeAndKeepBookmarks(parent);\n        }\n        DOM$2.remove(li);\n        if (isEmpty$1(editor.dom, list)) {\n            DOM$2.remove(list);\n        }\n    };\n\n    const isDescriptionDetail = isTag('dd');\n    const isDescriptionTerm = isTag('dt');\n    const outdentDlItem = (editor, item) => {\n        if (isDescriptionDetail(item)) {\n            mutate(item, 'dt');\n        }\n        else if (isDescriptionTerm(item)) {\n            parentElement(item).each((dl) => splitList(editor, dl.dom, item.dom));\n        }\n    };\n    const indentDlItem = (item) => {\n        if (isDescriptionTerm(item)) {\n            mutate(item, 'dd');\n        }\n    };\n    const dlIndentation = (editor, indentation, dlItems) => {\n        if (indentation === \"Indent\" /* Indentation.Indent */) {\n            each$1(dlItems, indentDlItem);\n        }\n        else {\n            each$1(dlItems, (item) => outdentDlItem(editor, item));\n        }\n    };\n\n    const getNormalizedPoint = (container, offset) => {\n        if (isTextNode$1(container)) {\n            return { container, offset };\n        }\n        const node = global$6.getNode(container, offset);\n        if (isTextNode$1(node)) {\n            return {\n                container: node,\n                offset: offset >= container.childNodes.length ? node.data.length : 0\n            };\n        }\n        else if (node.previousSibling && isTextNode$1(node.previousSibling)) {\n            return {\n                container: node.previousSibling,\n                offset: node.previousSibling.data.length\n            };\n        }\n        else if (node.nextSibling && isTextNode$1(node.nextSibling)) {\n            return {\n                container: node.nextSibling,\n                offset: 0\n            };\n        }\n        return { container, offset };\n    };\n    const normalizeRange = (rng) => {\n        const outRng = rng.cloneRange();\n        const rangeStart = getNormalizedPoint(rng.startContainer, rng.startOffset);\n        outRng.setStart(rangeStart.container, rangeStart.offset);\n        const rangeEnd = getNormalizedPoint(rng.endContainer, rng.endOffset);\n        outRng.setEnd(rangeEnd.container, rangeEnd.offset);\n        return outRng;\n    };\n\n    const listNames = ['OL', 'UL', 'DL'];\n    const listSelector = listNames.join(',');\n    const getParentList = (editor, node) => {\n        const selectionStart = node || editor.selection.getStart(true);\n        return editor.dom.getParent(selectionStart, listSelector, getClosestListHost(editor, selectionStart));\n    };\n    const isParentListSelected = (parentList, selectedBlocks) => isNonNullable(parentList) && selectedBlocks.length === 1 && selectedBlocks[0] === parentList;\n    const findSubLists = (parentList) => filter$1(parentList.querySelectorAll(listSelector), isListNode);\n    const getSelectedSubLists = (editor) => {\n        const parentList = getParentList(editor);\n        const selectedBlocks = editor.selection.getSelectedBlocks();\n        if (isParentListSelected(parentList, selectedBlocks)) {\n            return findSubLists(parentList);\n        }\n        else {\n            return filter$1(selectedBlocks, (elm) => {\n                return isListNode(elm) && parentList !== elm;\n            });\n        }\n    };\n    const findParentListItemsNodes = (editor, elms) => {\n        const listItemsElms = global$2.map(elms, (elm) => {\n            const parentLi = editor.dom.getParent(elm, 'li,dd,dt', getClosestListHost(editor, elm));\n            return parentLi ? parentLi : elm;\n        });\n        return unique(listItemsElms);\n    };\n    const getSelectedListItems = (editor) => {\n        const selectedBlocks = editor.selection.getSelectedBlocks();\n        return filter$1(findParentListItemsNodes(editor, selectedBlocks), isListItemNode);\n    };\n    const getSelectedDlItems = (editor) => filter$1(getSelectedListItems(editor), isDlItemNode);\n    const getClosestEditingHost = (editor, elm) => {\n        const parentTableCell = editor.dom.getParents(elm, 'TD,TH');\n        return parentTableCell.length > 0 ? parentTableCell[0] : editor.getBody();\n    };\n    const isListHost = (schema, node) => !isListNode(node) && !isListItemNode(node) && exists(listNames, (listName) => schema.isValidChild(node.nodeName, listName));\n    const getClosestListHost = (editor, elm) => {\n        const parentBlocks = editor.dom.getParents(elm, editor.dom.isBlock);\n        const isNotForcedRootBlock = (elm) => elm.nodeName.toLowerCase() !== getForcedRootBlock(editor);\n        const parentBlock = find(parentBlocks, (elm) => isNotForcedRootBlock(elm) && isListHost(editor.schema, elm));\n        return parentBlock.getOr(editor.getBody());\n    };\n    const isListInsideAnLiWithFirstAndLastNotListElement = (list) => parent(list).exists((parent) => isListItemNode(parent.dom)\n        && firstChild(parent).exists((firstChild) => !isListNode(firstChild.dom))\n        && lastChild(parent).exists((lastChild) => !isListNode(lastChild.dom)));\n    const findLastParentListNode = (editor, elm) => {\n        const parentLists = editor.dom.getParents(elm, 'ol,ul', getClosestListHost(editor, elm));\n        return last(parentLists);\n    };\n    const getSelectedLists = (editor) => {\n        const firstList = findLastParentListNode(editor, editor.selection.getStart());\n        const subsequentLists = filter$1(editor.selection.getSelectedBlocks(), isOlUlNode);\n        return firstList.toArray().concat(subsequentLists);\n    };\n    const getParentLists = (editor) => {\n        const elm = editor.selection.getStart();\n        return editor.dom.getParents(elm, 'ol,ul', getClosestListHost(editor, elm));\n    };\n    const getSelectedListRoots = (editor) => {\n        const selectedLists = getSelectedLists(editor);\n        const parentLists = getParentLists(editor);\n        return find(parentLists, (p) => isListInsideAnLiWithFirstAndLastNotListElement(SugarElement.fromDom(p))).fold(() => getUniqueListRoots(editor, selectedLists), (l) => [l]);\n    };\n    const getUniqueListRoots = (editor, lists) => {\n        const listRoots = map(lists, (list) => findLastParentListNode(editor, list).getOr(list));\n        return unique(listRoots);\n    };\n\n    const isCustomList = (list) => /\\btox\\-/.test(list.className);\n    const inList = (parents, listName) => findUntil(parents, isListNode, isTableCellNode)\n        .exists((list) => list.nodeName === listName && !isCustomList(list));\n    // Advlist/core/ListUtils.ts - Duplicated in Advlist plugin\n    const isWithinNonEditable = (editor, element) => element !== null && !editor.dom.isEditable(element);\n    const selectionIsWithinNonEditableList = (editor) => {\n        const parentList = getParentList(editor);\n        return isWithinNonEditable(editor, parentList) || !editor.selection.isEditable();\n    };\n    const isWithinNonEditableList = (editor, element) => {\n        const parentList = editor.dom.getParent(element, 'ol,ul,dl');\n        return isWithinNonEditable(editor, parentList) || !editor.selection.isEditable();\n    };\n    const setNodeChangeHandler = (editor, nodeChangeHandler) => {\n        const initialNode = editor.selection.getNode();\n        // Set the initial state\n        nodeChangeHandler({\n            parents: editor.dom.getParents(initialNode),\n            element: initialNode\n        });\n        editor.on('NodeChange', nodeChangeHandler);\n        return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n\n    const fireListEvent = (editor, action, element) => editor.dispatch('ListMutation', { action, element });\n\n    const isList = (el) => is(el, 'OL,UL');\n    const isListItem = (el) => is(el, 'LI');\n    const hasFirstChildList = (el) => firstChild(el).exists(isList);\n    const hasLastChildList = (el) => lastChild(el).exists(isList);\n\n    const isEntryList = (entry) => 'listAttributes' in entry;\n    const isEntryComment = (entry) => 'isComment' in entry;\n    const isEntryFragment = (entry) => 'isFragment' in entry;\n    const isIndented = (entry) => entry.depth > 0;\n    const isSelected = (entry) => entry.isSelected;\n    const cloneItemContent = (li) => {\n        const children$1 = children(li);\n        const content = hasLastChildList(li) ? children$1.slice(0, -1) : children$1;\n        return map(content, deep);\n    };\n    const createEntry = (li, depth, isSelected) => parent(li).filter(isElement$1).map((list) => ({\n        depth,\n        dirty: false,\n        isSelected,\n        content: cloneItemContent(li),\n        itemAttributes: clone$1(li),\n        listAttributes: clone$1(list),\n        listType: name(list),\n        isInPreviousLi: false\n    }));\n\n    const joinSegment = (parent, child) => {\n        append$1(parent.item, child.list);\n    };\n    const joinSegments = (segments) => {\n        for (let i = 1; i < segments.length; i++) {\n            joinSegment(segments[i - 1], segments[i]);\n        }\n    };\n    const appendSegments = (head$1, tail) => {\n        lift2(last(head$1), head(tail), joinSegment);\n    };\n    const createSegment = (scope, listType) => {\n        const segment = {\n            list: SugarElement.fromTag(listType, scope),\n            item: SugarElement.fromTag('li', scope)\n        };\n        append$1(segment.list, segment.item);\n        return segment;\n    };\n    const createSegments = (scope, entry, size) => {\n        const segments = [];\n        for (let i = 0; i < size; i++) {\n            segments.push(createSegment(scope, isEntryList(entry) ? entry.listType : entry.parentListType));\n        }\n        return segments;\n    };\n    const populateSegments = (segments, entry) => {\n        for (let i = 0; i < segments.length - 1; i++) {\n            set(segments[i].item, 'list-style-type', 'none');\n        }\n        last(segments).each((segment) => {\n            if (isEntryList(entry)) {\n                setAll(segment.list, entry.listAttributes);\n                setAll(segment.item, entry.itemAttributes);\n            }\n            append(segment.item, entry.content);\n        });\n    };\n    const normalizeSegment = (segment, entry) => {\n        if (name(segment.list) !== entry.listType) {\n            segment.list = mutate(segment.list, entry.listType);\n        }\n        setAll(segment.list, entry.listAttributes);\n    };\n    const createItem = (scope, attr, content) => {\n        const item = SugarElement.fromTag('li', scope);\n        setAll(item, attr);\n        append(item, content);\n        return item;\n    };\n    const appendItem = (segment, item) => {\n        append$1(segment.list, item);\n        segment.item = item;\n    };\n    const writeShallow = (scope, cast, entry) => {\n        const newCast = cast.slice(0, entry.depth);\n        last(newCast).each((segment) => {\n            if (isEntryList(entry)) {\n                const item = createItem(scope, entry.itemAttributes, entry.content);\n                appendItem(segment, item);\n                normalizeSegment(segment, entry);\n            }\n            else if (isEntryFragment(entry)) {\n                append(segment.item, entry.content);\n            }\n            else {\n                const item = SugarElement.fromHtml(`<!--${entry.content}-->`);\n                append$1(segment.list, item);\n            }\n        });\n        return newCast;\n    };\n    const writeDeep = (scope, cast, entry) => {\n        const segments = createSegments(scope, entry, entry.depth - cast.length);\n        joinSegments(segments);\n        populateSegments(segments, entry);\n        appendSegments(cast, segments);\n        return cast.concat(segments);\n    };\n    const composeList = (scope, entries) => {\n        let firstCommentEntryOpt = Optional.none();\n        const cast = foldl(entries, (cast, entry, i) => {\n            if (!isEntryComment(entry)) {\n                return entry.depth > cast.length ? writeDeep(scope, cast, entry) : writeShallow(scope, cast, entry);\n            }\n            else {\n                // this is needed becuase if the first element of the list is a comment we would not have the data to create the new list\n                if (i === 0) {\n                    firstCommentEntryOpt = Optional.some(entry);\n                    return cast;\n                }\n                return writeShallow(scope, cast, entry);\n            }\n        }, []);\n        firstCommentEntryOpt.each((firstCommentEntry) => {\n            const item = SugarElement.fromHtml(`<!--${firstCommentEntry.content}-->`);\n            head(cast).each((fistCast) => {\n                prepend(fistCast.list, item);\n            });\n        });\n        return head(cast).map((segment) => segment.list);\n    };\n\n    const indentEntry = (indentation, entry) => {\n        switch (indentation) {\n            case \"Indent\" /* Indentation.Indent */:\n                entry.depth++;\n                break;\n            case \"Outdent\" /* Indentation.Outdent */:\n                entry.depth--;\n                break;\n            case \"Flatten\" /* Indentation.Flatten */:\n                entry.depth = 0;\n        }\n        entry.dirty = true;\n    };\n\n    const cloneListProperties = (target, source) => {\n        if (isEntryList(target) && isEntryList(source)) {\n            target.listType = source.listType;\n            target.listAttributes = { ...source.listAttributes };\n        }\n    };\n    const cleanListProperties = (entry) => {\n        // Remove the start attribute if generating a new list\n        entry.listAttributes = filter(entry.listAttributes, (_value, key) => key !== 'start');\n    };\n    // Closest entry above/below in the same list\n    const closestSiblingEntry = (entries, start) => {\n        const depth = entries[start].depth;\n        // Ignore dirty items as they've been moved and won't have the right list data yet\n        const matches = (entry) => entry.depth === depth && !entry.dirty;\n        const until = (entry) => entry.depth < depth;\n        // Check in reverse to see if there's an entry as the same depth before the current entry\n        // but if not, then try to walk forwards as well\n        return findUntil(reverse(entries.slice(0, start)), matches, until)\n            .orThunk(() => findUntil(entries.slice(start + 1), matches, until));\n    };\n    const normalizeEntries = (entries) => {\n        each$1(entries, (entry, i) => {\n            closestSiblingEntry(entries, i).fold(() => {\n                if (entry.dirty && isEntryList(entry)) {\n                    cleanListProperties(entry);\n                }\n            }, (matchingEntry) => cloneListProperties(entry, matchingEntry));\n        });\n        return entries;\n    };\n\n    const parseSingleItem = (depth, itemSelection, selectionState, item) => {\n        var _a;\n        if (isComment(item)) {\n            return [{\n                    depth: depth + 1,\n                    content: (_a = item.dom.nodeValue) !== null && _a !== void 0 ? _a : '',\n                    dirty: false,\n                    isSelected: false,\n                    isComment: true\n                }];\n        }\n        itemSelection.each((selection) => {\n            if (eq(selection.start, item)) {\n                selectionState.set(true);\n            }\n        });\n        const currentItemEntry = createEntry(item, depth, selectionState.get());\n        // Update selectionState (end)\n        itemSelection.each((selection) => {\n            if (eq(selection.end, item)) {\n                selectionState.set(false);\n            }\n        });\n        const childListEntries = lastChild(item)\n            .filter(isList)\n            .map((list) => parseList(depth, itemSelection, selectionState, list))\n            .getOr([]);\n        return currentItemEntry.toArray().concat(childListEntries);\n    };\n    const parseItem = (depth, itemSelection, selectionState, item) => firstChild(item).filter(isList).fold(() => parseSingleItem(depth, itemSelection, selectionState, item), (list) => {\n        const parsedSiblings = foldl(children(item), (acc, liChild, i) => {\n            if (i === 0) {\n                return acc;\n            }\n            else {\n                if (isListItem(liChild)) {\n                    return acc.concat(parseSingleItem(depth, itemSelection, selectionState, liChild));\n                }\n                else {\n                    const fragment = {\n                        isFragment: true,\n                        depth,\n                        content: [liChild],\n                        isSelected: false,\n                        dirty: false,\n                        parentListType: name(list)\n                    };\n                    return acc.concat(fragment);\n                }\n            }\n        }, []);\n        return parseList(depth, itemSelection, selectionState, list).concat(parsedSiblings);\n    });\n    const parseList = (depth, itemSelection, selectionState, list) => bind(children(list), (element) => {\n        const parser = isList(element) ? parseList : parseItem;\n        const newDepth = depth + 1;\n        return parser(newDepth, itemSelection, selectionState, element);\n    });\n    const parseLists = (lists, itemSelection) => {\n        const selectionState = Cell(false);\n        const initialDepth = 0;\n        return map(lists, (list) => ({\n            sourceList: list,\n            entries: parseList(initialDepth, itemSelection, selectionState, list)\n        }));\n    };\n\n    const outdentedComposer = (editor, entries) => {\n        const normalizedEntries = normalizeEntries(entries);\n        return map(normalizedEntries, (entry) => {\n            const content = !isEntryComment(entry)\n                ? fromElements(entry.content)\n                : fromElements([SugarElement.fromHtml(`<!--${entry.content}-->`)]);\n            const listItemAttrs = isEntryList(entry) ? entry.itemAttributes : {};\n            return SugarElement.fromDom(createTextBlock(editor, content.dom, listItemAttrs));\n        });\n    };\n    const indentedComposer = (editor, entries) => {\n        const normalizedEntries = normalizeEntries(entries);\n        return composeList(editor.contentDocument, normalizedEntries).toArray();\n    };\n    const composeEntries = (editor, entries) => bind(groupBy(entries, isIndented), (entries) => {\n        const groupIsIndented = head(entries).exists(isIndented);\n        return groupIsIndented ? indentedComposer(editor, entries) : outdentedComposer(editor, entries);\n    });\n    const indentSelectedEntries = (entries, indentation) => {\n        each$1(filter$1(entries, isSelected), (entry) => indentEntry(indentation, entry));\n    };\n    const getItemSelection = (editor) => {\n        const selectedListItems = map(getSelectedListItems(editor), SugarElement.fromDom);\n        return lift2(find(selectedListItems, not(hasFirstChildList)), find(reverse(selectedListItems), not(hasFirstChildList)), (start, end) => ({ start, end }));\n    };\n    const listIndentation = (editor, lists, indentation) => {\n        const entrySets = parseLists(lists, getItemSelection(editor));\n        each$1(entrySets, (entrySet) => {\n            indentSelectedEntries(entrySet.entries, indentation);\n            const composedLists = composeEntries(editor, entrySet.entries);\n            each$1(composedLists, (composedList) => {\n                fireListEvent(editor, indentation === \"Indent\" /* Indentation.Indent */ ? \"IndentList\" /* ListAction.IndentList */ : \"OutdentList\" /* ListAction.OutdentList */, composedList.dom);\n            });\n            before(entrySet.sourceList, composedLists);\n            remove(entrySet.sourceList);\n        });\n    };\n\n    const selectionIndentation = (editor, indentation) => {\n        const lists = fromDom(getSelectedListRoots(editor));\n        const dlItems = fromDom(getSelectedDlItems(editor));\n        let isHandled = false;\n        if (lists.length || dlItems.length) {\n            const bookmark = editor.selection.getBookmark();\n            listIndentation(editor, lists, indentation);\n            dlIndentation(editor, indentation, dlItems);\n            editor.selection.moveToBookmark(bookmark);\n            editor.selection.setRng(normalizeRange(editor.selection.getRng()));\n            editor.nodeChanged();\n            isHandled = true;\n        }\n        return isHandled;\n    };\n    const handleIndentation = (editor, indentation) => !selectionIsWithinNonEditableList(editor) && selectionIndentation(editor, indentation);\n    const indentListSelection = (editor) => handleIndentation(editor, \"Indent\" /* Indentation.Indent */);\n    const outdentListSelection = (editor) => handleIndentation(editor, \"Outdent\" /* Indentation.Outdent */);\n    const flattenListSelection = (editor) => handleIndentation(editor, \"Flatten\" /* Indentation.Flatten */);\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.BookmarkManager');\n\n    const DOM$1 = global$3.DOM;\n    /**\n     * Returns a range bookmark. This will convert indexed bookmarks into temporary span elements with\n     * index 0 so that they can be restored properly after the DOM has been modified. Text bookmarks will not have spans\n     * added to them since they can be restored after a dom operation.\n     *\n     * So this: <p><b>|</b><b>|</b></p>\n     * becomes: <p><b><span data-mce-type=\"bookmark\">|</span></b><b data-mce-type=\"bookmark\">|</span></b></p>\n     *\n     * @param  {DOMRange} rng DOM Range to get bookmark on.\n     * @return {Object} Bookmark object.\n     */\n    const createBookmark = (rng) => {\n        const bookmark = {};\n        const setupEndPoint = (start) => {\n            let container = rng[start ? 'startContainer' : 'endContainer'];\n            let offset = rng[start ? 'startOffset' : 'endOffset'];\n            if (isElement(container)) {\n                const offsetNode = DOM$1.create('span', { 'data-mce-type': 'bookmark' });\n                if (container.hasChildNodes()) {\n                    offset = Math.min(offset, container.childNodes.length - 1);\n                    if (start) {\n                        container.insertBefore(offsetNode, container.childNodes[offset]);\n                    }\n                    else {\n                        DOM$1.insertAfter(offsetNode, container.childNodes[offset]);\n                    }\n                }\n                else {\n                    container.appendChild(offsetNode);\n                }\n                container = offsetNode;\n                offset = 0;\n            }\n            bookmark[start ? 'startContainer' : 'endContainer'] = container;\n            bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n        };\n        setupEndPoint(true);\n        if (!rng.collapsed) {\n            setupEndPoint();\n        }\n        return bookmark;\n    };\n    const resolveBookmark = (bookmark) => {\n        const restoreEndPoint = (start) => {\n            const nodeIndex = (container) => {\n                var _a;\n                let node = (_a = container.parentNode) === null || _a === void 0 ? void 0 : _a.firstChild;\n                let idx = 0;\n                while (node) {\n                    if (node === container) {\n                        return idx;\n                    }\n                    // Skip data-mce-type=bookmark nodes\n                    if (!isElement(node) || node.getAttribute('data-mce-type') !== 'bookmark') {\n                        idx++;\n                    }\n                    node = node.nextSibling;\n                }\n                return -1;\n            };\n            let container = bookmark[start ? 'startContainer' : 'endContainer'];\n            let offset = bookmark[start ? 'startOffset' : 'endOffset'];\n            if (!container) {\n                return;\n            }\n            if (isElement(container) && container.parentNode) {\n                const node = container;\n                offset = nodeIndex(container);\n                container = container.parentNode;\n                DOM$1.remove(node);\n                if (!container.hasChildNodes() && DOM$1.isBlock(container)) {\n                    container.appendChild(DOM$1.create('br'));\n                }\n            }\n            bookmark[start ? 'startContainer' : 'endContainer'] = container;\n            bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n        };\n        restoreEndPoint(true);\n        restoreEndPoint();\n        const rng = DOM$1.createRng();\n        rng.setStart(bookmark.startContainer, bookmark.startOffset);\n        if (bookmark.endContainer) {\n            rng.setEnd(bookmark.endContainer, bookmark.endOffset);\n        }\n        return normalizeRange(rng);\n    };\n\n    const listToggleActionFromListName = (listName) => {\n        switch (listName) {\n            case 'UL': return \"ToggleUlList\" /* ListAction.ToggleUlList */;\n            case 'OL': return \"ToggleOlList\" /* ListAction.ToggleOlList */;\n            case 'DL': return \"ToggleDLList\" /* ListAction.ToggleDLList */;\n        }\n    };\n\n    const updateListStyle = (dom, el, detail) => {\n        const type = detail['list-style-type'] ? detail['list-style-type'] : null;\n        dom.setStyle(el, 'list-style-type', type);\n    };\n    const setAttribs = (elm, attrs) => {\n        global$2.each(attrs, (value, key) => {\n            elm.setAttribute(key, value);\n        });\n    };\n    const updateListAttrs = (dom, el, detail) => {\n        setAttribs(el, detail['list-attributes']);\n        global$2.each(dom.select('li', el), (li) => {\n            setAttribs(li, detail['list-item-attributes']);\n        });\n    };\n    const updateListWithDetails = (dom, el, detail) => {\n        updateListStyle(dom, el, detail);\n        updateListAttrs(dom, el, detail);\n    };\n    const removeStyles = (dom, element, styles) => {\n        global$2.each(styles, (style) => dom.setStyle(element, style, ''));\n    };\n    const isInline = (editor, node) => isNonNullable(node) && !isBlock(node, editor.schema.getBlockElements());\n    const getEndPointNode = (editor, rng, start, root) => {\n        let container = rng[start ? 'startContainer' : 'endContainer'];\n        const offset = rng[start ? 'startOffset' : 'endOffset'];\n        // Resolve node index\n        if (isElement(container)) {\n            container = container.childNodes[Math.min(offset, container.childNodes.length - 1)] || container;\n        }\n        if (!start && isBr(container.nextSibling)) {\n            container = container.nextSibling;\n        }\n        const findBlockAncestor = (node) => {\n            while (!editor.dom.isBlock(node) && node.parentNode && root !== node) {\n                node = node.parentNode;\n            }\n            return node;\n        };\n        // The reason why the next two if statements exist is because when the root node is a table cell (possibly some other node types)\n        // then the highest we can go up the dom hierarchy is one level below the table cell.\n        // So what happens when we have a bunch of inline nodes and text nodes in the table cell\n        // and when the selection is collapsed inside one of the inline nodes then only that inline node (or text node) will be included\n        // in the created list because that would be one level below td node and the other inline nodes won't be included.\n        // So the fix proposed is to traverse left when looking for start node (and traverse right when looking for end node)\n        // and keep traversing as long as we have an inline or text node (same for traversing right).\n        // This way we end up including all the inline elements in the created list.\n        // For more info look at #TINY-6853\n        const findBetterContainer = (container, forward) => {\n            var _a;\n            const walker = new global$5(container, findBlockAncestor(container));\n            const dir = forward ? 'next' : 'prev';\n            let node;\n            while ((node = walker[dir]())) {\n                if (!(isVoid(editor, node) || isZwsp(node.textContent) || ((_a = node.textContent) === null || _a === void 0 ? void 0 : _a.length) === 0)) {\n                    return Optional.some(node);\n                }\n            }\n            return Optional.none();\n        };\n        // Traverse left to include inline/text nodes\n        if (start && isTextNode$1(container)) {\n            if (isZwsp(container.textContent)) {\n                container = findBetterContainer(container, false).getOr(container);\n            }\n            else {\n                if (container.parentNode !== null && isInline(editor, container.parentNode)) {\n                    container = container.parentNode;\n                }\n                while (container.previousSibling !== null && (isInline(editor, container.previousSibling) || isTextNode$1(container.previousSibling))) {\n                    container = container.previousSibling;\n                }\n            }\n        }\n        // Traverse right to include inline/text nodes\n        if (!start && isTextNode$1(container)) {\n            if (isZwsp(container.textContent)) {\n                container = findBetterContainer(container, true).getOr(container);\n            }\n            else {\n                if (container.parentNode !== null && isInline(editor, container.parentNode)) {\n                    container = container.parentNode;\n                }\n                while (container.nextSibling !== null && (isInline(editor, container.nextSibling) || isTextNode$1(container.nextSibling))) {\n                    container = container.nextSibling;\n                }\n            }\n        }\n        while (container.parentNode !== root) {\n            const parent = container.parentNode;\n            if (isTextBlock(editor, container)) {\n                return container;\n            }\n            if (/^(TD|TH)$/.test(parent.nodeName)) {\n                return container;\n            }\n            container = parent;\n        }\n        return container;\n    };\n    const getSelectedTextBlocks = (editor, rng, root) => {\n        const textBlocks = [];\n        const dom = editor.dom;\n        const startNode = getEndPointNode(editor, rng, true, root);\n        const endNode = getEndPointNode(editor, rng, false, root);\n        let block;\n        const siblings = [];\n        for (let node = startNode; node; node = node.nextSibling) {\n            siblings.push(node);\n            if (node === endNode) {\n                break;\n            }\n        }\n        global$2.each(siblings, (node) => {\n            var _a;\n            if (isTextBlock(editor, node)) {\n                textBlocks.push(node);\n                block = null;\n                return;\n            }\n            if (dom.isBlock(node) || isBr(node)) {\n                if (isBr(node)) {\n                    dom.remove(node);\n                }\n                block = null;\n                return;\n            }\n            const nextSibling = node.nextSibling;\n            if (global$1.isBookmarkNode(node)) {\n                if (isListNode(nextSibling) || isTextBlock(editor, nextSibling) || (!nextSibling && node.parentNode === root)) {\n                    block = null;\n                    return;\n                }\n            }\n            if (!block) {\n                block = dom.create('p');\n                (_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(block, node);\n                textBlocks.push(block);\n            }\n            block.appendChild(node);\n        });\n        return textBlocks;\n    };\n    const hasCompatibleStyle = (dom, sib, detail) => {\n        const sibStyle = dom.getStyle(sib, 'list-style-type');\n        let detailStyle = detail ? detail['list-style-type'] : '';\n        detailStyle = detailStyle === null ? '' : detailStyle;\n        return sibStyle === detailStyle;\n    };\n    /*\n      Find the first element we would transform into a li-element if given no constraints.\n      If the common ancestor is higher up than that provide it as the starting-point for the search for the root instead of the first selected element.\n      This helps avoid issues with divs that should become li-elements are detected as the root when they should not be.\n    */\n    const getRootSearchStart = (editor, range) => {\n        const start = editor.selection.getStart(true);\n        const startPoint = getEndPointNode(editor, range, true, editor.getBody());\n        if (ancestor(SugarElement.fromDom(startPoint), SugarElement.fromDom(range.commonAncestorContainer))) {\n            return range.commonAncestorContainer;\n        }\n        else {\n            return start;\n        }\n    };\n    const applyList = (editor, listName, detail) => {\n        const rng = editor.selection.getRng();\n        let listItemName = 'LI';\n        const root = getClosestListHost(editor, getRootSearchStart(editor, rng));\n        const dom = editor.dom;\n        if (dom.getContentEditable(editor.selection.getNode()) === 'false') {\n            return;\n        }\n        listName = listName.toUpperCase();\n        if (listName === 'DL') {\n            listItemName = 'DT';\n        }\n        const bookmark = createBookmark(rng);\n        const selectedTextBlocks = filter$1(getSelectedTextBlocks(editor, rng, root), editor.dom.isEditable);\n        global$2.each(selectedTextBlocks, (block) => {\n            let listBlock;\n            const sibling = block.previousSibling;\n            const parent = block.parentNode;\n            if (!isListItemNode(parent)) {\n                if (sibling && isListNode(sibling) && sibling.nodeName === listName && hasCompatibleStyle(dom, sibling, detail)) {\n                    listBlock = sibling;\n                    block = dom.rename(block, listItemName);\n                    sibling.appendChild(block);\n                }\n                else {\n                    listBlock = dom.create(listName);\n                    parent.insertBefore(listBlock, block);\n                    listBlock.appendChild(block);\n                    block = dom.rename(block, listItemName);\n                }\n                removeStyles(dom, block, [\n                    'margin', 'margin-right', 'margin-bottom', 'margin-left', 'margin-top',\n                    'padding', 'padding-right', 'padding-bottom', 'padding-left', 'padding-top'\n                ]);\n                updateListWithDetails(dom, listBlock, detail);\n                mergeWithAdjacentLists(editor.dom, listBlock);\n            }\n        });\n        editor.selection.setRng(resolveBookmark(bookmark));\n    };\n    const isValidLists = (list1, list2) => {\n        return isListNode(list1) && list1.nodeName === (list2 === null || list2 === void 0 ? void 0 : list2.nodeName);\n    };\n    const hasSameListStyle = (dom, list1, list2) => {\n        const targetStyle = dom.getStyle(list1, 'list-style-type', true);\n        const style = dom.getStyle(list2, 'list-style-type', true);\n        return targetStyle === style;\n    };\n    const hasSameClasses = (elm1, elm2) => {\n        return elm1.className === elm2.className;\n    };\n    const shouldMerge = (dom, list1, list2) => {\n        return isValidLists(list1, list2) &&\n            // Note: isValidLists will ensure list1 and list2 are a HTMLElement. Unfortunately TypeScript doesn't\n            // support type guards on multiple variables. See https://github.com/microsoft/TypeScript/issues/26916\n            hasSameListStyle(dom, list1, list2) &&\n            hasSameClasses(list1, list2);\n    };\n    const mergeWithAdjacentLists = (dom, listBlock) => {\n        let node;\n        let sibling = listBlock.nextSibling;\n        if (shouldMerge(dom, listBlock, sibling)) {\n            const liSibling = sibling;\n            while ((node = liSibling.firstChild)) {\n                listBlock.appendChild(node);\n            }\n            dom.remove(liSibling);\n        }\n        sibling = listBlock.previousSibling;\n        if (shouldMerge(dom, listBlock, sibling)) {\n            const liSibling = sibling;\n            while ((node = liSibling.lastChild)) {\n                listBlock.insertBefore(node, listBlock.firstChild);\n            }\n            dom.remove(liSibling);\n        }\n    };\n    const updateList$1 = (editor, list, listName, detail) => {\n        if (list.nodeName !== listName) {\n            const newList = editor.dom.rename(list, listName);\n            updateListWithDetails(editor.dom, newList, detail);\n            fireListEvent(editor, listToggleActionFromListName(listName), newList);\n        }\n        else {\n            updateListWithDetails(editor.dom, list, detail);\n            fireListEvent(editor, listToggleActionFromListName(listName), list);\n        }\n    };\n    const updateCustomList = (editor, list, listName, detail) => {\n        list.classList.forEach((cls, _, classList) => {\n            if (cls.startsWith('tox-')) {\n                classList.remove(cls);\n                if (classList.length === 0) {\n                    list.removeAttribute('class');\n                }\n            }\n        });\n        if (list.nodeName !== listName) {\n            const newList = editor.dom.rename(list, listName);\n            updateListWithDetails(editor.dom, newList, detail);\n            fireListEvent(editor, listToggleActionFromListName(listName), newList);\n        }\n        else {\n            updateListWithDetails(editor.dom, list, detail);\n            fireListEvent(editor, listToggleActionFromListName(listName), list);\n        }\n    };\n    const toggleMultipleLists = (editor, parentList, lists, listName, detail) => {\n        const parentIsList = isListNode(parentList);\n        if (parentIsList && parentList.nodeName === listName && !hasListStyleDetail(detail) && !isCustomList(parentList)) {\n            flattenListSelection(editor);\n        }\n        else {\n            applyList(editor, listName, detail);\n            const bookmark = createBookmark(editor.selection.getRng());\n            const allLists = parentIsList ? [parentList, ...lists] : lists;\n            const updateFunction = (parentIsList && isCustomList(parentList)) ? updateCustomList : updateList$1;\n            global$2.each(allLists, (elm) => {\n                updateFunction(editor, elm, listName, detail);\n            });\n            editor.selection.setRng(resolveBookmark(bookmark));\n        }\n    };\n    const hasListStyleDetail = (detail) => {\n        return 'list-style-type' in detail;\n    };\n    const toggleSingleList = (editor, parentList, listName, detail) => {\n        if (parentList === editor.getBody()) {\n            return;\n        }\n        if (parentList) {\n            if (parentList.nodeName === listName && !hasListStyleDetail(detail) && !isCustomList(parentList)) {\n                flattenListSelection(editor);\n            }\n            else {\n                const bookmark = createBookmark(editor.selection.getRng());\n                if (isCustomList(parentList)) {\n                    parentList.classList.forEach((cls, _, classList) => {\n                        if (cls.startsWith('tox-')) {\n                            classList.remove(cls);\n                            if (classList.length === 0) {\n                                parentList.removeAttribute('class');\n                            }\n                        }\n                    });\n                }\n                updateListWithDetails(editor.dom, parentList, detail);\n                const newList = editor.dom.rename(parentList, listName);\n                mergeWithAdjacentLists(editor.dom, newList);\n                editor.selection.setRng(resolveBookmark(bookmark));\n                applyList(editor, listName, detail);\n                fireListEvent(editor, listToggleActionFromListName(listName), newList);\n            }\n        }\n        else {\n            applyList(editor, listName, detail);\n            fireListEvent(editor, listToggleActionFromListName(listName), parentList);\n        }\n    };\n    const toggleList = (editor, listName, _detail) => {\n        const parentList = getParentList(editor);\n        if (isWithinNonEditableList(editor, parentList)) {\n            return;\n        }\n        const selectedSubLists = getSelectedSubLists(editor);\n        const detail = isObject(_detail) ? _detail : {};\n        if (selectedSubLists.length > 0) {\n            toggleMultipleLists(editor, parentList, selectedSubLists, listName, detail);\n        }\n        else {\n            toggleSingleList(editor, parentList, listName, detail);\n        }\n    };\n\n    const DOM = global$3.DOM;\n    const normalizeList = (dom, list) => {\n        const parentNode = list.parentElement;\n        // Move UL/OL to previous LI if it's the only child of a LI\n        if (parentNode && parentNode.nodeName === 'LI' && parentNode.firstChild === list) {\n            const sibling = parentNode.previousSibling;\n            if (sibling && sibling.nodeName === 'LI') {\n                sibling.appendChild(list);\n                if (isEmpty$1(dom, parentNode)) {\n                    DOM.remove(parentNode);\n                }\n            }\n            else {\n                DOM.setStyle(parentNode, 'listStyleType', 'none');\n            }\n        }\n        // Append OL/UL to previous LI if it's in a parent OL/UL i.e. old HTML4\n        if (isListNode(parentNode)) {\n            const sibling = parentNode.previousSibling;\n            if (sibling && sibling.nodeName === 'LI') {\n                sibling.appendChild(list);\n            }\n        }\n    };\n    const normalizeLists = (dom, element) => {\n        const lists = global$2.grep(dom.select('ol,ul', element));\n        global$2.each(lists, (list) => {\n            normalizeList(dom, list);\n        });\n    };\n\n    const findNextCaretContainer = (editor, rng, isForward, root) => {\n        let node = rng.startContainer;\n        const offset = rng.startOffset;\n        if (isTextNode$1(node) && (isForward ? offset < node.data.length : offset > 0)) {\n            return node;\n        }\n        const nonEmptyBlocks = editor.schema.getNonEmptyElements();\n        if (isElement(node)) {\n            node = global$6.getNode(node, offset);\n        }\n        const walker = new global$5(node, root);\n        // Delete at <li>|<br></li> then jump over the bogus br\n        if (isForward) {\n            if (isBogusBr(editor.dom, node)) {\n                walker.next();\n            }\n        }\n        const walkFn = isForward ? walker.next.bind(walker) : walker.prev2.bind(walker);\n        while ((node = walkFn())) {\n            if (node.nodeName === 'LI' && !node.hasChildNodes()) {\n                return node;\n            }\n            if (nonEmptyBlocks[node.nodeName]) {\n                return node;\n            }\n            if (isTextNode$1(node) && node.data.length > 0) {\n                return node;\n            }\n        }\n        return null;\n    };\n    const hasOnlyOneBlockChild = (dom, elm) => {\n        const childNodes = elm.childNodes;\n        return childNodes.length === 1 && !isListNode(childNodes[0]) && dom.isBlock(childNodes[0]);\n    };\n    const isUnwrappable = (node) => Optional.from(node)\n        .map(SugarElement.fromDom)\n        .filter(isHTMLElement)\n        .exists((el) => isEditable(el) && !contains$1(['details'], name(el)));\n    const unwrapSingleBlockChild = (dom, elm) => {\n        if (hasOnlyOneBlockChild(dom, elm) && isUnwrappable(elm.firstChild)) {\n            dom.remove(elm.firstChild, true);\n        }\n    };\n    const moveChildren = (dom, fromElm, toElm) => {\n        let node;\n        const targetElm = hasOnlyOneBlockChild(dom, toElm) ? toElm.firstChild : toElm;\n        unwrapSingleBlockChild(dom, fromElm);\n        if (!isEmpty$1(dom, fromElm, true)) {\n            while ((node = fromElm.firstChild)) {\n                targetElm.appendChild(node);\n            }\n        }\n    };\n    const mergeLiElements = (dom, fromElm, toElm) => {\n        let listNode;\n        const ul = fromElm.parentNode;\n        if (!isChildOfBody(dom, fromElm) || !isChildOfBody(dom, toElm)) {\n            return;\n        }\n        if (isListNode(toElm.lastChild)) {\n            listNode = toElm.lastChild;\n        }\n        if (ul === toElm.lastChild) {\n            if (isBr(ul.previousSibling)) {\n                dom.remove(ul.previousSibling);\n            }\n        }\n        const node = toElm.lastChild;\n        if (node && isBr(node) && fromElm.hasChildNodes()) {\n            dom.remove(node);\n        }\n        if (isEmpty$1(dom, toElm, true)) {\n            empty(SugarElement.fromDom(toElm));\n        }\n        moveChildren(dom, fromElm, toElm);\n        if (listNode) {\n            toElm.appendChild(listNode);\n        }\n        const contains$1 = contains(SugarElement.fromDom(toElm), SugarElement.fromDom(fromElm));\n        const nestedLists = contains$1 ? dom.getParents(fromElm, isListNode, toElm) : [];\n        dom.remove(fromElm);\n        each$1(nestedLists, (list) => {\n            if (isEmpty$1(dom, list) && list !== dom.getRoot()) {\n                dom.remove(list);\n            }\n        });\n    };\n    const mergeIntoEmptyLi = (editor, fromLi, toLi) => {\n        empty(SugarElement.fromDom(toLi));\n        mergeLiElements(editor.dom, fromLi, toLi);\n        editor.selection.setCursorLocation(toLi, 0);\n    };\n    const mergeForward = (editor, rng, fromLi, toLi) => {\n        const dom = editor.dom;\n        if (dom.isEmpty(toLi)) {\n            mergeIntoEmptyLi(editor, fromLi, toLi);\n        }\n        else {\n            const bookmark = createBookmark(rng);\n            mergeLiElements(dom, fromLi, toLi);\n            editor.selection.setRng(resolveBookmark(bookmark));\n        }\n    };\n    const mergeBackward = (editor, rng, fromLi, toLi) => {\n        const bookmark = createBookmark(rng);\n        mergeLiElements(editor.dom, fromLi, toLi);\n        const resolvedBookmark = resolveBookmark(bookmark);\n        editor.selection.setRng(resolvedBookmark);\n    };\n    const backspaceDeleteFromListToListCaret = (editor, isForward) => {\n        const dom = editor.dom, selection = editor.selection;\n        const selectionStartElm = selection.getStart();\n        const root = getClosestEditingHost(editor, selectionStartElm);\n        const li = dom.getParent(selection.getStart(), 'LI', root);\n        if (li) {\n            const ul = li.parentElement;\n            if (ul === editor.getBody() && isEmpty$1(dom, ul)) {\n                return true;\n            }\n            const rng = normalizeRange(selection.getRng());\n            const otherLi = dom.getParent(findNextCaretContainer(editor, rng, isForward, root), 'LI', root);\n            const willMergeParentIntoChild = otherLi && (isForward ? dom.isChildOf(li, otherLi) : dom.isChildOf(otherLi, li));\n            if (otherLi && otherLi !== li && !willMergeParentIntoChild) {\n                editor.undoManager.transact(() => {\n                    if (isForward) {\n                        mergeForward(editor, rng, otherLi, li);\n                    }\n                    else {\n                        if (isFirstChild(li)) {\n                            outdentListSelection(editor);\n                        }\n                        else {\n                            mergeBackward(editor, rng, li, otherLi);\n                        }\n                    }\n                });\n                return true;\n            }\n            else if (willMergeParentIntoChild && !isForward && otherLi !== li) {\n                const commonAncestorParent = rng.commonAncestorContainer.parentElement;\n                if (!commonAncestorParent || dom.isChildOf(otherLi, commonAncestorParent)) {\n                    return false;\n                }\n                editor.undoManager.transact(() => {\n                    const bookmark = createBookmark(rng);\n                    moveChildren(dom, commonAncestorParent, otherLi);\n                    commonAncestorParent.remove();\n                    const resolvedBookmark = resolveBookmark(bookmark);\n                    editor.selection.setRng(resolvedBookmark);\n                });\n                return true;\n            }\n            else if (!otherLi) {\n                if (!isForward && rng.startOffset === 0 && rng.endOffset === 0) {\n                    editor.undoManager.transact(() => {\n                        flattenListSelection(editor);\n                    });\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n    const removeBlock = (dom, block, root) => {\n        const parentBlock = dom.getParent(block.parentNode, dom.isBlock, root);\n        dom.remove(block);\n        if (parentBlock && dom.isEmpty(parentBlock)) {\n            dom.remove(parentBlock);\n        }\n    };\n    const backspaceDeleteIntoListCaret = (editor, isForward) => {\n        const dom = editor.dom;\n        const selectionStartElm = editor.selection.getStart();\n        const root = getClosestEditingHost(editor, selectionStartElm);\n        const block = dom.getParent(selectionStartElm, dom.isBlock, root);\n        if (block && dom.isEmpty(block, undefined, { checkRootAsContent: true })) {\n            const rng = normalizeRange(editor.selection.getRng());\n            const nextCaretContainer = findNextCaretContainer(editor, rng, isForward, root);\n            const otherLi = dom.getParent(nextCaretContainer, 'LI', root);\n            if (nextCaretContainer && otherLi) {\n                const findValidElement = (element) => contains$1(['td', 'th', 'caption'], name(element));\n                const findRoot = (node) => node.dom === root;\n                const otherLiCell = closest$2(SugarElement.fromDom(otherLi), findValidElement, findRoot);\n                const caretCell = closest$2(SugarElement.fromDom(rng.startContainer), findValidElement, findRoot);\n                if (!equals(otherLiCell, caretCell, eq)) {\n                    return false;\n                }\n                editor.undoManager.transact(() => {\n                    const parentNode = otherLi.parentNode;\n                    removeBlock(dom, block, root);\n                    mergeWithAdjacentLists(dom, parentNode);\n                    editor.selection.select(nextCaretContainer, true);\n                    editor.selection.collapse(isForward);\n                });\n                return true;\n            }\n        }\n        return false;\n    };\n    const backspaceDeleteCaret = (editor, isForward) => {\n        return backspaceDeleteFromListToListCaret(editor, isForward) || backspaceDeleteIntoListCaret(editor, isForward);\n    };\n    const hasListSelection = (editor) => {\n        const selectionStartElm = editor.selection.getStart();\n        const root = getClosestEditingHost(editor, selectionStartElm);\n        const startListParent = editor.dom.getParent(selectionStartElm, 'LI,DT,DD', root);\n        return startListParent || getSelectedListItems(editor).length > 0;\n    };\n    const backspaceDeleteRange = (editor) => {\n        if (hasListSelection(editor)) {\n            editor.undoManager.transact(() => {\n                // Some delete actions may prevent the input event from being fired. If we do not detect it, we fire it ourselves.\n                let shouldFireInput = true;\n                const inputHandler = () => shouldFireInput = false;\n                editor.on('input', inputHandler);\n                editor.execCommand('Delete');\n                editor.off('input', inputHandler);\n                if (shouldFireInput) {\n                    editor.dispatch('input');\n                }\n                normalizeLists(editor.dom, editor.getBody());\n            });\n            return true;\n        }\n        return false;\n    };\n    const backspaceDelete = (editor, isForward) => {\n        const selection = editor.selection;\n        return !isWithinNonEditableList(editor, selection.getNode()) && (selection.isCollapsed() ?\n            backspaceDeleteCaret(editor, isForward) : backspaceDeleteRange(editor));\n    };\n    const setup$2 = (editor) => {\n        editor.on('ExecCommand', (e) => {\n            const cmd = e.command.toLowerCase();\n            if ((cmd === 'delete' || cmd === 'forwarddelete') && hasListSelection(editor)) {\n                normalizeLists(editor.dom, editor.getBody());\n            }\n        });\n        editor.on('keydown', (e) => {\n            if (e.keyCode === global$4.BACKSPACE) {\n                if (backspaceDelete(editor, false)) {\n                    e.preventDefault();\n                }\n            }\n            else if (e.keyCode === global$4.DELETE) {\n                if (backspaceDelete(editor, true)) {\n                    e.preventDefault();\n                }\n            }\n        });\n    };\n\n    const get = (editor) => ({\n        backspaceDelete: (isForward) => {\n            backspaceDelete(editor, isForward);\n        }\n    });\n\n    const updateList = (editor, update) => {\n        const parentList = getParentList(editor);\n        if (parentList === null || isWithinNonEditableList(editor, parentList)) {\n            return;\n        }\n        editor.undoManager.transact(() => {\n            if (isObject(update.styles)) {\n                editor.dom.setStyles(parentList, update.styles);\n            }\n            if (isObject(update.attrs)) {\n                each(update.attrs, (v, k) => editor.dom.setAttrib(parentList, k, v));\n            }\n        });\n    };\n\n    // Example: 'AB' -> 28\n    const parseAlphabeticBase26 = (str) => {\n        const chars = reverse(trim(str).split(''));\n        const values = map(chars, (char, i) => {\n            const charValue = char.toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0) + 1;\n            return Math.pow(26, i) * charValue;\n        });\n        return foldl(values, (sum, v) => sum + v, 0);\n    };\n    // Example: 28 -> 'AB'\n    const composeAlphabeticBase26 = (value) => {\n        value--;\n        if (value < 0) {\n            return '';\n        }\n        else {\n            const remainder = value % 26;\n            const quotient = Math.floor(value / 26);\n            const rest = composeAlphabeticBase26(quotient);\n            const char = String.fromCharCode('A'.charCodeAt(0) + remainder);\n            return rest + char;\n        }\n    };\n    const isUppercase = (str) => /^[A-Z]+$/.test(str);\n    const isLowercase = (str) => /^[a-z]+$/.test(str);\n    const isNumeric = (str) => /^[0-9]+$/.test(str);\n    const deduceListType = (start) => {\n        if (isNumeric(start)) {\n            return 2 /* ListType.Numeric */;\n        }\n        else if (isUppercase(start)) {\n            return 0 /* ListType.UpperAlpha */;\n        }\n        else if (isLowercase(start)) {\n            return 1 /* ListType.LowerAlpha */;\n        }\n        else if (isEmpty$2(start)) {\n            return 3 /* ListType.None */;\n        }\n        else {\n            return 4 /* ListType.Unknown */;\n        }\n    };\n    const parseStartValue = (start) => {\n        switch (deduceListType(start)) {\n            case 2 /* ListType.Numeric */:\n                return Optional.some({\n                    listStyleType: Optional.none(),\n                    start\n                });\n            case 0 /* ListType.UpperAlpha */:\n                return Optional.some({\n                    listStyleType: Optional.some('upper-alpha'),\n                    start: parseAlphabeticBase26(start).toString()\n                });\n            case 1 /* ListType.LowerAlpha */:\n                return Optional.some({\n                    listStyleType: Optional.some('lower-alpha'),\n                    start: parseAlphabeticBase26(start).toString()\n                });\n            case 3 /* ListType.None */:\n                return Optional.some({\n                    listStyleType: Optional.none(),\n                    start: ''\n                });\n            case 4 /* ListType.Unknown */:\n                return Optional.none();\n        }\n    };\n    const parseDetail = (detail) => {\n        const start = parseInt(detail.start, 10);\n        if (is$2(detail.listStyleType, 'upper-alpha')) {\n            return composeAlphabeticBase26(start);\n        }\n        else if (is$2(detail.listStyleType, 'lower-alpha')) {\n            return composeAlphabeticBase26(start).toLowerCase();\n        }\n        else {\n            return detail.start;\n        }\n    };\n\n    const open = (editor) => {\n        // Find the current list and skip opening if the selection isn't in an ordered list\n        const currentList = getParentList(editor);\n        if (!isOlNode(currentList) || isWithinNonEditableList(editor, currentList)) {\n            return;\n        }\n        editor.windowManager.open({\n            title: 'List Properties',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        type: 'input',\n                        name: 'start',\n                        label: 'Start list at number',\n                        inputMode: 'numeric'\n                    }\n                ]\n            },\n            initialData: {\n                start: parseDetail({\n                    start: editor.dom.getAttrib(currentList, 'start', '1'),\n                    listStyleType: Optional.from(editor.dom.getStyle(currentList, 'list-style-type'))\n                })\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            onSubmit: (api) => {\n                const data = api.getData();\n                parseStartValue(data.start).each((detail) => {\n                    editor.execCommand('mceListUpdate', false, {\n                        attrs: {\n                            start: detail.start === '1' ? '' : detail.start\n                        },\n                        styles: {\n                            'list-style-type': detail.listStyleType.getOr('')\n                        }\n                    });\n                });\n                api.close();\n            }\n        });\n    };\n\n    const queryListCommandState = (editor, listName) => () => {\n        const parentList = getParentList(editor);\n        return isNonNullable(parentList) && parentList.nodeName === listName;\n    };\n    const registerDialog = (editor) => {\n        editor.addCommand('mceListProps', () => {\n            open(editor);\n        });\n    };\n    const register$2 = (editor) => {\n        editor.on('BeforeExecCommand', (e) => {\n            const cmd = e.command.toLowerCase();\n            if (cmd === 'indent') {\n                indentListSelection(editor);\n            }\n            else if (cmd === 'outdent') {\n                outdentListSelection(editor);\n            }\n        });\n        editor.addCommand('InsertUnorderedList', (ui, detail) => {\n            toggleList(editor, 'UL', detail);\n        });\n        editor.addCommand('InsertOrderedList', (ui, detail) => {\n            toggleList(editor, 'OL', detail);\n        });\n        editor.addCommand('InsertDefinitionList', (ui, detail) => {\n            toggleList(editor, 'DL', detail);\n        });\n        editor.addCommand('RemoveList', () => {\n            flattenListSelection(editor);\n        });\n        registerDialog(editor);\n        editor.addCommand('mceListUpdate', (ui, detail) => {\n            if (isObject(detail)) {\n                updateList(editor, detail);\n            }\n        });\n        editor.addQueryStateHandler('InsertUnorderedList', queryListCommandState(editor, 'UL'));\n        editor.addQueryStateHandler('InsertOrderedList', queryListCommandState(editor, 'OL'));\n        editor.addQueryStateHandler('InsertDefinitionList', queryListCommandState(editor, 'DL'));\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    const isTextNode = (node) => node.type === 3;\n    const isEmpty = (nodeBuffer) => nodeBuffer.length === 0;\n    const wrapInvalidChildren = (list) => {\n        const insertListItem = (buffer, refNode) => {\n            const li = global.create('li');\n            each$1(buffer, (node) => li.append(node));\n            if (refNode) {\n                list.insert(li, refNode, true);\n            }\n            else {\n                list.append(li);\n            }\n        };\n        const reducer = (buffer, node) => {\n            if (isTextNode(node)) {\n                return [...buffer, node];\n            }\n            else if (!isEmpty(buffer) && !isTextNode(node)) {\n                insertListItem(buffer, node);\n                return [];\n            }\n            else {\n                return buffer;\n            }\n        };\n        const restBuffer = foldl(list.children(), reducer, []);\n        if (!isEmpty(restBuffer)) {\n            insertListItem(restBuffer);\n        }\n    };\n    const setup$1 = (editor) => {\n        editor.on('PreInit', () => {\n            const { parser } = editor;\n            parser.addNodeFilter('ul,ol', (nodes) => each$1(nodes, wrapInvalidChildren));\n        });\n    };\n\n    const setupTabKey = (editor) => {\n        editor.on('keydown', (e) => {\n            // Check for tab but not ctrl/cmd+tab since it switches browser tabs\n            if (e.keyCode !== global$4.TAB || global$4.metaKeyPressed(e)) {\n                return;\n            }\n            editor.undoManager.transact(() => {\n                if (e.shiftKey ? outdentListSelection(editor) : indentListSelection(editor)) {\n                    e.preventDefault();\n                }\n            });\n        });\n    };\n    const setup = (editor) => {\n        if (shouldIndentOnTab(editor)) {\n            setupTabKey(editor);\n        }\n        setup$2(editor);\n    };\n\n    const setupToggleButtonHandler = (editor, listName) => (api) => {\n        const toggleButtonHandler = (e) => {\n            api.setActive(inList(e.parents, listName));\n            api.setEnabled(!isWithinNonEditableList(editor, e.element) && editor.selection.isEditable());\n        };\n        api.setEnabled(editor.selection.isEditable());\n        return setNodeChangeHandler(editor, toggleButtonHandler);\n    };\n    const register$1 = (editor) => {\n        const exec = (command) => () => editor.execCommand(command);\n        if (!editor.hasPlugin('advlist')) {\n            editor.ui.registry.addToggleButton('numlist', {\n                icon: 'ordered-list',\n                active: false,\n                tooltip: 'Numbered list',\n                onAction: exec('InsertOrderedList'),\n                onSetup: setupToggleButtonHandler(editor, 'OL')\n            });\n            editor.ui.registry.addToggleButton('bullist', {\n                icon: 'unordered-list',\n                active: false,\n                tooltip: 'Bullet list',\n                onAction: exec('InsertUnorderedList'),\n                onSetup: setupToggleButtonHandler(editor, 'UL')\n            });\n        }\n    };\n\n    const setupMenuButtonHandler = (editor, listName) => (api) => {\n        const menuButtonHandler = (e) => api.setEnabled(inList(e.parents, listName) && !isWithinNonEditableList(editor, e.element));\n        return setNodeChangeHandler(editor, menuButtonHandler);\n    };\n    const register = (editor) => {\n        const listProperties = {\n            text: 'List properties...',\n            icon: 'ordered-list',\n            onAction: () => editor.execCommand('mceListProps'),\n            onSetup: setupMenuButtonHandler(editor, 'OL')\n        };\n        editor.ui.registry.addMenuItem('listprops', listProperties);\n        editor.ui.registry.addContextMenu('lists', {\n            update: (node) => {\n                const parentList = getParentList(editor, node);\n                return isOlNode(parentList) ? ['listprops'] : [];\n            }\n        });\n    };\n\n    var Plugin = () => {\n        global$7.add('lists', (editor) => {\n            register$3(editor);\n            setup$1(editor);\n            if (!editor.hasPlugin('rtc', true)) {\n                setup(editor);\n                register$2(editor);\n            }\n            else {\n                registerDialog(editor);\n            }\n            register$1(editor);\n            register(editor);\n            return get(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,WAAW,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACxD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,WAAW,SAAS;IAC1B,MAAM,WAAW,SAAS;IAC1B,MAAM,UAAU,SAAS;IACzB,MAAM,YAAY,aAAa;IAC/B,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAChC,MAAM,WAAW,aAAa;IAE9B,MAAM,OAAO,KAAQ;IACrB,gGAAgG,GAChG,MAAM,WAAW,CAAC,KAAK,MAAQ,CAAC,IAAM,IAAI,IAAI;IAC9C,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC,GAAG;QACrB,OAAO,MAAM;IACjB;IACA,+DAA+D;IAC/D,SAAS,MAAM,EAAE;QAAE,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,cAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YAAG,YAAH,OAAA,KAAA,SAAA,CAAA,KAAc;;QAC7B,OAAO;6CAAI;gBAAA;;YACP,MAAM,MAAM,YAAY,MAAM,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,MAAM,CAAC,IAAM,CAAC,IAAM,CAAC,EAAE;IAC7B,MAAM,QAAQ,SAAS;IAEvB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,gBAAgB,MAAM,SAAS,CAAC,OAAO;IAC7C,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,iBAAiB,GACjB,MAAM,aAAa,CAAC,IAAI,IAAM,cAAc,IAAI,CAAC,IAAI;IACrD,MAAM,aAAa,CAAC,IAAI,IAAM,WAAW,IAAI,KAAK,CAAC;IACnD,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,WAAW,CAAC,IAAI;QAClB,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,EAAE,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;KAUC,GACD,MAAM,UAAU,CAAC,IAAI;QACjB,IAAI,GAAG,MAAM,KAAK,GAAG;YACjB,OAAO,EAAE;QACb,OACK;YACD,IAAI,UAAU,EAAE,EAAE,CAAC,EAAE,GAAG,4BAA4B;YACpD,MAAM,IAAI,EAAE;YACZ,IAAI,QAAQ,EAAE;YACd,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;gBACf,MAAM,OAAO,EAAE;gBACf,IAAI,SAAS,SAAS;oBAClB,EAAE,IAAI,CAAC;oBACP,QAAQ,EAAE;gBACd;gBACA,UAAU;gBACV,MAAM,IAAI,CAAC;YACf;YACA,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB,EAAE,IAAI,CAAC;YACX;YACA,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,CAAC,IAAI,GAAG;QAClB,OAAO,IAAI,CAAC,GAAG;YACX,MAAM,EAAE,KAAK,GAAG;QACpB;QACA,OAAO;IACX;IACA,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,OAAO,CAAC,IAAI;QACd,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,IAAI,IAAM,QAAQ,IAAI,IAAI;IACxC,MAAM,UAAU,CAAC;QACb,MAAM,IAAI,YAAY,IAAI,CAAC,IAAI;QAC/B,EAAE,OAAO;QACT,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,IAAI,IAAM,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI;IACvF,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI;IAC/B,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC3C,MAAM,SAAS,CAAC,IAAI;QAChB,MAAM,IAAI,EAAE;QACZ,MAAM,eAAe,WAAW,cAC5B,CAAC,IAAM,OAAO,GAAG,CAAC,IAAM,WAAW,GAAG,MACtC,CAAC,IAAM,WAAW,GAAG;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,CAAC,aAAa,IAAI;gBAClB,EAAE,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,SAAS,CAAC,IAAM,CAAC,GAAG;YACtB,CAAC,CAAC,EAAE,GAAG;QACX;IACA,MAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ;QACvC,KAAK,KAAK,CAAC,GAAG;YACV,CAAC,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,GAAG;QACvC;IACJ;IACA,MAAM,SAAS,CAAC,KAAK;QACjB,MAAM,IAAI,CAAC;QACX,eAAe,KAAK,MAAM,OAAO,IAAI;QACrC,OAAO;IACX;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,sFAAsF;IACtF,8DAA8D;IAC9D,MAAM,SAAS,uCAAgC,SAAS;IAExD;;KAEC,GACD,MAAM,OAAO,SAAC,KAAK;YAAK,8EAAa;eAAiB,IAAI,MAAM,CAAC,CAAC,OAAS,WAAW,MAAM;;IAC5F;;;KAGC,GACD,MAAM,SAAS,SAAC,KAAK;YAAK,8EAAa;eAAiB,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,IAAI,MAAM,MAAM,IAAI,MAAM;;IACpH;;;;;IAKA,GACA,MAAM,QAAQ,CAAC,IAAI,IAAI,IAAM,GAAG,MAAM,MAAM,GAAG,MAAM,KAAK,SAAS,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS,IAAI;IAExH,wCAAwC,GACxC,MAAM,OAAO,CAAC,OAAO;QACjB,IAAI,IAAI,UAAU,aAAa,UAAU,OAAO,QAAQ;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa,MAAM,MAAM,EAAE,EAAG;YACpE,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;QACnB;QACA,OAAO;IACX;IACA,yCAAyC,GACzC,MAAM,UAAU,CAAC,GAAG;QAChB,MAAM,QAAQ,EAAE,KAAK,CAAC;QACtB,OAAO,KAAK,OAAO;IACvB;IAEA,MAAM,QAAQ,CAAC,IAAM,CAAC,IAAM,EAAE,OAAO,CAAC,GAAG;IACzC,4CAA4C,GAC5C,MAAM,OAAO,MAAM;IACnB,MAAM,aAAa,CAAC,IAAM,EAAE,MAAM,GAAG;IACrC,MAAM,YAAY,CAAC,IAAM,CAAC,WAAW;IAErC,MAAM,YAAY;IAClB,MAAM,SAAS,CAAC,OAAS,SAAS;IAElC,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,IAAI,aAAa,CAAC;QAC9B,IAAI,SAAS,GAAG;QAChB,IAAI,CAAC,IAAI,aAAa,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,UAAU;YAChB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,UAAU,IAAI,UAAU,CAAC,EAAE;IACtC;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,aAAa,CAAC;QAC/B,OAAO,UAAU;IACrB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,cAAc,CAAC;QAChC,OAAO,UAAU;IACrB;IACA,MAAM,YAAY,CAAC;QACf,8DAA8D;QAC9D,IAAI,SAAS,QAAQ,SAAS,WAAW;YACrC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;YACH,KAAK;QACT;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,GAAG,IAAM,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,CAAC;IACzF,yCAAyC;IACzC,MAAM,eAAe;QACjB;QACA;QACA;QACA,SAAS;QACT;IACJ;IAEA,MAAM,UAAU;IAChB,MAAM,oBAAoB;IAC1B,MAAM,UAAU;IAChB,MAAM,OAAO;IAEb,MAAM,OAAO,CAAC,SAAS;QACnB,MAAM,MAAM,QAAQ,GAAG;QACvB,IAAI,IAAI,QAAQ,KAAK,SAAS;YAC1B,OAAO;QACX,OACK;YACD,MAAM,OAAO;YACb,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,OAAO,KAAK,OAAO,CAAC;YACxB,OACK,IAAI,KAAK,iBAAiB,KAAK,WAAW;gBAC3C,OAAO,KAAK,iBAAiB,CAAC;YAClC,OACK,IAAI,KAAK,qBAAqB,KAAK,WAAW;gBAC/C,OAAO,KAAK,qBAAqB,CAAC;YACtC,OACK,IAAI,KAAK,kBAAkB,KAAK,WAAW;gBAC5C,gEAAgE;gBAChE,OAAO,KAAK,kBAAkB,CAAC;YACnC,OACK;gBACD,MAAM,IAAI,MAAM;YACpB,EAAE,kDAAkD;QACxD;IACJ;IAEA,MAAM,KAAK,CAAC,IAAI,KAAO,GAAG,GAAG,KAAK,GAAG,GAAG;IACxC,yDAAyD;IACzD,8DAA8D;IAC9D,MAAM,WAAW,CAAC,IAAI;QAClB,MAAM,KAAK,GAAG,GAAG;QACjB,MAAM,KAAK,GAAG,GAAG;QACjB,OAAO,OAAO,KAAK,QAAQ,GAAG,QAAQ,CAAC;IAC3C;IACA,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,MAAM;QAClB,OAAO,QAAQ,MAAM;IACzB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,SAAS,OAAO,MAAM;QAC5B,IAAI,WAAW,aAAa,WAAW,MAAM;YACzC,MAAM,IAAI,MAAM,OAAO;QAC3B;QACA,OAAO;IACX;IAEA,MAAM,iBAAiB,OAAO,cAAc;IAC5C;;;;;KAKC,GACD,MAAM,kBAAkB,CAAC;QACrB,OAAO,SAAS,eAAe;IACnC;IACA,MAAM,gBAAgB,CAAC;QACnB,4FAA4F;QAC5F,0DAA0D;QAC1D,MAAM,QAAQ,QAAQ,6BAA6B;QACnD,8FAA8F;QAC9F,2FAA2F;QAC3F,OAAO,SAAS,MAAM,CAAC,gBAAgB,OAAO,SAAS,CAAC,aAAa,CAAC,MAAM,mBAAmB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC;IAC3I;IAEA,MAAM,OAAO,CAAC;QACV,MAAM,IAAI,QAAQ,GAAG,CAAC,QAAQ;QAC9B,OAAO,EAAE,WAAW;IACxB;IACA,MAAM,OAAO,CAAC,UAAY,QAAQ,GAAG,CAAC,QAAQ;IAC9C,MAAM,SAAS,CAAC,IAAM,CAAC,UAAY,KAAK,aAAa;IACrD,MAAM,YAAY,CAAC,UAAY,KAAK,aAAa,WAAW,KAAK,aAAa;IAC9E,MAAM,gBAAgB,CAAC,UAAY,YAAY,YAAY,cAAc,QAAQ,GAAG;IACpF,MAAM,cAAc,OAAO;IAC3B,MAAM,SAAS,OAAO;IACtB,MAAM,qBAAqB,OAAO;IAClC,MAAM,QAAQ,CAAC,MAAQ,CAAC,IAAM,YAAY,MAAM,KAAK,OAAO;IAE5D,MAAM,SAAS,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,OAAO;IAC1F,MAAM,gBAAgB,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,OAAO;IACpG,MAAM,cAAc,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,OAAO;IAChG,MAAM,WAAW,CAAC,UAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,aAAa,OAAO;IAC9E,MAAM,QAAQ,CAAC,SAAS;QACpB,MAAM,KAAK,QAAQ,GAAG,CAAC,UAAU;QACjC,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa,OAAO;IAC5D;IACA,MAAM,aAAa,CAAC,UAAY,MAAM,SAAS;IAC/C,MAAM,YAAY,CAAC,UAAY,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG;IAE9E;;;;;KAKC,GACD,MAAM,eAAe,CAAC,MAAQ,mBAAmB,QAAQ,cAAc,IAAI,GAAG,CAAC,IAAI;IACnF,MAAM,cAAc,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,WAAW;IACjE,mDAAmD,GACnD,MAAM,gBAAgB,CAAC;QACnB,MAAM,IAAI,YAAY;QACtB,OAAO,aAAa,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;IAC7D;IACA;;;;KAIC,GACD,MAAM,gBAAgB,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,IAAI;IAE5D,MAAM,WAAW,CAAC,QAAQ;QACtB,MAAM,WAAW,OAAO;QACxB,SAAS,IAAI,CAAC,CAAC;YACX,EAAE,GAAG,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,OAAO,GAAG;QAC9C;IACJ;IACA,MAAM,QAAQ,CAAC,QAAQ;QACnB,MAAM,UAAU,YAAY;QAC5B,QAAQ,IAAI,CAAC;YACT,MAAM,WAAW,OAAO;YACxB,SAAS,IAAI,CAAC,CAAC;gBACX,SAAS,GAAG;YAChB;QACJ,GAAG,CAAC;YACA,SAAS,GAAG;QAChB;IACJ;IACA,MAAM,UAAU,CAAC,QAAQ;QACrB,MAAM,eAAe,WAAW;QAChC,aAAa,IAAI,CAAC;YACd,SAAS,QAAQ;QACrB,GAAG,CAAC;YACA,OAAO,GAAG,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,EAAE,GAAG;QAC9C;IACJ;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,OAAO,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG;IACtC;IAEA,MAAM,SAAS,CAAC,QAAQ;QACpB,OAAO,UAAU,CAAC;YACd,SAAS,QAAQ;QACrB;IACJ;IACA,MAAM,SAAS,CAAC,QAAQ;QACpB,OAAO,UAAU,CAAC;YACd,SAAS,QAAQ;QACrB;IACJ;IAEA,MAAM,SAAS,CAAC,KAAK,KAAK;QACtB;;;;SAIC,GACD,IAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;YACxD,IAAI,YAAY,CAAC,KAAK,QAAQ;QAClC,OACK;YACD,sCAAsC;YACtC,QAAQ,KAAK,CAAC,uCAAuC,KAAK,aAAa,OAAO,eAAe;YAC7F,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,MAAM,MAAM,QAAQ,GAAG;QACvB,KAAK,OAAO,CAAC,GAAG;YACZ,OAAO,KAAK,GAAG;QACnB;IACJ;IACA,MAAM,UAAU,CAAC,UAAY,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,CAAC,KAAK;YAC7D,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK;YAC3B,OAAO;QACX,GAAG,CAAC;IAEJ,MAAM,QAAQ,CAAC;QACX,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,WAAW,GAAG;QAC1B,+GAA+G;QAC/G,2CAA2C;QAC3C,4FAA4F;QAC5F,gDAAgD;QAChD,OAAO,SAAS,UAAU,CAAC;YACvB,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,MAAM,QAAQ,GAAG;QACvB,IAAI,IAAI,UAAU,KAAK,MAAM;YACzB,IAAI,UAAU,CAAC,WAAW,CAAC;QAC/B;IACJ;IAEA,MAAM,QAAQ,CAAC,UAAU,SAAW,aAAa,OAAO,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC;IAChF,sDAAsD,GACtD,MAAM,OAAO,CAAC,WAAa,MAAM,UAAU;IAC3C,kCAAkC,GAClC,MAAM,YAAY,CAAC,UAAU;QACzB,MAAM,KAAK,aAAa,OAAO,CAAC;QAChC,MAAM,aAAa,QAAQ;QAC3B,OAAO,IAAI;QACX,OAAO;IACX;IACA,+CAA+C,GAC/C,MAAM,SAAS,CAAC,UAAU;QACtB,MAAM,KAAK,UAAU,UAAU;QAC/B,MAAM,UAAU;QAChB,MAAM,aAAa,SAAS;QAC5B,OAAO,IAAI;QACX,OAAO;QACP,OAAO;IACX;IAEA,MAAM,UAAU,CAAC,QAAU,IAAI,OAAO,aAAa,OAAO;IAE1D,6DAA6D;IAC7D,4FAA4F;IAC5F,MAAM,cAAc,CAAC,MACrB,6DAA6D;QAC7D,IAAI,KAAK,KAAK,aAAa,WAAW,IAAI,KAAK,CAAC,gBAAgB;IAEhE,uDAAuD;IACvD,0CAA0C;IAC1C,MAAM,SAAS,CAAC;QACZ,0FAA0F;QAC1F,2FAA2F;QAC3F,MAAM,MAAM,OAAO,WAAW,QAAQ,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG;QAClE,8DAA8D;QAC9D,wFAAwF;QACxF,IAAI,QAAQ,aAAa,QAAQ,QAAQ,IAAI,aAAa,KAAK,MAAM;YACjE,OAAO;QACX;QACA,MAAM,MAAM,IAAI,aAAa;QAC7B,OAAO,cAAc,aAAa,OAAO,CAAC,MAAM,IAAI,CAAC,IAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,QAAQ;IACxG;IAEA,MAAM,cAAc,CAAC,KAAK,UAAU;QAChC,oCAAoC;QACpC,qGAAqG;QACrG,4CAA4C;QAC5C,IAAI,CAAC,SAAS,QAAQ;YAClB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,sCAAsC,UAAU,aAAa,OAAO,eAAe;YACjG,MAAM,IAAI,MAAM,iCAAiC;QACrD;QACA,sGAAsG;QACtG,IAAI,YAAY,MAAM;YAClB,IAAI,KAAK,CAAC,WAAW,CAAC,UAAU;QACpC;IACJ;IACA,MAAM,MAAM,CAAC,SAAS,UAAU;QAC5B,MAAM,MAAM,QAAQ,GAAG;QACvB,YAAY,KAAK,UAAU;IAC/B;IAEA,MAAM,eAAe,CAAC,UAAU;QAC5B,MAAM,MAAM,SAAS;QACrB,MAAM,WAAW,IAAI,sBAAsB;QAC3C,OAAO,UAAU,CAAC;YACd,SAAS,WAAW,CAAC,QAAQ,GAAG;QACpC;QACA,OAAO,aAAa,OAAO,CAAC;IAChC;IAEA,IAAI,oBAAoB,CAAC,IAAI,UAAU,OAAO,GAAG;QAC7C,IAAI,GAAG,OAAO,IAAI;YACd,OAAO,SAAS,IAAI,CAAC;QACzB,OACK,IAAI,WAAW,WAAW,OAAO,QAAQ;YAC1C,OAAO,SAAS,IAAI;QACxB,OACK;YACD,OAAO,SAAS,OAAO,GAAG;QAC9B;IACJ;IAEA,MAAM,aAAa,CAAC,OAAO,WAAW;QAClC,IAAI,UAAU,MAAM,GAAG;QACvB,MAAM,OAAO,WAAW,UAAU,SAAS;QAC3C,MAAO,QAAQ,UAAU,CAAE;YACvB,UAAU,QAAQ,UAAU;YAC5B,MAAM,KAAK,aAAa,OAAO,CAAC;YAChC,IAAI,UAAU,KAAK;gBACf,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,KAAK,KAAK;gBACf;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,YAAY,CAAC,OAAO,WAAW;QACjC,8EAA8E;QAC9E,MAAM,KAAK,CAAC,GAAG,OAAS,KAAK;QAC7B,OAAO,kBAAkB,IAAI,YAAY,OAAO,WAAW;IAC/D;IAEA,MAAM,aAAa,CAAC,OAAO,UAAU,SAAW,WAAW,OAAO,CAAC,IAAM,KAAK,GAAG,WAAW;IAC5F,yGAAyG;IACzG,MAAM,YAAY,CAAC,OAAO,UAAU;QAChC,MAAM,KAAK,CAAC,SAAS,WAAa,KAAK,SAAS;QAChD,OAAO,kBAAkB,IAAI,YAAY,OAAO,UAAU;IAC9D;IAEA,MAAM,UAAU,CAAC,SAAW,UAAU,QAAQ;IAC9C,MAAM,aAAa,SAAC;YAAS,kFAAiB;QAC1C,IAAI,OAAO,UAAU;YACjB,OAAO,QAAQ,GAAG,CAAC,iBAAiB;QACxC,OACK;YACD,sEAAsE;YACtE,OAAO,QAAQ,SAAS,IAAI,CAAC,SAAS,iBAAiB,CAAC,WAAa,OAAO,cAAc;QAC9F;IACJ;IACA,MAAM,SAAS,CAAC,UAAY,QAAQ,GAAG,CAAC,eAAe;IAEvD,MAAM,aAAa,CAAC,OAAO,WAAW,SAAW,WAAW,OAAO,WAAW,QAAQ,MAAM;IAE5F,MAAM,WAAW,CAAC,SAAS,SAAW,WAAW,SAAS,MAAM,IAAI;IAEpE,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,gBAAgB,CAAC,OAAS,CAAC,OAAS,cAAc,SAAS,KAAK,QAAQ,CAAC,WAAW,OAAO;IACjG,MAAM,iBAAiB,CAAC,QAAU,CAAC,OAAS,cAAc,SAAS,MAAM,IAAI,CAAC,KAAK,QAAQ;IAC3F,MAAM,eAAe,CAAC,OAAS,cAAc,SAAS,KAAK,QAAQ,KAAK;IACxE,MAAM,YAAY,CAAC,OAAS,cAAc,SAAS,KAAK,QAAQ,KAAK;IACrE,MAAM,aAAa,eAAe;IAClC,MAAM,aAAa,eAAe;IAClC,MAAM,WAAW,cAAc;IAC/B,MAAM,iBAAiB,eAAe;IACtC,MAAM,eAAe,eAAe;IACpC,MAAM,kBAAkB,eAAe;IACvC,MAAM,OAAO,cAAc;IAC3B,MAAM,eAAe,CAAC;QAAW,IAAI;QAAI,OAAO,CAAC,CAAC,KAAK,KAAK,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,MAAM;IAAM;IACtI,MAAM,cAAc,CAAC,QAAQ,OAAS,cAAc,SAAS,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,oBAAoB;IAChH,MAAM,UAAU,CAAC,MAAM,gBAAkB,cAAc,SAAS,KAAK,QAAQ,IAAI;IACjF,MAAM,SAAS,CAAC,QAAQ,OAAS,cAAc,SAAS,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,eAAe;IACtG,MAAM,YAAY,CAAC,KAAK;QACpB,IAAI,CAAC,KAAK,OAAO;YACb,OAAO;QACX;QACA,OAAO,IAAI,OAAO,CAAC,KAAK,WAAW,KAAK,CAAC,KAAK,KAAK,eAAe;IACtE;IACA,MAAM,YAAY,CAAC,KAAK,KAAK;QACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;QAC1B,IAAI,iBAAiB,IAAI,MAAM,CAAC,gCAAgC,KAAK,MAAM,GAAG,GAAG;YAC7E,OAAO;QACX;QACA,OAAO;IACX;IACA,MAAM,gBAAgB,CAAC,KAAK,MAAQ,IAAI,SAAS,CAAC,KAAK,IAAI,OAAO;IAElE,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,uBAAuB;YAClC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,oBAAoB,OAAO;IACjC,MAAM,qBAAqB,OAAO;IAClC,MAAM,0BAA0B,OAAO;IAEvC,MAAM,kBAAkB,SAAC,QAAQ;YAAa,yEAAQ,CAAC;QACnD,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,gBAAgB,OAAO,MAAM,CAAC,gBAAgB;QACpD,MAAM,WAAW,IAAI,cAAc;QACnC,MAAM,YAAY,mBAAmB;QACrC,MAAM,aAAa,wBAAwB;QAC3C,IAAI;QACJ,IAAI;QACJ,IAAI,iBAAiB;QACrB,YAAY,IAAI,MAAM,CAAC,WAAW;YAC9B,GAAG,UAAU;YACb,GAAI,MAAM,KAAK,GAAG;gBAAE,OAAO,MAAM,KAAK;YAAC,IAAI,CAAC,CAAC;QACjD;QACA,IAAI,CAAC,QAAQ,YAAY,UAAU,EAAE,gBAAgB;YACjD,SAAS,WAAW,CAAC;QACzB;QACA,MAAQ,OAAO,YAAY,UAAU,CAAG;YACpC,MAAM,WAAW,KAAK,QAAQ;YAC9B,IAAI,CAAC,kBAAkB,CAAC,aAAa,UAAU,KAAK,YAAY,CAAC,qBAAqB,UAAU,GAAG;gBAC/F,iBAAiB;YACrB;YACA,IAAI,QAAQ,MAAM,gBAAgB;gBAC9B,SAAS,WAAW,CAAC;gBACrB,YAAY;YAChB,OACK;gBACD,IAAI,CAAC,WAAW;oBACZ,YAAY,IAAI,MAAM,CAAC,WAAW;oBAClC,SAAS,WAAW,CAAC;gBACzB;gBACA,UAAU,WAAW,CAAC;YAC1B;QACJ;QACA,+BAA+B;QAC/B,IAAI,CAAC,kBAAkB,WAAW;YAC9B,UAAU,WAAW,CAAC,IAAI,MAAM,CAAC,MAAM;gBAAE,kBAAkB;YAAI;QACnE;QACA,OAAO;IACX;IAEA,MAAM,QAAQ,SAAS,GAAG;IAC1B,MAAM,YAAY,CAAC,QAAQ,MAAM;QAC7B,MAAM,yBAAyB,CAAC;YAC5B,MAAM,SAAS,WAAW,UAAU;YACpC,IAAI,QAAQ;gBACR,SAAS,IAAI,CAAC,WAAW,CAAC;oBACtB,OAAO,YAAY,CAAC,MAAM,GAAG,UAAU;gBAC3C;YACJ;YACA,MAAM,MAAM,CAAC;QACjB;QACA,MAAM,YAAY,MAAM,MAAM,CAAC,kCAAkC;QACjE,MAAM,WAAW,gBAAgB,QAAQ;QACzC,MAAM,SAAS,MAAM,SAAS;QAC9B,OAAO,aAAa,CAAC;QACrB,OAAO,WAAW,CAAC;QACnB,MAAM,WAAW,OAAO,eAAe;QACvC,IAAK,IAAI,OAAO,SAAS,UAAU,EAAE,MAAM,OAAO,KAAK,UAAU,CAAE;YAC/D,IAAI,KAAK,QAAQ,KAAK,QAAQ,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO;gBACpD,MAAM,MAAM,CAAC;gBACb;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW;YAC/B,MAAM,WAAW,CAAC,UAAU;QAChC;QACA,MAAM,WAAW,CAAC,UAAU;QAC5B,MAAM,SAAS,GAAG,aAAa;QAC/B,IAAI,UAAU,UAAU,OAAO,GAAG,EAAE,SAAS;YACzC,uBAAuB;QAC3B;QACA,MAAM,MAAM,CAAC;QACb,IAAI,UAAU,OAAO,GAAG,EAAE,OAAO;YAC7B,MAAM,MAAM,CAAC;QACjB;IACJ;IAEA,MAAM,sBAAsB,MAAM;IAClC,MAAM,oBAAoB,MAAM;IAChC,MAAM,gBAAgB,CAAC,QAAQ;QAC3B,IAAI,oBAAoB,OAAO;YAC3B,OAAO,MAAM;QACjB,OACK,IAAI,kBAAkB,OAAO;YAC9B,cAAc,MAAM,IAAI,CAAC,CAAC,KAAO,UAAU,QAAQ,GAAG,GAAG,EAAE,KAAK,GAAG;QACvE;IACJ;IACA,MAAM,eAAe,CAAC;QAClB,IAAI,kBAAkB,OAAO;YACzB,OAAO,MAAM;QACjB;IACJ;IACA,MAAM,gBAAgB,CAAC,QAAQ,aAAa;QACxC,IAAI,gBAAgB,SAAS,sBAAsB,KAAI;YACnD,OAAO,SAAS;QACpB,OACK;YACD,OAAO,SAAS,CAAC,OAAS,cAAc,QAAQ;QACpD;IACJ;IAEA,MAAM,qBAAqB,CAAC,WAAW;QACnC,IAAI,aAAa,YAAY;YACzB,OAAO;gBAAE;gBAAW;YAAO;QAC/B;QACA,MAAM,OAAO,SAAS,OAAO,CAAC,WAAW;QACzC,IAAI,aAAa,OAAO;YACpB,OAAO;gBACH,WAAW;gBACX,QAAQ,UAAU,UAAU,UAAU,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM,GAAG;YACvE;QACJ,OACK,IAAI,KAAK,eAAe,IAAI,aAAa,KAAK,eAAe,GAAG;YACjE,OAAO;gBACH,WAAW,KAAK,eAAe;gBAC/B,QAAQ,KAAK,eAAe,CAAC,IAAI,CAAC,MAAM;YAC5C;QACJ,OACK,IAAI,KAAK,WAAW,IAAI,aAAa,KAAK,WAAW,GAAG;YACzD,OAAO;gBACH,WAAW,KAAK,WAAW;gBAC3B,QAAQ;YACZ;QACJ;QACA,OAAO;YAAE;YAAW;QAAO;IAC/B;IACA,MAAM,iBAAiB,CAAC;QACpB,MAAM,SAAS,IAAI,UAAU;QAC7B,MAAM,aAAa,mBAAmB,IAAI,cAAc,EAAE,IAAI,WAAW;QACzE,OAAO,QAAQ,CAAC,WAAW,SAAS,EAAE,WAAW,MAAM;QACvD,MAAM,WAAW,mBAAmB,IAAI,YAAY,EAAE,IAAI,SAAS;QACnE,OAAO,MAAM,CAAC,SAAS,SAAS,EAAE,SAAS,MAAM;QACjD,OAAO;IACX;IAEA,MAAM,YAAY;QAAC;QAAM;QAAM;KAAK;IACpC,MAAM,eAAe,UAAU,IAAI,CAAC;IACpC,MAAM,gBAAgB,CAAC,QAAQ;QAC3B,MAAM,iBAAiB,QAAQ,OAAO,SAAS,CAAC,QAAQ,CAAC;QACzD,OAAO,OAAO,GAAG,CAAC,SAAS,CAAC,gBAAgB,cAAc,mBAAmB,QAAQ;IACzF;IACA,MAAM,uBAAuB,CAAC,YAAY,iBAAmB,cAAc,eAAe,eAAe,MAAM,KAAK,KAAK,cAAc,CAAC,EAAE,KAAK;IAC/I,MAAM,eAAe,CAAC,aAAe,SAAS,WAAW,gBAAgB,CAAC,eAAe;IACzF,MAAM,sBAAsB,CAAC;QACzB,MAAM,aAAa,cAAc;QACjC,MAAM,iBAAiB,OAAO,SAAS,CAAC,iBAAiB;QACzD,IAAI,qBAAqB,YAAY,iBAAiB;YAClD,OAAO,aAAa;QACxB,OACK;YACD,OAAO,SAAS,gBAAgB,CAAC;gBAC7B,OAAO,WAAW,QAAQ,eAAe;YAC7C;QACJ;IACJ;IACA,MAAM,2BAA2B,CAAC,QAAQ;QACtC,MAAM,gBAAgB,SAAS,GAAG,CAAC,MAAM,CAAC;YACtC,MAAM,WAAW,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,YAAY,mBAAmB,QAAQ;YAClF,OAAO,WAAW,WAAW;QACjC;QACA,OAAO,OAAO;IAClB;IACA,MAAM,uBAAuB,CAAC;QAC1B,MAAM,iBAAiB,OAAO,SAAS,CAAC,iBAAiB;QACzD,OAAO,SAAS,yBAAyB,QAAQ,iBAAiB;IACtE;IACA,MAAM,qBAAqB,CAAC,SAAW,SAAS,qBAAqB,SAAS;IAC9E,MAAM,wBAAwB,CAAC,QAAQ;QACnC,MAAM,kBAAkB,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK;QACnD,OAAO,gBAAgB,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,GAAG,OAAO,OAAO;IAC3E;IACA,MAAM,aAAa,CAAC,QAAQ,OAAS,CAAC,WAAW,SAAS,CAAC,eAAe,SAAS,OAAO,WAAW,CAAC,WAAa,OAAO,YAAY,CAAC,KAAK,QAAQ,EAAE;IACtJ,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,eAAe,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,OAAO,GAAG,CAAC,OAAO;QAClE,MAAM,uBAAuB,CAAC,MAAQ,IAAI,QAAQ,CAAC,WAAW,OAAO,mBAAmB;QACxF,MAAM,cAAc,KAAK,cAAc,CAAC,MAAQ,qBAAqB,QAAQ,WAAW,OAAO,MAAM,EAAE;QACvG,OAAO,YAAY,KAAK,CAAC,OAAO,OAAO;IAC3C;IACA,MAAM,iDAAiD,CAAC,OAAS,OAAO,MAAM,MAAM,CAAC,CAAC,SAAW,eAAe,OAAO,GAAG,KACnH,WAAW,QAAQ,MAAM,CAAC,CAAC,aAAe,CAAC,WAAW,WAAW,GAAG,MACpE,UAAU,QAAQ,MAAM,CAAC,CAAC,YAAc,CAAC,WAAW,UAAU,GAAG;IACxE,MAAM,yBAAyB,CAAC,QAAQ;QACpC,MAAM,cAAc,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,SAAS,mBAAmB,QAAQ;QACnF,OAAO,KAAK;IAChB;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,YAAY,uBAAuB,QAAQ,OAAO,SAAS,CAAC,QAAQ;QAC1E,MAAM,kBAAkB,SAAS,OAAO,SAAS,CAAC,iBAAiB,IAAI;QACvE,OAAO,UAAU,OAAO,GAAG,MAAM,CAAC;IACtC;IACA,MAAM,iBAAiB,CAAC;QACpB,MAAM,MAAM,OAAO,SAAS,CAAC,QAAQ;QACrC,OAAO,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,SAAS,mBAAmB,QAAQ;IAC1E;IACA,MAAM,uBAAuB,CAAC;QAC1B,MAAM,gBAAgB,iBAAiB;QACvC,MAAM,cAAc,eAAe;QACnC,OAAO,KAAK,aAAa,CAAC,IAAM,+CAA+C,aAAa,OAAO,CAAC,KAAK,IAAI,CAAC,IAAM,mBAAmB,QAAQ,gBAAgB,CAAC,IAAM;gBAAC;aAAE;IAC7K;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,YAAY,IAAI,OAAO,CAAC,OAAS,uBAAuB,QAAQ,MAAM,KAAK,CAAC;QAClF,OAAO,OAAO;IAClB;IAEA,MAAM,eAAe,CAAC,OAAS,UAAU,IAAI,CAAC,KAAK,SAAS;IAC5D,MAAM,SAAS,CAAC,SAAS,WAAa,UAAU,SAAS,YAAY,iBAChE,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,YAAY,CAAC,aAAa;IAClE,2DAA2D;IAC3D,MAAM,sBAAsB,CAAC,QAAQ,UAAY,YAAY,QAAQ,CAAC,OAAO,GAAG,CAAC,UAAU,CAAC;IAC5F,MAAM,mCAAmC,CAAC;QACtC,MAAM,aAAa,cAAc;QACjC,OAAO,oBAAoB,QAAQ,eAAe,CAAC,OAAO,SAAS,CAAC,UAAU;IAClF;IACA,MAAM,0BAA0B,CAAC,QAAQ;QACrC,MAAM,aAAa,OAAO,GAAG,CAAC,SAAS,CAAC,SAAS;QACjD,OAAO,oBAAoB,QAAQ,eAAe,CAAC,OAAO,SAAS,CAAC,UAAU;IAClF;IACA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,MAAM,cAAc,OAAO,SAAS,CAAC,OAAO;QAC5C,wBAAwB;QACxB,kBAAkB;YACd,SAAS,OAAO,GAAG,CAAC,UAAU,CAAC;YAC/B,SAAS;QACb;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,IAAM,OAAO,GAAG,CAAC,cAAc;IAC1C;IAEA,MAAM,gBAAgB,CAAC,QAAQ,QAAQ,UAAY,OAAO,QAAQ,CAAC,gBAAgB;YAAE;YAAQ;QAAQ;IAErG,MAAM,SAAS,CAAC,KAAO,GAAG,IAAI;IAC9B,MAAM,aAAa,CAAC,KAAO,GAAG,IAAI;IAClC,MAAM,oBAAoB,CAAC,KAAO,WAAW,IAAI,MAAM,CAAC;IACxD,MAAM,mBAAmB,CAAC,KAAO,UAAU,IAAI,MAAM,CAAC;IAEtD,MAAM,cAAc,CAAC,QAAU,oBAAoB;IACnD,MAAM,iBAAiB,CAAC,QAAU,eAAe;IACjD,MAAM,kBAAkB,CAAC,QAAU,gBAAgB;IACnD,MAAM,aAAa,CAAC,QAAU,MAAM,KAAK,GAAG;IAC5C,MAAM,aAAa,CAAC,QAAU,MAAM,UAAU;IAC9C,MAAM,mBAAmB,CAAC;QACtB,MAAM,aAAa,SAAS;QAC5B,MAAM,UAAU,iBAAiB,MAAM,WAAW,KAAK,CAAC,GAAG,CAAC,KAAK;QACjE,OAAO,IAAI,SAAS;IACxB;IACA,MAAM,cAAc,CAAC,IAAI,OAAO,aAAe,OAAO,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,OAAS,CAAC;gBACzF;gBACA,OAAO;gBACP;gBACA,SAAS,iBAAiB;gBAC1B,gBAAgB,QAAQ;gBACxB,gBAAgB,QAAQ;gBACxB,UAAU,KAAK;gBACf,gBAAgB;YACpB,CAAC;IAED,MAAM,cAAc,CAAC,QAAQ;QACzB,SAAS,OAAO,IAAI,EAAE,MAAM,IAAI;IACpC;IACA,MAAM,eAAe,CAAC;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,YAAY,QAAQ,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;QAC5C;IACJ;IACA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,MAAM,KAAK,SAAS,KAAK,OAAO;IACpC;IACA,MAAM,gBAAgB,CAAC,OAAO;QAC1B,MAAM,UAAU;YACZ,MAAM,aAAa,OAAO,CAAC,UAAU;YACrC,MAAM,aAAa,OAAO,CAAC,MAAM;QACrC;QACA,SAAS,QAAQ,IAAI,EAAE,QAAQ,IAAI;QACnC,OAAO;IACX;IACA,MAAM,iBAAiB,CAAC,OAAO,OAAO;QAClC,MAAM,WAAW,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC3B,SAAS,IAAI,CAAC,cAAc,OAAO,YAAY,SAAS,MAAM,QAAQ,GAAG,MAAM,cAAc;QACjG;QACA,OAAO;IACX;IACA,MAAM,mBAAmB,CAAC,UAAU;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,GAAG,GAAG,IAAK;YAC1C,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,mBAAmB;QAC7C;QACA,KAAK,UAAU,IAAI,CAAC,CAAC;YACjB,IAAI,YAAY,QAAQ;gBACpB,OAAO,QAAQ,IAAI,EAAE,MAAM,cAAc;gBACzC,OAAO,QAAQ,IAAI,EAAE,MAAM,cAAc;YAC7C;YACA,OAAO,QAAQ,IAAI,EAAE,MAAM,OAAO;QACtC;IACJ;IACA,MAAM,mBAAmB,CAAC,SAAS;QAC/B,IAAI,KAAK,QAAQ,IAAI,MAAM,MAAM,QAAQ,EAAE;YACvC,QAAQ,IAAI,GAAG,OAAO,QAAQ,IAAI,EAAE,MAAM,QAAQ;QACtD;QACA,OAAO,QAAQ,IAAI,EAAE,MAAM,cAAc;IAC7C;IACA,MAAM,aAAa,CAAC,OAAO,MAAM;QAC7B,MAAM,OAAO,aAAa,OAAO,CAAC,MAAM;QACxC,OAAO,MAAM;QACb,OAAO,MAAM;QACb,OAAO;IACX;IACA,MAAM,aAAa,CAAC,SAAS;QACzB,SAAS,QAAQ,IAAI,EAAE;QACvB,QAAQ,IAAI,GAAG;IACnB;IACA,MAAM,eAAe,CAAC,OAAO,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK;QACzC,KAAK,SAAS,IAAI,CAAC,CAAC;YAChB,IAAI,YAAY,QAAQ;gBACpB,MAAM,OAAO,WAAW,OAAO,MAAM,cAAc,EAAE,MAAM,OAAO;gBAClE,WAAW,SAAS;gBACpB,iBAAiB,SAAS;YAC9B,OACK,IAAI,gBAAgB,QAAQ;gBAC7B,OAAO,QAAQ,IAAI,EAAE,MAAM,OAAO;YACtC,OACK;gBACD,MAAM,OAAO,aAAa,QAAQ,CAAC,AAAC,OAAoB,OAAd,MAAM,OAAO,EAAC;gBACxD,SAAS,QAAQ,IAAI,EAAE;YAC3B;QACJ;QACA,OAAO;IACX;IACA,MAAM,YAAY,CAAC,OAAO,MAAM;QAC5B,MAAM,WAAW,eAAe,OAAO,OAAO,MAAM,KAAK,GAAG,KAAK,MAAM;QACvE,aAAa;QACb,iBAAiB,UAAU;QAC3B,eAAe,MAAM;QACrB,OAAO,KAAK,MAAM,CAAC;IACvB;IACA,MAAM,cAAc,CAAC,OAAO;QACxB,IAAI,uBAAuB,SAAS,IAAI;QACxC,MAAM,OAAO,MAAM,SAAS,CAAC,MAAM,OAAO;YACtC,IAAI,CAAC,eAAe,QAAQ;gBACxB,OAAO,MAAM,KAAK,GAAG,KAAK,MAAM,GAAG,UAAU,OAAO,MAAM,SAAS,aAAa,OAAO,MAAM;YACjG,OACK;gBACD,yHAAyH;gBACzH,IAAI,MAAM,GAAG;oBACT,uBAAuB,SAAS,IAAI,CAAC;oBACrC,OAAO;gBACX;gBACA,OAAO,aAAa,OAAO,MAAM;YACrC;QACJ,GAAG,EAAE;QACL,qBAAqB,IAAI,CAAC,CAAC;YACvB,MAAM,OAAO,aAAa,QAAQ,CAAC,AAAC,OAAgC,OAA1B,kBAAkB,OAAO,EAAC;YACpE,KAAK,MAAM,IAAI,CAAC,CAAC;gBACb,QAAQ,SAAS,IAAI,EAAE;YAC3B;QACJ;QACA,OAAO,KAAK,MAAM,GAAG,CAAC,CAAC,UAAY,QAAQ,IAAI;IACnD;IAEA,MAAM,cAAc,CAAC,aAAa;QAC9B,OAAQ;YACJ,KAAK,SAAS,sBAAsB;gBAChC,MAAM,KAAK;gBACX;YACJ,KAAK,UAAU,uBAAuB;gBAClC,MAAM,KAAK;gBACX;YACJ,KAAK,UAAU,uBAAuB;gBAClC,MAAM,KAAK,GAAG;QACtB;QACA,MAAM,KAAK,GAAG;IAClB;IAEA,MAAM,sBAAsB,CAAC,QAAQ;QACjC,IAAI,YAAY,WAAW,YAAY,SAAS;YAC5C,OAAO,QAAQ,GAAG,OAAO,QAAQ;YACjC,OAAO,cAAc,GAAG;gBAAE,GAAG,OAAO,cAAc;YAAC;QACvD;IACJ;IACA,MAAM,sBAAsB,CAAC;QACzB,sDAAsD;QACtD,MAAM,cAAc,GAAG,OAAO,MAAM,cAAc,EAAE,CAAC,QAAQ,MAAQ,QAAQ;IACjF;IACA,6CAA6C;IAC7C,MAAM,sBAAsB,CAAC,SAAS;QAClC,MAAM,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK;QAClC,kFAAkF;QAClF,MAAM,UAAU,CAAC,QAAU,MAAM,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK;QAChE,MAAM,QAAQ,CAAC,QAAU,MAAM,KAAK,GAAG;QACvC,yFAAyF;QACzF,gDAAgD;QAChD,OAAO,UAAU,QAAQ,QAAQ,KAAK,CAAC,GAAG,SAAS,SAAS,OACvD,OAAO,CAAC,IAAM,UAAU,QAAQ,KAAK,CAAC,QAAQ,IAAI,SAAS;IACpE;IACA,MAAM,mBAAmB,CAAC;QACtB,OAAO,SAAS,CAAC,OAAO;YACpB,oBAAoB,SAAS,GAAG,IAAI,CAAC;gBACjC,IAAI,MAAM,KAAK,IAAI,YAAY,QAAQ;oBACnC,oBAAoB;gBACxB;YACJ,GAAG,CAAC,gBAAkB,oBAAoB,OAAO;QACrD;QACA,OAAO;IACX;IAEA,MAAM,kBAAkB,CAAC,OAAO,eAAe,gBAAgB;QAC3D,IAAI;QACJ,IAAI,UAAU,OAAO;YACjB,OAAO;gBAAC;oBACA,OAAO,QAAQ;oBACf,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;oBACpE,OAAO;oBACP,YAAY;oBACZ,WAAW;gBACf;aAAE;QACV;QACA,cAAc,IAAI,CAAC,CAAC;YAChB,IAAI,GAAG,UAAU,KAAK,EAAE,OAAO;gBAC3B,eAAe,GAAG,CAAC;YACvB;QACJ;QACA,MAAM,mBAAmB,YAAY,MAAM,OAAO,eAAe,GAAG;QACpE,8BAA8B;QAC9B,cAAc,IAAI,CAAC,CAAC;YAChB,IAAI,GAAG,UAAU,GAAG,EAAE,OAAO;gBACzB,eAAe,GAAG,CAAC;YACvB;QACJ;QACA,MAAM,mBAAmB,UAAU,MAC9B,MAAM,CAAC,QACP,GAAG,CAAC,CAAC,OAAS,UAAU,OAAO,eAAe,gBAAgB,OAC9D,KAAK,CAAC,EAAE;QACb,OAAO,iBAAiB,OAAO,GAAG,MAAM,CAAC;IAC7C;IACA,MAAM,YAAY,CAAC,OAAO,eAAe,gBAAgB,OAAS,WAAW,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAM,gBAAgB,OAAO,eAAe,gBAAgB,OAAO,CAAC;YACvK,MAAM,iBAAiB,MAAM,SAAS,OAAO,CAAC,KAAK,SAAS;gBACxD,IAAI,MAAM,GAAG;oBACT,OAAO;gBACX,OACK;oBACD,IAAI,WAAW,UAAU;wBACrB,OAAO,IAAI,MAAM,CAAC,gBAAgB,OAAO,eAAe,gBAAgB;oBAC5E,OACK;wBACD,MAAM,WAAW;4BACb,YAAY;4BACZ;4BACA,SAAS;gCAAC;6BAAQ;4BAClB,YAAY;4BACZ,OAAO;4BACP,gBAAgB,KAAK;wBACzB;wBACA,OAAO,IAAI,MAAM,CAAC;oBACtB;gBACJ;YACJ,GAAG,EAAE;YACL,OAAO,UAAU,OAAO,eAAe,gBAAgB,MAAM,MAAM,CAAC;QACxE;IACA,MAAM,YAAY,CAAC,OAAO,eAAe,gBAAgB,OAAS,KAAK,SAAS,OAAO,CAAC;YACpF,MAAM,SAAS,OAAO,WAAW,YAAY;YAC7C,MAAM,WAAW,QAAQ;YACzB,OAAO,OAAO,UAAU,eAAe,gBAAgB;QAC3D;IACA,MAAM,aAAa,CAAC,OAAO;QACvB,MAAM,iBAAiB,KAAK;QAC5B,MAAM,eAAe;QACrB,OAAO,IAAI,OAAO,CAAC,OAAS,CAAC;gBACzB,YAAY;gBACZ,SAAS,UAAU,cAAc,eAAe,gBAAgB;YACpE,CAAC;IACL;IAEA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,MAAM,oBAAoB,iBAAiB;QAC3C,OAAO,IAAI,mBAAmB,CAAC;YAC3B,MAAM,UAAU,CAAC,eAAe,SAC1B,aAAa,MAAM,OAAO,IAC1B,aAAa;gBAAC,aAAa,QAAQ,CAAC,AAAC,OAAoB,OAAd,MAAM,OAAO,EAAC;aAAM;YACrE,MAAM,gBAAgB,YAAY,SAAS,MAAM,cAAc,GAAG,CAAC;YACnE,OAAO,aAAa,OAAO,CAAC,gBAAgB,QAAQ,QAAQ,GAAG,EAAE;QACrE;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,MAAM,oBAAoB,iBAAiB;QAC3C,OAAO,YAAY,OAAO,eAAe,EAAE,mBAAmB,OAAO;IACzE;IACA,MAAM,iBAAiB,CAAC,QAAQ,UAAY,KAAK,QAAQ,SAAS,aAAa,CAAC;YAC5E,MAAM,kBAAkB,KAAK,SAAS,MAAM,CAAC;YAC7C,OAAO,kBAAkB,iBAAiB,QAAQ,WAAW,kBAAkB,QAAQ;QAC3F;IACA,MAAM,wBAAwB,CAAC,SAAS;QACpC,OAAO,SAAS,SAAS,aAAa,CAAC,QAAU,YAAY,aAAa;IAC9E;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,oBAAoB,IAAI,qBAAqB,SAAS,aAAa,OAAO;QAChF,OAAO,MAAM,KAAK,mBAAmB,IAAI,qBAAqB,KAAK,QAAQ,oBAAoB,IAAI,qBAAqB,CAAC,OAAO,MAAQ,CAAC;gBAAE;gBAAO;YAAI,CAAC;IAC3J;IACA,MAAM,kBAAkB,CAAC,QAAQ,OAAO;QACpC,MAAM,YAAY,WAAW,OAAO,iBAAiB;QACrD,OAAO,WAAW,CAAC;YACf,sBAAsB,SAAS,OAAO,EAAE;YACxC,MAAM,gBAAgB,eAAe,QAAQ,SAAS,OAAO;YAC7D,OAAO,eAAe,CAAC;gBACnB,cAAc,QAAQ,gBAAgB,SAAS,sBAAsB,MAAK,aAAa,yBAAyB,MAAK,cAAc,0BAA0B,KAAI,aAAa,GAAG;YACrL;YACA,OAAO,SAAS,UAAU,EAAE;YAC5B,OAAO,SAAS,UAAU;QAC9B;IACJ;IAEA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,MAAM,QAAQ,QAAQ,qBAAqB;QAC3C,MAAM,UAAU,QAAQ,mBAAmB;QAC3C,IAAI,YAAY;QAChB,IAAI,MAAM,MAAM,IAAI,QAAQ,MAAM,EAAE;YAChC,MAAM,WAAW,OAAO,SAAS,CAAC,WAAW;YAC7C,gBAAgB,QAAQ,OAAO;YAC/B,cAAc,QAAQ,aAAa;YACnC,OAAO,SAAS,CAAC,cAAc,CAAC;YAChC,OAAO,SAAS,CAAC,MAAM,CAAC,eAAe,OAAO,SAAS,CAAC,MAAM;YAC9D,OAAO,WAAW;YAClB,YAAY;QAChB;QACA,OAAO;IACX;IACA,MAAM,oBAAoB,CAAC,QAAQ,cAAgB,CAAC,iCAAiC,WAAW,qBAAqB,QAAQ;IAC7H,MAAM,sBAAsB,CAAC,SAAW,kBAAkB,QAAQ,SAAS,sBAAsB;IACjG,MAAM,uBAAuB,CAAC,SAAW,kBAAkB,QAAQ,UAAU,uBAAuB;IACpG,MAAM,uBAAuB,CAAC,SAAW,kBAAkB,QAAQ,UAAU,uBAAuB;IAEpG,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,QAAQ,SAAS,GAAG;IAC1B;;;;;;;;;;KAUC,GACD,MAAM,iBAAiB,CAAC;QACpB,MAAM,WAAW,CAAC;QAClB,MAAM,gBAAgB,CAAC;YACnB,IAAI,YAAY,GAAG,CAAC,QAAQ,mBAAmB,eAAe;YAC9D,IAAI,SAAS,GAAG,CAAC,QAAQ,gBAAgB,YAAY;YACrD,IAAI,UAAU,YAAY;gBACtB,MAAM,aAAa,MAAM,MAAM,CAAC,QAAQ;oBAAE,iBAAiB;gBAAW;gBACtE,IAAI,UAAU,aAAa,IAAI;oBAC3B,SAAS,KAAK,GAAG,CAAC,QAAQ,UAAU,UAAU,CAAC,MAAM,GAAG;oBACxD,IAAI,OAAO;wBACP,UAAU,YAAY,CAAC,YAAY,UAAU,UAAU,CAAC,OAAO;oBACnE,OACK;wBACD,MAAM,WAAW,CAAC,YAAY,UAAU,UAAU,CAAC,OAAO;oBAC9D;gBACJ,OACK;oBACD,UAAU,WAAW,CAAC;gBAC1B;gBACA,YAAY;gBACZ,SAAS;YACb;YACA,QAAQ,CAAC,QAAQ,mBAAmB,eAAe,GAAG;YACtD,QAAQ,CAAC,QAAQ,gBAAgB,YAAY,GAAG;QACpD;QACA,cAAc;QACd,IAAI,CAAC,IAAI,SAAS,EAAE;YAChB;QACJ;QACA,OAAO;IACX;IACA,MAAM,kBAAkB,CAAC;QACrB,MAAM,kBAAkB,CAAC;YACrB,MAAM,YAAY,CAAC;gBACf,IAAI;gBACJ,IAAI,OAAO,CAAC,KAAK,UAAU,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;gBACzF,IAAI,MAAM;gBACV,MAAO,KAAM;oBACT,IAAI,SAAS,WAAW;wBACpB,OAAO;oBACX;oBACA,oCAAoC;oBACpC,IAAI,CAAC,UAAU,SAAS,KAAK,YAAY,CAAC,qBAAqB,YAAY;wBACvE;oBACJ;oBACA,OAAO,KAAK,WAAW;gBAC3B;gBACA,OAAO,CAAC;YACZ;YACA,IAAI,YAAY,QAAQ,CAAC,QAAQ,mBAAmB,eAAe;YACnE,IAAI,SAAS,QAAQ,CAAC,QAAQ,gBAAgB,YAAY;YAC1D,IAAI,CAAC,WAAW;gBACZ;YACJ;YACA,IAAI,UAAU,cAAc,UAAU,UAAU,EAAE;gBAC9C,MAAM,OAAO;gBACb,SAAS,UAAU;gBACnB,YAAY,UAAU,UAAU;gBAChC,MAAM,MAAM,CAAC;gBACb,IAAI,CAAC,UAAU,aAAa,MAAM,MAAM,OAAO,CAAC,YAAY;oBACxD,UAAU,WAAW,CAAC,MAAM,MAAM,CAAC;gBACvC;YACJ;YACA,QAAQ,CAAC,QAAQ,mBAAmB,eAAe,GAAG;YACtD,QAAQ,CAAC,QAAQ,gBAAgB,YAAY,GAAG;QACpD;QACA,gBAAgB;QAChB;QACA,MAAM,MAAM,MAAM,SAAS;QAC3B,IAAI,QAAQ,CAAC,SAAS,cAAc,EAAE,SAAS,WAAW;QAC1D,IAAI,SAAS,YAAY,EAAE;YACvB,IAAI,MAAM,CAAC,SAAS,YAAY,EAAE,SAAS,SAAS;QACxD;QACA,OAAO,eAAe;IAC1B;IAEA,MAAM,+BAA+B,CAAC;QAClC,OAAQ;YACJ,KAAK;gBAAM,OAAO,eAAe,2BAA2B;YAC5D,KAAK;gBAAM,OAAO,eAAe,2BAA2B;YAC5D,KAAK;gBAAM,OAAO,eAAe,2BAA2B;QAChE;IACJ;IAEA,MAAM,kBAAkB,CAAC,KAAK,IAAI;QAC9B,MAAM,OAAO,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,GAAG;QACrE,IAAI,QAAQ,CAAC,IAAI,mBAAmB;IACxC;IACA,MAAM,aAAa,CAAC,KAAK;QACrB,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO;YACzB,IAAI,YAAY,CAAC,KAAK;QAC1B;IACJ;IACA,MAAM,kBAAkB,CAAC,KAAK,IAAI;QAC9B,WAAW,IAAI,MAAM,CAAC,kBAAkB;QACxC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YACjC,WAAW,IAAI,MAAM,CAAC,uBAAuB;QACjD;IACJ;IACA,MAAM,wBAAwB,CAAC,KAAK,IAAI;QACpC,gBAAgB,KAAK,IAAI;QACzB,gBAAgB,KAAK,IAAI;IAC7B;IACA,MAAM,eAAe,CAAC,KAAK,SAAS;QAChC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAU,IAAI,QAAQ,CAAC,SAAS,OAAO;IAClE;IACA,MAAM,WAAW,CAAC,QAAQ,OAAS,cAAc,SAAS,CAAC,QAAQ,MAAM,OAAO,MAAM,CAAC,gBAAgB;IACvG,MAAM,kBAAkB,CAAC,QAAQ,KAAK,OAAO;QACzC,IAAI,YAAY,GAAG,CAAC,QAAQ,mBAAmB,eAAe;QAC9D,MAAM,SAAS,GAAG,CAAC,QAAQ,gBAAgB,YAAY;QACvD,qBAAqB;QACrB,IAAI,UAAU,YAAY;YACtB,YAAY,UAAU,UAAU,CAAC,KAAK,GAAG,CAAC,QAAQ,UAAU,UAAU,CAAC,MAAM,GAAG,GAAG,IAAI;QAC3F;QACA,IAAI,CAAC,SAAS,KAAK,UAAU,WAAW,GAAG;YACvC,YAAY,UAAU,WAAW;QACrC;QACA,MAAM,oBAAoB,CAAC;YACvB,MAAO,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,KAAK,UAAU,IAAI,SAAS,KAAM;gBAClE,OAAO,KAAK,UAAU;YAC1B;YACA,OAAO;QACX;QACA,iIAAiI;QACjI,qFAAqF;QACrF,wFAAwF;QACxF,gIAAgI;QAChI,kHAAkH;QAClH,qHAAqH;QACrH,6FAA6F;QAC7F,4EAA4E;QAC5E,mCAAmC;QACnC,MAAM,sBAAsB,CAAC,WAAW;YACpC,IAAI;YACJ,MAAM,SAAS,IAAI,SAAS,WAAW,kBAAkB;YACzD,MAAM,MAAM,UAAU,SAAS;YAC/B,IAAI;YACJ,MAAQ,OAAO,MAAM,CAAC,IAAI,GAAK;gBAC3B,IAAI,CAAC,CAAC,OAAO,QAAQ,SAAS,OAAO,KAAK,WAAW,KAAK,CAAC,CAAC,KAAK,KAAK,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,CAAC,GAAG;oBACvI,OAAO,SAAS,IAAI,CAAC;gBACzB;YACJ;YACA,OAAO,SAAS,IAAI;QACxB;QACA,6CAA6C;QAC7C,IAAI,SAAS,aAAa,YAAY;YAClC,IAAI,OAAO,UAAU,WAAW,GAAG;gBAC/B,YAAY,oBAAoB,WAAW,OAAO,KAAK,CAAC;YAC5D,OACK;gBACD,IAAI,UAAU,UAAU,KAAK,QAAQ,SAAS,QAAQ,UAAU,UAAU,GAAG;oBACzE,YAAY,UAAU,UAAU;gBACpC;gBACA,MAAO,UAAU,eAAe,KAAK,QAAQ,CAAC,SAAS,QAAQ,UAAU,eAAe,KAAK,aAAa,UAAU,eAAe,CAAC,EAAG;oBACnI,YAAY,UAAU,eAAe;gBACzC;YACJ;QACJ;QACA,8CAA8C;QAC9C,IAAI,CAAC,SAAS,aAAa,YAAY;YACnC,IAAI,OAAO,UAAU,WAAW,GAAG;gBAC/B,YAAY,oBAAoB,WAAW,MAAM,KAAK,CAAC;YAC3D,OACK;gBACD,IAAI,UAAU,UAAU,KAAK,QAAQ,SAAS,QAAQ,UAAU,UAAU,GAAG;oBACzE,YAAY,UAAU,UAAU;gBACpC;gBACA,MAAO,UAAU,WAAW,KAAK,QAAQ,CAAC,SAAS,QAAQ,UAAU,WAAW,KAAK,aAAa,UAAU,WAAW,CAAC,EAAG;oBACvH,YAAY,UAAU,WAAW;gBACrC;YACJ;QACJ;QACA,MAAO,UAAU,UAAU,KAAK,KAAM;YAClC,MAAM,SAAS,UAAU,UAAU;YACnC,IAAI,YAAY,QAAQ,YAAY;gBAChC,OAAO;YACX;YACA,IAAI,YAAY,IAAI,CAAC,OAAO,QAAQ,GAAG;gBACnC,OAAO;YACX;YACA,YAAY;QAChB;QACA,OAAO;IACX;IACA,MAAM,wBAAwB,CAAC,QAAQ,KAAK;QACxC,MAAM,aAAa,EAAE;QACrB,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,YAAY,gBAAgB,QAAQ,KAAK,MAAM;QACrD,MAAM,UAAU,gBAAgB,QAAQ,KAAK,OAAO;QACpD,IAAI;QACJ,MAAM,WAAW,EAAE;QACnB,IAAK,IAAI,OAAO,WAAW,MAAM,OAAO,KAAK,WAAW,CAAE;YACtD,SAAS,IAAI,CAAC;YACd,IAAI,SAAS,SAAS;gBAClB;YACJ;QACJ;QACA,SAAS,IAAI,CAAC,UAAU,CAAC;YACrB,IAAI;YACJ,IAAI,YAAY,QAAQ,OAAO;gBAC3B,WAAW,IAAI,CAAC;gBAChB,QAAQ;gBACR;YACJ;YACA,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,OAAO;gBACjC,IAAI,KAAK,OAAO;oBACZ,IAAI,MAAM,CAAC;gBACf;gBACA,QAAQ;gBACR;YACJ;YACA,MAAM,cAAc,KAAK,WAAW;YACpC,IAAI,SAAS,cAAc,CAAC,OAAO;gBAC/B,IAAI,WAAW,gBAAgB,YAAY,QAAQ,gBAAiB,CAAC,eAAe,KAAK,UAAU,KAAK,MAAO;oBAC3G,QAAQ;oBACR;gBACJ;YACJ;YACA,IAAI,CAAC,OAAO;gBACR,QAAQ,IAAI,MAAM,CAAC;gBACnB,CAAC,KAAK,KAAK,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,OAAO;gBACnF,WAAW,IAAI,CAAC;YACpB;YACA,MAAM,WAAW,CAAC;QACtB;QACA,OAAO;IACX;IACA,MAAM,qBAAqB,CAAC,KAAK,KAAK;QAClC,MAAM,WAAW,IAAI,QAAQ,CAAC,KAAK;QACnC,IAAI,cAAc,SAAS,MAAM,CAAC,kBAAkB,GAAG;QACvD,cAAc,gBAAgB,OAAO,KAAK;QAC1C,OAAO,aAAa;IACxB;IACA;;;;IAIA,GACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,QAAQ,OAAO,SAAS,CAAC,QAAQ,CAAC;QACxC,MAAM,aAAa,gBAAgB,QAAQ,OAAO,MAAM,OAAO,OAAO;QACtE,IAAI,SAAS,aAAa,OAAO,CAAC,aAAa,aAAa,OAAO,CAAC,MAAM,uBAAuB,IAAI;YACjG,OAAO,MAAM,uBAAuB;QACxC,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,UAAU;QACjC,MAAM,MAAM,OAAO,SAAS,CAAC,MAAM;QACnC,IAAI,eAAe;QACnB,MAAM,OAAO,mBAAmB,QAAQ,mBAAmB,QAAQ;QACnE,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI,IAAI,kBAAkB,CAAC,OAAO,SAAS,CAAC,OAAO,QAAQ,SAAS;YAChE;QACJ;QACA,WAAW,SAAS,WAAW;QAC/B,IAAI,aAAa,MAAM;YACnB,eAAe;QACnB;QACA,MAAM,WAAW,eAAe;QAChC,MAAM,qBAAqB,SAAS,sBAAsB,QAAQ,KAAK,OAAO,OAAO,GAAG,CAAC,UAAU;QACnG,SAAS,IAAI,CAAC,oBAAoB,CAAC;YAC/B,IAAI;YACJ,MAAM,UAAU,MAAM,eAAe;YACrC,MAAM,SAAS,MAAM,UAAU;YAC/B,IAAI,CAAC,eAAe,SAAS;gBACzB,IAAI,WAAW,WAAW,YAAY,QAAQ,QAAQ,KAAK,YAAY,mBAAmB,KAAK,SAAS,SAAS;oBAC7G,YAAY;oBACZ,QAAQ,IAAI,MAAM,CAAC,OAAO;oBAC1B,QAAQ,WAAW,CAAC;gBACxB,OACK;oBACD,YAAY,IAAI,MAAM,CAAC;oBACvB,OAAO,YAAY,CAAC,WAAW;oBAC/B,UAAU,WAAW,CAAC;oBACtB,QAAQ,IAAI,MAAM,CAAC,OAAO;gBAC9B;gBACA,aAAa,KAAK,OAAO;oBACrB;oBAAU;oBAAgB;oBAAiB;oBAAe;oBAC1D;oBAAW;oBAAiB;oBAAkB;oBAAgB;iBACjE;gBACD,sBAAsB,KAAK,WAAW;gBACtC,uBAAuB,OAAO,GAAG,EAAE;YACvC;QACJ;QACA,OAAO,SAAS,CAAC,MAAM,CAAC,gBAAgB;IAC5C;IACA,MAAM,eAAe,CAAC,OAAO;QACzB,OAAO,WAAW,UAAU,MAAM,QAAQ,KAAK,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,QAAQ;IAChH;IACA,MAAM,mBAAmB,CAAC,KAAK,OAAO;QAClC,MAAM,cAAc,IAAI,QAAQ,CAAC,OAAO,mBAAmB;QAC3D,MAAM,QAAQ,IAAI,QAAQ,CAAC,OAAO,mBAAmB;QACrD,OAAO,gBAAgB;IAC3B;IACA,MAAM,iBAAiB,CAAC,MAAM;QAC1B,OAAO,KAAK,SAAS,KAAK,KAAK,SAAS;IAC5C;IACA,MAAM,cAAc,CAAC,KAAK,OAAO;QAC7B,OAAO,aAAa,OAAO,UACvB,qGAAqG;QACrG,sGAAsG;QACtG,iBAAiB,KAAK,OAAO,UAC7B,eAAe,OAAO;IAC9B;IACA,MAAM,yBAAyB,CAAC,KAAK;QACjC,IAAI;QACJ,IAAI,UAAU,UAAU,WAAW;QACnC,IAAI,YAAY,KAAK,WAAW,UAAU;YACtC,MAAM,YAAY;YAClB,MAAQ,OAAO,UAAU,UAAU,CAAG;gBAClC,UAAU,WAAW,CAAC;YAC1B;YACA,IAAI,MAAM,CAAC;QACf;QACA,UAAU,UAAU,eAAe;QACnC,IAAI,YAAY,KAAK,WAAW,UAAU;YACtC,MAAM,YAAY;YAClB,MAAQ,OAAO,UAAU,SAAS,CAAG;gBACjC,UAAU,YAAY,CAAC,MAAM,UAAU,UAAU;YACrD;YACA,IAAI,MAAM,CAAC;QACf;IACJ;IACA,MAAM,eAAe,CAAC,QAAQ,MAAM,UAAU;QAC1C,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC5B,MAAM,UAAU,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM;YACxC,sBAAsB,OAAO,GAAG,EAAE,SAAS;YAC3C,cAAc,QAAQ,6BAA6B,WAAW;QAClE,OACK;YACD,sBAAsB,OAAO,GAAG,EAAE,MAAM;YACxC,cAAc,QAAQ,6BAA6B,WAAW;QAClE;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ,MAAM,UAAU;QAC9C,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG;YAC5B,IAAI,IAAI,UAAU,CAAC,SAAS;gBACxB,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,MAAM,KAAK,GAAG;oBACxB,KAAK,eAAe,CAAC;gBACzB;YACJ;QACJ;QACA,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC5B,MAAM,UAAU,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM;YACxC,sBAAsB,OAAO,GAAG,EAAE,SAAS;YAC3C,cAAc,QAAQ,6BAA6B,WAAW;QAClE,OACK;YACD,sBAAsB,OAAO,GAAG,EAAE,MAAM;YACxC,cAAc,QAAQ,6BAA6B,WAAW;QAClE;IACJ;IACA,MAAM,sBAAsB,CAAC,QAAQ,YAAY,OAAO,UAAU;QAC9D,MAAM,eAAe,WAAW;QAChC,IAAI,gBAAgB,WAAW,QAAQ,KAAK,YAAY,CAAC,mBAAmB,WAAW,CAAC,aAAa,aAAa;YAC9G,qBAAqB;QACzB,OACK;YACD,UAAU,QAAQ,UAAU;YAC5B,MAAM,WAAW,eAAe,OAAO,SAAS,CAAC,MAAM;YACvD,MAAM,WAAW,eAAe;gBAAC;mBAAe;aAAM,GAAG;YACzD,MAAM,iBAAiB,AAAC,gBAAgB,aAAa,cAAe,mBAAmB;YACvF,SAAS,IAAI,CAAC,UAAU,CAAC;gBACrB,eAAe,QAAQ,KAAK,UAAU;YAC1C;YACA,OAAO,SAAS,CAAC,MAAM,CAAC,gBAAgB;QAC5C;IACJ;IACA,MAAM,qBAAqB,CAAC;QACxB,OAAO,qBAAqB;IAChC;IACA,MAAM,mBAAmB,CAAC,QAAQ,YAAY,UAAU;QACpD,IAAI,eAAe,OAAO,OAAO,IAAI;YACjC;QACJ;QACA,IAAI,YAAY;YACZ,IAAI,WAAW,QAAQ,KAAK,YAAY,CAAC,mBAAmB,WAAW,CAAC,aAAa,aAAa;gBAC9F,qBAAqB;YACzB,OACK;gBACD,MAAM,WAAW,eAAe,OAAO,SAAS,CAAC,MAAM;gBACvD,IAAI,aAAa,aAAa;oBAC1B,WAAW,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG;wBAClC,IAAI,IAAI,UAAU,CAAC,SAAS;4BACxB,UAAU,MAAM,CAAC;4BACjB,IAAI,UAAU,MAAM,KAAK,GAAG;gCACxB,WAAW,eAAe,CAAC;4BAC/B;wBACJ;oBACJ;gBACJ;gBACA,sBAAsB,OAAO,GAAG,EAAE,YAAY;gBAC9C,MAAM,UAAU,OAAO,GAAG,CAAC,MAAM,CAAC,YAAY;gBAC9C,uBAAuB,OAAO,GAAG,EAAE;gBACnC,OAAO,SAAS,CAAC,MAAM,CAAC,gBAAgB;gBACxC,UAAU,QAAQ,UAAU;gBAC5B,cAAc,QAAQ,6BAA6B,WAAW;YAClE;QACJ,OACK;YACD,UAAU,QAAQ,UAAU;YAC5B,cAAc,QAAQ,6BAA6B,WAAW;QAClE;IACJ;IACA,MAAM,aAAa,CAAC,QAAQ,UAAU;QAClC,MAAM,aAAa,cAAc;QACjC,IAAI,wBAAwB,QAAQ,aAAa;YAC7C;QACJ;QACA,MAAM,mBAAmB,oBAAoB;QAC7C,MAAM,SAAS,SAAS,WAAW,UAAU,CAAC;QAC9C,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC7B,oBAAoB,QAAQ,YAAY,kBAAkB,UAAU;QACxE,OACK;YACD,iBAAiB,QAAQ,YAAY,UAAU;QACnD;IACJ;IAEA,MAAM,MAAM,SAAS,GAAG;IACxB,MAAM,gBAAgB,CAAC,KAAK;QACxB,MAAM,aAAa,KAAK,aAAa;QACrC,2DAA2D;QAC3D,IAAI,cAAc,WAAW,QAAQ,KAAK,QAAQ,WAAW,UAAU,KAAK,MAAM;YAC9E,MAAM,UAAU,WAAW,eAAe;YAC1C,IAAI,WAAW,QAAQ,QAAQ,KAAK,MAAM;gBACtC,QAAQ,WAAW,CAAC;gBACpB,IAAI,UAAU,KAAK,aAAa;oBAC5B,IAAI,MAAM,CAAC;gBACf;YACJ,OACK;gBACD,IAAI,QAAQ,CAAC,YAAY,iBAAiB;YAC9C;QACJ;QACA,uEAAuE;QACvE,IAAI,WAAW,aAAa;YACxB,MAAM,UAAU,WAAW,eAAe;YAC1C,IAAI,WAAW,QAAQ,QAAQ,KAAK,MAAM;gBACtC,QAAQ,WAAW,CAAC;YACxB;QACJ;IACJ;IACA,MAAM,iBAAiB,CAAC,KAAK;QACzB,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS;QAChD,SAAS,IAAI,CAAC,OAAO,CAAC;YAClB,cAAc,KAAK;QACvB;IACJ;IAEA,MAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW;QACpD,IAAI,OAAO,IAAI,cAAc;QAC7B,MAAM,SAAS,IAAI,WAAW;QAC9B,IAAI,aAAa,SAAS,CAAC,YAAY,SAAS,KAAK,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG;YAC5E,OAAO;QACX;QACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,mBAAmB;QACxD,IAAI,UAAU,OAAO;YACjB,OAAO,SAAS,OAAO,CAAC,MAAM;QAClC;QACA,MAAM,SAAS,IAAI,SAAS,MAAM;QAClC,uDAAuD;QACvD,IAAI,WAAW;YACX,IAAI,UAAU,OAAO,GAAG,EAAE,OAAO;gBAC7B,OAAO,IAAI;YACf;QACJ;QACA,MAAM,SAAS,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,OAAO,KAAK,CAAC,IAAI,CAAC;QACxE,MAAQ,OAAO,SAAW;YACtB,IAAI,KAAK,QAAQ,KAAK,QAAQ,CAAC,KAAK,aAAa,IAAI;gBACjD,OAAO;YACX;YACA,IAAI,cAAc,CAAC,KAAK,QAAQ,CAAC,EAAE;gBAC/B,OAAO;YACX;YACA,IAAI,aAAa,SAAS,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC5C,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,uBAAuB,CAAC,KAAK;QAC/B,MAAM,aAAa,IAAI,UAAU;QACjC,OAAO,WAAW,MAAM,KAAK,KAAK,CAAC,WAAW,UAAU,CAAC,EAAE,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;IAC7F;IACA,MAAM,gBAAgB,CAAC,OAAS,SAAS,IAAI,CAAC,MACzC,GAAG,CAAC,aAAa,OAAO,EACxB,MAAM,CAAC,eACP,MAAM,CAAC,CAAC,KAAO,WAAW,OAAO,CAAC,WAAW;gBAAC;aAAU,EAAE,KAAK;IACpE,MAAM,yBAAyB,CAAC,KAAK;QACjC,IAAI,qBAAqB,KAAK,QAAQ,cAAc,IAAI,UAAU,GAAG;YACjE,IAAI,MAAM,CAAC,IAAI,UAAU,EAAE;QAC/B;IACJ;IACA,MAAM,eAAe,CAAC,KAAK,SAAS;QAChC,IAAI;QACJ,MAAM,YAAY,qBAAqB,KAAK,SAAS,MAAM,UAAU,GAAG;QACxE,uBAAuB,KAAK;QAC5B,IAAI,CAAC,UAAU,KAAK,SAAS,OAAO;YAChC,MAAQ,OAAO,QAAQ,UAAU,CAAG;gBAChC,UAAU,WAAW,CAAC;YAC1B;QACJ;IACJ;IACA,MAAM,kBAAkB,CAAC,KAAK,SAAS;QACnC,IAAI;QACJ,MAAM,KAAK,QAAQ,UAAU;QAC7B,IAAI,CAAC,cAAc,KAAK,YAAY,CAAC,cAAc,KAAK,QAAQ;YAC5D;QACJ;QACA,IAAI,WAAW,MAAM,SAAS,GAAG;YAC7B,WAAW,MAAM,SAAS;QAC9B;QACA,IAAI,OAAO,MAAM,SAAS,EAAE;YACxB,IAAI,KAAK,GAAG,eAAe,GAAG;gBAC1B,IAAI,MAAM,CAAC,GAAG,eAAe;YACjC;QACJ;QACA,MAAM,OAAO,MAAM,SAAS;QAC5B,IAAI,QAAQ,KAAK,SAAS,QAAQ,aAAa,IAAI;YAC/C,IAAI,MAAM,CAAC;QACf;QACA,IAAI,UAAU,KAAK,OAAO,OAAO;YAC7B,MAAM,aAAa,OAAO,CAAC;QAC/B;QACA,aAAa,KAAK,SAAS;QAC3B,IAAI,UAAU;YACV,MAAM,WAAW,CAAC;QACtB;QACA,MAAM,aAAa,SAAS,aAAa,OAAO,CAAC,QAAQ,aAAa,OAAO,CAAC;QAC9E,MAAM,cAAc,aAAa,IAAI,UAAU,CAAC,SAAS,YAAY,SAAS,EAAE;QAChF,IAAI,MAAM,CAAC;QACX,OAAO,aAAa,CAAC;YACjB,IAAI,UAAU,KAAK,SAAS,SAAS,IAAI,OAAO,IAAI;gBAChD,IAAI,MAAM,CAAC;YACf;QACJ;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ,QAAQ;QACtC,MAAM,aAAa,OAAO,CAAC;QAC3B,gBAAgB,OAAO,GAAG,EAAE,QAAQ;QACpC,OAAO,SAAS,CAAC,iBAAiB,CAAC,MAAM;IAC7C;IACA,MAAM,eAAe,CAAC,QAAQ,KAAK,QAAQ;QACvC,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI,IAAI,OAAO,CAAC,OAAO;YACnB,iBAAiB,QAAQ,QAAQ;QACrC,OACK;YACD,MAAM,WAAW,eAAe;YAChC,gBAAgB,KAAK,QAAQ;YAC7B,OAAO,SAAS,CAAC,MAAM,CAAC,gBAAgB;QAC5C;IACJ;IACA,MAAM,gBAAgB,CAAC,QAAQ,KAAK,QAAQ;QACxC,MAAM,WAAW,eAAe;QAChC,gBAAgB,OAAO,GAAG,EAAE,QAAQ;QACpC,MAAM,mBAAmB,gBAAgB;QACzC,OAAO,SAAS,CAAC,MAAM,CAAC;IAC5B;IACA,MAAM,qCAAqC,CAAC,QAAQ;QAChD,MAAM,MAAM,OAAO,GAAG,EAAE,YAAY,OAAO,SAAS;QACpD,MAAM,oBAAoB,UAAU,QAAQ;QAC5C,MAAM,OAAO,sBAAsB,QAAQ;QAC3C,MAAM,KAAK,IAAI,SAAS,CAAC,UAAU,QAAQ,IAAI,MAAM;QACrD,IAAI,IAAI;YACJ,MAAM,KAAK,GAAG,aAAa;YAC3B,IAAI,OAAO,OAAO,OAAO,MAAM,UAAU,KAAK,KAAK;gBAC/C,OAAO;YACX;YACA,MAAM,MAAM,eAAe,UAAU,MAAM;YAC3C,MAAM,UAAU,IAAI,SAAS,CAAC,uBAAuB,QAAQ,KAAK,WAAW,OAAO,MAAM;YAC1F,MAAM,2BAA2B,WAAW,CAAC,YAAY,IAAI,SAAS,CAAC,IAAI,WAAW,IAAI,SAAS,CAAC,SAAS,GAAG;YAChH,IAAI,WAAW,YAAY,MAAM,CAAC,0BAA0B;gBACxD,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACxB,IAAI,WAAW;wBACX,aAAa,QAAQ,KAAK,SAAS;oBACvC,OACK;wBACD,IAAI,aAAa,KAAK;4BAClB,qBAAqB;wBACzB,OACK;4BACD,cAAc,QAAQ,KAAK,IAAI;wBACnC;oBACJ;gBACJ;gBACA,OAAO;YACX,OACK,IAAI,4BAA4B,CAAC,aAAa,YAAY,IAAI;gBAC/D,MAAM,uBAAuB,IAAI,uBAAuB,CAAC,aAAa;gBACtE,IAAI,CAAC,wBAAwB,IAAI,SAAS,CAAC,SAAS,uBAAuB;oBACvE,OAAO;gBACX;gBACA,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACxB,MAAM,WAAW,eAAe;oBAChC,aAAa,KAAK,sBAAsB;oBACxC,qBAAqB,MAAM;oBAC3B,MAAM,mBAAmB,gBAAgB;oBACzC,OAAO,SAAS,CAAC,MAAM,CAAC;gBAC5B;gBACA,OAAO;YACX,OACK,IAAI,CAAC,SAAS;gBACf,IAAI,CAAC,aAAa,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,KAAK,GAAG;oBAC5D,OAAO,WAAW,CAAC,QAAQ,CAAC;wBACxB,qBAAqB;oBACzB;oBACA,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAC,KAAK,OAAO;QAC7B,MAAM,cAAc,IAAI,SAAS,CAAC,MAAM,UAAU,EAAE,IAAI,OAAO,EAAE;QACjE,IAAI,MAAM,CAAC;QACX,IAAI,eAAe,IAAI,OAAO,CAAC,cAAc;YACzC,IAAI,MAAM,CAAC;QACf;IACJ;IACA,MAAM,+BAA+B,CAAC,QAAQ;QAC1C,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,oBAAoB,OAAO,SAAS,CAAC,QAAQ;QACnD,MAAM,OAAO,sBAAsB,QAAQ;QAC3C,MAAM,QAAQ,IAAI,SAAS,CAAC,mBAAmB,IAAI,OAAO,EAAE;QAC5D,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,WAAW;YAAE,oBAAoB;QAAK,IAAI;YACtE,MAAM,MAAM,eAAe,OAAO,SAAS,CAAC,MAAM;YAClD,MAAM,qBAAqB,uBAAuB,QAAQ,KAAK,WAAW;YAC1E,MAAM,UAAU,IAAI,SAAS,CAAC,oBAAoB,MAAM;YACxD,IAAI,sBAAsB,SAAS;gBAC/B,MAAM,mBAAmB,CAAC,UAAY,WAAW;wBAAC;wBAAM;wBAAM;qBAAU,EAAE,KAAK;gBAC/E,MAAM,WAAW,CAAC,OAAS,KAAK,GAAG,KAAK;gBACxC,MAAM,cAAc,UAAU,aAAa,OAAO,CAAC,UAAU,kBAAkB;gBAC/E,MAAM,YAAY,UAAU,aAAa,OAAO,CAAC,IAAI,cAAc,GAAG,kBAAkB;gBACxF,IAAI,CAAC,OAAO,aAAa,WAAW,KAAK;oBACrC,OAAO;gBACX;gBACA,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACxB,MAAM,aAAa,QAAQ,UAAU;oBACrC,YAAY,KAAK,OAAO;oBACxB,uBAAuB,KAAK;oBAC5B,OAAO,SAAS,CAAC,MAAM,CAAC,oBAAoB;oBAC5C,OAAO,SAAS,CAAC,QAAQ,CAAC;gBAC9B;gBACA,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,OAAO,mCAAmC,QAAQ,cAAc,6BAA6B,QAAQ;IACzG;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,oBAAoB,OAAO,SAAS,CAAC,QAAQ;QACnD,MAAM,OAAO,sBAAsB,QAAQ;QAC3C,MAAM,kBAAkB,OAAO,GAAG,CAAC,SAAS,CAAC,mBAAmB,YAAY;QAC5E,OAAO,mBAAmB,qBAAqB,QAAQ,MAAM,GAAG;IACpE;IACA,MAAM,uBAAuB,CAAC;QAC1B,IAAI,iBAAiB,SAAS;YAC1B,OAAO,WAAW,CAAC,QAAQ,CAAC;gBACxB,kHAAkH;gBAClH,IAAI,kBAAkB;gBACtB,MAAM,eAAe,IAAM,kBAAkB;gBAC7C,OAAO,EAAE,CAAC,SAAS;gBACnB,OAAO,WAAW,CAAC;gBACnB,OAAO,GAAG,CAAC,SAAS;gBACpB,IAAI,iBAAiB;oBACjB,OAAO,QAAQ,CAAC;gBACpB;gBACA,eAAe,OAAO,GAAG,EAAE,OAAO,OAAO;YAC7C;YACA,OAAO;QACX;QACA,OAAO;IACX;IACA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,MAAM,YAAY,OAAO,SAAS;QAClC,OAAO,CAAC,wBAAwB,QAAQ,UAAU,OAAO,OAAO,CAAC,UAAU,WAAW,KAClF,qBAAqB,QAAQ,aAAa,qBAAqB,OAAO;IAC9E;IACA,MAAM,UAAU,CAAC;QACb,OAAO,EAAE,CAAC,eAAe,CAAC;YACtB,MAAM,MAAM,EAAE,OAAO,CAAC,WAAW;YACjC,IAAI,CAAC,QAAQ,YAAY,QAAQ,eAAe,KAAK,iBAAiB,SAAS;gBAC3E,eAAe,OAAO,GAAG,EAAE,OAAO,OAAO;YAC7C;QACJ;QACA,OAAO,EAAE,CAAC,WAAW,CAAC;YAClB,IAAI,EAAE,OAAO,KAAK,SAAS,SAAS,EAAE;gBAClC,IAAI,gBAAgB,QAAQ,QAAQ;oBAChC,EAAE,cAAc;gBACpB;YACJ,OACK,IAAI,EAAE,OAAO,KAAK,SAAS,MAAM,EAAE;gBACpC,IAAI,gBAAgB,QAAQ,OAAO;oBAC/B,EAAE,cAAc;gBACpB;YACJ;QACJ;IACJ;IAEA,MAAM,MAAM,CAAC,SAAW,CAAC;YACrB,iBAAiB,CAAC;gBACd,gBAAgB,QAAQ;YAC5B;QACJ,CAAC;IAED,MAAM,aAAa,CAAC,QAAQ;QACxB,MAAM,aAAa,cAAc;QACjC,IAAI,eAAe,QAAQ,wBAAwB,QAAQ,aAAa;YACpE;QACJ;QACA,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,IAAI,SAAS,OAAO,MAAM,GAAG;gBACzB,OAAO,GAAG,CAAC,SAAS,CAAC,YAAY,OAAO,MAAM;YAClD;YACA,IAAI,SAAS,OAAO,KAAK,GAAG;gBACxB,KAAK,OAAO,KAAK,EAAE,CAAC,GAAG,IAAM,OAAO,GAAG,CAAC,SAAS,CAAC,YAAY,GAAG;YACrE;QACJ;IACJ;IAEA,sBAAsB;IACtB,MAAM,wBAAwB,CAAC;QAC3B,MAAM,QAAQ,QAAQ,KAAK,KAAK,KAAK,CAAC;QACtC,MAAM,SAAS,IAAI,OAAO,CAAC,MAAM;YAC7B,MAAM,YAAY,KAAK,WAAW,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK;YACzE,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK;QAC7B;QACA,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAM,MAAM,GAAG;IAC9C;IACA,sBAAsB;IACtB,MAAM,0BAA0B,CAAC;QAC7B;QACA,IAAI,QAAQ,GAAG;YACX,OAAO;QACX,OACK;YACD,MAAM,YAAY,QAAQ;YAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ;YACpC,MAAM,OAAO,wBAAwB;YACrC,MAAM,OAAO,OAAO,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK;YACrD,OAAO,OAAO;QAClB;IACJ;IACA,MAAM,cAAc,CAAC,MAAQ,WAAW,IAAI,CAAC;IAC7C,MAAM,cAAc,CAAC,MAAQ,WAAW,IAAI,CAAC;IAC7C,MAAM,YAAY,CAAC,MAAQ,WAAW,IAAI,CAAC;IAC3C,MAAM,iBAAiB,CAAC;QACpB,IAAI,UAAU,QAAQ;YAClB,OAAO,EAAE,oBAAoB;QACjC,OACK,IAAI,YAAY,QAAQ;YACzB,OAAO,EAAE,uBAAuB;QACpC,OACK,IAAI,YAAY,QAAQ;YACzB,OAAO,EAAE,uBAAuB;QACpC,OACK,IAAI,UAAU,QAAQ;YACvB,OAAO,EAAE,iBAAiB;QAC9B,OACK;YACD,OAAO,EAAE,oBAAoB;QACjC;IACJ;IACA,MAAM,kBAAkB,CAAC;QACrB,OAAQ,eAAe;YACnB,KAAK,EAAE,oBAAoB;gBACvB,OAAO,SAAS,IAAI,CAAC;oBACjB,eAAe,SAAS,IAAI;oBAC5B;gBACJ;YACJ,KAAK,EAAE,uBAAuB;gBAC1B,OAAO,SAAS,IAAI,CAAC;oBACjB,eAAe,SAAS,IAAI,CAAC;oBAC7B,OAAO,sBAAsB,OAAO,QAAQ;gBAChD;YACJ,KAAK,EAAE,uBAAuB;gBAC1B,OAAO,SAAS,IAAI,CAAC;oBACjB,eAAe,SAAS,IAAI,CAAC;oBAC7B,OAAO,sBAAsB,OAAO,QAAQ;gBAChD;YACJ,KAAK,EAAE,iBAAiB;gBACpB,OAAO,SAAS,IAAI,CAAC;oBACjB,eAAe,SAAS,IAAI;oBAC5B,OAAO;gBACX;YACJ,KAAK,EAAE,oBAAoB;gBACvB,OAAO,SAAS,IAAI;QAC5B;IACJ;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,QAAQ,SAAS,OAAO,KAAK,EAAE;QACrC,IAAI,KAAK,OAAO,aAAa,EAAE,gBAAgB;YAC3C,OAAO,wBAAwB;QACnC,OACK,IAAI,KAAK,OAAO,aAAa,EAAE,gBAAgB;YAChD,OAAO,wBAAwB,OAAO,WAAW;QACrD,OACK;YACD,OAAO,OAAO,KAAK;QACvB;IACJ;IAEA,MAAM,OAAO,CAAC;QACV,mFAAmF;QACnF,MAAM,cAAc,cAAc;QAClC,IAAI,CAAC,SAAS,gBAAgB,wBAAwB,QAAQ,cAAc;YACxE;QACJ;QACA,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,WAAW;oBACf;iBACH;YACL;YACA,aAAa;gBACT,OAAO,YAAY;oBACf,OAAO,OAAO,GAAG,CAAC,SAAS,CAAC,aAAa,SAAS;oBAClD,eAAe,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,aAAa;gBAClE;YACJ;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,UAAU,CAAC;gBACP,MAAM,OAAO,IAAI,OAAO;gBACxB,gBAAgB,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC;oBAC9B,OAAO,WAAW,CAAC,iBAAiB,OAAO;wBACvC,OAAO;4BACH,OAAO,OAAO,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK;wBACnD;wBACA,QAAQ;4BACJ,mBAAmB,OAAO,aAAa,CAAC,KAAK,CAAC;wBAClD;oBACJ;gBACJ;gBACA,IAAI,KAAK;YACb;QACJ;IACJ;IAEA,MAAM,wBAAwB,CAAC,QAAQ,WAAa;YAChD,MAAM,aAAa,cAAc;YACjC,OAAO,cAAc,eAAe,WAAW,QAAQ,KAAK;QAChE;IACA,MAAM,iBAAiB,CAAC;QACpB,OAAO,UAAU,CAAC,gBAAgB;YAC9B,KAAK;QACT;IACJ;IACA,MAAM,aAAa,CAAC;QAChB,OAAO,EAAE,CAAC,qBAAqB,CAAC;YAC5B,MAAM,MAAM,EAAE,OAAO,CAAC,WAAW;YACjC,IAAI,QAAQ,UAAU;gBAClB,oBAAoB;YACxB,OACK,IAAI,QAAQ,WAAW;gBACxB,qBAAqB;YACzB;QACJ;QACA,OAAO,UAAU,CAAC,uBAAuB,CAAC,IAAI;YAC1C,WAAW,QAAQ,MAAM;QAC7B;QACA,OAAO,UAAU,CAAC,qBAAqB,CAAC,IAAI;YACxC,WAAW,QAAQ,MAAM;QAC7B;QACA,OAAO,UAAU,CAAC,wBAAwB,CAAC,IAAI;YAC3C,WAAW,QAAQ,MAAM;QAC7B;QACA,OAAO,UAAU,CAAC,cAAc;YAC5B,qBAAqB;QACzB;QACA,eAAe;QACf,OAAO,UAAU,CAAC,iBAAiB,CAAC,IAAI;YACpC,IAAI,SAAS,SAAS;gBAClB,WAAW,QAAQ;YACvB;QACJ;QACA,OAAO,oBAAoB,CAAC,uBAAuB,sBAAsB,QAAQ;QACjF,OAAO,oBAAoB,CAAC,qBAAqB,sBAAsB,QAAQ;QAC/E,OAAO,oBAAoB,CAAC,wBAAwB,sBAAsB,QAAQ;IACtF;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,aAAa,CAAC,OAAS,KAAK,IAAI,KAAK;IAC3C,MAAM,UAAU,CAAC,aAAe,WAAW,MAAM,KAAK;IACtD,MAAM,sBAAsB,CAAC;QACzB,MAAM,iBAAiB,CAAC,QAAQ;YAC5B,MAAM,KAAK,OAAO,MAAM,CAAC;YACzB,OAAO,QAAQ,CAAC,OAAS,GAAG,MAAM,CAAC;YACnC,IAAI,SAAS;gBACT,KAAK,MAAM,CAAC,IAAI,SAAS;YAC7B,OACK;gBACD,KAAK,MAAM,CAAC;YAChB;QACJ;QACA,MAAM,UAAU,CAAC,QAAQ;YACrB,IAAI,WAAW,OAAO;gBAClB,OAAO;uBAAI;oBAAQ;iBAAK;YAC5B,OACK,IAAI,CAAC,QAAQ,WAAW,CAAC,WAAW,OAAO;gBAC5C,eAAe,QAAQ;gBACvB,OAAO,EAAE;YACb,OACK;gBACD,OAAO;YACX;QACJ;QACA,MAAM,aAAa,MAAM,KAAK,QAAQ,IAAI,SAAS,EAAE;QACrD,IAAI,CAAC,QAAQ,aAAa;YACtB,eAAe;QACnB;IACJ;IACA,MAAM,UAAU,CAAC;QACb,OAAO,EAAE,CAAC,WAAW;YACjB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,aAAa,CAAC,SAAS,CAAC,QAAU,OAAO,OAAO;QAC3D;IACJ;IAEA,MAAM,cAAc,CAAC;QACjB,OAAO,EAAE,CAAC,WAAW,CAAC;YAClB,oEAAoE;YACpE,IAAI,EAAE,OAAO,KAAK,SAAS,GAAG,IAAI,SAAS,cAAc,CAAC,IAAI;gBAC1D;YACJ;YACA,OAAO,WAAW,CAAC,QAAQ,CAAC;gBACxB,IAAI,EAAE,QAAQ,GAAG,qBAAqB,UAAU,oBAAoB,SAAS;oBACzE,EAAE,cAAc;gBACpB;YACJ;QACJ;IACJ;IACA,MAAM,QAAQ,CAAC;QACX,IAAI,kBAAkB,SAAS;YAC3B,YAAY;QAChB;QACA,QAAQ;IACZ;IAEA,MAAM,2BAA2B,CAAC,QAAQ,WAAa,CAAC;YACpD,MAAM,sBAAsB,CAAC;gBACzB,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE;gBAChC,IAAI,UAAU,CAAC,CAAC,wBAAwB,QAAQ,EAAE,OAAO,KAAK,OAAO,SAAS,CAAC,UAAU;YAC7F;YACA,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC1C,OAAO,qBAAqB,QAAQ;QACxC;IACA,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,CAAC,UAAY,IAAM,OAAO,WAAW,CAAC;QACnD,IAAI,CAAC,OAAO,SAAS,CAAC,YAAY;YAC9B,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW;gBAC1C,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU,KAAK;gBACf,SAAS,yBAAyB,QAAQ;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW;gBAC1C,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU,KAAK;gBACf,SAAS,yBAAyB,QAAQ;YAC9C;QACJ;IACJ;IAEA,MAAM,yBAAyB,CAAC,QAAQ,WAAa,CAAC;YAClD,MAAM,oBAAoB,CAAC,IAAM,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,wBAAwB,QAAQ,EAAE,OAAO;YACzH,OAAO,qBAAqB,QAAQ;QACxC;IACA,MAAM,WAAW,CAAC;QACd,MAAM,iBAAiB;YACnB,MAAM;YACN,MAAM;YACN,UAAU,IAAM,OAAO,WAAW,CAAC;YACnC,SAAS,uBAAuB,QAAQ;QAC5C;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa;QAC5C,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS;YACvC,QAAQ,CAAC;gBACL,MAAM,aAAa,cAAc,QAAQ;gBACzC,OAAO,SAAS,cAAc;oBAAC;iBAAY,GAAG,EAAE;YACpD;QACJ;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,SAAS,CAAC;YACnB,WAAW;YACX,QAAQ;YACR,IAAI,CAAC,OAAO,SAAS,CAAC,OAAO,OAAO;gBAChC,MAAM;gBACN,WAAW;YACf,OACK;gBACD,eAAe;YACnB;YACA,WAAW;YACX,SAAS;YACT,OAAO,IAAI;QACf;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/lists/index.js"], "sourcesContent": ["// Exports the \"lists\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/lists')\n//   ES2015:\n//     import 'tinymce/plugins/lists'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,SAAS;AACT,cAAc;AACd,uCAAuC;AACvC,YAAY;AACZ,qCAAqC", "ignoreList": [0], "debugId": null}}]}