{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/advlist/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const applyListFormat = (editor, listName, styleValue) => {\n        const cmd = listName === 'UL' ? 'InsertUnorderedList' : 'InsertOrderedList';\n        editor.execCommand(cmd, false, styleValue === false ? null : { 'list-style-type': styleValue });\n    };\n\n    const register$2 = (editor) => {\n        editor.addCommand('ApplyUnorderedListStyle', (ui, value) => {\n            applyListFormat(editor, 'UL', value['list-style-type']);\n        });\n        editor.addCommand('ApplyOrderedListStyle', (ui, value) => {\n            applyListFormat(editor, 'OL', value['list-style-type']);\n        });\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$1 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('advlist_number_styles', {\n            processor: 'string[]',\n            default: 'default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman'.split(',')\n        });\n        registerOption('advlist_bullet_styles', {\n            processor: 'string[]',\n            default: 'default,disc,circle,square'.split(',')\n        });\n    };\n    const getNumberStyles = option('advlist_number_styles');\n    const getBulletStyles = option('advlist_bullet_styles');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    /* eslint-enable */\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const map = (obj, f) => {\n        return tupleMap(obj, (x, i) => ({\n            k: i,\n            v: f(x, i)\n        }));\n    };\n    const tupleMap = (obj, f) => {\n        const r = {};\n        each(obj, (x, i) => {\n            const tuple = f(x, i);\n            r[tuple.k] = tuple.v;\n        });\n        return r;\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const isCustomList = (list) => /\\btox\\-/.test(list.className);\n    const isChildOfBody = (editor, elm) => {\n        return editor.dom.isChildOf(elm, editor.getBody());\n    };\n    const matchNodeNames = (regex) => (node) => isNonNullable(node) && regex.test(node.nodeName);\n    const isListNode = matchNodeNames(/^(OL|UL|DL)$/);\n    const isTableCellNode = matchNodeNames(/^(TH|TD)$/);\n    const inList = (editor, parents, nodeName) => findUntil(parents, (parent) => isListNode(parent) && !isCustomList(parent), isTableCellNode)\n        .exists((list) => list.nodeName === nodeName && isChildOfBody(editor, list));\n    const getSelectedStyleType = (editor) => {\n        const listElm = editor.dom.getParent(editor.selection.getNode(), 'ol,ul');\n        const style = editor.dom.getStyle(listElm, 'listStyleType');\n        return Optional.from(style);\n    };\n    // Lists/core/Util.ts - Duplicated in Lists plugin\n    const isWithinNonEditable = (editor, element) => element !== null && !editor.dom.isEditable(element);\n    const isWithinNonEditableList = (editor, element) => {\n        const parentList = editor.dom.getParent(element, 'ol,ul,dl');\n        return isWithinNonEditable(editor, parentList) || !editor.selection.isEditable();\n    };\n    const setNodeChangeHandler = (editor, nodeChangeHandler) => {\n        const initialNode = editor.selection.getNode();\n        // Set the initial state\n        nodeChangeHandler({\n            parents: editor.dom.getParents(initialNode),\n            element: initialNode\n        });\n        editor.on('NodeChange', nodeChangeHandler);\n        return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n\n    // <ListStyles>\n    const styleValueToText = (styleValue) => {\n        return styleValue.replace(/\\-/g, ' ').replace(/\\b\\w/g, (chr) => {\n            return chr.toUpperCase();\n        });\n    };\n    const normalizeStyleValue = (styleValue) => isNullable(styleValue) || styleValue === 'default' ? '' : styleValue;\n    const makeSetupHandler = (editor, nodeName) => (api) => {\n        const updateButtonState = (editor, parents) => {\n            const element = editor.selection.getStart(true);\n            api.setActive(inList(editor, parents, nodeName));\n            api.setEnabled(!isWithinNonEditableList(editor, element));\n        };\n        const nodeChangeHandler = (e) => updateButtonState(editor, e.parents);\n        return setNodeChangeHandler(editor, nodeChangeHandler);\n    };\n    const addSplitButton = (editor, id, tooltip, cmd, nodeName, styles) => {\n        const listStyleTypeAliases = {\n            'lower-latin': 'lower-alpha',\n            'upper-latin': 'upper-alpha',\n            'lower-alpha': 'lower-latin',\n            'upper-alpha': 'upper-latin'\n        };\n        const stylesContainsAliasMap = map(listStyleTypeAliases, (alias) => contains(styles, alias));\n        editor.ui.registry.addSplitButton(id, {\n            tooltip,\n            icon: nodeName === \"OL\" /* ListType.OrderedList */ ? 'ordered-list' : 'unordered-list',\n            presets: 'listpreview',\n            columns: nodeName === \"OL\" /* ListType.OrderedList */ ? 3 : 4,\n            fetch: (callback) => {\n                const items = global.map(styles, (styleValue) => {\n                    const iconStyle = nodeName === \"OL\" /* ListType.OrderedList */ ? 'num' : 'bull';\n                    const iconName = styleValue === 'decimal' ? 'default' : styleValue;\n                    const itemValue = normalizeStyleValue(styleValue);\n                    const displayText = styleValueToText(styleValue);\n                    return {\n                        type: 'choiceitem',\n                        value: itemValue,\n                        icon: 'list-' + iconStyle + '-' + iconName,\n                        text: displayText\n                    };\n                });\n                callback(items);\n            },\n            onAction: () => editor.execCommand(cmd),\n            onItemAction: (_splitButtonApi, value) => {\n                applyListFormat(editor, nodeName, value);\n            },\n            select: (value) => {\n                const listStyleType = getSelectedStyleType(editor);\n                return listStyleType.exists((listStyle) => value === listStyle || (listStyleTypeAliases[listStyle] === value && !stylesContainsAliasMap[value]));\n            },\n            onSetup: makeSetupHandler(editor, nodeName)\n        });\n    };\n    const addButton = (editor, id, tooltip, cmd, nodeName, styleValue) => {\n        editor.ui.registry.addToggleButton(id, {\n            active: false,\n            tooltip,\n            icon: nodeName === \"OL\" /* ListType.OrderedList */ ? 'ordered-list' : 'unordered-list',\n            onSetup: makeSetupHandler(editor, nodeName),\n            // Need to make sure the button removes rather than applies if a list of the same type is selected\n            onAction: () => editor.queryCommandState(cmd) || styleValue === '' ? editor.execCommand(cmd) : applyListFormat(editor, nodeName, styleValue)\n        });\n    };\n    const addControl = (editor, id, tooltip, cmd, nodeName, styles) => {\n        if (styles.length > 1) {\n            addSplitButton(editor, id, tooltip, cmd, nodeName, styles);\n        }\n        else {\n            addButton(editor, id, tooltip, cmd, nodeName, normalizeStyleValue(styles[0]));\n        }\n    };\n    const register = (editor) => {\n        addControl(editor, 'numlist', 'Numbered list', 'InsertOrderedList', \"OL\" /* ListType.OrderedList */, getNumberStyles(editor));\n        addControl(editor, 'bullist', 'Bullet list', 'InsertUnorderedList', \"UL\" /* ListType.UnorderedList */, getBulletStyles(editor));\n    };\n\n    var Plugin = () => {\n        global$1.add('advlist', (editor) => {\n            if (editor.hasPlugin('lists')) {\n                register$1(editor);\n                register(editor);\n                register$2(editor);\n            }\n            else {\n                // eslint-disable-next-line no-console\n                console.error('Please use the Lists plugin together with the List Styles plugin.');\n            }\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,kBAAkB,CAAC,QAAQ,UAAU;QACvC,MAAM,MAAM,aAAa,OAAO,wBAAwB;QACxD,OAAO,WAAW,CAAC,KAAK,OAAO,eAAe,QAAQ,OAAO;YAAE,mBAAmB;QAAW;IACjG;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,2BAA2B,CAAC,IAAI;YAC9C,gBAAgB,QAAQ,MAAM,KAAK,CAAC,kBAAkB;QAC1D;QACA,OAAO,UAAU,CAAC,yBAAyB,CAAC,IAAI;YAC5C,gBAAgB,QAAQ,MAAM,KAAK,CAAC,kBAAkB;QAC1D;IACJ;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS,sEAAsE,KAAK,CAAC;QACzF;QACA,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS,6BAA6B,KAAK,CAAC;QAChD;IACJ;IACA,MAAM,kBAAkB,OAAO;IAC/B,MAAM,kBAAkB,OAAO;IAE/B,6DAA6D,GAC7D,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IAEzC;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,MAAM,gBAAgB,MAAM,SAAS,CAAC,OAAO;IAC7C,iBAAiB,GACjB,MAAM,aAAa,CAAC,IAAI,IAAM,cAAc,IAAI,CAAC,IAAI;IACrD,MAAM,WAAW,CAAC,IAAI,IAAM,WAAW,IAAI,KAAK,CAAC;IACjD,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,MAAM,CAAC,KAAK;QACd,OAAO,SAAS,KAAK,CAAC,GAAG,IAAM,CAAC;gBAC5B,GAAG;gBACH,GAAG,EAAE,GAAG;YACZ,CAAC;IACL;IACA,MAAM,WAAW,CAAC,KAAK;QACnB,MAAM,IAAI,CAAC;QACX,KAAK,KAAK,CAAC,GAAG;YACV,MAAM,QAAQ,EAAE,GAAG;YACnB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;QACxB;QACA,OAAO;IACX;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,eAAe,CAAC,OAAS,UAAU,IAAI,CAAC,KAAK,SAAS;IAC5D,MAAM,gBAAgB,CAAC,QAAQ;QAC3B,OAAO,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,OAAO,OAAO;IACnD;IACA,MAAM,iBAAiB,CAAC,QAAU,CAAC,OAAS,cAAc,SAAS,MAAM,IAAI,CAAC,KAAK,QAAQ;IAC3F,MAAM,aAAa,eAAe;IAClC,MAAM,kBAAkB,eAAe;IACvC,MAAM,SAAS,CAAC,QAAQ,SAAS,WAAa,UAAU,SAAS,CAAC,SAAW,WAAW,WAAW,CAAC,aAAa,SAAS,iBACrH,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,YAAY,cAAc,QAAQ;IAC1E,MAAM,uBAAuB,CAAC;QAC1B,MAAM,UAAU,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,OAAO,IAAI;QACjE,MAAM,QAAQ,OAAO,GAAG,CAAC,QAAQ,CAAC,SAAS;QAC3C,OAAO,SAAS,IAAI,CAAC;IACzB;IACA,kDAAkD;IAClD,MAAM,sBAAsB,CAAC,QAAQ,UAAY,YAAY,QAAQ,CAAC,OAAO,GAAG,CAAC,UAAU,CAAC;IAC5F,MAAM,0BAA0B,CAAC,QAAQ;QACrC,MAAM,aAAa,OAAO,GAAG,CAAC,SAAS,CAAC,SAAS;QACjD,OAAO,oBAAoB,QAAQ,eAAe,CAAC,OAAO,SAAS,CAAC,UAAU;IAClF;IACA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,MAAM,cAAc,OAAO,SAAS,CAAC,OAAO;QAC5C,wBAAwB;QACxB,kBAAkB;YACd,SAAS,OAAO,GAAG,CAAC,UAAU,CAAC;YAC/B,SAAS;QACb;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,IAAM,OAAO,GAAG,CAAC,cAAc;IAC1C;IAEA,eAAe;IACf,MAAM,mBAAmB,CAAC;QACtB,OAAO,WAAW,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,CAAC;YACpD,OAAO,IAAI,WAAW;QAC1B;IACJ;IACA,MAAM,sBAAsB,CAAC,aAAe,WAAW,eAAe,eAAe,YAAY,KAAK;IACtG,MAAM,mBAAmB,CAAC,QAAQ,WAAa,CAAC;YAC5C,MAAM,oBAAoB,CAAC,QAAQ;gBAC/B,MAAM,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC;gBAC1C,IAAI,SAAS,CAAC,OAAO,QAAQ,SAAS;gBACtC,IAAI,UAAU,CAAC,CAAC,wBAAwB,QAAQ;YACpD;YACA,MAAM,oBAAoB,CAAC,IAAM,kBAAkB,QAAQ,EAAE,OAAO;YACpE,OAAO,qBAAqB,QAAQ;QACxC;IACA,MAAM,iBAAiB,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU;QACxD,MAAM,uBAAuB;YACzB,eAAe;YACf,eAAe;YACf,eAAe;YACf,eAAe;QACnB;QACA,MAAM,yBAAyB,IAAI,sBAAsB,CAAC,QAAU,SAAS,QAAQ;QACrF,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI;YAClC;YACA,MAAM,aAAa,KAAK,wBAAwB,MAAK,iBAAiB;YACtE,SAAS;YACT,SAAS,aAAa,KAAK,wBAAwB,MAAK,IAAI;YAC5D,OAAO,CAAC;gBACJ,MAAM,QAAQ,OAAO,GAAG,CAAC,QAAQ,CAAC;oBAC9B,MAAM,YAAY,aAAa,KAAK,wBAAwB,MAAK,QAAQ;oBACzE,MAAM,WAAW,eAAe,YAAY,YAAY;oBACxD,MAAM,YAAY,oBAAoB;oBACtC,MAAM,cAAc,iBAAiB;oBACrC,OAAO;wBACH,MAAM;wBACN,OAAO;wBACP,MAAM,UAAU,YAAY,MAAM;wBAClC,MAAM;oBACV;gBACJ;gBACA,SAAS;YACb;YACA,UAAU,IAAM,OAAO,WAAW,CAAC;YACnC,cAAc,CAAC,iBAAiB;gBAC5B,gBAAgB,QAAQ,UAAU;YACtC;YACA,QAAQ,CAAC;gBACL,MAAM,gBAAgB,qBAAqB;gBAC3C,OAAO,cAAc,MAAM,CAAC,CAAC,YAAc,UAAU,aAAc,oBAAoB,CAAC,UAAU,KAAK,SAAS,CAAC,sBAAsB,CAAC,MAAM;YAClJ;YACA,SAAS,iBAAiB,QAAQ;QACtC;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU;QACnD,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI;YACnC,QAAQ;YACR;YACA,MAAM,aAAa,KAAK,wBAAwB,MAAK,iBAAiB;YACtE,SAAS,iBAAiB,QAAQ;YAClC,kGAAkG;YAClG,UAAU,IAAM,OAAO,iBAAiB,CAAC,QAAQ,eAAe,KAAK,OAAO,WAAW,CAAC,OAAO,gBAAgB,QAAQ,UAAU;QACrI;IACJ;IACA,MAAM,aAAa,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU;QACpD,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,eAAe,QAAQ,IAAI,SAAS,KAAK,UAAU;QACvD,OACK;YACD,UAAU,QAAQ,IAAI,SAAS,KAAK,UAAU,oBAAoB,MAAM,CAAC,EAAE;QAC/E;IACJ;IACA,MAAM,WAAW,CAAC;QACd,WAAW,QAAQ,WAAW,iBAAiB,qBAAqB,KAAK,wBAAwB,KAAI,gBAAgB;QACrH,WAAW,QAAQ,WAAW,eAAe,uBAAuB,KAAK,0BAA0B,KAAI,gBAAgB;IAC3H;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,WAAW,CAAC;YACrB,IAAI,OAAO,SAAS,CAAC,UAAU;gBAC3B,WAAW;gBACX,SAAS;gBACT,WAAW;YACf,OACK;gBACD,sCAAsC;gBACtC,QAAQ,KAAK,CAAC;YAClB;QACJ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/advlist/index.js"], "sourcesContent": ["// Exports the \"advlist\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/advlist')\n//   ES2015:\n//     import 'tinymce/plugins/advlist'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,SAAS;AACT,cAAc;AACd,yCAAyC;AACzC,YAAY;AACZ,uCAAuC", "ignoreList": [0], "debugId": null}}]}