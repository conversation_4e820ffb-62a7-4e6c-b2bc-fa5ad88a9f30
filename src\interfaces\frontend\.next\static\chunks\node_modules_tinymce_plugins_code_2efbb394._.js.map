{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/code/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const setContent = (editor, html) => {\n        // We get a lovely \"Wrong document\" error in IE 11 if we\n        // don't move the focus to the editor before creating an undo\n        // transaction since it tries to make a bookmark for the current selection\n        editor.focus();\n        editor.undoManager.transact(() => {\n            editor.setContent(html);\n        });\n        editor.selection.setCursorLocation();\n        editor.nodeChanged();\n    };\n    const getContent = (editor) => {\n        return editor.getContent({ source_view: true });\n    };\n\n    const open = (editor) => {\n        const editorContent = getContent(editor);\n        editor.windowManager.open({\n            title: 'Source Code',\n            size: 'large',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        type: 'textarea',\n                        name: 'code'\n                    }\n                ]\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: {\n                code: editorContent\n            },\n            onSubmit: (api) => {\n                setContent(editor, api.getData().code);\n                api.close();\n            }\n        });\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('mceCodeEditor', () => {\n            open(editor);\n        });\n    };\n\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceCodeEditor');\n        editor.ui.registry.addButton('code', {\n            icon: 'sourcecode',\n            tooltip: 'Source code',\n            onAction\n        });\n        editor.ui.registry.addMenuItem('code', {\n            icon: 'sourcecode',\n            text: 'Source code',\n            onAction\n        });\n    };\n\n    var Plugin = () => {\n        global.add('code', (editor) => {\n            register$1(editor);\n            register(editor);\n            return {};\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,aAAa,CAAC,QAAQ;QACxB,wDAAwD;QACxD,6DAA6D;QAC7D,0EAA0E;QAC1E,OAAO,KAAK;QACZ,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,OAAO,UAAU,CAAC;QACtB;QACA,OAAO,SAAS,CAAC,iBAAiB;QAClC,OAAO,WAAW;IACtB;IACA,MAAM,aAAa,CAAC;QAChB,OAAO,OAAO,UAAU,CAAC;YAAE,aAAa;QAAK;IACjD;IAEA,MAAM,OAAO,CAAC;QACV,MAAM,gBAAgB,WAAW;QACjC,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;oBACV;iBACH;YACL;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;gBACT,MAAM;YACV;YACA,UAAU,CAAC;gBACP,WAAW,QAAQ,IAAI,OAAO,GAAG,IAAI;gBACrC,IAAI,KAAK;YACb;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,iBAAiB;YAC/B,KAAK;QACT;IACJ;IAEA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ;YACjC,MAAM;YACN,SAAS;YACT;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ;YACnC,MAAM;YACN,MAAM;YACN;QACJ;IACJ;IAEA,IAAI,SAAS;QACT,OAAO,GAAG,CAAC,QAAQ,CAAC;YAChB,WAAW;YACX,SAAS;YACT,OAAO,CAAC;QACZ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/code/index.js"], "sourcesContent": ["// Exports the \"code\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/code')\n//   ES2015:\n//     import 'tinymce/plugins/code'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,SAAS;AACT,cAAc;AACd,sCAAsC;AACtC,YAAY;AACZ,oCAAoC", "ignoreList": [0], "debugId": null}}]}