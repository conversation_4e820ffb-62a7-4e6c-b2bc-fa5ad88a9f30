from fastapi import <PERSON><PERSON><PERSON>, Form, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from src.langgraph.workflow import build_newsletter_graph
from src.mcp.newsletter_instanciation import newsletter_client
from src.tools.email_utils import send_newsletter_to_csv
from fastapi.staticfiles import StaticFiles
from src.tools.utils_file_name import generate_newsletter_filename
from pydantic import BaseModel
from src.tools.revise_manually import revise_manually
import traceback
import logging
from datetime import datetime
import os
import shutil


app = FastAPI()
app.mount("/static", StaticFiles(directory="exported_newsletters"), name="static")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

graph = build_newsletter_graph(with_revision=False, skip_vectorization=True)
last_generated_newsletter = None

GENERATED_DIR = "exported_newsletters"
os.makedirs(GENERATED_DIR, exist_ok=True)


# === MODELES DE DONNÉES ===
class NewsletterRequest(BaseModel):
    theme: str
    description: str

class FeedbackRequest(BaseModel):
    feedback: str
    theme: str
    description: str

# === ROUTES PRINCIPALES ===

@app.post("/generate-web")
async def generate_newsletter(data: NewsletterRequest):
    global last_generated_newsletter
    try:
        if newsletter_client.session is None:
            await newsletter_client.start()

        result = await graph.ainvoke({
            "theme": data.theme.strip(),
            "description": data.description.strip(),
            "use_file": False
        })

        newsletter = result.get("newsletter")
        if not newsletter:
            raise HTTPException(status_code=500, detail="Newsletter vide générée.")

        # Enregistrement
        filename = os.path.join(GENERATED_DIR, generate_newsletter_filename(data.theme)) 

        with open(filename, "w", encoding="utf-8") as f:
            f.write(newsletter)

        last_generated_newsletter = newsletter

        return {"newsletter": newsletter}

    except Exception as e:
        logging.error("Erreur génération web : %s", str(e))
        logging.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Erreur génération via web : {str(e)}")


@app.post("/generate-file")
async def generate_newsletter_from_file(
    theme: str = Form(...),
    description: str = Form(...),
    file: UploadFile = File(...)
):
    global last_generated_newsletter
    try:
        content_bytes = await file.read()
        text = content_bytes.decode("utf-8", errors="ignore")

        if newsletter_client.session is None:
            await newsletter_client.start()

        result = await graph.ainvoke({
            "theme": theme.strip(),
            "description": description.strip(),
            "text": text,
            "use_file": True
        })

        newsletter = result.get("newsletter")
        if not newsletter:
            raise HTTPException(status_code=500, detail="Newsletter vide générée.")

        # Enregistrement
        filename = os.path.join(GENERATED_DIR, generate_newsletter_filename(theme))  
        with open(filename, "w", encoding="utf-8") as f:
            f.write(newsletter)

        last_generated_newsletter = newsletter

        return {"newsletter": newsletter}

    except Exception as e:
        logging.error("Erreur génération fichier : %s", str(e))
        logging.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Erreur génération via fichier : {str(e)}")



@app.post("/revise")
async def revise_newsletter(data: FeedbackRequest):
    global last_generated_newsletter
    if not last_generated_newsletter:
        raise HTTPException(status_code=400, detail="Aucune newsletter générée.")
    try:
        if newsletter_client.session is None:
            await newsletter_client.start()

        revised = await newsletter_client.call_tool("revise_newsletter", {
            "original_newsletter": last_generated_newsletter,
            "feedback": data.feedback,
            "theme": data.theme.strip(),
            "description": data.description.strip()
        })

        #  On remplace la newsletter courante par la version révisée
        last_generated_newsletter = revised

    
        filename = os.path.join(GENERATED_DIR, generate_newsletter_filename(data.theme)) 

        with open(filename, "w", encoding="utf-8") as f:
            f.write(revised)    

        return {"revised_newsletter": revised}
    except Exception as e:
        logging.error("Erreur révision : %s", str(e))
        logging.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Erreur révision : {str(e)}")




@app.get("/")
def root():
    return {"message": "API de génération de newsletter prête."}


@app.get("/download-html")
def download_html():
    if not last_generated_newsletter:
        raise HTTPException(status_code=404, detail="Aucune newsletter générée.")

    filename = f"{GENERATED_DIR}/newsletter_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(last_generated_newsletter)

    return FileResponse(filename, media_type="text/html", filename=os.path.basename(filename))


@app.get("/list-newsletters")
def list_newsletters():
    files = sorted(os.listdir(GENERATED_DIR), reverse=True)
    return {"files": files}


@app.post("/send-newsletter")
async def send_newsletter(newsletter_file: str = Form(...), csv_file: UploadFile = File(...)):
    try:
        temp_csv_path = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
        with open(temp_csv_path, "wb") as f:
            shutil.copyfileobj(csv_file.file, f)

        newsletter_path = os.path.join(GENERATED_DIR, newsletter_file)
        result = send_newsletter_to_csv(newsletter_path, temp_csv_path)

        os.remove(temp_csv_path)
        return JSONResponse(content={"detail": f"{result} emails envoyés avec succès."})
    except Exception as e:
        logging.error("Erreur d'envoi : %s", str(e))
        raise HTTPException(status_code=500, detail=f"Erreur d'envoi : {str(e)}")




from fastapi import Query

@app.delete("/delete-newsletter")
def delete_newsletter(filename: str = Query(...)):
    file_path = os.path.join(GENERATED_DIR, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Fichier non trouvé.")
    try:
        os.remove(file_path)
        return {"detail": "Newsletter supprimée avec succès."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur de suppression : {str(e)}")







class ManualSaveRequest(BaseModel):
    theme: str
    content: str

# @app.post("/save-newsletter")
# async def save_newsletter(data: ManualSaveRequest):
#     try:

#         filename = os.path.join(GENERATED_DIR, generate_newsletter_filename(data.theme))
#         with open(filename, "w", encoding="utf-8") as f:
#             newsletter= revise_manually(data.content)
#             f.write(newsletter)
        
#         return {"detail": "Newsletter sauvegardée"}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Erreur sauvegarde : {str(e)}")

# # api.py
# @app.post("/save-newsletter")
# async def save_newsletter(data: ManualSaveRequest):
#     try:
#         revised = revise_manually(data.content) or data.content
#         filename = os.path.join(GENERATED_DIR, generate_newsletter_filename(data.theme))
#         with open(filename, "w", encoding="utf-8") as f:
#             f.write(revised)
#         return {"detail": "Newsletter sauvegardée"}
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Erreur sauvegarde : {str(e)}")



@app.post("/save-newsletter")
async def save_newsletter(data: ManualSaveRequest):
    try:
        # Essayer de réviser le contenu, mais avec gestion d'erreur
        try:
            revised = revise_manually(data.content)
            if not revised or not revised.strip():
                revised = data.content
        except Exception as revise_error:
            print(f"Erreur lors de la révision: {revise_error}")
            # En cas d'erreur de révision, utiliser le contenu original
            revised = data.content
        
        filename = generate_newsletter_filename(data.theme)
        path = os.path.join(GENERATED_DIR, filename)
        
        # S'assurer que le contenu n'est pas None
        content_to_save = revised if revised else data.content
        
        with open(path, "w", encoding="utf-8") as f:
            f.write(content_to_save)

        return {"detail": "Newsletter sauvegardée", "filename": filename, "content": content_to_save}
    except Exception as e:
        print(f"Erreur générale lors de la sauvegarde: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur sauvegarde : {str(e)}")



















