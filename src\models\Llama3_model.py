import os
import requests
from dotenv import load_dotenv

#  Charger les variables d'environnement (.env)
load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")

if not GROQ_API_KEY:
    raise RuntimeError(" GROQ_API_KEY non trouvé")

#  Fonction générique pour interroger Llama3 via l’API Groq
def generate_llama3(prompt: str, temperature: float = 0.7, max_tokens: int = 512, model: str = "llama3-8b-8192") -> str:
    url = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": temperature,
        "max_tokens": max_tokens
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"].strip()
    except Exception as e:
        return f"[ Erreur Llama3 via Groq] {e}"
