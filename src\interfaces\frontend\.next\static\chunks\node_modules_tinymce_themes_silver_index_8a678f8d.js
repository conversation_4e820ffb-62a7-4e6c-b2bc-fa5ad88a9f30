(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/tinymce/themes/silver/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
// Exports the "silver" theme for usage with module loaders
// Usage:
//   CommonJS:
//     require('tinymce/themes/silver')
//   ES2015:
//     import 'tinymce/themes/silver'
__turbopack_context__.r("[project]/node_modules/tinymce/themes/silver/theme.js [app-client] (ecmascript)");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/tinymce/themes/silver/index.js [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/node_modules/tinymce/themes/silver/index.js [app-client] (ecmascript)"));
}),
}]);

//# sourceMappingURL=node_modules_tinymce_themes_silver_index_8a678f8d.js.map