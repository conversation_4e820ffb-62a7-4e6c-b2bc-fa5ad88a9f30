




import asyncio
import os
import logging
from typing import Dict, Any, Optional, List, TypedDict
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters, stdio_client
from contextlib import AsyncExitStack
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

class ToolDefinition(TypedDict):
    name: str
    description: str
    input_schema: dict

class NewsletterClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.available_tools: List[ToolDefinition] = []
        self.tool_to_session: Dict[str, ClientSession] = {}
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)
        self.exit_stack = AsyncExitStack()

    async def start(self):
        if self.session is not None:
            self.logger.warning(" Session déjà active.")
            return

        await self.exit_stack.__aenter__()

        self.logger.info(" Lancement du client MCP (stdio via uv run)...")
        script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "mcp_server.py"))

        server_params = StdioServerParameters(
            command="uv",
            args=["run", script_path],
            timeout=180
        )

        stdio = await self.exit_stack.enter_async_context(stdio_client(server_params))
        read, write = stdio
        self.session = await self.exit_stack.enter_async_context(ClientSession(read, write))
        await self.session.initialize()

        response = await self.session.list_tools()
        for tool in response.tools:
            self.tool_to_session[tool.name] = self.session
            self.available_tools.append({
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema
            })

        self.logger.info(f" {len(self.available_tools)} outils disponibles chargés.")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Any:
        if not self.session:
            raise RuntimeError(" Session non initialisée. Appelle `start()` d'abord.")
        if tool_name not in self.tool_to_session:
            raise ValueError(f" Outil non trouvé : {tool_name}")
        arguments = arguments or {}

        try:
            response = await self.session.call_tool(tool_name, arguments)
            if response.content:
                if len(response.content) == 1 and hasattr(response.content[0], "text"):
                    return response.content[0].text
                return response.content
            else:
                self.logger.warning(f"ℹ Aucun contenu retourné par {tool_name}")
                return None
        except Exception as e:
            self.logger.error(f" Erreur appel {tool_name} : {e}")
            raise


# Singleton MCP utilisé globalement
newsletter_client = NewsletterClient()





























