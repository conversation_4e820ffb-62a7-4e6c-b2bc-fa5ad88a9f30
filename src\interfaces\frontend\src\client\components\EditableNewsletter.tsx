'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/client/components/ui/button';
import TinyMC<PERSON><PERSON>orCD<PERSON> from './TinyMCEEditorCDN';
import TinyMCEGuide from './TinyMCEGuide';



// ===== Wrapper visuel pour l’aperçu (facultatif) =====
const WRAP_START =
  `<div class="newsletter-container" style="font-family:'Segoe UI', Roboto, sans-serif;max-width:700px;margin:auto;padding:30px;background-color:#ffffff;color:#333;">`;
const WRAP_END = `</div>`;

if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `
    .newsletter-container img { display:block; margin:auto; }
    .newsletter-container h1,.newsletter-container h2,.newsletter-container h3 { color:#1a73e8; }
    .newsletter-container a { color:#1a73e8; text-decoration:underline; }
  `;
  document.head.appendChild(style);
}

interface Props {
  initialContent: string;
  theme: string;
  onSave: (content: string) => void;
  editing: boolean;
  onEditingChange: (editing: boolean) => void;
}

export default function EditableNewsletter({
  initialContent,
  theme,
  onSave,
  editing,
  onEditingChange,
}: Props) {
  const [content, setContent] = useState(initialContent);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const res = await fetch('http://localhost:8000/save-newsletter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ theme, content }),
      });
      
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.detail || `Erreur ${res.status}: ${res.statusText}`);
      }
      
      const data = await res.json();
      
      // Vérifier que le contenu révisé est valide
      if (data.content && typeof data.content === 'string' && data.content.trim()) {
        onSave(data.content);
      } else {
        // Si pas de contenu révisé, utiliser le contenu original
        console.warn('Aucun contenu révisé reçu, utilisation du contenu original');
        onSave(content);
      }
      
      onEditingChange(false);
    } catch (e: any) {
      console.error('Erreur lors de la sauvegarde:', e);
      alert(`Erreur lors de l'enregistrement: ${e.message || e}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setContent(initialContent);
    onEditingChange(false);
  };

  // Fonction pour nettoyer le HTML et éviter les erreurs d'affichage
  const sanitizeContent = (html: string): string => {
    if (!html || typeof html !== 'string') return '';
    
    // Supprimer les scripts potentiellement dangereux
    let cleaned = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    
    // Supprimer les balises on* (événements JavaScript)
    cleaned = cleaned.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
    
    // Supprimer les balises style avec du JavaScript
    cleaned = cleaned.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    return cleaned;
  };

  return (
    <div className="space-y-4">
      {!editing ? (
        <div 
          dangerouslySetInnerHTML={{ 
            __html: WRAP_START + sanitizeContent(content) + WRAP_END 
          }} 
        />
      ) : (
        <div>
          {/* Guide d'utilisation pour TinyMCE */}
          <TinyMCEGuide />
          
          {/* Éditeur TinyMCE */}
          <TinyMCEEditorCDN content={content} onChange={setContent} />
          
          <div className="flex gap-4 mt-4">
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
              className={`relative overflow-hidden transition-all duration-300 ${
                isSaving 
                  ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse' 
                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'
              } text-white shadow-lg hover:shadow-xl transform hover:scale-105`}
            >
              {isSaving ? (
                <>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse"></div>
                  <div className="relative flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Enregistrement...</span>
                  </div>
                </>
              ) : (
                <span>Enregistrer</span>
              )}
            </Button>
            <Button onClick={handleCancel} variant="outline" disabled={isSaving}>Annuler</Button>
          </div>
        </div>
      )}
    </div>
  );
}





















