{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/themes/silver/index.js"], "sourcesContent": ["// Exports the \"silver\" theme for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/themes/silver')\n//   ES2015:\n//     import 'tinymce/themes/silver'\nrequire('./theme.js');"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,SAAS;AACT,cAAc;AACd,uCAAuC;AACvC,YAAY;AACZ,qCAAqC", "ignoreList": [0], "debugId": null}}]}