{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/table/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType$1 = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq$1 = (t) => (a) => t === a;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => { };\n    /** Compose two unary functions. Similar to compose, but avoids using Function.prototype.apply. */\n    const compose1 = (fbc, fab) => (a) => fbc(fab(a));\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const identity = (x) => {\n        return x;\n    };\n    const tripleEquals = (a, b) => {\n        return a === b;\n    };\n    // eslint-disable-next-line prefer-arrow/prefer-arrow-functions\n    function curry(fn, ...initialArgs) {\n        return (...restArgs) => {\n            const all = initialArgs.concat(restArgs);\n            return fn.apply(null, all);\n        };\n    }\n    const call = (f) => {\n        f();\n    };\n    const never = constant(false);\n    const always = constant(true);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    /* eslint-enable */\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const range = (num, f) => {\n        const r = [];\n        for (let i = 0; i < num; i++) {\n            r.push(f(i));\n        }\n        return r;\n    };\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const eachr = (xs, f) => {\n        for (let i = xs.length - 1; i >= 0; i--) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const partition = (xs, pred) => {\n        const pass = [];\n        const fail = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            const arr = pred(x, i) ? pass : fail;\n            arr.push(x);\n        }\n        return { pass, fail };\n    };\n    const filter$1 = (xs, pred) => {\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                r.push(x);\n            }\n        }\n        return r;\n    };\n    const foldr = (xs, f, acc) => {\n        eachr(xs, (x, i) => {\n            acc = f(acc, x, i);\n        });\n        return acc;\n    };\n    const foldl = (xs, f, acc) => {\n        each$1(xs, (x, i) => {\n            acc = f(acc, x, i);\n        });\n        return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const findIndex = (xs, pred) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(i);\n            }\n        }\n        return Optional.none();\n    };\n    const flatten$1 = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind = (xs, f) => flatten$1(map(xs, f));\n    const forall = (xs, pred) => {\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            const x = xs[i];\n            if (pred(x, i) !== true) {\n                return false;\n            }\n        }\n        return true;\n    };\n    const mapToObject = (xs, f) => {\n        const r = {};\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            r[String(x)] = f(x, i);\n        }\n        return r;\n    };\n    const get$4 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = (xs) => get$4(xs, 0);\n    const last = (xs) => get$4(xs, xs.length - 1);\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n    const findMap = (arr, f) => {\n        for (let i = 0; i < arr.length; i++) {\n            const r = f(arr[i], i);\n            if (r.isSome()) {\n                return r;\n            }\n        }\n        return Optional.none();\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const objAcc = (r) => (x, i) => {\n        r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n        each(obj, (x, i) => {\n            (pred(x, i) ? onTrue : onFalse)(x, i);\n        });\n    };\n    const filter = (obj, pred) => {\n        const t = {};\n        internalFilter(obj, pred, objAcc(t), noop);\n        return t;\n    };\n    const mapToArray = (obj, f) => {\n        const r = [];\n        each(obj, (value, name) => {\n            r.push(f(value, name));\n        });\n        return r;\n    };\n    const values = (obj) => {\n        return mapToArray(obj, identity);\n    };\n    const size = (obj) => {\n        return keys(obj).length;\n    };\n    const get$3 = (obj, key) => {\n        return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    const isEmpty$1 = (r) => {\n        for (const x in r) {\n            if (hasOwnProperty.call(r, x)) {\n                return false;\n            }\n        }\n        return true;\n    };\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    /**\n     * **Is** the value stored inside this Optional object equal to `rhs`?\n     */\n    const is$2 = (lhs, rhs, comparator = tripleEquals) => lhs.exists((left) => comparator(left, rhs));\n    const cat = (arr) => {\n        const r = [];\n        const push = (x) => {\n            r.push(x);\n        };\n        for (let i = 0; i < arr.length; i++) {\n            arr[i].each(push);\n        }\n        return r;\n    };\n    /*\n    Notes on the lift functions:\n    - We used to have a generic liftN, but we were concerned about its type-safety, and the below variants were faster in microbenchmarks.\n    - The getOrDie calls are partial functions, but are checked beforehand. This is faster and more convenient (but less safe) than folds.\n    - && is used instead of a loop for simplicity and performance.\n    */\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n    const flatten = (oot) => oot.bind(identity);\n    // This can help with type inference, by specifying the type param on the none case, so the caller doesn't have to.\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const singleton = (doRevoke) => {\n        const subject = Cell(Optional.none());\n        const revoke = () => subject.get().each(doRevoke);\n        const clear = () => {\n            revoke();\n            subject.set(Optional.none());\n        };\n        const isSet = () => subject.get().isSome();\n        const get = () => subject.get();\n        const set = (s) => {\n            revoke();\n            subject.set(Optional.some(s));\n        };\n        return {\n            clear,\n            isSet,\n            get,\n            set\n        };\n    };\n    const unbindable = () => singleton((s) => s.unbind());\n\n    const removeFromStart = (str, numChars) => {\n        return str.substring(numChars);\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const removeLeading = (str, prefix) => {\n        return startsWith(str, prefix) ? removeFromStart(str, prefix.length) : str;\n    };\n    /** Does 'str' start with 'prefix'?\n     *  Note: all strings start with the empty string.\n     *        More formally, for all strings x, startsWith(x, \"\").\n     *        This is so that for all strings x and y, startsWith(y + x, y)\n     */\n    const startsWith = (str, prefix) => {\n        return checkRange(str, prefix, 0);\n    };\n    const blank = (r) => (s) => s.replace(r, '');\n    /** removes all leading and trailing spaces */\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = (s) => s.length > 0;\n    const isEmpty = (s) => !isNotEmpty(s);\n    const toInt = (value, radix = 10) => {\n        const num = parseInt(value, radix);\n        return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n    const toFloat = (value) => {\n        const num = parseFloat(value);\n        return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n\n    const cached = (f) => {\n        let called = false;\n        let r;\n        return (...args) => {\n            if (!called) {\n                called = true;\n                r = f.apply(null, args);\n            }\n            return r;\n        };\n    };\n\n    const fromHtml = (html, scope) => {\n        const doc = scope || document;\n        const div = doc.createElement('div');\n        div.innerHTML = html;\n        if (!div.hasChildNodes() || div.childNodes.length > 1) {\n            const message = 'HTML does not have a single root node';\n            // eslint-disable-next-line no-console\n            console.error(message, html);\n            throw new Error(message);\n        }\n        return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n        const doc = scope || document;\n        const node = doc.createElement(tag);\n        return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n        const doc = scope || document;\n        const node = doc.createTextNode(text);\n        return fromDom$1(node);\n    };\n    const fromDom$1 = (node) => {\n        // TODO: Consider removing this check, but left atm for safety\n        if (node === null || node === undefined) {\n            throw new Error('Node cannot be null or undefined');\n        }\n        return {\n            dom: node\n        };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    // tslint:disable-next-line:variable-name\n    const SugarElement = {\n        fromHtml,\n        fromTag,\n        fromText,\n        fromDom: fromDom$1,\n        fromPoint\n    };\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const is$1 = (element, selector) => {\n        const dom = element.dom;\n        if (dom.nodeType !== ELEMENT) {\n            return false;\n        }\n        else {\n            const elem = dom;\n            if (elem.matches !== undefined) {\n                return elem.matches(selector);\n            }\n            else if (elem.msMatchesSelector !== undefined) {\n                return elem.msMatchesSelector(selector);\n            }\n            else if (elem.webkitMatchesSelector !== undefined) {\n                return elem.webkitMatchesSelector(selector);\n            }\n            else if (elem.mozMatchesSelector !== undefined) {\n                // cast to any as mozMatchesSelector doesn't exist in TS DOM lib\n                return elem.mozMatchesSelector(selector);\n            }\n            else {\n                throw new Error('Browser lacks native selectors');\n            } // unfortunately we can't throw this on startup :(\n        }\n    };\n    const bypassSelector = (dom) => \n    // Only elements, documents and shadow roots support querySelector\n    // shadow root element type is DOCUMENT_FRAGMENT\n    dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT ||\n        // IE fix for complex queries on empty nodes: http://jsfiddle.net/spyder/fv9ptr5L/\n        dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n        const base = scope === undefined ? document : scope.dom;\n        return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n    const one = (selector, scope) => {\n        const base = scope === undefined ? document : scope.dom;\n        return bypassSelector(base) ? Optional.none() : Optional.from(base.querySelector(selector)).map(SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    const is = is$1;\n\n    const name = (element) => {\n        const r = element.dom.nodeName;\n        return r.toLowerCase();\n    };\n    const type = (element) => element.dom.nodeType;\n    const isType = (t) => (element) => type(element) === t;\n    const isComment = (element) => type(element) === COMMENT || name(element) === '#comment';\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = (tag) => (e) => isElement(e) && name(e) === tag;\n\n    /**\n     * The document associated with the current element\n     * NOTE: this will throw if the owner is null.\n     */\n    const owner = (element) => SugarElement.fromDom(element.dom.ownerDocument);\n    /**\n     * If the element is a document, return it. Otherwise, return its ownerDocument.\n     * @param dos\n     */\n    const documentOrOwner = (dos) => isDocument(dos) ? dos : owner(dos);\n    const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n        const stop = isFunction(isRoot) ? isRoot : never;\n        // This is used a *lot* so it needs to be performant, not recursive\n        let dom = element.dom;\n        const ret = [];\n        while (dom.parentNode !== null && dom.parentNode !== undefined) {\n            const rawParent = dom.parentNode;\n            const p = SugarElement.fromDom(rawParent);\n            ret.push(p);\n            if (stop(p) === true) {\n                break;\n            }\n            else {\n                dom = rawParent;\n            }\n        }\n        return ret;\n    };\n    const prevSibling = (element) => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);\n    const nextSibling = (element) => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children$3 = (element) => map(element.dom.childNodes, SugarElement.fromDom);\n    const child$3 = (element, index) => {\n        const cs = element.dom.childNodes;\n        return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = (element) => child$3(element, 0);\n\n    /**\n     * Is the element a ShadowRoot?\n     *\n     * Note: this is insufficient to test if any element is a shadow root, but it is sufficient to differentiate between\n     * a Document and a ShadowRoot.\n     */\n    const isShadowRoot = (dos) => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const getRootNode = (e) => SugarElement.fromDom(e.dom.getRootNode());\n    /** If this element is in a ShadowRoot, return it. */\n    const getShadowRoot = (e) => {\n        const r = getRootNode(e);\n        return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    /** Return the host of a ShadowRoot.\n     *\n     * This function will throw if Shadow DOM is unsupported in the browser, or if the host is null.\n     * If you actually have a ShadowRoot, this shouldn't happen.\n     */\n    const getShadowHost = (e) => SugarElement.fromDom(e.dom.host);\n\n    const before = (marker, element) => {\n        const parent$1 = parent(marker);\n        parent$1.each((v) => {\n            v.dom.insertBefore(element.dom, marker.dom);\n        });\n    };\n    const after$1 = (marker, element) => {\n        const sibling = nextSibling(marker);\n        sibling.fold(() => {\n            const parent$1 = parent(marker);\n            parent$1.each((v) => {\n                append$1(v, element);\n            });\n        }, (v) => {\n            before(v, element);\n        });\n    };\n    const prepend = (parent, element) => {\n        const firstChild$1 = firstChild(parent);\n        firstChild$1.fold(() => {\n            append$1(parent, element);\n        }, (v) => {\n            parent.dom.insertBefore(element.dom, v.dom);\n        });\n    };\n    const append$1 = (parent, element) => {\n        parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n        before(element, wrapper);\n        append$1(wrapper, element);\n    };\n\n    const after = (marker, elements) => {\n        each$1(elements, (x, i) => {\n            const e = i === 0 ? marker : elements[i - 1];\n            after$1(e, x);\n        });\n    };\n    const append = (parent, elements) => {\n        each$1(elements, (x) => {\n            append$1(parent, x);\n        });\n    };\n\n    const rawSet = (dom, key, value) => {\n        /*\n         * JQuery coerced everything to a string, and silently did nothing on text node/null/undefined.\n         *\n         * We fail on those invalid cases, only allowing numbers and booleans.\n         */\n        if (isString(value) || isBoolean(value) || isNumber(value)) {\n            dom.setAttribute(key, value + '');\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n            throw new Error('Attribute value was not simple');\n        }\n    };\n    const set$2 = (element, key, value) => {\n        rawSet(element.dom, key, value);\n    };\n    const setAll = (element, attrs) => {\n        const dom = element.dom;\n        each(attrs, (v, k) => {\n            rawSet(dom, k, v);\n        });\n    };\n    const get$2 = (element, key) => {\n        const v = element.dom.getAttribute(key);\n        // undefined is the more appropriate value for JS, and this matches JQuery\n        return v === null ? undefined : v;\n    };\n    const getOpt = (element, key) => Optional.from(get$2(element, key));\n    const remove$2 = (element, key) => {\n        element.dom.removeAttribute(key);\n    };\n    const clone = (element) => foldl(element.dom.attributes, (acc, attr) => {\n        acc[attr.name] = attr.value;\n        return acc;\n    }, {});\n\n    const remove$1 = (element) => {\n        const dom = element.dom;\n        if (dom.parentNode !== null) {\n            dom.parentNode.removeChild(dom);\n        }\n    };\n    const unwrap = (wrapper) => {\n        const children = children$3(wrapper);\n        if (children.length > 0) {\n            after(wrapper, children);\n        }\n        remove$1(wrapper);\n    };\n\n    const fromDom = (nodes) => map(nodes, SugarElement.fromDom);\n\n    // some elements, such as mathml, don't have style attributes\n    // others, such as angular elements, have style attributes that aren't a CSSStyleDeclaration\n    const isSupported = (dom) => \n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    // Node.contains() is very, very, very good performance\n    // http://jsperf.com/closest-vs-contains/5\n    const inBody = (element) => {\n        // Technically this is only required on IE, where contains() returns false for text nodes.\n        // But it's cheap enough to run everywhere and Sugar doesn't have platform detection (yet).\n        const dom = isText(element) ? element.dom.parentNode : element.dom;\n        // use ownerDocument.body to ensure this works inside iframes.\n        // Normally contains is bad because an element \"contains\" itself, but here we want that.\n        if (dom === undefined || dom === null || dom.ownerDocument === null) {\n            return false;\n        }\n        const doc = dom.ownerDocument;\n        return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    const internalSet = (dom, property, value) => {\n        // This is going to hurt. Apologies.\n        // JQuery coerces numbers to pixels for certain property names, and other times lets numbers through.\n        // we're going to be explicit; strings only.\n        if (!isString(value)) {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n            throw new Error('CSS value must be a string: ' + value);\n        }\n        // removed: support for dom().style[property] where prop is camel case instead of normal property name\n        if (isSupported(dom)) {\n            dom.style.setProperty(property, value);\n        }\n    };\n    const internalRemove = (dom, property) => {\n        /*\n         * IE9 and above - MDN doesn't have details, but here's a couple of random internet claims\n         *\n         * http://help.dottoro.com/ljopsjck.php\n         * http://stackoverflow.com/a/7901886/7546\n         */\n        if (isSupported(dom)) {\n            dom.style.removeProperty(property);\n        }\n    };\n    const set$1 = (element, property, value) => {\n        const dom = element.dom;\n        internalSet(dom, property, value);\n    };\n    /*\n     * NOTE: For certain properties, this returns the \"used value\" which is subtly different to the \"computed value\" (despite calling getComputedStyle).\n     * Blame CSS 2.0.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n     */\n    const get$1 = (element, property) => {\n        const dom = element.dom;\n        /*\n         * IE9 and above per\n         * https://developer.mozilla.org/en/docs/Web/API/window.getComputedStyle\n         *\n         * Not in numerosity, because it doesn't memoize and looking this up dynamically in performance critical code would be horrendous.\n         *\n         * JQuery has some magic here for IE popups, but we don't really need that.\n         * It also uses element.ownerDocument.defaultView to handle iframes but that hasn't been required since FF 3.6.\n         */\n        const styles = window.getComputedStyle(dom);\n        const r = styles.getPropertyValue(property);\n        // jquery-ism: If r is an empty string, check that the element is not in a document. If it isn't, return the raw value.\n        // Turns out we do this a lot.\n        return (r === '' && !inBody(element)) ? getUnsafeProperty(dom, property) : r;\n    };\n    // removed: support for dom().style[property] where prop is camel case instead of normal property name\n    // empty string is what the browsers (IE11 and Chrome) return when the propertyValue doesn't exists.\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    /*\n     * Gets the raw value from the style attribute. Useful for retrieving \"used values\" from the DOM:\n     * https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n     *\n     * Returns NONE if the property isn't set, or the value is an empty string.\n     */\n    const getRaw$1 = (element, property) => {\n        const dom = element.dom;\n        const raw = getUnsafeProperty(dom, property);\n        return Optional.from(raw).filter((r) => r.length > 0);\n    };\n    const remove = (element, property) => {\n        const dom = element.dom;\n        internalRemove(dom, property);\n        if (is$2(getOpt(element, 'style').map(trim), '')) {\n            // No more styles left, remove the style attribute as well\n            remove$2(element, 'style');\n        }\n    };\n\n    const Dimension = (name, getOffset) => {\n        const set = (element, h) => {\n            if (!isNumber(h) && !h.match(/^[0-9]+$/)) {\n                throw new Error(name + '.set accepts only positive integer values. Value was ' + h);\n            }\n            const dom = element.dom;\n            if (isSupported(dom)) {\n                dom.style[name] = h + 'px';\n            }\n        };\n        /*\n         * jQuery supports querying width and height on the document and window objects.\n         *\n         * TBIO doesn't do this, so the code is removed to save space, but left here just in case.\n         */\n        /*\n        var getDocumentWidth = (element) => {\n          var dom = element.dom;\n          if (Node.isDocument(element)) {\n            var body = dom.body;\n            var doc = dom.documentElement;\n            return Math.max(\n              body.scrollHeight,\n              doc.scrollHeight,\n              body.offsetHeight,\n              doc.offsetHeight,\n              doc.clientHeight\n            );\n          }\n        };\n      \n        var getWindowWidth = (element) => {\n          var dom = element.dom;\n          if (dom.window === dom) {\n            // There is no offsetHeight on a window, so use the clientHeight of the document\n            return dom.document.documentElement.clientHeight;\n          }\n        };\n      */\n        const get = (element) => {\n            const r = getOffset(element);\n            // zero or null means non-standard or disconnected, fall back to CSS\n            if (r <= 0 || r === null) {\n                const css = get$1(element, name);\n                // ugh this feels dirty, but it saves cycles\n                return parseFloat(css) || 0;\n            }\n            return r;\n        };\n        // in jQuery, getOuter replicates (or uses) box-sizing: border-box calculations\n        // although these calculations only seem relevant for quirks mode, and edge cases TBIO doesn't rely on\n        const getOuter = get;\n        const aggregate = (element, properties) => foldl(properties, (acc, property) => {\n            const val = get$1(element, property);\n            const value = val === undefined ? 0 : parseInt(val, 10);\n            return isNaN(value) ? acc : acc + value;\n        }, 0);\n        const max = (element, value, properties) => {\n            const cumulativeInclusions = aggregate(element, properties);\n            // if max-height is 100px and your cumulativeInclusions is 150px, there is no way max-height can be 100px, so we return 0.\n            const absoluteMax = value > cumulativeInclusions ? value - cumulativeInclusions : 0;\n            return absoluteMax;\n        };\n        return {\n            set,\n            get,\n            getOuter,\n            aggregate,\n            max\n        };\n    };\n\n    const toNumber = (px, fallback) => toFloat(px).getOr(fallback);\n    const getProp = (element, name, fallback) => toNumber(get$1(element, name), fallback);\n    const calcContentBoxSize = (element, size, upper, lower) => {\n        const paddingUpper = getProp(element, `padding-${upper}`, 0);\n        const paddingLower = getProp(element, `padding-${lower}`, 0);\n        const borderUpper = getProp(element, `border-${upper}-width`, 0);\n        const borderLower = getProp(element, `border-${lower}-width`, 0);\n        return size - paddingUpper - paddingLower - borderUpper - borderLower;\n    };\n    const getCalculatedWidth = (element, boxSizing) => {\n        const dom = element.dom;\n        const width = dom.getBoundingClientRect().width || dom.offsetWidth;\n        return boxSizing === 'border-box' ? width : calcContentBoxSize(element, width, 'left', 'right');\n    };\n    const getInnerWidth = (element) => getCalculatedWidth(element, 'content-box');\n\n    Dimension('width', (element) => \n    // IMO passing this function is better than using dom['offset' + 'width']\n    element.dom.offsetWidth);\n    Dimension('width', (element) => {\n        const dom = element.dom;\n        return inBody(element) ? dom.getBoundingClientRect().width : dom.offsetWidth;\n    });\n    const getInner = getInnerWidth;\n\n    const NodeValue = (is, name) => {\n        const get = (element) => {\n            if (!is(element)) {\n                throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n            }\n            return getOption(element).getOr('');\n        };\n        const getOption = (element) => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n        const set = (element, value) => {\n            if (!is(element)) {\n                throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n            }\n            element.dom.nodeValue = value;\n        };\n        return {\n            get,\n            getOption,\n            set\n        };\n    };\n\n    const api = NodeValue(isText, 'text');\n    const get = (element) => api.get(element);\n    const set = (element, value) => api.set(element, value);\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n        if (is(scope, a)) {\n            return Optional.some(scope);\n        }\n        else if (isFunction(isRoot) && isRoot(scope)) {\n            return Optional.none();\n        }\n        else {\n            return ancestor(scope, a, isRoot);\n        }\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n        let element = scope.dom;\n        const stop = isFunction(isRoot) ? isRoot : never;\n        while (element.parentNode) {\n            element = element.parentNode;\n            const el = SugarElement.fromDom(element);\n            if (predicate(el)) {\n                return Optional.some(el);\n            }\n            else if (stop(el)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n        // This is required to avoid ClosestOrAncestor passing the predicate to itself\n        const is = (s, test) => test(s);\n        return ClosestOrAncestor(is, ancestor$1, scope, predicate, isRoot);\n    };\n    const child$2 = (scope, predicate) => {\n        const pred = (node) => predicate(SugarElement.fromDom(node));\n        const result = find(scope.dom.childNodes, pred);\n        return result.map(SugarElement.fromDom);\n    };\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, (e) => is$1(e, selector), isRoot);\n    const child$1 = (scope, selector) => child$2(scope, (e) => is$1(e, selector));\n    const descendant = (scope, selector) => one(selector, scope);\n    // Returns Some(closest ancestor element (sugared)) matching 'selector' up to isRoot, or None() otherwise\n    const closest$1 = (scope, selector, isRoot) => {\n        const is = (element, selector) => is$1(element, selector);\n        return ClosestOrAncestor(is, ancestor, scope, selector, isRoot);\n    };\n\n    const closest = (target) => closest$1(target, '[contenteditable]');\n    const isEditable = (element, assumeEditable = false) => {\n        if (inBody(element)) {\n            return element.dom.isContentEditable;\n        }\n        else {\n            // Find the closest contenteditable element and check if it's editable\n            return closest(element).fold(constant(assumeEditable), (editable) => getRaw(editable) === 'true');\n        }\n    };\n    const getRaw = (element) => element.dom.contentEditable;\n\n    const children$2 = (scope, predicate) => filter$1(children$3(scope), predicate);\n    const descendants$1 = (scope, predicate) => {\n        let result = [];\n        // Recurse.toArray() might help here\n        each$1(children$3(scope), (x) => {\n            if (predicate(x)) {\n                result = result.concat([x]);\n            }\n            result = result.concat(descendants$1(x, predicate));\n        });\n        return result;\n    };\n\n    const children$1 = (scope, selector) => \n    // It may surprise you to learn this is exactly what JQuery does\n    // TODO: Avoid all the wrapping and unwrapping\n    children$2(scope, (e) => is$1(e, selector));\n    const descendants = (scope, selector) => all$1(selector, scope);\n\n    const child = (scope, selector) => child$1(scope, selector).isSome();\n\n    /*\n     NOTE: This file is partially duplicated in the following locations:\n      - models/dom/table/core/TableUtils.ts\n      - advtable\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const getNodeName = (elm) => elm.nodeName.toLowerCase();\n    const getBody = (editor) => SugarElement.fromDom(editor.getBody());\n    const getIsRoot = (editor) => (element) => eq(element, getBody(editor));\n    const removePxSuffix = (size) => size ? size.replace(/px$/, '') : '';\n    const addPxSuffix = (size) => /^\\d+(\\.\\d+)?$/.test(size) ? size + 'px' : size;\n    const getSelectionStart = (editor) => SugarElement.fromDom(editor.selection.getStart());\n    const getSelectionEnd = (editor) => SugarElement.fromDom(editor.selection.getEnd());\n    const isInEditableContext = (cell) => closest$2(cell, isTag('table')).forall(isEditable);\n\n    const validSectionList = ['tfoot', 'thead', 'tbody', 'colgroup'];\n    const isValidSection = (parentName) => contains(validSectionList, parentName);\n    const grid = (rows, columns) => ({\n        rows,\n        columns\n    });\n    const detail = (element, rowspan, colspan) => ({\n        element,\n        rowspan,\n        colspan\n    });\n    const extended = (element, rowspan, colspan, row, column, isLocked) => ({\n        element,\n        rowspan,\n        colspan,\n        row,\n        column,\n        isLocked\n    });\n    const rowdetail = (element, cells, section) => ({\n        element,\n        cells,\n        section\n    });\n    const bounds = (startRow, startCol, finishRow, finishCol) => ({\n        startRow,\n        startCol,\n        finishRow,\n        finishCol\n    });\n    const columnext = (element, colspan, column) => ({\n        element,\n        colspan,\n        column\n    });\n    const colgroup = (element, columns) => ({\n        element,\n        columns\n    });\n\n    const getAttrValue = (cell, name, fallback = 0) => getOpt(cell, name).map((value) => parseInt(value, 10)).getOr(fallback);\n\n    const firstLayer = (scope, selector) => {\n        return filterFirstLayer(scope, selector, always);\n    };\n    const filterFirstLayer = (scope, selector, predicate) => {\n        return bind(children$3(scope), (x) => {\n            if (is$1(x, selector)) {\n                return predicate(x) ? [x] : [];\n            }\n            else {\n                return filterFirstLayer(x, selector, predicate);\n            }\n        });\n    };\n\n    // lookup inside this table\n    const lookup = (tags, element, isRoot = never) => {\n        // If the element we're inspecting is the root, we definitely don't want it.\n        if (isRoot(element)) {\n            return Optional.none();\n        }\n        // This looks a lot like SelectorFind.closest, with one big exception - the isRoot check.\n        // The code here will look for parents if passed a table, SelectorFind.closest with that specific isRoot check won't.\n        if (contains(tags, name(element))) {\n            return Optional.some(element);\n        }\n        const isRootOrUpperTable = (elm) => is$1(elm, 'table') || isRoot(elm);\n        return ancestor(element, tags.join(','), isRootOrUpperTable);\n    };\n    /*\n     * Identify the optional cell that element represents.\n     */\n    const cell = (element, isRoot) => lookup(['td', 'th'], element, isRoot);\n    const cells = (ancestor) => firstLayer(ancestor, 'th,td');\n    const columns = (ancestor) => {\n        if (is$1(ancestor, 'colgroup')) {\n            return children$1(ancestor, 'col');\n        }\n        else {\n            return bind(columnGroups(ancestor), (columnGroup) => children$1(columnGroup, 'col'));\n        }\n    };\n    const table = (element, isRoot) => closest$1(element, 'table', isRoot);\n    const rows = (ancestor) => firstLayer(ancestor, 'tr');\n    const columnGroups = (ancestor) => table(ancestor).fold(constant([]), (table) => children$1(table, 'colgroup'));\n\n    const isHeaderCell = isTag('th');\n    const getRowHeaderType = (isHeaderRow, isHeaderCells) => {\n        if (isHeaderRow && isHeaderCells) {\n            return 'sectionCells';\n        }\n        else if (isHeaderRow) {\n            return 'section';\n        }\n        else {\n            return 'cells';\n        }\n    };\n    const getRowType$1 = (row) => {\n        // Header rows can use a combination of theads and ths - want to detect the different combinations\n        const isHeaderRow = row.section === 'thead';\n        const isHeaderCells = is$2(findCommonCellType(row.cells), 'th');\n        if (row.section === 'tfoot') {\n            return { type: 'footer' };\n        }\n        else if (isHeaderRow || isHeaderCells) {\n            return { type: 'header', subType: getRowHeaderType(isHeaderRow, isHeaderCells) };\n        }\n        else {\n            return { type: 'body' };\n        }\n    };\n    const findCommonCellType = (cells) => {\n        const headerCells = filter$1(cells, (cell) => isHeaderCell(cell.element));\n        if (headerCells.length === 0) {\n            return Optional.some('td');\n        }\n        else if (headerCells.length === cells.length) {\n            return Optional.some('th');\n        }\n        else {\n            return Optional.none();\n        }\n    };\n    const findCommonRowType = (rows) => {\n        const rowTypes = map(rows, (row) => getRowType$1(row).type);\n        const hasHeader = contains(rowTypes, 'header');\n        const hasFooter = contains(rowTypes, 'footer');\n        if (!hasHeader && !hasFooter) {\n            return Optional.some('body');\n        }\n        else {\n            const hasBody = contains(rowTypes, 'body');\n            if (hasHeader && !hasBody && !hasFooter) {\n                return Optional.some('header');\n            }\n            else if (!hasHeader && !hasBody && hasFooter) {\n                return Optional.some('footer');\n            }\n            else {\n                return Optional.none();\n            }\n        }\n    };\n\n    const fromRowsOrColGroups = (elems, getSection) => map(elems, (row) => {\n        if (name(row) === 'colgroup') {\n            const cells = map(columns(row), (column) => {\n                const colspan = getAttrValue(column, 'span', 1);\n                return detail(column, 1, colspan);\n            });\n            return rowdetail(row, cells, 'colgroup');\n        }\n        else {\n            const cells$1 = map(cells(row), (cell) => {\n                const rowspan = getAttrValue(cell, 'rowspan', 1);\n                const colspan = getAttrValue(cell, 'colspan', 1);\n                return detail(cell, rowspan, colspan);\n            });\n            return rowdetail(row, cells$1, getSection(row));\n        }\n    });\n    const getParentSection = (group) => parent(group).map((parent) => {\n        const parentName = name(parent);\n        return isValidSection(parentName) ? parentName : 'tbody';\n    }).getOr('tbody');\n    /*\n     * Takes a DOM table and returns a list of list of:\n       element: row element\n       cells: (id, rowspan, colspan) structs\n     */\n    const fromTable$1 = (table) => {\n        const rows$1 = rows(table);\n        const columnGroups$1 = columnGroups(table);\n        const elems = [...columnGroups$1, ...rows$1];\n        return fromRowsOrColGroups(elems, getParentSection);\n    };\n\n    const LOCKED_COL_ATTR = 'data-snooker-locked-cols';\n    const getLockedColumnsFromTable = (table) => getOpt(table, LOCKED_COL_ATTR)\n        .bind((lockedColStr) => Optional.from(lockedColStr.match(/\\d+/g)))\n        .map((lockedCols) => mapToObject(lockedCols, always));\n\n    const key = (row, column) => {\n        return row + ',' + column;\n    };\n    const getAt = (warehouse, row, column) => Optional.from(warehouse.access[key(row, column)]);\n    const findItem = (warehouse, item, comparator) => {\n        const filtered = filterItems(warehouse, (detail) => {\n            return comparator(item, detail.element);\n        });\n        return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();\n    };\n    const filterItems = (warehouse, predicate) => {\n        const all = bind(warehouse.all, (r) => {\n            return r.cells;\n        });\n        return filter$1(all, predicate);\n    };\n    const generateColumns = (rowData) => {\n        const columnsGroup = {};\n        let index = 0;\n        each$1(rowData.cells, (column) => {\n            const colspan = column.colspan;\n            range(colspan, (columnIndex) => {\n                const colIndex = index + columnIndex;\n                columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);\n            });\n            index += colspan;\n        });\n        return columnsGroup;\n    };\n    /*\n     * From a list of list of Detail, generate three pieces of information:\n     *  1. the grid size\n     *  2. a data structure which can efficiently identify which cell is in which row,column position\n     *  3. a list of all cells in order left-to-right, top-to-bottom\n     */\n    const generate = (list) => {\n        // list is an array of objects, made by cells and elements\n        // elements: is the TR\n        // cells: is an array of objects representing the cells in the row.\n        //        It is made of:\n        //          colspan (merge cell)\n        //          element\n        //          rowspan (merge cols)\n        const access = {};\n        const cells = [];\n        const tableOpt = head(list).map((rowData) => rowData.element).bind(table);\n        const lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});\n        let maxRows = 0;\n        let maxColumns = 0;\n        let rowCount = 0;\n        const { pass: colgroupRows, fail: rows } = partition(list, (rowData) => rowData.section === 'colgroup');\n        // Handle rows first\n        each$1(rows, (rowData) => {\n            const currentRow = [];\n            each$1(rowData.cells, (rowCell) => {\n                let start = 0;\n                // If this spot has been taken by a previous rowspan, skip it.\n                while (access[key(rowCount, start)] !== undefined) {\n                    start++;\n                }\n                const isLocked = hasNonNullableKey(lockedColumns, start.toString());\n                const current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);\n                // Occupy all the (row, column) positions that this cell spans for.\n                for (let occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {\n                    for (let occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {\n                        const rowPosition = rowCount + occupiedRowPosition;\n                        const columnPosition = start + occupiedColumnPosition;\n                        const newpos = key(rowPosition, columnPosition);\n                        access[newpos] = current;\n                        maxColumns = Math.max(maxColumns, columnPosition + 1);\n                    }\n                }\n                currentRow.push(current);\n            });\n            maxRows++;\n            cells.push(rowdetail(rowData.element, currentRow, rowData.section));\n            rowCount++;\n        });\n        // Handle colgroups\n        // Note: Currently only a single colgroup is supported so just use the last one\n        const { columns, colgroups } = last(colgroupRows).map((rowData) => {\n            const columns = generateColumns(rowData);\n            const colgroup$1 = colgroup(rowData.element, values(columns));\n            return {\n                colgroups: [colgroup$1],\n                columns\n            };\n        }).getOrThunk(() => ({\n            colgroups: [],\n            columns: {}\n        }));\n        const grid$1 = grid(maxRows, maxColumns);\n        return {\n            grid: grid$1,\n            access,\n            all: cells,\n            columns,\n            colgroups\n        };\n    };\n    const fromTable = (table) => {\n        const list = fromTable$1(table);\n        return generate(list);\n    };\n    const justCells = (warehouse) => bind(warehouse.all, (w) => w.cells);\n    const justColumns = (warehouse) => values(warehouse.columns);\n    const hasColumns = (warehouse) => keys(warehouse.columns).length > 0;\n    const getColumnAt = (warehouse, columnIndex) => Optional.from(warehouse.columns[columnIndex]);\n    const Warehouse = {\n        fromTable,\n        generate,\n        getAt,\n        findItem,\n        filterItems,\n        justCells,\n        justColumns,\n        hasColumns,\n        getColumnAt\n    };\n\n    const findInWarehouse = (warehouse, element) => findMap(warehouse.all, (r) => find(r.cells, (e) => eq(element, e.element)));\n    const extractCells = (warehouse, target, predicate) => {\n        const details = map(target.selection, (cell$1) => {\n            return cell(cell$1)\n                .bind((lc) => findInWarehouse(warehouse, lc))\n                .filter(predicate);\n        });\n        const cells = cat(details);\n        return someIf(cells.length > 0, cells);\n    };\n    const onMergable = (_warehouse, target) => target.mergable;\n    const onUnmergable = (_warehouse, target) => target.unmergable;\n    const onCells = (warehouse, target) => extractCells(warehouse, target, always);\n    const isUnlockedTableCell = (warehouse, cell) => findInWarehouse(warehouse, cell).exists((detail) => !detail.isLocked);\n    const allUnlocked = (warehouse, cells) => forall(cells, (cell) => isUnlockedTableCell(warehouse, cell));\n    // If any locked columns are present in the selection, then don't want to be able to merge\n    const onUnlockedMergable = (warehouse, target) => onMergable(warehouse, target).filter((mergeable) => allUnlocked(warehouse, mergeable.cells));\n    // If any locked columns are present in the selection, then don't want to be able to unmerge\n    const onUnlockedUnmergable = (warehouse, target) => onUnmergable(warehouse, target).filter((cells) => allUnlocked(warehouse, cells));\n\n    const isCol = isTag('col');\n    const isColgroup = isTag('colgroup');\n    const isRow = (element) => name(element) === 'tr' || isColgroup(element);\n    const elementToData = (element) => {\n        const colspan = getAttrValue(element, 'colspan', 1);\n        const rowspan = getAttrValue(element, 'rowspan', 1);\n        return {\n            element,\n            colspan,\n            rowspan\n        };\n    };\n    // note that `toData` seems to be only for testing\n    const modification = (generators, toData = elementToData) => {\n        const nuCell = (data) => isCol(data.element) ? generators.col(data) : generators.cell(data);\n        const nuRow = (data) => isColgroup(data.element) ? generators.colgroup(data) : generators.row(data);\n        const add = (element) => {\n            if (isRow(element)) {\n                return nuRow({ element });\n            }\n            else {\n                const cell = element;\n                const replacement = nuCell(toData(cell));\n                recent = Optional.some({ item: cell, replacement });\n                return replacement;\n            }\n        };\n        let recent = Optional.none();\n        const getOrInit = (element, comparator) => {\n            return recent.fold(() => {\n                return add(element);\n            }, (p) => {\n                return comparator(element, p.item) ? p.replacement : add(element);\n            });\n        };\n        return {\n            getOrInit\n        };\n    };\n    const transform = (tag) => {\n        return (generators) => {\n            const list = [];\n            const find$1 = (element, comparator) => {\n                return find(list, (x) => {\n                    return comparator(x.item, element);\n                });\n            };\n            const makeNew = (element) => {\n                // Ensure scope is never set on a td element as it's a deprecated attribute\n                const attrs = tag === 'td' ? { scope: null } : {};\n                const cell = generators.replace(element, tag, attrs);\n                list.push({\n                    item: element,\n                    sub: cell\n                });\n                return cell;\n            };\n            const replaceOrInit = (element, comparator) => {\n                if (isRow(element) || isCol(element)) {\n                    return element;\n                }\n                else {\n                    const cell = element;\n                    return find$1(cell, comparator).fold(() => {\n                        return makeNew(cell);\n                    }, (p) => {\n                        return comparator(element, p.item) ? p.sub : makeNew(cell);\n                    });\n                }\n            };\n            return {\n                replaceOrInit\n            };\n        };\n    };\n    const getScopeAttribute = (cell) => getOpt(cell, 'scope').map(\n    // Attribute can be col, colgroup, row, and rowgroup.\n    // As col and colgroup are to be treated as if they are the same, lob off everything after the first three characters and there is no difference.\n    (attribute) => attribute.substr(0, 3));\n    const merging = (generators) => {\n        const unmerge = (cell) => {\n            const scope = getScopeAttribute(cell);\n            scope.each((attribute) => set$2(cell, 'scope', attribute));\n            return () => {\n                const raw = generators.cell({\n                    element: cell,\n                    colspan: 1,\n                    rowspan: 1\n                });\n                // Remove any width calculations because they are no longer relevant.\n                remove(raw, 'width');\n                remove(cell, 'width');\n                scope.each((attribute) => set$2(raw, 'scope', attribute));\n                return raw;\n            };\n        };\n        const merge = (cells) => {\n            const getScopeProperty = () => {\n                const stringAttributes = cat(map(cells, getScopeAttribute));\n                if (stringAttributes.length === 0) {\n                    return Optional.none();\n                }\n                else {\n                    const baseScope = stringAttributes[0];\n                    const scopes = ['row', 'col'];\n                    const isMixed = exists(stringAttributes, (attribute) => {\n                        return attribute !== baseScope && contains(scopes, attribute);\n                    });\n                    return isMixed ? Optional.none() : Optional.from(baseScope);\n                }\n            };\n            remove(cells[0], 'width');\n            getScopeProperty().fold(() => remove$2(cells[0], 'scope'), (attribute) => set$2(cells[0], 'scope', attribute + 'group'));\n            return constant(cells[0]);\n        };\n        return {\n            unmerge,\n            merge\n        };\n    };\n    const Generators = {\n        modification,\n        transform,\n        merging\n    };\n\n    var TagBoundaries = [\n        'body',\n        'p',\n        'div',\n        'article',\n        'aside',\n        'figcaption',\n        'figure',\n        'footer',\n        'header',\n        'nav',\n        'section',\n        'ol',\n        'ul',\n        'li',\n        'table',\n        'thead',\n        'tbody',\n        'tfoot',\n        'caption',\n        'tr',\n        'td',\n        'th',\n        'h1',\n        'h2',\n        'h3',\n        'h4',\n        'h5',\n        'h6',\n        'blockquote',\n        'pre',\n        'address'\n    ];\n\n    var DomUniverse = () => {\n        const clone$1 = (element) => {\n            return SugarElement.fromDom(element.dom.cloneNode(false));\n        };\n        const document = (element) => documentOrOwner(element).dom;\n        const isBoundary = (element) => {\n            if (!isElement(element)) {\n                return false;\n            }\n            if (name(element) === 'body') {\n                return true;\n            }\n            return contains(TagBoundaries, name(element));\n        };\n        const isEmptyTag = (element) => {\n            if (!isElement(element)) {\n                return false;\n            }\n            return contains(['br', 'img', 'hr', 'input'], name(element));\n        };\n        const isNonEditable = (element) => isElement(element) && get$2(element, 'contenteditable') === 'false';\n        const comparePosition = (element, other) => {\n            return element.dom.compareDocumentPosition(other.dom);\n        };\n        const copyAttributesTo = (source, destination) => {\n            const as = clone(source);\n            setAll(destination, as);\n        };\n        const isSpecial = (element) => {\n            const tag = name(element);\n            return contains([\n                'script', 'noscript', 'iframe', 'noframes', 'noembed', 'title', 'style', 'textarea', 'xmp'\n            ], tag);\n        };\n        const getLanguage = (element) => isElement(element) ? getOpt(element, 'lang') : Optional.none();\n        return {\n            up: constant({\n                selector: ancestor,\n                closest: closest$1,\n                predicate: ancestor$1,\n                all: parents\n            }),\n            down: constant({\n                selector: descendants,\n                predicate: descendants$1\n            }),\n            styles: constant({\n                get: get$1,\n                getRaw: getRaw$1,\n                set: set$1,\n                remove: remove\n            }),\n            attrs: constant({\n                get: get$2,\n                set: set$2,\n                remove: remove$2,\n                copyTo: copyAttributesTo\n            }),\n            insert: constant({\n                before: before,\n                after: after$1,\n                afterAll: after,\n                append: append$1,\n                appendAll: append,\n                prepend: prepend,\n                wrap: wrap\n            }),\n            remove: constant({\n                unwrap: unwrap,\n                remove: remove$1\n            }),\n            create: constant({\n                nu: SugarElement.fromTag,\n                clone: clone$1,\n                text: SugarElement.fromText\n            }),\n            query: constant({\n                comparePosition,\n                prevSibling: prevSibling,\n                nextSibling: nextSibling\n            }),\n            property: constant({\n                children: children$3,\n                name: name,\n                parent: parent,\n                document,\n                isText: isText,\n                isComment: isComment,\n                isElement: isElement,\n                isSpecial,\n                getLanguage,\n                getText: get,\n                setText: set,\n                isBoundary,\n                isEmptyTag,\n                isNonEditable\n            }),\n            eq: eq,\n            is: is\n        };\n    };\n\n    const leftRight = (left, right) => ({\n        left,\n        right\n    });\n    const brokenPath = (first, second, splits) => ({\n        first,\n        second,\n        splits\n    });\n    const bisect = (universe, parent, child) => {\n        const children = universe.property().children(parent);\n        const index = findIndex(children, curry(universe.eq, child));\n        return index.map((ind) => {\n            return {\n                before: children.slice(0, ind),\n                after: children.slice(ind + 1)\n            };\n        });\n    };\n    /**\n     * Clone parent to the RIGHT and move everything after child in the parent element into\n     * a clone of the parent (placed after parent).\n     */\n    const breakToRight = (universe, parent, child) => {\n        return bisect(universe, parent, child).map((parts) => {\n            const second = universe.create().clone(parent);\n            universe.insert().appendAll(second, parts.after);\n            universe.insert().after(parent, second);\n            return leftRight(parent, second);\n        });\n    };\n    /**\n     * Clone parent to the LEFT and move everything before and including child into\n     * the a clone of the parent (placed before parent)\n     */\n    const breakToLeft = (universe, parent, child) => {\n        return bisect(universe, parent, child).map((parts) => {\n            const prior = universe.create().clone(parent);\n            universe.insert().appendAll(prior, parts.before.concat([child]));\n            universe.insert().appendAll(parent, parts.after);\n            universe.insert().before(parent, prior);\n            return leftRight(prior, parent);\n        });\n    };\n    /*\n     * Using the breaker, break from the child up to the top element defined by the predicate.\n     * It returns three values:\n     *   first: the top level element that completed the break\n     *   second: the optional element representing second part of the top-level split if the breaking completed successfully to the top\n     *   splits: a list of (Element, Element) pairs that represent the splits that have occurred on the way to the top.\n     */\n    const breakPath = (universe, item, isTop, breaker) => {\n        const next = (child, group, splits) => {\n            const fallback = brokenPath(child, Optional.none(), splits);\n            // Found the top, so stop.\n            if (isTop(child)) {\n                return brokenPath(child, group, splits);\n            }\n            else {\n                // Split the child at parent, and keep going\n                return universe.property().parent(child).bind((parent) => {\n                    return breaker(universe, parent, child).map((breakage) => {\n                        const extra = [{ first: breakage.left, second: breakage.right }];\n                        // Our isTop is based on the left-side parent, so keep it regardless of split.\n                        const nextChild = isTop(parent) ? parent : breakage.left;\n                        return next(nextChild, Optional.some(breakage.right), splits.concat(extra));\n                    });\n                }).getOr(fallback);\n            }\n        };\n        return next(item, Optional.none(), []);\n    };\n\n    const all = (universe, look, elements, f) => {\n        const head = elements[0];\n        const tail = elements.slice(1);\n        return f(universe, look, head, tail);\n    };\n    /**\n     * Check if look returns the same element for all elements, and return it if it exists.\n     */\n    const oneAll = (universe, look, elements) => {\n        return elements.length > 0 ?\n            all(universe, look, elements, unsafeOne) :\n            Optional.none();\n    };\n    const unsafeOne = (universe, look, head, tail) => {\n        const start = look(universe, head);\n        return foldr(tail, (b, a) => {\n            const current = look(universe, a);\n            return commonElement(universe, b, current);\n        }, start);\n    };\n    const commonElement = (universe, start, end) => {\n        return start.bind((s) => {\n            return end.filter(curry(universe.eq, s));\n        });\n    };\n\n    const sharedOne$1 = oneAll;\n    breakToLeft;\n    breakToRight;\n    breakPath;\n\n    const universe = DomUniverse();\n    const sharedOne = (look, elements) => {\n        return sharedOne$1(universe, (_universe, element) => {\n            return look(element);\n        }, elements);\n    };\n\n    const opGetRowsType = (table, target) => {\n        const house = Warehouse.fromTable(table);\n        const details = onCells(house, target);\n        return details.bind((selectedCells) => {\n            const lastSelectedCell = selectedCells[selectedCells.length - 1];\n            const minRowRange = selectedCells[0].row;\n            const maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;\n            const selectedRows = house.all.slice(minRowRange, maxRowRange);\n            return findCommonRowType(selectedRows);\n        }).getOr('');\n    };\n    Generators.transform('th');\n    Generators.transform('td');\n    const getRowsType = opGetRowsType;\n\n    // Note, something is *within* if it is completely contained within the bounds.\n    const isWithin = (bounds, detail) => {\n        return (detail.column >= bounds.startCol &&\n            (detail.column + detail.colspan - 1) <= bounds.finishCol &&\n            detail.row >= bounds.startRow &&\n            (detail.row + detail.rowspan - 1) <= bounds.finishRow);\n    };\n    const isRectangular = (warehouse, bounds) => {\n        let isRect = true;\n        const detailIsWithin = curry(isWithin, bounds);\n        for (let i = bounds.startRow; i <= bounds.finishRow; i++) {\n            for (let j = bounds.startCol; j <= bounds.finishCol; j++) {\n                isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);\n            }\n        }\n        return isRect ? Optional.some(bounds) : Optional.none();\n    };\n\n    const getBounds = (detailA, detailB) => {\n        return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));\n    };\n    const getAnyBox = (warehouse, startCell, finishCell) => {\n        const startCoords = Warehouse.findItem(warehouse, startCell, eq);\n        const finishCoords = Warehouse.findItem(warehouse, finishCell, eq);\n        return startCoords.bind((sc) => {\n            return finishCoords.map((fc) => {\n                return getBounds(sc, fc);\n            });\n        });\n    };\n    const getBox$1 = (warehouse, startCell, finishCell) => {\n        return getAnyBox(warehouse, startCell, finishCell).bind((bounds) => {\n            return isRectangular(warehouse, bounds);\n        });\n    };\n\n    const getBox = (table, first, last) => {\n        const warehouse = getWarehouse(table);\n        return getBox$1(warehouse, first, last);\n    };\n    // Private method ... keep warehouse in snooker, please.\n    const getWarehouse = Warehouse.fromTable;\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getTDTHOverallStyle = (dom, elm, name) => {\n        const cells = dom.select('td,th', elm);\n        let firstChildStyle;\n        for (let i = 0; i < cells.length; i++) {\n            const currentStyle = dom.getStyle(cells[i], name);\n            if (isUndefined(firstChildStyle)) {\n                firstChildStyle = currentStyle;\n            }\n            if (firstChildStyle !== currentStyle) {\n                return '';\n            }\n        }\n        return firstChildStyle;\n    };\n    const setAlign = (editor, elm, name) => {\n        // Alignment formats may not use the same styles so ensure to remove any existing horizontal alignment format first\n        global$2.each('left center right'.split(' '), (align) => {\n            if (align !== name) {\n                editor.formatter.remove('align' + align, {}, elm);\n            }\n        });\n        if (name) {\n            editor.formatter.apply('align' + name, {}, elm);\n        }\n    };\n    const setVAlign = (editor, elm, name) => {\n        // Alignment formats may not use the same styles so ensure to remove any existing vertical alignment format first\n        global$2.each('top middle bottom'.split(' '), (align) => {\n            if (align !== name) {\n                editor.formatter.remove('valign' + align, {}, elm);\n            }\n        });\n        if (name) {\n            editor.formatter.apply('valign' + name, {}, elm);\n        }\n    };\n\n    /*\n     NOTE: This file is duplicated in the following locations:\n      - core/api/TableEvents.ts\n      - models/dom/table/api/Events.ts\n      - advtable\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const fireTableModified = (editor, table, data) => {\n        editor.dispatch('TableModified', { ...data, table });\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const defaultTableToolbar = 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol';\n    const defaultCellBorderWidths = range(5, (i) => {\n        const size = `${i + 1}px`;\n        return { title: size, value: size };\n    });\n    const defaultCellBorderStyles = map(['Solid', 'Dotted', 'Dashed', 'Double', 'Groove', 'Ridge', 'Inset', 'Outset', 'None', 'Hidden'], (type) => {\n        return { title: type, value: type.toLowerCase() };\n    });\n    // Note: This is also contained in the core Options.ts file\n    const defaultWidth = '100%';\n    const getPixelForcedWidth = (editor) => {\n        var _a;\n        // Determine the inner size of the parent block element where the table will be inserted\n        const dom = editor.dom;\n        const parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();\n        return getInner(SugarElement.fromDom(parentBlock)) + 'px';\n    };\n    // Note: This is also contained in the core Options.ts file\n    const determineDefaultStyles = (editor, defaultStyles) => {\n        if (isResponsiveForced(editor) || !shouldStyleWithCss(editor)) {\n            return defaultStyles;\n        }\n        else if (isPixelsForced(editor)) {\n            return { ...defaultStyles, width: getPixelForcedWidth(editor) };\n        }\n        else {\n            return { ...defaultStyles, width: defaultWidth };\n        }\n    };\n    // Note: This is also contained in the core Options.ts file\n    const determineDefaultAttributes = (editor, defaultAttributes) => {\n        if (isResponsiveForced(editor) || shouldStyleWithCss(editor)) {\n            return defaultAttributes;\n        }\n        else if (isPixelsForced(editor)) {\n            return { ...defaultAttributes, width: getPixelForcedWidth(editor) };\n        }\n        else {\n            return { ...defaultAttributes, width: defaultWidth };\n        }\n    };\n    const option = (name) => (editor) => editor.options.get(name);\n    const register = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('table_border_widths', {\n            processor: 'object[]',\n            default: defaultCellBorderWidths\n        });\n        registerOption('table_border_styles', {\n            processor: 'object[]',\n            default: defaultCellBorderStyles\n        });\n        registerOption('table_cell_advtab', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('table_row_advtab', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('table_advtab', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('table_appearance_options', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('table_grid', {\n            processor: 'boolean',\n            // Table grid relies on hover, which isn't available on touch devices so use the dialog instead\n            default: !global$1.deviceType.isTouch()\n        });\n        registerOption('table_cell_class_list', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('table_row_class_list', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('table_class_list', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('table_toolbar', {\n            processor: 'string',\n            default: defaultTableToolbar\n        });\n        registerOption('table_background_color_map', {\n            processor: 'object[]',\n            default: []\n        });\n        registerOption('table_border_color_map', {\n            processor: 'object[]',\n            default: []\n        });\n    };\n    const getTableSizingMode = option('table_sizing_mode');\n    const getTableBorderWidths = option('table_border_widths');\n    const getTableBorderStyles = option('table_border_styles');\n    const hasAdvancedCellTab = option('table_cell_advtab');\n    const hasAdvancedRowTab = option('table_row_advtab');\n    const hasAdvancedTableTab = option('table_advtab');\n    const hasAppearanceOptions = option('table_appearance_options');\n    const hasTableGrid = option('table_grid');\n    const shouldStyleWithCss = option('table_style_by_css');\n    const getCellClassList = option('table_cell_class_list');\n    const getRowClassList = option('table_row_class_list');\n    const getTableClassList = option('table_class_list');\n    const getToolbar = option('table_toolbar');\n    const getTableBackgroundColorMap = option('table_background_color_map');\n    const getTableBorderColorMap = option('table_border_color_map');\n    const isPixelsForced = (editor) => getTableSizingMode(editor) === 'fixed';\n    const isResponsiveForced = (editor) => getTableSizingMode(editor) === 'responsive';\n    const getDefaultStyles = (editor) => {\n        // Note: The we don't rely on the default here as we need to dynamically lookup the widths based on the current editor state\n        const options = editor.options;\n        const defaultStyles = options.get('table_default_styles');\n        return options.isSet('table_default_styles') ? defaultStyles : determineDefaultStyles(editor, defaultStyles);\n    };\n    const getDefaultAttributes = (editor) => {\n        // Note: The we don't rely on the default here as we need to dynamically lookup the widths based on the current editor state\n        const options = editor.options;\n        const defaultAttributes = options.get('table_default_attributes');\n        return options.isSet('table_default_attributes') ? defaultAttributes : determineDefaultAttributes(editor, defaultAttributes);\n    };\n\n    const lookupTable = (container) => {\n        return ancestor(container, 'table');\n    };\n    const retrieve$1 = (container, selector) => {\n        const sels = descendants(container, selector);\n        return sels.length > 0 ? Optional.some(sels) : Optional.none();\n    };\n    const getEdges = (container, firstSelectedSelector, lastSelectedSelector) => {\n        return descendant(container, firstSelectedSelector).bind((first) => {\n            return descendant(container, lastSelectedSelector).bind((last) => {\n                return sharedOne(lookupTable, [first, last]).map((table) => {\n                    return {\n                        first,\n                        last,\n                        table\n                    };\n                });\n            });\n        });\n    };\n\n    // Explicitly calling CellSelection.retrieve so that we can see the API signature.\n    const retrieve = (container, selector) => {\n        return retrieve$1(container, selector);\n    };\n    const retrieveBox = (container, firstSelectedSelector, lastSelectedSelector) => {\n        return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind((edges) => {\n            const isRoot = (ancestor) => {\n                return eq(container, ancestor);\n            };\n            const sectionSelector = 'thead,tfoot,tbody,table';\n            const firstAncestor = ancestor(edges.first, sectionSelector, isRoot);\n            const lastAncestor = ancestor(edges.last, sectionSelector, isRoot);\n            return firstAncestor.bind((fA) => {\n                return lastAncestor.bind((lA) => {\n                    return eq(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();\n                });\n            });\n        });\n    };\n\n    const selection = identity;\n    const unmergable = (selectedCells) => {\n        const hasSpan = (elem, type) => getOpt(elem, type).exists((span) => parseInt(span, 10) > 1);\n        const hasRowOrColSpan = (elem) => hasSpan(elem, 'rowspan') || hasSpan(elem, 'colspan');\n        return selectedCells.length > 0 && forall(selectedCells, hasRowOrColSpan) ? Optional.some(selectedCells) : Optional.none();\n    };\n    const mergable = (table, selectedCells, ephemera) => {\n        if (selectedCells.length <= 1) {\n            return Optional.none();\n        }\n        else {\n            return retrieveBox(table, ephemera.firstSelectedSelector, ephemera.lastSelectedSelector)\n                .map((bounds) => ({ bounds, cells: selectedCells }));\n        }\n    };\n\n    /*\n     NOTE: This file is duplicated in the following locations:\n      - models/dom/table/selection/Ephemera.ts\n      - advtable\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const strSelected = 'data-mce-selected';\n    const strSelectedSelector = 'td[' + strSelected + '],th[' + strSelected + ']';\n    const strFirstSelected = 'data-mce-first-selected';\n    const strFirstSelectedSelector = 'td[' + strFirstSelected + '],th[' + strFirstSelected + ']';\n    const strLastSelected = 'data-mce-last-selected';\n    const strLastSelectedSelector = 'td[' + strLastSelected + '],th[' + strLastSelected + ']';\n    const ephemera = {\n        selected: strSelected,\n        selectedSelector: strSelectedSelector,\n        firstSelected: strFirstSelected,\n        firstSelectedSelector: strFirstSelectedSelector,\n        lastSelected: strLastSelected,\n        lastSelectedSelector: strLastSelectedSelector\n    };\n\n    /*\n     NOTE: This file is partially duplicated in the following locations:\n      - models/dom/table/selection/TableSelection.ts\n      - advtable\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const getSelectionCellFallback = (element) => table(element).bind((table) => retrieve(table, ephemera.firstSelectedSelector)).fold(constant(element), (cells) => cells[0]);\n    const getSelectionFromSelector = (selector) => (initCell, isRoot) => {\n        const cellName = name(initCell);\n        const cell = cellName === 'col' || cellName === 'colgroup' ? getSelectionCellFallback(initCell) : initCell;\n        return closest$1(cell, selector, isRoot);\n    };\n    const getSelectionCellOrCaption = getSelectionFromSelector('th,td,caption');\n    const getSelectionCell = getSelectionFromSelector('th,td');\n    const getCellsFromSelection = (editor) => fromDom(editor.model.table.getSelectedCells());\n    const getRowsFromSelection = (selected, selector) => {\n        const cellOpt = getSelectionCell(selected);\n        const rowsOpt = cellOpt.bind((cell) => table(cell))\n            .map((table) => rows(table));\n        return lift2(cellOpt, rowsOpt, (cell, rows) => filter$1(rows, (row) => exists(fromDom(row.dom.cells), (rowCell) => get$2(rowCell, selector) === '1' || eq(rowCell, cell)))).getOr([]);\n    };\n\n    const verticalAlignValues = [\n        {\n            text: 'None',\n            value: ''\n        },\n        {\n            text: 'Top',\n            value: 'top'\n        },\n        {\n            text: 'Middle',\n            value: 'middle'\n        },\n        {\n            text: 'Bottom',\n            value: 'bottom'\n        }\n    ];\n\n    const hexColour = (value) => ({\n        value: normalizeHex(value)\n    });\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n    const longformRegex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\n    const isHexString = (hex) => shorthandRegex.test(hex) || longformRegex.test(hex);\n    const normalizeHex = (hex) => removeLeading(hex, '#').toUpperCase();\n    const fromString$1 = (hex) => isHexString(hex) ? Optional.some({ value: normalizeHex(hex) }) : Optional.none();\n    const toHex = (component) => {\n        const hex = component.toString(16);\n        return (hex.length === 1 ? '0' + hex : hex).toUpperCase();\n    };\n    const fromRgba = (rgbaColour) => {\n        const value = toHex(rgbaColour.red) + toHex(rgbaColour.green) + toHex(rgbaColour.blue);\n        return hexColour(value);\n    };\n\n    /* eslint-disable no-console */\n    const rgbRegex = /^\\s*rgb\\s*\\(\\s*(\\d+)\\s*[,\\s]\\s*(\\d+)\\s*[,\\s]\\s*(\\d+)\\s*\\)\\s*$/i;\n    // This regex will match rgba(0, 0, 0, 0.5) or rgba(0, 0, 0, 50%) , or without commas\n    const rgbaRegex = /^\\s*rgba\\s*\\(\\s*(\\d+)\\s*[,\\s]\\s*(\\d+)\\s*[,\\s]\\s*(\\d+)\\s*[,\\s]\\s*((?:\\d?\\.\\d+|\\d+)%?)\\s*\\)\\s*$/i;\n    const rgbaColour = (red, green, blue, alpha) => ({\n        red,\n        green,\n        blue,\n        alpha\n    });\n    const fromStringValues = (red, green, blue, alpha) => {\n        const r = parseInt(red, 10);\n        const g = parseInt(green, 10);\n        const b = parseInt(blue, 10);\n        const a = parseFloat(alpha);\n        return rgbaColour(r, g, b, a);\n    };\n    const fromString = (rgbaString) => {\n        const rgbMatch = rgbRegex.exec(rgbaString);\n        if (rgbMatch !== null) {\n            return Optional.some(fromStringValues(rgbMatch[1], rgbMatch[2], rgbMatch[3], '1'));\n        }\n        const rgbaMatch = rgbaRegex.exec(rgbaString);\n        if (rgbaMatch !== null) {\n            return Optional.some(fromStringValues(rgbaMatch[1], rgbaMatch[2], rgbaMatch[3], rgbaMatch[4]));\n        }\n        return Optional.none();\n    };\n\n    const anyToHex = (color) => fromString$1(color)\n        .orThunk(() => fromString(color).map(fromRgba))\n        .getOrThunk(() => {\n        // Not dealing with Hex or RGBA so use a canvas to parse the color\n        const canvas = document.createElement('canvas');\n        canvas.height = 1;\n        canvas.width = 1;\n        const canvasContext = canvas.getContext('2d');\n        // all valid colors after this point\n        canvasContext.clearRect(0, 0, canvas.width, canvas.height);\n        // invalid colors will be shown as white - the first assignment will pass and the second may be ignored\n        canvasContext.fillStyle = '#FFFFFF';\n        canvasContext.fillStyle = color;\n        canvasContext.fillRect(0, 0, 1, 1);\n        const rgba = canvasContext.getImageData(0, 0, 1, 1).data;\n        const r = rgba[0];\n        const g = rgba[1];\n        const b = rgba[2];\n        const a = rgba[3];\n        return fromRgba(rgbaColour(r, g, b, a));\n    });\n    const rgbaToHexString = (color) => fromString(color)\n        .map(fromRgba)\n        .map((h) => '#' + h.value)\n        .getOr(color);\n\n    const onSetupToggle = (editor, formatName, formatValue) => {\n        return (api) => {\n            const boundCallback = unbindable();\n            const isNone = isEmpty(formatValue);\n            const init = () => {\n                const selectedCells = getCellsFromSelection(editor);\n                const checkNode = (cell) => editor.formatter.match(formatName, { value: formatValue }, cell.dom, isNone);\n                // If value is empty (A None-entry in the list), check if the format is not set at all. Otherwise, check if the format is set to the correct value.\n                if (isNone) {\n                    api.setActive(!exists(selectedCells, checkNode));\n                    boundCallback.set(editor.formatter.formatChanged(formatName, (match) => api.setActive(!match), true));\n                }\n                else {\n                    api.setActive(forall(selectedCells, checkNode));\n                    boundCallback.set(editor.formatter.formatChanged(formatName, api.setActive, false, { value: formatValue }));\n                }\n            };\n            // The editor may or may not have been setup yet, so check for that\n            editor.initialized ? init() : editor.on('init', init);\n            return boundCallback.clear;\n        };\n    };\n    const isListGroup = (item) => hasNonNullableKey(item, 'menu');\n    const buildListItems = (items) => map(items, (item) => {\n        // item.text is not documented - maybe deprecated option we can delete??\n        const text = item.text || item.title || '';\n        if (isListGroup(item)) {\n            return {\n                text,\n                items: buildListItems(item.menu)\n            };\n        }\n        else {\n            return {\n                text,\n                value: item.value\n            };\n        }\n    });\n    const buildClassList = (classList) => {\n        if (!classList.length) {\n            return Optional.none();\n        }\n        return Optional.some(buildListItems([{ text: 'Select...', value: 'mce-no-match' }, ...classList]));\n    };\n    const buildMenuItems = (editor, items, format, onAction) => map(items, (item) => {\n        // item.text is not documented - maybe deprecated option we can delete??\n        const text = item.text || item.title;\n        if (isListGroup(item)) {\n            return {\n                type: 'nestedmenuitem',\n                text,\n                getSubmenuItems: () => buildMenuItems(editor, item.menu, format, onAction)\n            };\n        }\n        else {\n            return {\n                text,\n                type: 'togglemenuitem',\n                onAction: () => onAction(item.value),\n                onSetup: onSetupToggle(editor, format, item.value)\n            };\n        }\n    });\n    const applyTableCellStyle = (editor, style) => (value) => {\n        editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n    };\n    const filterNoneItem = (list) => bind(list, (item) => {\n        if (isListGroup(item)) {\n            return [{ ...item, menu: filterNoneItem(item.menu) }];\n        }\n        else {\n            return isNotEmpty(item.value) ? [item] : [];\n        }\n    });\n    const generateMenuItemsCallback = (editor, items, format, onAction) => (callback) => callback(buildMenuItems(editor, items, format, onAction));\n    const buildColorMenu = (editor, colorList, style) => {\n        const colorMap = map(colorList, (entry) => ({\n            text: entry.title,\n            value: '#' + anyToHex(entry.value).value,\n            type: 'choiceitem'\n        }));\n        return [{\n                type: 'fancymenuitem',\n                fancytype: 'colorswatch',\n                initData: {\n                    colors: colorMap.length > 0 ? colorMap : undefined,\n                    allowCustomColors: false\n                },\n                onAction: (data) => {\n                    const value = data.value === 'remove' ? '' : data.value;\n                    editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n                }\n            }];\n    };\n    const changeRowHeader = (editor) => () => {\n        const currentType = editor.queryCommandValue('mceTableRowType');\n        const newType = currentType === 'header' ? 'body' : 'header';\n        editor.execCommand('mceTableRowType', false, { type: newType });\n    };\n    const changeColumnHeader = (editor) => () => {\n        const currentType = editor.queryCommandValue('mceTableColType');\n        const newType = currentType === 'th' ? 'td' : 'th';\n        editor.execCommand('mceTableColType', false, { type: newType });\n    };\n\n    const getClassList$1 = (editor) => buildClassList(getCellClassList(editor))\n        .map((items) => ({\n        name: 'class',\n        type: 'listbox',\n        label: 'Class',\n        items\n    }));\n    const children = [\n        {\n            name: 'width',\n            type: 'input',\n            label: 'Width'\n        },\n        {\n            name: 'celltype',\n            type: 'listbox',\n            label: 'Cell type',\n            items: [\n                { text: 'Cell', value: 'td' },\n                { text: 'Header cell', value: 'th' }\n            ]\n        },\n        {\n            name: 'scope',\n            type: 'listbox',\n            label: 'Scope',\n            items: [\n                { text: 'None', value: '' },\n                { text: 'Row', value: 'row' },\n                { text: 'Column', value: 'col' },\n                { text: 'Row group', value: 'rowgroup' },\n                { text: 'Column group', value: 'colgroup' }\n            ]\n        },\n        {\n            name: 'halign',\n            type: 'listbox',\n            label: 'Horizontal align',\n            items: [\n                { text: 'None', value: '' },\n                { text: 'Left', value: 'left' },\n                { text: 'Center', value: 'center' },\n                { text: 'Right', value: 'right' }\n            ]\n        },\n        {\n            name: 'valign',\n            type: 'listbox',\n            label: 'Vertical align',\n            items: verticalAlignValues\n        }\n    ];\n    const getItems$2 = (editor) => children.concat(getClassList$1(editor).toArray());\n\n    const getAdvancedTab = (editor, dialogName) => {\n        const emptyBorderStyle = [{ text: 'Select...', value: '' }];\n        const advTabItems = [\n            {\n                name: 'borderstyle',\n                type: 'listbox',\n                label: 'Border style',\n                items: emptyBorderStyle.concat(buildListItems(getTableBorderStyles(editor)))\n            },\n            {\n                name: 'bordercolor',\n                type: 'colorinput',\n                label: 'Border color'\n            },\n            {\n                name: 'backgroundcolor',\n                type: 'colorinput',\n                label: 'Background color'\n            }\n        ];\n        const borderWidth = {\n            name: 'borderwidth',\n            type: 'input',\n            label: 'Border width'\n        };\n        const items = dialogName === 'cell' ? [borderWidth].concat(advTabItems) : advTabItems;\n        return {\n            title: 'Advanced',\n            name: 'advanced',\n            items\n        };\n    };\n\n    // The get node is required here because it can be transformed\n    // when switching between tags (e.g. th and td)\n    const normal = (editor, element) => {\n        const dom = editor.dom;\n        const setAttrib = (attr, value) => {\n            dom.setAttrib(element, attr, value);\n        };\n        const setStyle = (prop, value) => {\n            dom.setStyle(element, prop, value);\n        };\n        const setFormat = (formatName, value) => {\n            // Remove format if given an empty string\n            if (value === '') {\n                editor.formatter.remove(formatName, { value: null }, element, true);\n            }\n            else {\n                editor.formatter.apply(formatName, { value }, element);\n            }\n        };\n        return {\n            setAttrib,\n            setStyle,\n            setFormat\n        };\n    };\n    const DomModifier = {\n        normal\n    };\n\n    const rgbToHex = (value) => startsWith(value, 'rgb') ? rgbaToHexString(value) : value;\n    const extractAdvancedStyles = (elm) => {\n        const element = SugarElement.fromDom(elm);\n        return {\n            borderwidth: getRaw$1(element, 'border-width').getOr(''),\n            borderstyle: getRaw$1(element, 'border-style').getOr(''),\n            bordercolor: getRaw$1(element, 'border-color').map(rgbToHex).getOr(''),\n            backgroundcolor: getRaw$1(element, 'background-color').map(rgbToHex).getOr('')\n        };\n    };\n    const getSharedValues = (data) => {\n        // TODO surely there's a better way to do this??\n        // Mutates baseData to return an object that contains only the values\n        // that were the same across all objects in data\n        const baseData = data[0];\n        const comparisonData = data.slice(1);\n        each$1(comparisonData, (items) => {\n            each$1(keys(baseData), (key) => {\n                each(items, (itemValue, itemKey) => {\n                    const comparisonValue = baseData[key];\n                    if (comparisonValue !== '' && key === itemKey) {\n                        if (comparisonValue !== itemValue) {\n                            baseData[key] = key === 'class' ? 'mce-no-match' : '';\n                        }\n                    }\n                });\n            });\n        });\n        return baseData;\n    };\n    // The extractDataFrom... functions are in this file partly for code reuse and partly so we can test them,\n    // because some of these are crazy complicated\n    const getAlignment = (formats, formatName, editor, elm) => find(formats, (name) => !isUndefined(editor.formatter.matchNode(elm, formatName + name))).getOr('');\n    const getHAlignment = curry(getAlignment, ['left', 'center', 'right'], 'align');\n    const getVAlignment = curry(getAlignment, ['top', 'middle', 'bottom'], 'valign');\n    const extractDataFromSettings = (editor, hasAdvTableTab) => {\n        const style = getDefaultStyles(editor);\n        const attrs = getDefaultAttributes(editor);\n        const extractAdvancedStyleData = () => ({\n            borderstyle: get$3(style, 'border-style').getOr(''),\n            bordercolor: rgbToHex(get$3(style, 'border-color').getOr('')),\n            backgroundcolor: rgbToHex(get$3(style, 'background-color').getOr(''))\n        });\n        const defaultData = {\n            height: '',\n            width: '100%',\n            cellspacing: '',\n            cellpadding: '',\n            caption: false,\n            class: '',\n            align: '',\n            border: ''\n        };\n        const getBorder = () => {\n            const borderWidth = style['border-width'];\n            if (shouldStyleWithCss(editor) && borderWidth) {\n                return { border: borderWidth };\n            }\n            return get$3(attrs, 'border').fold(() => ({}), (border) => ({ border }));\n        };\n        const advStyle = (hasAdvTableTab ? extractAdvancedStyleData() : {});\n        const getCellPaddingCellSpacing = () => {\n            const spacing = get$3(style, 'border-spacing').or(get$3(attrs, 'cellspacing')).fold(() => ({}), (cellspacing) => ({ cellspacing }));\n            const padding = get$3(style, 'border-padding').or(get$3(attrs, 'cellpadding')).fold(() => ({}), (cellpadding) => ({ cellpadding }));\n            return {\n                ...spacing,\n                ...padding\n            };\n        };\n        const data = {\n            ...defaultData,\n            ...style,\n            ...attrs,\n            ...advStyle,\n            ...getBorder(),\n            ...getCellPaddingCellSpacing()\n        };\n        return data;\n    };\n    const getRowType = (elm) => table(SugarElement.fromDom(elm)).map((table) => {\n        const target = { selection: fromDom(elm.cells) };\n        return getRowsType(table, target);\n    }).getOr('');\n    const extractDataFromTableElement = (editor, elm, hasAdvTableTab) => {\n        const getBorder = (dom, elm) => {\n            // Cases (in order to check):\n            // 1. shouldStyleWithCss - extract border-width style if it exists\n            // 2. !shouldStyleWithCss && border attribute - set border attribute as value\n            // 3. !shouldStyleWithCss && nothing on the table - grab styles from the first th or td\n            const optBorderWidth = getRaw$1(SugarElement.fromDom(elm), 'border-width');\n            if (shouldStyleWithCss(editor) && optBorderWidth.isSome()) {\n                return optBorderWidth.getOr('');\n            }\n            return dom.getAttrib(elm, 'border') || getTDTHOverallStyle(editor.dom, elm, 'border-width')\n                || getTDTHOverallStyle(editor.dom, elm, 'border') || '';\n        };\n        const dom = editor.dom;\n        const cellspacing = shouldStyleWithCss(editor) ?\n            dom.getStyle(elm, 'border-spacing') || dom.getAttrib(elm, 'cellspacing') :\n            dom.getAttrib(elm, 'cellspacing') || dom.getStyle(elm, 'border-spacing');\n        const cellpadding = shouldStyleWithCss(editor) ?\n            getTDTHOverallStyle(dom, elm, 'padding') || dom.getAttrib(elm, 'cellpadding') :\n            dom.getAttrib(elm, 'cellpadding') || getTDTHOverallStyle(dom, elm, 'padding');\n        return {\n            width: dom.getStyle(elm, 'width') || dom.getAttrib(elm, 'width'),\n            height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n            cellspacing: cellspacing !== null && cellspacing !== void 0 ? cellspacing : '',\n            cellpadding: cellpadding !== null && cellpadding !== void 0 ? cellpadding : '',\n            border: getBorder(dom, elm),\n            caption: !!dom.select('caption', elm)[0],\n            class: dom.getAttrib(elm, 'class', ''),\n            align: getHAlignment(editor, elm),\n            ...(hasAdvTableTab ? extractAdvancedStyles(elm) : {})\n        };\n    };\n    const extractDataFromRowElement = (editor, elm, hasAdvancedRowTab) => {\n        const dom = editor.dom;\n        return {\n            height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n            class: dom.getAttrib(elm, 'class', ''),\n            type: getRowType(elm),\n            align: getHAlignment(editor, elm),\n            ...(hasAdvancedRowTab ? extractAdvancedStyles(elm) : {})\n        };\n    };\n    const extractDataFromCellElement = (editor, cell, hasAdvancedCellTab, column) => {\n        const dom = editor.dom;\n        const colElm = column.getOr(cell);\n        const getStyle = (element, style) => dom.getStyle(element, style) || dom.getAttrib(element, style);\n        return {\n            width: getStyle(colElm, 'width'),\n            scope: dom.getAttrib(cell, 'scope'),\n            celltype: getNodeName(cell),\n            class: dom.getAttrib(cell, 'class', ''),\n            halign: getHAlignment(editor, cell),\n            valign: getVAlignment(editor, cell),\n            ...(hasAdvancedCellTab ? extractAdvancedStyles(cell) : {})\n        };\n    };\n\n    const getSelectedCells = (table, cells) => {\n        const warehouse = Warehouse.fromTable(table);\n        const allCells = Warehouse.justCells(warehouse);\n        const filtered = filter$1(allCells, (cellA) => exists(cells, (cellB) => eq(cellA.element, cellB)));\n        return map(filtered, (cell) => ({\n            element: cell.element.dom,\n            column: Warehouse.getColumnAt(warehouse, cell.column).map((col) => col.element.dom)\n        }));\n    };\n    const updateSimpleProps$1 = (modifier, colModifier, data, shouldUpdate) => {\n        if (shouldUpdate('scope')) {\n            modifier.setAttrib('scope', data.scope);\n        }\n        if (shouldUpdate('class') && data.class !== 'mce-no-match') {\n            modifier.setAttrib('class', data.class);\n        }\n        if (shouldUpdate('width')) {\n            colModifier.setStyle('width', addPxSuffix(data.width));\n        }\n    };\n    const updateAdvancedProps$1 = (modifier, data, shouldUpdate) => {\n        if (shouldUpdate('backgroundcolor')) {\n            modifier.setFormat('tablecellbackgroundcolor', data.backgroundcolor);\n        }\n        if (shouldUpdate('bordercolor')) {\n            modifier.setFormat('tablecellbordercolor', data.bordercolor);\n        }\n        if (shouldUpdate('borderstyle')) {\n            modifier.setFormat('tablecellborderstyle', data.borderstyle);\n        }\n        if (shouldUpdate('borderwidth')) {\n            modifier.setFormat('tablecellborderwidth', addPxSuffix(data.borderwidth));\n        }\n    };\n    const applyStyleData$1 = (editor, cells, data, wasChanged) => {\n        const isSingleCell = cells.length === 1;\n        each$1(cells, (item) => {\n            const cellElm = item.element;\n            const shouldOverrideCurrentValue = isSingleCell ? always : wasChanged;\n            const modifier = DomModifier.normal(editor, cellElm);\n            const colModifier = item.column.map((col) => DomModifier.normal(editor, col)).getOr(modifier);\n            updateSimpleProps$1(modifier, colModifier, data, shouldOverrideCurrentValue);\n            if (hasAdvancedCellTab(editor)) {\n                updateAdvancedProps$1(modifier, data, shouldOverrideCurrentValue);\n            }\n            // Apply alignment\n            if (wasChanged('halign')) {\n                setAlign(editor, cellElm, data.halign);\n            }\n            // Apply vertical alignment\n            if (wasChanged('valign')) {\n                setVAlign(editor, cellElm, data.valign);\n            }\n        });\n    };\n    const applyStructureData$1 = (editor, data) => {\n        // Switch cell type if applicable. Note that we specifically tell the command to not fire events\n        // as we'll batch the events and fire a `TableModified` event at the end of the updates.\n        editor.execCommand('mceTableCellType', false, { type: data.celltype, no_events: true });\n    };\n    const applyCellData = (editor, cells, oldData, data) => {\n        const modifiedData = filter(data, (value, key) => oldData[key] !== value);\n        if (size(modifiedData) > 0 && cells.length >= 1) {\n            // Retrieve the table before the cells are modified as there is a case where cells\n            // are replaced and the reference will be lost when trying to fire events.\n            table(cells[0]).each((table) => {\n                const selectedCells = getSelectedCells(table, cells);\n                // style modified if there's at least one other change apart from 'celltype' and 'scope'\n                const styleModified = size(filter(modifiedData, (_value, key) => key !== 'scope' && key !== 'celltype')) > 0;\n                const structureModified = has(modifiedData, 'celltype');\n                // Update the cells styling using the dialog data\n                if (styleModified || has(modifiedData, 'scope')) {\n                    applyStyleData$1(editor, selectedCells, data, curry(has, modifiedData));\n                }\n                // Update the cells structure using the dialog data\n                if (structureModified) {\n                    applyStructureData$1(editor, data);\n                }\n                fireTableModified(editor, table.dom, {\n                    structure: structureModified,\n                    style: styleModified,\n                });\n            });\n        }\n    };\n    const onSubmitCellForm = (editor, cells, oldData, api) => {\n        const data = api.getData();\n        api.close();\n        editor.undoManager.transact(() => {\n            applyCellData(editor, cells, oldData, data);\n            editor.focus();\n        });\n    };\n    const getData$1 = (editor, cells) => {\n        const cellsData = table(cells[0]).map((table) => map(getSelectedCells(table, cells), (item) => extractDataFromCellElement(editor, item.element, hasAdvancedCellTab(editor), item.column)));\n        return getSharedValues(cellsData.getOrDie());\n    };\n    const open$2 = (editor) => {\n        const cells = getCellsFromSelection(editor);\n        // Check if there are any cells to operate on\n        if (cells.length === 0) {\n            return;\n        }\n        const data = getData$1(editor, cells);\n        const dialogTabPanel = {\n            type: 'tabpanel',\n            tabs: [\n                {\n                    title: 'General',\n                    name: 'general',\n                    items: getItems$2(editor)\n                },\n                getAdvancedTab(editor, 'cell')\n            ]\n        };\n        const dialogPanel = {\n            type: 'panel',\n            items: [\n                {\n                    type: 'grid',\n                    columns: 2,\n                    items: getItems$2(editor)\n                }\n            ]\n        };\n        editor.windowManager.open({\n            title: 'Cell Properties',\n            size: 'normal',\n            body: hasAdvancedCellTab(editor) ? dialogTabPanel : dialogPanel,\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: data,\n            onSubmit: curry(onSubmitCellForm, editor, cells, data)\n        });\n    };\n\n    const getClassList = (editor) => buildClassList(getRowClassList(editor))\n        .map((items) => ({\n        name: 'class',\n        type: 'listbox',\n        label: 'Class',\n        items\n    }));\n    const formChildren = [\n        {\n            type: 'listbox',\n            name: 'type',\n            label: 'Row type',\n            items: [\n                { text: 'Header', value: 'header' },\n                { text: 'Body', value: 'body' },\n                { text: 'Footer', value: 'footer' }\n            ]\n        },\n        {\n            type: 'listbox',\n            name: 'align',\n            label: 'Alignment',\n            items: [\n                { text: 'None', value: '' },\n                { text: 'Left', value: 'left' },\n                { text: 'Center', value: 'center' },\n                { text: 'Right', value: 'right' }\n            ]\n        },\n        {\n            label: 'Height',\n            name: 'height',\n            type: 'input'\n        }\n    ];\n    const getItems$1 = (editor) => formChildren.concat(getClassList(editor).toArray());\n\n    const updateSimpleProps = (modifier, data, shouldUpdate) => {\n        if (shouldUpdate('class') && data.class !== 'mce-no-match') {\n            modifier.setAttrib('class', data.class);\n        }\n        if (shouldUpdate('height')) {\n            modifier.setStyle('height', addPxSuffix(data.height));\n        }\n    };\n    const updateAdvancedProps = (modifier, data, shouldUpdate) => {\n        if (shouldUpdate('backgroundcolor')) {\n            modifier.setStyle('background-color', data.backgroundcolor);\n        }\n        if (shouldUpdate('bordercolor')) {\n            modifier.setStyle('border-color', data.bordercolor);\n        }\n        if (shouldUpdate('borderstyle')) {\n            modifier.setStyle('border-style', data.borderstyle);\n        }\n    };\n    const applyStyleData = (editor, rows, data, wasChanged) => {\n        const isSingleRow = rows.length === 1;\n        const shouldOverrideCurrentValue = isSingleRow ? always : wasChanged;\n        each$1(rows, (rowElm) => {\n            const rowCells = children$1(SugarElement.fromDom(rowElm), 'td,th');\n            const modifier = DomModifier.normal(editor, rowElm);\n            updateSimpleProps(modifier, data, shouldOverrideCurrentValue);\n            if (hasAdvancedRowTab(editor)) {\n                updateAdvancedProps(modifier, data, shouldOverrideCurrentValue);\n            }\n            // TINY-10617: Simplify number of height styles when applying height on tr\n            if (wasChanged('height')) {\n                each$1(rowCells, (cell) => {\n                    editor.dom.setStyle(cell.dom, 'height', null);\n                });\n            }\n            if (wasChanged('align')) {\n                setAlign(editor, rowElm, data.align);\n            }\n        });\n    };\n    const applyStructureData = (editor, data) => {\n        // Switch cell type if applicable. Note that we specifically tell the command to not fire events\n        // as we'll batch the events and fire a `TableModified` event at the end of the updates.\n        editor.execCommand('mceTableRowType', false, { type: data.type, no_events: true });\n    };\n    const applyRowData = (editor, rows, oldData, data) => {\n        const modifiedData = filter(data, (value, key) => oldData[key] !== value);\n        if (size(modifiedData) > 0) {\n            const typeModified = has(modifiedData, 'type');\n            // style modified if there's at least one other change apart from 'type'\n            const styleModified = typeModified ? size(modifiedData) > 1 : true;\n            // Update the rows styling using the dialog data\n            if (styleModified) {\n                applyStyleData(editor, rows, data, curry(has, modifiedData));\n            }\n            // Update the rows structure using the dialog data\n            if (typeModified) {\n                applyStructureData(editor, data);\n            }\n            table(SugarElement.fromDom(rows[0])).each((table) => fireTableModified(editor, table.dom, {\n                structure: typeModified,\n                style: styleModified\n            }));\n        }\n    };\n    const onSubmitRowForm = (editor, rows, oldData, api) => {\n        const data = api.getData();\n        api.close();\n        editor.undoManager.transact(() => {\n            applyRowData(editor, rows, oldData, data);\n            editor.focus();\n        });\n    };\n    const open$1 = (editor) => {\n        const rows = getRowsFromSelection(getSelectionStart(editor), ephemera.selected);\n        // Check if there are any rows to operate on\n        if (rows.length === 0) {\n            return;\n        }\n        // Get current data and find shared values between rows\n        const rowsData = map(rows, (rowElm) => extractDataFromRowElement(editor, rowElm.dom, hasAdvancedRowTab(editor)));\n        const data = getSharedValues(rowsData);\n        const dialogTabPanel = {\n            type: 'tabpanel',\n            tabs: [\n                {\n                    title: 'General',\n                    name: 'general',\n                    items: getItems$1(editor)\n                },\n                getAdvancedTab(editor, 'row')\n            ]\n        };\n        const dialogPanel = {\n            type: 'panel',\n            items: [\n                {\n                    type: 'grid',\n                    columns: 2,\n                    items: getItems$1(editor)\n                }\n            ]\n        };\n        editor.windowManager.open({\n            title: 'Row Properties',\n            size: 'normal',\n            body: hasAdvancedRowTab(editor) ? dialogTabPanel : dialogPanel,\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: data,\n            onSubmit: curry(onSubmitRowForm, editor, map(rows, (r) => r.dom), data)\n        });\n    };\n\n    const getItems = (editor, classes, insertNewTable) => {\n        const rowColCountItems = !insertNewTable ? [] : [\n            {\n                type: 'input',\n                name: 'cols',\n                label: 'Cols',\n                inputMode: 'numeric'\n            },\n            {\n                type: 'input',\n                name: 'rows',\n                label: 'Rows',\n                inputMode: 'numeric'\n            }\n        ];\n        const alwaysItems = [\n            {\n                type: 'input',\n                name: 'width',\n                label: 'Width'\n            },\n            {\n                type: 'input',\n                name: 'height',\n                label: 'Height'\n            }\n        ];\n        const appearanceItems = hasAppearanceOptions(editor) ? [\n            {\n                type: 'input',\n                name: 'cellspacing',\n                label: 'Cell spacing',\n                inputMode: 'numeric'\n            },\n            {\n                type: 'input',\n                name: 'cellpadding',\n                label: 'Cell padding',\n                inputMode: 'numeric'\n            },\n            {\n                type: 'input',\n                name: 'border',\n                label: 'Border width'\n            },\n            {\n                type: 'label',\n                label: 'Caption',\n                items: [\n                    {\n                        type: 'checkbox',\n                        name: 'caption',\n                        label: 'Show caption'\n                    }\n                ]\n            }\n        ] : [];\n        const alignmentItem = [\n            {\n                type: 'listbox',\n                name: 'align',\n                label: 'Alignment',\n                items: [\n                    { text: 'None', value: '' },\n                    { text: 'Left', value: 'left' },\n                    { text: 'Center', value: 'center' },\n                    { text: 'Right', value: 'right' }\n                ]\n            }\n        ];\n        const classListItem = classes.length > 0 ? [\n            {\n                name: 'class',\n                type: 'listbox',\n                label: 'Class',\n                items: classes\n            }\n        ] : [];\n        return rowColCountItems.concat(alwaysItems).concat(appearanceItems).concat(alignmentItem).concat(classListItem);\n    };\n\n    // Explore the layers of the table till we find the first layer of tds or ths\n    const styleTDTH = (dom, elm, name, value) => {\n        if (elm.tagName === 'TD' || elm.tagName === 'TH') {\n            if (isString(name) && isNonNullable(value)) {\n                dom.setStyle(elm, name, value);\n            }\n            else {\n                dom.setStyles(elm, name);\n            }\n        }\n        else {\n            if (elm.children) {\n                for (let i = 0; i < elm.children.length; i++) {\n                    styleTDTH(dom, elm.children[i], name, value);\n                }\n            }\n        }\n    };\n    const applyDataToElement = (editor, tableElm, data, shouldApplyOnCell) => {\n        const dom = editor.dom;\n        const attrs = {};\n        const styles = {};\n        const shouldStyleWithCss$1 = shouldStyleWithCss(editor);\n        const hasAdvancedTableTab$1 = hasAdvancedTableTab(editor);\n        const borderIsZero = parseFloat(data.border) === 0;\n        if (!isUndefined(data.class) && data.class !== 'mce-no-match') {\n            attrs.class = data.class;\n        }\n        styles.height = addPxSuffix(data.height);\n        if (shouldStyleWithCss$1) {\n            styles.width = addPxSuffix(data.width);\n        }\n        else if (dom.getAttrib(tableElm, 'width')) {\n            attrs.width = removePxSuffix(data.width);\n        }\n        if (shouldStyleWithCss$1) {\n            if (borderIsZero) {\n                attrs.border = 0;\n                styles['border-width'] = '';\n            }\n            else {\n                styles['border-width'] = addPxSuffix(data.border);\n                attrs.border = 1;\n            }\n            styles['border-spacing'] = addPxSuffix(data.cellspacing);\n        }\n        else {\n            attrs.border = borderIsZero ? 0 : data.border;\n            attrs.cellpadding = data.cellpadding;\n            attrs.cellspacing = data.cellspacing;\n        }\n        // TINY-9837: Relevant data are applied on child TD/THs only if they have been modified since the previous dialog submission\n        if (shouldStyleWithCss$1 && tableElm.children) {\n            const cellStyles = {};\n            if (borderIsZero) {\n                cellStyles['border-width'] = '';\n            }\n            else if (shouldApplyOnCell.border) {\n                cellStyles['border-width'] = addPxSuffix(data.border);\n            }\n            if (shouldApplyOnCell.cellpadding) {\n                cellStyles.padding = addPxSuffix(data.cellpadding);\n            }\n            if (hasAdvancedTableTab$1 && shouldApplyOnCell.bordercolor) {\n                cellStyles['border-color'] = data.bordercolor;\n            }\n            if (!isEmpty$1(cellStyles)) {\n                for (let i = 0; i < tableElm.children.length; i++) {\n                    styleTDTH(dom, tableElm.children[i], cellStyles);\n                }\n            }\n        }\n        if (hasAdvancedTableTab$1) {\n            const advData = data;\n            styles['background-color'] = advData.backgroundcolor;\n            styles['border-color'] = advData.bordercolor;\n            styles['border-style'] = advData.borderstyle;\n        }\n        dom.setStyles(tableElm, { ...getDefaultStyles(editor), ...styles });\n        dom.setAttribs(tableElm, { ...getDefaultAttributes(editor), ...attrs });\n    };\n    const onSubmitTableForm = (editor, tableElm, oldData, api) => {\n        const dom = editor.dom;\n        const data = api.getData();\n        const modifiedData = filter(data, (value, key) => oldData[key] !== value);\n        api.close();\n        editor.undoManager.transact(() => {\n            if (!tableElm) {\n                const cols = toInt(data.cols).getOr(1);\n                const rows = toInt(data.rows).getOr(1);\n                // Cases 1 & 3 - inserting a table\n                editor.execCommand('mceInsertTable', false, { rows, columns: cols });\n                tableElm = getSelectionCell(getSelectionStart(editor), getIsRoot(editor))\n                    .bind((cell) => table(cell, getIsRoot(editor)))\n                    .map((table) => table.dom)\n                    .getOrDie();\n            }\n            if (size(modifiedData) > 0) {\n                const applicableCellProperties = {\n                    border: has(modifiedData, 'border'),\n                    bordercolor: has(modifiedData, 'bordercolor'),\n                    cellpadding: has(modifiedData, 'cellpadding')\n                };\n                applyDataToElement(editor, tableElm, data, applicableCellProperties);\n                // Toggle caption on/off\n                const captionElm = dom.select('caption', tableElm)[0];\n                if (captionElm && !data.caption || !captionElm && data.caption) {\n                    editor.execCommand('mceTableToggleCaption');\n                }\n                setAlign(editor, tableElm, data.align);\n            }\n            editor.focus();\n            editor.addVisual();\n            if (size(modifiedData) > 0) {\n                const captionModified = has(modifiedData, 'caption');\n                // style modified if there's at least one other change apart from 'caption'\n                const styleModified = captionModified ? size(modifiedData) > 1 : true;\n                fireTableModified(editor, tableElm, { structure: captionModified, style: styleModified });\n            }\n        });\n    };\n    const open = (editor, insertNewTable) => {\n        const dom = editor.dom;\n        let tableElm;\n        let data = extractDataFromSettings(editor, hasAdvancedTableTab(editor));\n        // Cases for creation/update of tables:\n        // 1. isNew == true - called by mceInsertTable - we are inserting a new table so we don't care what the selection's parent is,\n        //    and we need to add cols and rows input fields to the dialog\n        // 2. isNew == false && selection parent is a table - update the table\n        // 3. isNew == false && selection parent isn't a table - open dialog with default values and insert a table\n        if (insertNewTable) {\n            // Case 1 - isNew == true. We're inserting a new table so use defaults and add cols and rows + adv properties.\n            data.cols = '1';\n            data.rows = '1';\n            if (hasAdvancedTableTab(editor)) {\n                data.borderstyle = '';\n                data.bordercolor = '';\n                data.backgroundcolor = '';\n            }\n        }\n        else {\n            tableElm = dom.getParent(editor.selection.getStart(), 'table', editor.getBody());\n            if (tableElm) {\n                // Case 2 - isNew == false && table parent\n                data = extractDataFromTableElement(editor, tableElm, hasAdvancedTableTab(editor));\n            }\n            else {\n                // Case 3 - isNew == false && non-table parent. data is set to basic defaults so just add the adv properties if needed\n                if (hasAdvancedTableTab(editor)) {\n                    data.borderstyle = '';\n                    data.bordercolor = '';\n                    data.backgroundcolor = '';\n                }\n            }\n        }\n        const classes = buildClassList(getTableClassList(editor));\n        if (classes.isSome()) {\n            if (data.class) {\n                data.class = data.class.replace(/\\s*mce\\-item\\-table\\s*/g, '');\n            }\n        }\n        const generalPanel = {\n            type: 'grid',\n            columns: 2,\n            items: getItems(editor, classes.getOr([]), insertNewTable)\n        };\n        const nonAdvancedForm = () => ({\n            type: 'panel',\n            items: [generalPanel]\n        });\n        const advancedForm = () => ({\n            type: 'tabpanel',\n            tabs: [\n                {\n                    title: 'General',\n                    name: 'general',\n                    items: [generalPanel]\n                },\n                getAdvancedTab(editor, 'table')\n            ]\n        });\n        const dialogBody = hasAdvancedTableTab(editor) ? advancedForm() : nonAdvancedForm();\n        editor.windowManager.open({\n            title: 'Table Properties',\n            size: 'normal',\n            body: dialogBody,\n            onSubmit: curry(onSubmitTableForm, editor, tableElm, data),\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: data\n        });\n    };\n\n    const registerCommands = (editor) => {\n        const runAction = (f) => {\n            if (isInEditableContext(getSelectionStart(editor))) {\n                f();\n            }\n        };\n        // Register dialog commands\n        each({\n            // AP-101 TableDialog.open renders a slightly different dialog if isNew is true\n            mceTableProps: curry(open, editor, false),\n            mceTableRowProps: curry(open$1, editor),\n            mceTableCellProps: curry(open$2, editor),\n            mceInsertTableDialog: curry(open, editor, true),\n        }, (func, name) => editor.addCommand(name, () => runAction(func)));\n    };\n\n    /*\n     NOTE: This file is partially duplicated in the following locations:\n      - models/dom/table/queries/TableTargets.ts\n      - advtable\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const noMenu = (cell) => ({\n        element: cell,\n        mergable: Optional.none(),\n        unmergable: Optional.none(),\n        selection: [cell]\n    });\n    const forMenu = (selectedCells, table, cell) => ({\n        element: cell,\n        mergable: mergable(table, selectedCells, ephemera),\n        unmergable: unmergable(selectedCells),\n        selection: selection(selectedCells)\n    });\n\n    const getSelectionTargets = (editor) => {\n        const targets = Cell(Optional.none());\n        const changeHandlers = Cell([]);\n        let selectionDetails = Optional.none();\n        const isCaption = isTag('caption');\n        const isDisabledForSelection = (key) => selectionDetails.forall((details) => !details[key]);\n        const getStart = () => getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor));\n        const getEnd = () => getSelectionCellOrCaption(getSelectionEnd(editor), getIsRoot(editor));\n        const findTargets = () => getStart().bind((startCellOrCaption) => flatten(lift2(table(startCellOrCaption), getEnd().bind(table), (startTable, endTable) => {\n            if (eq(startTable, endTable)) {\n                if (isCaption(startCellOrCaption)) {\n                    return Optional.some(noMenu(startCellOrCaption));\n                }\n                else {\n                    return Optional.some(forMenu(getCellsFromSelection(editor), startTable, startCellOrCaption));\n                }\n            }\n            return Optional.none();\n        })));\n        const getExtractedDetails = (targets) => {\n            const tableOpt = table(targets.element);\n            return tableOpt.map((table) => {\n                const warehouse = Warehouse.fromTable(table);\n                const selectedCells = onCells(warehouse, targets).getOr([]);\n                const locked = foldl(selectedCells, (acc, cell) => {\n                    if (cell.isLocked) {\n                        acc.onAny = true;\n                        if (cell.column === 0) {\n                            acc.onFirst = true;\n                        }\n                        else if (cell.column + cell.colspan >= warehouse.grid.columns) {\n                            acc.onLast = true;\n                        }\n                    }\n                    return acc;\n                }, { onAny: false, onFirst: false, onLast: false });\n                return {\n                    mergeable: onUnlockedMergable(warehouse, targets).isSome(),\n                    unmergeable: onUnlockedUnmergable(warehouse, targets).isSome(),\n                    locked\n                };\n            });\n        };\n        const resetTargets = () => {\n            // Reset the targets\n            targets.set(cached(findTargets)());\n            // Reset the selection details\n            selectionDetails = targets.get().bind(getExtractedDetails);\n            // Trigger change handlers\n            each$1(changeHandlers.get(), call);\n        };\n        const setupHandler = (handler) => {\n            // Execute the handler to set the initial state\n            handler();\n            // Register the handler so we can update the state when resetting targets\n            changeHandlers.set(changeHandlers.get().concat([handler]));\n            return () => {\n                changeHandlers.set(filter$1(changeHandlers.get(), (h) => h !== handler));\n            };\n        };\n        const onSetup = (api, isDisabled) => setupHandler(() => targets.get().fold(() => {\n            api.setEnabled(false);\n        }, (targets) => {\n            api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n        }));\n        const onSetupWithToggle = (api, isDisabled, isActive) => setupHandler(() => targets.get().fold(() => {\n            api.setEnabled(false);\n            api.setActive(false);\n        }, (targets) => {\n            api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n            api.setActive(isActive(targets));\n        }));\n        const isDisabledFromLocked = (lockedDisable) => selectionDetails.exists((details) => details.locked[lockedDisable]);\n        const onSetupTable = (api) => onSetup(api, (_) => false);\n        const onSetupCellOrRow = (api) => onSetup(api, (targets) => isCaption(targets.element));\n        const onSetupColumn = (lockedDisable) => (api) => onSetup(api, (targets) => isCaption(targets.element) || isDisabledFromLocked(lockedDisable));\n        const onSetupPasteable = (getClipboardData) => (api) => onSetup(api, (targets) => isCaption(targets.element) || getClipboardData().isNone());\n        const onSetupPasteableColumn = (getClipboardData, lockedDisable) => (api) => onSetup(api, (targets) => isCaption(targets.element) || getClipboardData().isNone() || isDisabledFromLocked(lockedDisable));\n        const onSetupMergeable = (api) => onSetup(api, (_targets) => isDisabledForSelection('mergeable'));\n        const onSetupUnmergeable = (api) => onSetup(api, (_targets) => isDisabledForSelection('unmergeable'));\n        const onSetupTableWithCaption = (api) => {\n            return onSetupWithToggle(api, never, (targets) => {\n                const tableOpt = table(targets.element, getIsRoot(editor));\n                return tableOpt.exists((table) => child(table, 'caption'));\n            });\n        };\n        const onSetupTableHeaders = (command, headerType) => (api) => {\n            return onSetupWithToggle(api, (targets) => isCaption(targets.element), () => editor.queryCommandValue(command) === headerType);\n        };\n        const onSetupTableRowHeaders = onSetupTableHeaders('mceTableRowType', 'header');\n        const onSetupTableColumnHeaders = onSetupTableHeaders('mceTableColType', 'th');\n        editor.on('NodeChange ExecCommand TableSelectorChange', resetTargets);\n        return {\n            onSetupTable,\n            onSetupCellOrRow,\n            onSetupColumn,\n            onSetupPasteable,\n            onSetupPasteableColumn,\n            onSetupMergeable,\n            onSetupUnmergeable,\n            resetTargets,\n            onSetupTableWithCaption,\n            onSetupTableRowHeaders,\n            onSetupTableColumnHeaders,\n            targets: targets.get\n        };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.FakeClipboard');\n\n    /*\n     NOTE: This file is duplicated in the following locations:\n      - models/dom/table/api/Clipboard.ts\n     Make sure that if making changes to this file, the other files are updated as well\n     */\n    const tableTypeBase = 'x-tinymce/dom-table-';\n    const tableTypeRow = tableTypeBase + 'rows';\n    const tableTypeColumn = tableTypeBase + 'columns';\n    const getData = (type) => {\n        var _a;\n        const items = (_a = global.read()) !== null && _a !== void 0 ? _a : [];\n        return findMap(items, (item) => Optional.from(item.getType(type)));\n    };\n    const getRows = () => getData(tableTypeRow);\n    const getColumns = () => getData(tableTypeColumn);\n\n    const onSetupEditable$1 = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const addButtons = (editor, selectionTargets) => {\n        editor.ui.registry.addMenuButton('table', {\n            tooltip: 'Table',\n            icon: 'table',\n            onSetup: onSetupEditable$1(editor),\n            fetch: (callback) => callback('inserttable | cell row column | advtablesort | tableprops deletetable')\n        });\n        const cmd = (command) => () => editor.execCommand(command);\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        const addButtonIfRegistered = (name, spec) => {\n            if (editor.queryCommandSupported(spec.command)) {\n                editor.ui.registry.addButton(name, {\n                    ...spec,\n                    onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n                });\n            }\n        };\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        const addToggleButtonIfRegistered = (name, spec) => {\n            if (editor.queryCommandSupported(spec.command)) {\n                editor.ui.registry.addToggleButton(name, {\n                    ...spec,\n                    onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n                });\n            }\n        };\n        addButtonIfRegistered('tableprops', {\n            tooltip: 'Table properties',\n            command: 'mceTableProps',\n            icon: 'table',\n            onSetup: selectionTargets.onSetupTable\n        });\n        addButtonIfRegistered('tabledelete', {\n            tooltip: 'Delete table',\n            command: 'mceTableDelete',\n            icon: 'table-delete-table',\n            onSetup: selectionTargets.onSetupTable\n        });\n        addButtonIfRegistered('tablecellprops', {\n            tooltip: 'Cell properties',\n            command: 'mceTableCellProps',\n            icon: 'table-cell-properties',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tablemergecells', {\n            tooltip: 'Merge cells',\n            command: 'mceTableMergeCells',\n            icon: 'table-merge-cells',\n            onSetup: selectionTargets.onSetupMergeable\n        });\n        addButtonIfRegistered('tablesplitcells', {\n            tooltip: 'Split cell',\n            command: 'mceTableSplitCells',\n            icon: 'table-split-cells',\n            onSetup: selectionTargets.onSetupUnmergeable\n        });\n        addButtonIfRegistered('tableinsertrowbefore', {\n            tooltip: 'Insert row before',\n            command: 'mceTableInsertRowBefore',\n            icon: 'table-insert-row-above',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tableinsertrowafter', {\n            tooltip: 'Insert row after',\n            command: 'mceTableInsertRowAfter',\n            icon: 'table-insert-row-after',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tabledeleterow', {\n            tooltip: 'Delete row',\n            command: 'mceTableDeleteRow',\n            icon: 'table-delete-row',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tablerowprops', {\n            tooltip: 'Row properties',\n            command: 'mceTableRowProps',\n            icon: 'table-row-properties',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tableinsertcolbefore', {\n            tooltip: 'Insert column before',\n            command: 'mceTableInsertColBefore',\n            icon: 'table-insert-column-before',\n            onSetup: selectionTargets.onSetupColumn(\"onFirst\" /* LockedDisable.onFirst */)\n        });\n        addButtonIfRegistered('tableinsertcolafter', {\n            tooltip: 'Insert column after',\n            command: 'mceTableInsertColAfter',\n            icon: 'table-insert-column-after',\n            onSetup: selectionTargets.onSetupColumn(\"onLast\" /* LockedDisable.onLast */)\n        });\n        addButtonIfRegistered('tabledeletecol', {\n            tooltip: 'Delete column',\n            command: 'mceTableDeleteCol',\n            icon: 'table-delete-column',\n            onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n        });\n        addButtonIfRegistered('tablecutrow', {\n            tooltip: 'Cut row',\n            command: 'mceTableCutRow',\n            icon: 'cut-row',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tablecopyrow', {\n            tooltip: 'Copy row',\n            command: 'mceTableCopyRow',\n            icon: 'duplicate-row',\n            onSetup: selectionTargets.onSetupCellOrRow\n        });\n        addButtonIfRegistered('tablepasterowbefore', {\n            tooltip: 'Paste row before',\n            command: 'mceTablePasteRowBefore',\n            icon: 'paste-row-before',\n            onSetup: selectionTargets.onSetupPasteable(getRows)\n        });\n        addButtonIfRegistered('tablepasterowafter', {\n            tooltip: 'Paste row after',\n            command: 'mceTablePasteRowAfter',\n            icon: 'paste-row-after',\n            onSetup: selectionTargets.onSetupPasteable(getRows)\n        });\n        addButtonIfRegistered('tablecutcol', {\n            tooltip: 'Cut column',\n            command: 'mceTableCutCol',\n            icon: 'cut-column',\n            onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n        });\n        addButtonIfRegistered('tablecopycol', {\n            tooltip: 'Copy column',\n            command: 'mceTableCopyCol',\n            icon: 'duplicate-column',\n            onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n        });\n        addButtonIfRegistered('tablepastecolbefore', {\n            tooltip: 'Paste column before',\n            command: 'mceTablePasteColBefore',\n            icon: 'paste-column-before',\n            onSetup: selectionTargets.onSetupPasteableColumn(getColumns, \"onFirst\" /* LockedDisable.onFirst */)\n        });\n        addButtonIfRegistered('tablepastecolafter', {\n            tooltip: 'Paste column after',\n            command: 'mceTablePasteColAfter',\n            icon: 'paste-column-after',\n            onSetup: selectionTargets.onSetupPasteableColumn(getColumns, \"onLast\" /* LockedDisable.onLast */)\n        });\n        addButtonIfRegistered('tableinsertdialog', {\n            tooltip: 'Insert table',\n            command: 'mceInsertTableDialog',\n            icon: 'table',\n            onSetup: onSetupEditable$1(editor)\n        });\n        const tableClassList = filterNoneItem(getTableClassList(editor));\n        if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n            editor.ui.registry.addMenuButton('tableclass', {\n                icon: 'table-classes',\n                tooltip: 'Table styles',\n                fetch: generateMenuItemsCallback(editor, tableClassList, 'tableclass', (value) => editor.execCommand('mceTableToggleClass', false, value)),\n                onSetup: selectionTargets.onSetupTable\n            });\n        }\n        const tableCellClassList = filterNoneItem(getCellClassList(editor));\n        if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n            editor.ui.registry.addMenuButton('tablecellclass', {\n                icon: 'table-cell-classes',\n                tooltip: 'Cell styles',\n                fetch: generateMenuItemsCallback(editor, tableCellClassList, 'tablecellclass', (value) => editor.execCommand('mceTableCellToggleClass', false, value)),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n        }\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n            editor.ui.registry.addMenuButton('tablecellvalign', {\n                icon: 'vertical-align',\n                tooltip: 'Vertical align',\n                fetch: generateMenuItemsCallback(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addMenuButton('tablecellborderwidth', {\n                icon: 'border-width',\n                tooltip: 'Border width',\n                fetch: generateMenuItemsCallback(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addMenuButton('tablecellborderstyle', {\n                icon: 'border-style',\n                tooltip: 'Border style',\n                fetch: generateMenuItemsCallback(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addMenuButton('tablecellbackgroundcolor', {\n                icon: 'cell-background-color',\n                tooltip: 'Background color',\n                fetch: (callback) => callback(buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addMenuButton('tablecellbordercolor', {\n                icon: 'cell-border-color',\n                tooltip: 'Border color',\n                fetch: (callback) => callback(buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n        }\n        addToggleButtonIfRegistered('tablecaption', {\n            tooltip: 'Table caption',\n            icon: 'table-caption',\n            command: 'mceTableToggleCaption',\n            onSetup: selectionTargets.onSetupTableWithCaption\n        });\n        addToggleButtonIfRegistered('tablerowheader', {\n            tooltip: 'Row header',\n            icon: 'table-top-header',\n            command: 'mceTableRowType',\n            onAction: changeRowHeader(editor),\n            onSetup: selectionTargets.onSetupTableRowHeaders\n        });\n        addToggleButtonIfRegistered('tablecolheader', {\n            tooltip: 'Column header',\n            icon: 'table-left-header',\n            command: 'mceTableColType',\n            onAction: changeColumnHeader(editor),\n            onSetup: selectionTargets.onSetupTableColumnHeaders\n        });\n    };\n    const addToolbars = (editor) => {\n        const isEditableTable = (table) => editor.dom.is(table, 'table') && editor.getBody().contains(table) && editor.dom.isEditable(table.parentNode);\n        const toolbar = getToolbar(editor);\n        if (toolbar.length > 0) {\n            editor.ui.registry.addContextToolbar('table', {\n                predicate: isEditableTable,\n                items: toolbar,\n                scope: 'node',\n                position: 'node'\n            });\n        }\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const addMenuItems = (editor, selectionTargets) => {\n        const cmd = (command) => () => editor.execCommand(command);\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        const addMenuIfRegistered = (name, spec) => {\n            if (editor.queryCommandSupported(spec.command)) {\n                editor.ui.registry.addMenuItem(name, {\n                    ...spec,\n                    onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n                });\n                return true;\n            }\n            else {\n                return false;\n            }\n        };\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        const addToggleMenuIfRegistered = (name, spec) => {\n            if (editor.queryCommandSupported(spec.command)) {\n                editor.ui.registry.addToggleMenuItem(name, {\n                    ...spec,\n                    onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n                });\n            }\n        };\n        const insertTableAction = (data) => {\n            editor.execCommand('mceInsertTable', false, {\n                rows: data.numRows,\n                columns: data.numColumns\n            });\n        };\n        const hasRowMenuItems = [\n            addMenuIfRegistered('tableinsertrowbefore', {\n                text: 'Insert row before',\n                icon: 'table-insert-row-above',\n                command: 'mceTableInsertRowBefore',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tableinsertrowafter', {\n                text: 'Insert row after',\n                icon: 'table-insert-row-after',\n                command: 'mceTableInsertRowAfter',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tabledeleterow', {\n                text: 'Delete row',\n                icon: 'table-delete-row',\n                command: 'mceTableDeleteRow',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tablerowprops', {\n                text: 'Row properties',\n                icon: 'table-row-properties',\n                command: 'mceTableRowProps',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tablecutrow', {\n                text: 'Cut row',\n                icon: 'cut-row',\n                command: 'mceTableCutRow',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tablecopyrow', {\n                text: 'Copy row',\n                icon: 'duplicate-row',\n                command: 'mceTableCopyRow',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tablepasterowbefore', {\n                text: 'Paste row before',\n                icon: 'paste-row-before',\n                command: 'mceTablePasteRowBefore',\n                onSetup: selectionTargets.onSetupPasteable(getRows)\n            }),\n            addMenuIfRegistered('tablepasterowafter', {\n                text: 'Paste row after',\n                icon: 'paste-row-after',\n                command: 'mceTablePasteRowAfter',\n                onSetup: selectionTargets.onSetupPasteable(getRows)\n            }),\n        ];\n        const hasColumnMenuItems = [\n            addMenuIfRegistered('tableinsertcolumnbefore', {\n                text: 'Insert column before',\n                icon: 'table-insert-column-before',\n                command: 'mceTableInsertColBefore',\n                onSetup: selectionTargets.onSetupColumn(\"onFirst\" /* LockedDisable.onFirst */)\n            }),\n            addMenuIfRegistered('tableinsertcolumnafter', {\n                text: 'Insert column after',\n                icon: 'table-insert-column-after',\n                command: 'mceTableInsertColAfter',\n                onSetup: selectionTargets.onSetupColumn(\"onLast\" /* LockedDisable.onLast */)\n            }),\n            addMenuIfRegistered('tabledeletecolumn', {\n                text: 'Delete column',\n                icon: 'table-delete-column',\n                command: 'mceTableDeleteCol',\n                onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n            }),\n            addMenuIfRegistered('tablecutcolumn', {\n                text: 'Cut column',\n                icon: 'cut-column',\n                command: 'mceTableCutCol',\n                onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n            }),\n            addMenuIfRegistered('tablecopycolumn', {\n                text: 'Copy column',\n                icon: 'duplicate-column',\n                command: 'mceTableCopyCol',\n                onSetup: selectionTargets.onSetupColumn(\"onAny\" /* LockedDisable.onAny */)\n            }),\n            addMenuIfRegistered('tablepastecolumnbefore', {\n                text: 'Paste column before',\n                icon: 'paste-column-before',\n                command: 'mceTablePasteColBefore',\n                onSetup: selectionTargets.onSetupPasteableColumn(getColumns, \"onFirst\" /* LockedDisable.onFirst */)\n            }),\n            addMenuIfRegistered('tablepastecolumnafter', {\n                text: 'Paste column after',\n                icon: 'paste-column-after',\n                command: 'mceTablePasteColAfter',\n                onSetup: selectionTargets.onSetupPasteableColumn(getColumns, \"onLast\" /* LockedDisable.onLast */)\n            }),\n        ];\n        const hasCellMenuItems = [\n            addMenuIfRegistered('tablecellprops', {\n                text: 'Cell properties',\n                icon: 'table-cell-properties',\n                command: 'mceTableCellProps',\n                onSetup: selectionTargets.onSetupCellOrRow\n            }),\n            addMenuIfRegistered('tablemergecells', {\n                text: 'Merge cells',\n                icon: 'table-merge-cells',\n                command: 'mceTableMergeCells',\n                onSetup: selectionTargets.onSetupMergeable\n            }),\n            addMenuIfRegistered('tablesplitcells', {\n                text: 'Split cell',\n                icon: 'table-split-cells',\n                command: 'mceTableSplitCells',\n                onSetup: selectionTargets.onSetupUnmergeable\n            }),\n        ];\n        if (!hasTableGrid(editor)) {\n            editor.ui.registry.addMenuItem('inserttable', {\n                text: 'Table',\n                icon: 'table',\n                onAction: cmd('mceInsertTableDialog'),\n                onSetup: onSetupEditable(editor)\n            });\n        }\n        else {\n            editor.ui.registry.addNestedMenuItem('inserttable', {\n                text: 'Table',\n                icon: 'table',\n                getSubmenuItems: () => [{ type: 'fancymenuitem', fancytype: 'inserttable', onAction: insertTableAction }],\n                onSetup: onSetupEditable(editor)\n            });\n        }\n        // TINY-3636: We want a way to use the dialog even when tablegrid true.\n        // If tablegrid false then inserttable and inserttabledialog are the same,\n        // but that's preferrable to breaking things at this point.\n        editor.ui.registry.addMenuItem('inserttabledialog', {\n            text: 'Insert table',\n            icon: 'table',\n            onAction: cmd('mceInsertTableDialog'),\n            onSetup: onSetupEditable(editor)\n        });\n        addMenuIfRegistered('tableprops', {\n            text: 'Table properties',\n            onSetup: selectionTargets.onSetupTable,\n            command: 'mceTableProps'\n        });\n        addMenuIfRegistered('deletetable', {\n            text: 'Delete table',\n            icon: 'table-delete-table',\n            onSetup: selectionTargets.onSetupTable,\n            command: 'mceTableDelete'\n        });\n        // if any of the row menu items returned true\n        if (contains(hasRowMenuItems, true)) {\n            editor.ui.registry.addNestedMenuItem('row', {\n                type: 'nestedmenuitem',\n                text: 'Row',\n                getSubmenuItems: constant('tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter')\n            });\n        }\n        if (contains(hasColumnMenuItems, true)) {\n            editor.ui.registry.addNestedMenuItem('column', {\n                type: 'nestedmenuitem',\n                text: 'Column',\n                getSubmenuItems: constant('tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter')\n            });\n        }\n        if (contains(hasCellMenuItems, true)) {\n            editor.ui.registry.addNestedMenuItem('cell', {\n                type: 'nestedmenuitem',\n                text: 'Cell',\n                getSubmenuItems: constant('tablecellprops tablemergecells tablesplitcells')\n            });\n        }\n        editor.ui.registry.addContextMenu('table', {\n            update: () => {\n                // context menu fires before node change, so check the selection here first\n                selectionTargets.resetTargets();\n                // ignoring element since it's monitored elsewhere\n                return selectionTargets.targets().fold(constant(''), (targets) => {\n                    // If clicking in a caption, then we shouldn't show the cell/row/column options\n                    if (name(targets.element) === 'caption') {\n                        return 'tableprops deletetable';\n                    }\n                    else {\n                        return 'cell row column | advtablesort | tableprops deletetable';\n                    }\n                });\n            }\n        });\n        const tableClassList = filterNoneItem(getTableClassList(editor));\n        if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n            editor.ui.registry.addNestedMenuItem('tableclass', {\n                icon: 'table-classes',\n                text: 'Table styles',\n                getSubmenuItems: () => buildMenuItems(editor, tableClassList, 'tableclass', (value) => editor.execCommand('mceTableToggleClass', false, value)),\n                onSetup: selectionTargets.onSetupTable\n            });\n        }\n        const tableCellClassList = filterNoneItem(getCellClassList(editor));\n        if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n            editor.ui.registry.addNestedMenuItem('tablecellclass', {\n                icon: 'table-cell-classes',\n                text: 'Cell styles',\n                getSubmenuItems: () => buildMenuItems(editor, tableCellClassList, 'tablecellclass', (value) => editor.execCommand('mceTableCellToggleClass', false, value)),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n        }\n        // TODO: TINY-8172 Unwind this when an alternative solution is found\n        if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n            editor.ui.registry.addNestedMenuItem('tablecellvalign', {\n                icon: 'vertical-align',\n                text: 'Vertical align',\n                getSubmenuItems: () => buildMenuItems(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addNestedMenuItem('tablecellborderwidth', {\n                icon: 'border-width',\n                text: 'Border width',\n                getSubmenuItems: () => buildMenuItems(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addNestedMenuItem('tablecellborderstyle', {\n                icon: 'border-style',\n                text: 'Border style',\n                getSubmenuItems: () => buildMenuItems(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addNestedMenuItem('tablecellbackgroundcolor', {\n                icon: 'cell-background-color',\n                text: 'Background color',\n                getSubmenuItems: () => buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color'),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n            editor.ui.registry.addNestedMenuItem('tablecellbordercolor', {\n                icon: 'cell-border-color',\n                text: 'Border color',\n                getSubmenuItems: () => buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color'),\n                onSetup: selectionTargets.onSetupCellOrRow\n            });\n        }\n        addToggleMenuIfRegistered('tablecaption', {\n            icon: 'table-caption',\n            text: 'Table caption',\n            command: 'mceTableToggleCaption',\n            onSetup: selectionTargets.onSetupTableWithCaption\n        });\n        addToggleMenuIfRegistered('tablerowheader', {\n            text: 'Row header',\n            icon: 'table-top-header',\n            command: 'mceTableRowType',\n            onAction: changeRowHeader(editor),\n            onSetup: selectionTargets.onSetupTableRowHeaders\n        });\n        addToggleMenuIfRegistered('tablecolheader', {\n            text: 'Column header',\n            icon: 'table-left-header',\n            command: 'mceTableColType',\n            onAction: changeColumnHeader(editor),\n            onSetup: selectionTargets.onSetupTableRowHeaders\n        });\n    };\n\n    const Plugin = (editor) => {\n        const selectionTargets = getSelectionTargets(editor);\n        register(editor);\n        registerCommands(editor);\n        addMenuItems(editor, selectionTargets);\n        addButtons(editor, selectionTargets);\n        addToolbars(editor);\n    };\n    var Plugin$1 = () => {\n        global$3.add('table', Plugin);\n    };\n\n    Plugin$1();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,WAAW,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACxD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,OAAO,CAAC,IAAM,CAAC,IAAM,MAAM;IACjC,MAAM,WAAW,SAAS;IAC1B,MAAM,UAAU,SAAS;IACzB,MAAM,YAAY,aAAa;IAC/B,MAAM,cAAc,KAAK;IACzB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAChC,MAAM,WAAW,aAAa;IAE9B,MAAM,OAAO,KAAQ;IACrB,gGAAgG,GAChG,MAAM,WAAW,CAAC,KAAK,MAAQ,CAAC,IAAM,IAAI,IAAI;IAC9C,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,WAAW,CAAC;QACd,OAAO;IACX;IACA,MAAM,eAAe,CAAC,GAAG;QACrB,OAAO,MAAM;IACjB;IACA,+DAA+D;IAC/D,SAAS,MAAM,EAAE;QAAE,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,cAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YAAG,YAAH,OAAA,KAAA,SAAA,CAAA,KAAc;;QAC7B,OAAO;6CAAI;gBAAA;;YACP,MAAM,MAAM,YAAY,MAAM,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,OAAO,CAAC;QACV;IACJ;IACA,MAAM,QAAQ,SAAS;IACvB,MAAM,SAAS,SAAS;IAExB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,gBAAgB,MAAM,SAAS,CAAC,OAAO;IAC7C,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,iBAAiB,GACjB,MAAM,aAAa,CAAC,IAAI,IAAM,cAAc,IAAI,CAAC,IAAI;IACrD,MAAM,WAAW,CAAC,IAAI,IAAM,WAAW,IAAI,KAAK,CAAC;IACjD,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,KAAK;QAChB,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,EAAE,IAAI,CAAC,EAAE;QACb;QACA,OAAO;IACX;IACA,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,QAAQ,CAAC,IAAI;QACf,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACrC,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,YAAY,CAAC,IAAI;QACnB,MAAM,OAAO,EAAE;QACf,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,MAAM,MAAM,KAAK,GAAG,KAAK,OAAO;YAChC,IAAI,IAAI,CAAC;QACb;QACA,OAAO;YAAE;YAAM;QAAK;IACxB;IACA,MAAM,WAAW,CAAC,IAAI;QAClB,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,EAAE,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,IAAI,GAAG;QAClB,MAAM,IAAI,CAAC,GAAG;YACV,MAAM,EAAE,KAAK,GAAG;QACpB;QACA,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,IAAI,GAAG;QAClB,OAAO,IAAI,CAAC,GAAG;YACX,MAAM,EAAE,KAAK,GAAG;QACpB;QACA,OAAO;IACX;IACA,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,OAAO,CAAC,IAAI;QACd,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,YAAY,CAAC,IAAI;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,YAAY,CAAC;QACf,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,IAAI,IAAM,UAAU,IAAI,IAAI;IAC1C,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,OAAO,MAAM;gBACrB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAC,IAAI;QACrB,MAAM,IAAI,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG;QACxB;QACA,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,IAAI,IAAM,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI;IACvF,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI;IAC/B,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC3C,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAC9D,MAAM,UAAU,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,MAAM,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,SAAS,CAAC,IAAM,CAAC,GAAG;YACtB,CAAC,CAAC,EAAE,GAAG;QACX;IACA,MAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ;QACvC,KAAK,KAAK,CAAC,GAAG;YACV,CAAC,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,GAAG;QACvC;IACJ;IACA,MAAM,SAAS,CAAC,KAAK;QACjB,MAAM,IAAI,CAAC;QACX,eAAe,KAAK,MAAM,OAAO,IAAI;QACrC,OAAO;IACX;IACA,MAAM,aAAa,CAAC,KAAK;QACrB,MAAM,IAAI,EAAE;QACZ,KAAK,KAAK,CAAC,OAAO;YACd,EAAE,IAAI,CAAC,EAAE,OAAO;QACpB;QACA,OAAO;IACX;IACA,MAAM,SAAS,CAAC;QACZ,OAAO,WAAW,KAAK;IAC3B;IACA,MAAM,OAAO,CAAC;QACV,OAAO,KAAK,KAAK,MAAM;IAC3B;IACA,MAAM,QAAQ,CAAC,KAAK;QAChB,OAAO,IAAI,KAAK,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,SAAS,IAAI;IAClE;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IACnD,MAAM,oBAAoB,CAAC,KAAK,MAAQ,IAAI,KAAK,QAAQ,GAAG,CAAC,IAAI,KAAK,aAAa,GAAG,CAAC,IAAI,KAAK;IAChG,MAAM,YAAY,CAAC;QACf,IAAK,MAAM,KAAK,EAAG;YACf,IAAI,eAAe,IAAI,CAAC,GAAG,IAAI;gBAC3B,OAAO;YACX;QACJ;QACA,OAAO;IACX;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA;;KAEC,GACD,MAAM,OAAO,SAAC,KAAK;YAAK,8EAAa;eAAiB,IAAI,MAAM,CAAC,CAAC,OAAS,WAAW,MAAM;;IAC5F,MAAM,MAAM,CAAC;QACT,MAAM,IAAI,EAAE;QACZ,MAAM,OAAO,CAAC;YACV,EAAE,IAAI,CAAC;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IACA;;;;;IAKA,GACA,MAAM,QAAQ,CAAC,IAAI,IAAI,IAAM,GAAG,MAAM,MAAM,GAAG,MAAM,KAAK,SAAS,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS,IAAI;IACxH,MAAM,UAAU,CAAC,MAAQ,IAAI,IAAI,CAAC;IAClC,mHAAmH;IACnH,MAAM,SAAS,CAAC,GAAG,IAAM,IAAI,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;IAE7D,MAAM,YAAY,CAAC;QACf,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,SAAS,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC7B;QACA,MAAM,QAAQ,IAAM,QAAQ,GAAG,GAAG,MAAM;QACxC,MAAM,MAAM,IAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,CAAC;YACT;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC;QAC9B;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,aAAa,IAAM,UAAU,CAAC,IAAM,EAAE,MAAM;IAElD,MAAM,kBAAkB,CAAC,KAAK;QAC1B,OAAO,IAAI,SAAS,CAAC;IACzB;IAEA,MAAM,aAAa,CAAC,KAAK,QAAQ,QAAU,WAAW,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,QAAQ,OAAO,MAAM,MAAM;IACxI,MAAM,gBAAgB,CAAC,KAAK;QACxB,OAAO,WAAW,KAAK,UAAU,gBAAgB,KAAK,OAAO,MAAM,IAAI;IAC3E;IACA;;;;KAIC,GACD,MAAM,aAAa,CAAC,KAAK;QACrB,OAAO,WAAW,KAAK,QAAQ;IACnC;IACA,MAAM,QAAQ,CAAC,IAAM,CAAC,IAAM,EAAE,OAAO,CAAC,GAAG;IACzC,4CAA4C,GAC5C,MAAM,OAAO,MAAM;IACnB,MAAM,aAAa,CAAC,IAAM,EAAE,MAAM,GAAG;IACrC,MAAM,UAAU,CAAC,IAAM,CAAC,WAAW;IACnC,MAAM,QAAQ,SAAC;YAAO,yEAAQ;QAC1B,MAAM,MAAM,SAAS,OAAO;QAC5B,OAAO,MAAM,OAAO,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC;IACxD;IACA,MAAM,UAAU,CAAC;QACb,MAAM,MAAM,WAAW;QACvB,OAAO,MAAM,OAAO,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC;IACxD;IAEA,MAAM,SAAS,CAAC;QACZ,IAAI,SAAS;QACb,IAAI;QACJ,OAAO;6CAAI;gBAAA;;YACP,IAAI,CAAC,QAAQ;gBACT,SAAS;gBACT,IAAI,EAAE,KAAK,CAAC,MAAM;YACtB;YACA,OAAO;QACX;IACJ;IAEA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,IAAI,aAAa,CAAC;QAC9B,IAAI,SAAS,GAAG;QAChB,IAAI,CAAC,IAAI,aAAa,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,UAAU;YAChB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,UAAU,IAAI,UAAU,CAAC,EAAE;IACtC;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,aAAa,CAAC;QAC/B,OAAO,UAAU;IACrB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,cAAc,CAAC;QAChC,OAAO,UAAU;IACrB;IACA,MAAM,YAAY,CAAC;QACf,8DAA8D;QAC9D,IAAI,SAAS,QAAQ,SAAS,WAAW;YACrC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;YACH,KAAK;QACT;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,GAAG,IAAM,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,CAAC;IACzF,yCAAyC;IACzC,MAAM,eAAe;QACjB;QACA;QACA;QACA,SAAS;QACT;IACJ;IAEA,MAAM,UAAU;IAChB,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAC1B,MAAM,UAAU;IAChB,MAAM,OAAO;IAEb,MAAM,OAAO,CAAC,SAAS;QACnB,MAAM,MAAM,QAAQ,GAAG;QACvB,IAAI,IAAI,QAAQ,KAAK,SAAS;YAC1B,OAAO;QACX,OACK;YACD,MAAM,OAAO;YACb,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,OAAO,KAAK,OAAO,CAAC;YACxB,OACK,IAAI,KAAK,iBAAiB,KAAK,WAAW;gBAC3C,OAAO,KAAK,iBAAiB,CAAC;YAClC,OACK,IAAI,KAAK,qBAAqB,KAAK,WAAW;gBAC/C,OAAO,KAAK,qBAAqB,CAAC;YACtC,OACK,IAAI,KAAK,kBAAkB,KAAK,WAAW;gBAC5C,gEAAgE;gBAChE,OAAO,KAAK,kBAAkB,CAAC;YACnC,OACK;gBACD,MAAM,IAAI,MAAM;YACpB,EAAE,kDAAkD;QACxD;IACJ;IACA,MAAM,iBAAiB,CAAC,MACxB,kEAAkE;QAClE,gDAAgD;QAChD,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,qBACtE,kFAAkF;QAClF,IAAI,iBAAiB,KAAK;IAC9B,MAAM,QAAQ,CAAC,UAAU;QACrB,MAAM,OAAO,UAAU,YAAY,WAAW,MAAM,GAAG;QACvD,OAAO,eAAe,QAAQ,EAAE,GAAG,IAAI,KAAK,gBAAgB,CAAC,WAAW,aAAa,OAAO;IAChG;IACA,MAAM,MAAM,CAAC,UAAU;QACnB,MAAM,OAAO,UAAU,YAAY,WAAW,MAAM,GAAG;QACvD,OAAO,eAAe,QAAQ,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,aAAa,CAAC,WAAW,GAAG,CAAC,aAAa,OAAO;IACxH;IAEA,MAAM,KAAK,CAAC,IAAI,KAAO,GAAG,GAAG,KAAK,GAAG,GAAG;IACxC,MAAM,KAAK;IAEX,MAAM,OAAO,CAAC;QACV,MAAM,IAAI,QAAQ,GAAG,CAAC,QAAQ;QAC9B,OAAO,EAAE,WAAW;IACxB;IACA,MAAM,OAAO,CAAC,UAAY,QAAQ,GAAG,CAAC,QAAQ;IAC9C,MAAM,SAAS,CAAC,IAAM,CAAC,UAAY,KAAK,aAAa;IACrD,MAAM,YAAY,CAAC,UAAY,KAAK,aAAa,WAAW,KAAK,aAAa;IAC9E,MAAM,YAAY,OAAO;IACzB,MAAM,SAAS,OAAO;IACtB,MAAM,aAAa,OAAO;IAC1B,MAAM,qBAAqB,OAAO;IAClC,MAAM,QAAQ,CAAC,MAAQ,CAAC,IAAM,UAAU,MAAM,KAAK,OAAO;IAE1D;;;KAGC,GACD,MAAM,QAAQ,CAAC,UAAY,aAAa,OAAO,CAAC,QAAQ,GAAG,CAAC,aAAa;IACzE;;;KAGC,GACD,MAAM,kBAAkB,CAAC,MAAQ,WAAW,OAAO,MAAM,MAAM;IAC/D,MAAM,SAAS,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,OAAO;IAC1F,MAAM,UAAU,CAAC,SAAS;QACtB,MAAM,OAAO,WAAW,UAAU,SAAS;QAC3C,mEAAmE;QACnE,IAAI,MAAM,QAAQ,GAAG;QACrB,MAAM,MAAM,EAAE;QACd,MAAO,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,UAAW;YAC5D,MAAM,YAAY,IAAI,UAAU;YAChC,MAAM,IAAI,aAAa,OAAO,CAAC;YAC/B,IAAI,IAAI,CAAC;YACT,IAAI,KAAK,OAAO,MAAM;gBAClB;YACJ,OACK;gBACD,MAAM;YACV;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,aAAa,OAAO;IACpG,MAAM,cAAc,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,OAAO;IAChG,MAAM,aAAa,CAAC,UAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,aAAa,OAAO;IAChF,MAAM,UAAU,CAAC,SAAS;QACtB,MAAM,KAAK,QAAQ,GAAG,CAAC,UAAU;QACjC,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa,OAAO;IAC5D;IACA,MAAM,aAAa,CAAC,UAAY,QAAQ,SAAS;IAEjD;;;;;KAKC,GACD,MAAM,eAAe,CAAC,MAAQ,mBAAmB,QAAQ,cAAc,IAAI,GAAG,CAAC,IAAI;IACnF,MAAM,cAAc,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,WAAW;IACjE,mDAAmD,GACnD,MAAM,gBAAgB,CAAC;QACnB,MAAM,IAAI,YAAY;QACtB,OAAO,aAAa,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;IAC7D;IACA;;;;KAIC,GACD,MAAM,gBAAgB,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,IAAI;IAE5D,MAAM,SAAS,CAAC,QAAQ;QACpB,MAAM,WAAW,OAAO;QACxB,SAAS,IAAI,CAAC,CAAC;YACX,EAAE,GAAG,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,OAAO,GAAG;QAC9C;IACJ;IACA,MAAM,UAAU,CAAC,QAAQ;QACrB,MAAM,UAAU,YAAY;QAC5B,QAAQ,IAAI,CAAC;YACT,MAAM,WAAW,OAAO;YACxB,SAAS,IAAI,CAAC,CAAC;gBACX,SAAS,GAAG;YAChB;QACJ,GAAG,CAAC;YACA,OAAO,GAAG;QACd;IACJ;IACA,MAAM,UAAU,CAAC,QAAQ;QACrB,MAAM,eAAe,WAAW;QAChC,aAAa,IAAI,CAAC;YACd,SAAS,QAAQ;QACrB,GAAG,CAAC;YACA,OAAO,GAAG,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,EAAE,GAAG;QAC9C;IACJ;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,OAAO,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG;IACtC;IACA,MAAM,OAAO,CAAC,SAAS;QACnB,OAAO,SAAS;QAChB,SAAS,SAAS;IACtB;IAEA,MAAM,QAAQ,CAAC,QAAQ;QACnB,OAAO,UAAU,CAAC,GAAG;YACjB,MAAM,IAAI,MAAM,IAAI,SAAS,QAAQ,CAAC,IAAI,EAAE;YAC5C,QAAQ,GAAG;QACf;IACJ;IACA,MAAM,SAAS,CAAC,QAAQ;QACpB,OAAO,UAAU,CAAC;YACd,SAAS,QAAQ;QACrB;IACJ;IAEA,MAAM,SAAS,CAAC,KAAK,KAAK;QACtB;;;;SAIC,GACD,IAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;YACxD,IAAI,YAAY,CAAC,KAAK,QAAQ;QAClC,OACK;YACD,sCAAsC;YACtC,QAAQ,KAAK,CAAC,uCAAuC,KAAK,aAAa,OAAO,eAAe;YAC7F,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,QAAQ,CAAC,SAAS,KAAK;QACzB,OAAO,QAAQ,GAAG,EAAE,KAAK;IAC7B;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,MAAM,MAAM,QAAQ,GAAG;QACvB,KAAK,OAAO,CAAC,GAAG;YACZ,OAAO,KAAK,GAAG;QACnB;IACJ;IACA,MAAM,QAAQ,CAAC,SAAS;QACpB,MAAM,IAAI,QAAQ,GAAG,CAAC,YAAY,CAAC;QACnC,0EAA0E;QAC1E,OAAO,MAAM,OAAO,YAAY;IACpC;IACA,MAAM,SAAS,CAAC,SAAS,MAAQ,SAAS,IAAI,CAAC,MAAM,SAAS;IAC9D,MAAM,WAAW,CAAC,SAAS;QACvB,QAAQ,GAAG,CAAC,eAAe,CAAC;IAChC;IACA,MAAM,QAAQ,CAAC,UAAY,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,CAAC,KAAK;YAC3D,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK;YAC3B,OAAO;QACX,GAAG,CAAC;IAEJ,MAAM,WAAW,CAAC;QACd,MAAM,MAAM,QAAQ,GAAG;QACvB,IAAI,IAAI,UAAU,KAAK,MAAM;YACzB,IAAI,UAAU,CAAC,WAAW,CAAC;QAC/B;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,WAAW,WAAW;QAC5B,IAAI,SAAS,MAAM,GAAG,GAAG;YACrB,MAAM,SAAS;QACnB;QACA,SAAS;IACb;IAEA,MAAM,UAAU,CAAC,QAAU,IAAI,OAAO,aAAa,OAAO;IAE1D,6DAA6D;IAC7D,4FAA4F;IAC5F,MAAM,cAAc,CAAC,MACrB,6DAA6D;QAC7D,IAAI,KAAK,KAAK,aAAa,WAAW,IAAI,KAAK,CAAC,gBAAgB;IAEhE,uDAAuD;IACvD,0CAA0C;IAC1C,MAAM,SAAS,CAAC;QACZ,0FAA0F;QAC1F,2FAA2F;QAC3F,MAAM,MAAM,OAAO,WAAW,QAAQ,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG;QAClE,8DAA8D;QAC9D,wFAAwF;QACxF,IAAI,QAAQ,aAAa,QAAQ,QAAQ,IAAI,aAAa,KAAK,MAAM;YACjE,OAAO;QACX;QACA,MAAM,MAAM,IAAI,aAAa;QAC7B,OAAO,cAAc,aAAa,OAAO,CAAC,MAAM,IAAI,CAAC,IAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,QAAQ;IACxG;IAEA,MAAM,cAAc,CAAC,KAAK,UAAU;QAChC,oCAAoC;QACpC,qGAAqG;QACrG,4CAA4C;QAC5C,IAAI,CAAC,SAAS,QAAQ;YAClB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,sCAAsC,UAAU,aAAa,OAAO,eAAe;YACjG,MAAM,IAAI,MAAM,iCAAiC;QACrD;QACA,sGAAsG;QACtG,IAAI,YAAY,MAAM;YAClB,IAAI,KAAK,CAAC,WAAW,CAAC,UAAU;QACpC;IACJ;IACA,MAAM,iBAAiB,CAAC,KAAK;QACzB;;;;;SAKC,GACD,IAAI,YAAY,MAAM;YAClB,IAAI,KAAK,CAAC,cAAc,CAAC;QAC7B;IACJ;IACA,MAAM,QAAQ,CAAC,SAAS,UAAU;QAC9B,MAAM,MAAM,QAAQ,GAAG;QACvB,YAAY,KAAK,UAAU;IAC/B;IACA;;;;;KAKC,GACD,MAAM,QAAQ,CAAC,SAAS;QACpB,MAAM,MAAM,QAAQ,GAAG;QACvB;;;;;;;;SAQC,GACD,MAAM,SAAS,OAAO,gBAAgB,CAAC;QACvC,MAAM,IAAI,OAAO,gBAAgB,CAAC;QAClC,uHAAuH;QACvH,8BAA8B;QAC9B,OAAO,AAAC,MAAM,MAAM,CAAC,OAAO,WAAY,kBAAkB,KAAK,YAAY;IAC/E;IACA,sGAAsG;IACtG,oGAAoG;IACpG,MAAM,oBAAoB,CAAC,KAAK,WAAa,YAAY,OAAO,IAAI,KAAK,CAAC,gBAAgB,CAAC,YAAY;IACvG;;;;;KAKC,GACD,MAAM,WAAW,CAAC,SAAS;QACvB,MAAM,MAAM,QAAQ,GAAG;QACvB,MAAM,MAAM,kBAAkB,KAAK;QACnC,OAAO,SAAS,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,GAAG;IACvD;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,MAAM,MAAM,QAAQ,GAAG;QACvB,eAAe,KAAK;QACpB,IAAI,KAAK,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO,KAAK;YAC9C,0DAA0D;YAC1D,SAAS,SAAS;QACtB;IACJ;IAEA,MAAM,YAAY,CAAC,MAAM;QACrB,MAAM,MAAM,CAAC,SAAS;YAClB,IAAI,CAAC,SAAS,MAAM,CAAC,EAAE,KAAK,CAAC,aAAa;gBACtC,MAAM,IAAI,MAAM,OAAO,0DAA0D;YACrF;YACA,MAAM,MAAM,QAAQ,GAAG;YACvB,IAAI,YAAY,MAAM;gBAClB,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;YAC1B;QACJ;QACA;;;;SAIC,GACD;;;;;;;;;;;;;;;;;;;;;;;MAuBF,GACE,MAAM,MAAM,CAAC;YACT,MAAM,IAAI,UAAU;YACpB,oEAAoE;YACpE,IAAI,KAAK,KAAK,MAAM,MAAM;gBACtB,MAAM,MAAM,MAAM,SAAS;gBAC3B,4CAA4C;gBAC5C,OAAO,WAAW,QAAQ;YAC9B;YACA,OAAO;QACX;QACA,+EAA+E;QAC/E,sGAAsG;QACtG,MAAM,WAAW;QACjB,MAAM,YAAY,CAAC,SAAS,aAAe,MAAM,YAAY,CAAC,KAAK;gBAC/D,MAAM,MAAM,MAAM,SAAS;gBAC3B,MAAM,QAAQ,QAAQ,YAAY,IAAI,SAAS,KAAK;gBACpD,OAAO,MAAM,SAAS,MAAM,MAAM;YACtC,GAAG;QACH,MAAM,MAAM,CAAC,SAAS,OAAO;YACzB,MAAM,uBAAuB,UAAU,SAAS;YAChD,0HAA0H;YAC1H,MAAM,cAAc,QAAQ,uBAAuB,QAAQ,uBAAuB;YAClF,OAAO;QACX;QACA,OAAO;YACH;YACA;YACA;YACA;YACA;QACJ;IACJ;IAEA,MAAM,WAAW,CAAC,IAAI,WAAa,QAAQ,IAAI,KAAK,CAAC;IACrD,MAAM,UAAU,CAAC,SAAS,MAAM,WAAa,SAAS,MAAM,SAAS,OAAO;IAC5E,MAAM,qBAAqB,CAAC,SAAS,MAAM,OAAO;QAC9C,MAAM,eAAe,QAAQ,SAAS,AAAC,WAAgB,OAAN,QAAS;QAC1D,MAAM,eAAe,QAAQ,SAAS,AAAC,WAAgB,OAAN,QAAS;QAC1D,MAAM,cAAc,QAAQ,SAAS,AAAC,UAAe,OAAN,OAAM,WAAS;QAC9D,MAAM,cAAc,QAAQ,SAAS,AAAC,UAAe,OAAN,OAAM,WAAS;QAC9D,OAAO,OAAO,eAAe,eAAe,cAAc;IAC9D;IACA,MAAM,qBAAqB,CAAC,SAAS;QACjC,MAAM,MAAM,QAAQ,GAAG;QACvB,MAAM,QAAQ,IAAI,qBAAqB,GAAG,KAAK,IAAI,IAAI,WAAW;QAClE,OAAO,cAAc,eAAe,QAAQ,mBAAmB,SAAS,OAAO,QAAQ;IAC3F;IACA,MAAM,gBAAgB,CAAC,UAAY,mBAAmB,SAAS;IAE/D,UAAU,SAAS,CAAC,UACpB,yEAAyE;QACzE,QAAQ,GAAG,CAAC,WAAW;IACvB,UAAU,SAAS,CAAC;QAChB,MAAM,MAAM,QAAQ,GAAG;QACvB,OAAO,OAAO,WAAW,IAAI,qBAAqB,GAAG,KAAK,GAAG,IAAI,WAAW;IAChF;IACA,MAAM,WAAW;IAEjB,MAAM,YAAY,CAAC,IAAI;QACnB,MAAM,MAAM,CAAC;YACT,IAAI,CAAC,GAAG,UAAU;gBACd,MAAM,IAAI,MAAM,kBAAkB,OAAO,iBAAiB,OAAO;YACrE;YACA,OAAO,UAAU,SAAS,KAAK,CAAC;QACpC;QACA,MAAM,YAAY,CAAC,UAAY,GAAG,WAAW,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAAI,SAAS,IAAI;QACjG,MAAM,MAAM,CAAC,SAAS;YAClB,IAAI,CAAC,GAAG,UAAU;gBACd,MAAM,IAAI,MAAM,sBAAsB,OAAO,iBAAiB,OAAO;YACzE;YACA,QAAQ,GAAG,CAAC,SAAS,GAAG;QAC5B;QACA,OAAO;YACH;YACA;YACA;QACJ;IACJ;IAEA,MAAM,MAAM,UAAU,QAAQ;IAC9B,MAAM,MAAM,CAAC,UAAY,IAAI,GAAG,CAAC;IACjC,MAAM,MAAM,CAAC,SAAS,QAAU,IAAI,GAAG,CAAC,SAAS;IAEjD,IAAI,oBAAoB,CAAC,IAAI,UAAU,OAAO,GAAG;QAC7C,IAAI,GAAG,OAAO,IAAI;YACd,OAAO,SAAS,IAAI,CAAC;QACzB,OACK,IAAI,WAAW,WAAW,OAAO,QAAQ;YAC1C,OAAO,SAAS,IAAI;QACxB,OACK;YACD,OAAO,SAAS,OAAO,GAAG;QAC9B;IACJ;IAEA,MAAM,aAAa,CAAC,OAAO,WAAW;QAClC,IAAI,UAAU,MAAM,GAAG;QACvB,MAAM,OAAO,WAAW,UAAU,SAAS;QAC3C,MAAO,QAAQ,UAAU,CAAE;YACvB,UAAU,QAAQ,UAAU;YAC5B,MAAM,KAAK,aAAa,OAAO,CAAC;YAChC,IAAI,UAAU,KAAK;gBACf,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,KAAK,KAAK;gBACf;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,YAAY,CAAC,OAAO,WAAW;QACjC,8EAA8E;QAC9E,MAAM,KAAK,CAAC,GAAG,OAAS,KAAK;QAC7B,OAAO,kBAAkB,IAAI,YAAY,OAAO,WAAW;IAC/D;IACA,MAAM,UAAU,CAAC,OAAO;QACpB,MAAM,OAAO,CAAC,OAAS,UAAU,aAAa,OAAO,CAAC;QACtD,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,UAAU,EAAE;QAC1C,OAAO,OAAO,GAAG,CAAC,aAAa,OAAO;IAC1C;IAEA,MAAM,WAAW,CAAC,OAAO,UAAU,SAAW,WAAW,OAAO,CAAC,IAAM,KAAK,GAAG,WAAW;IAC1F,MAAM,UAAU,CAAC,OAAO,WAAa,QAAQ,OAAO,CAAC,IAAM,KAAK,GAAG;IACnE,MAAM,aAAa,CAAC,OAAO,WAAa,IAAI,UAAU;IACtD,yGAAyG;IACzG,MAAM,YAAY,CAAC,OAAO,UAAU;QAChC,MAAM,KAAK,CAAC,SAAS,WAAa,KAAK,SAAS;QAChD,OAAO,kBAAkB,IAAI,UAAU,OAAO,UAAU;IAC5D;IAEA,MAAM,UAAU,CAAC,SAAW,UAAU,QAAQ;IAC9C,MAAM,aAAa,SAAC;YAAS,kFAAiB;QAC1C,IAAI,OAAO,UAAU;YACjB,OAAO,QAAQ,GAAG,CAAC,iBAAiB;QACxC,OACK;YACD,sEAAsE;YACtE,OAAO,QAAQ,SAAS,IAAI,CAAC,SAAS,iBAAiB,CAAC,WAAa,OAAO,cAAc;QAC9F;IACJ;IACA,MAAM,SAAS,CAAC,UAAY,QAAQ,GAAG,CAAC,eAAe;IAEvD,MAAM,aAAa,CAAC,OAAO,YAAc,SAAS,WAAW,QAAQ;IACrE,MAAM,gBAAgB,CAAC,OAAO;QAC1B,IAAI,SAAS,EAAE;QACf,oCAAoC;QACpC,OAAO,WAAW,QAAQ,CAAC;YACvB,IAAI,UAAU,IAAI;gBACd,SAAS,OAAO,MAAM,CAAC;oBAAC;iBAAE;YAC9B;YACA,SAAS,OAAO,MAAM,CAAC,cAAc,GAAG;QAC5C;QACA,OAAO;IACX;IAEA,MAAM,aAAa,CAAC,OAAO,WAC3B,gEAAgE;QAChE,8CAA8C;QAC9C,WAAW,OAAO,CAAC,IAAM,KAAK,GAAG;IACjC,MAAM,cAAc,CAAC,OAAO,WAAa,MAAM,UAAU;IAEzD,MAAM,QAAQ,CAAC,OAAO,WAAa,QAAQ,OAAO,UAAU,MAAM;IAElE;;;;;KAKC,GACD,MAAM,cAAc,CAAC,MAAQ,IAAI,QAAQ,CAAC,WAAW;IACrD,MAAM,UAAU,CAAC,SAAW,aAAa,OAAO,CAAC,OAAO,OAAO;IAC/D,MAAM,YAAY,CAAC,SAAW,CAAC,UAAY,GAAG,SAAS,QAAQ;IAC/D,MAAM,iBAAiB,CAAC,OAAS,OAAO,KAAK,OAAO,CAAC,OAAO,MAAM;IAClE,MAAM,cAAc,CAAC,OAAS,gBAAgB,IAAI,CAAC,QAAQ,OAAO,OAAO;IACzE,MAAM,oBAAoB,CAAC,SAAW,aAAa,OAAO,CAAC,OAAO,SAAS,CAAC,QAAQ;IACpF,MAAM,kBAAkB,CAAC,SAAW,aAAa,OAAO,CAAC,OAAO,SAAS,CAAC,MAAM;IAChF,MAAM,sBAAsB,CAAC,OAAS,UAAU,MAAM,MAAM,UAAU,MAAM,CAAC;IAE7E,MAAM,mBAAmB;QAAC;QAAS;QAAS;QAAS;KAAW;IAChE,MAAM,iBAAiB,CAAC,aAAe,SAAS,kBAAkB;IAClE,MAAM,OAAO,CAAC,MAAM,UAAY,CAAC;YAC7B;YACA;QACJ,CAAC;IACD,MAAM,SAAS,CAAC,SAAS,SAAS,UAAY,CAAC;YAC3C;YACA;YACA;QACJ,CAAC;IACD,MAAM,WAAW,CAAC,SAAS,SAAS,SAAS,KAAK,QAAQ,WAAa,CAAC;YACpE;YACA;YACA;YACA;YACA;YACA;QACJ,CAAC;IACD,MAAM,YAAY,CAAC,SAAS,OAAO,UAAY,CAAC;YAC5C;YACA;YACA;QACJ,CAAC;IACD,MAAM,SAAS,CAAC,UAAU,UAAU,WAAW,YAAc,CAAC;YAC1D;YACA;YACA;YACA;QACJ,CAAC;IACD,MAAM,YAAY,CAAC,SAAS,SAAS,SAAW,CAAC;YAC7C;YACA;YACA;QACJ,CAAC;IACD,MAAM,WAAW,CAAC,SAAS,UAAY,CAAC;YACpC;YACA;QACJ,CAAC;IAED,MAAM,eAAe,SAAC,MAAM;YAAM,4EAAW;eAAM,OAAO,MAAM,MAAM,GAAG,CAAC,CAAC,QAAU,SAAS,OAAO,KAAK,KAAK,CAAC;;IAEhH,MAAM,aAAa,CAAC,OAAO;QACvB,OAAO,iBAAiB,OAAO,UAAU;IAC7C;IACA,MAAM,mBAAmB,CAAC,OAAO,UAAU;QACvC,OAAO,KAAK,WAAW,QAAQ,CAAC;YAC5B,IAAI,KAAK,GAAG,WAAW;gBACnB,OAAO,UAAU,KAAK;oBAAC;iBAAE,GAAG,EAAE;YAClC,OACK;gBACD,OAAO,iBAAiB,GAAG,UAAU;YACzC;QACJ;IACJ;IAEA,2BAA2B;IAC3B,MAAM,SAAS,SAAC,MAAM;YAAS,0EAAS;QACpC,4EAA4E;QAC5E,IAAI,OAAO,UAAU;YACjB,OAAO,SAAS,IAAI;QACxB;QACA,yFAAyF;QACzF,qHAAqH;QACrH,IAAI,SAAS,MAAM,KAAK,WAAW;YAC/B,OAAO,SAAS,IAAI,CAAC;QACzB;QACA,MAAM,qBAAqB,CAAC,MAAQ,KAAK,KAAK,YAAY,OAAO;QACjE,OAAO,SAAS,SAAS,KAAK,IAAI,CAAC,MAAM;IAC7C;IACA;;KAEC,GACD,MAAM,OAAO,CAAC,SAAS,SAAW,OAAO;YAAC;YAAM;SAAK,EAAE,SAAS;IAChE,MAAM,QAAQ,CAAC,WAAa,WAAW,UAAU;IACjD,MAAM,UAAU,CAAC;QACb,IAAI,KAAK,UAAU,aAAa;YAC5B,OAAO,WAAW,UAAU;QAChC,OACK;YACD,OAAO,KAAK,aAAa,WAAW,CAAC,cAAgB,WAAW,aAAa;QACjF;IACJ;IACA,MAAM,QAAQ,CAAC,SAAS,SAAW,UAAU,SAAS,SAAS;IAC/D,MAAM,OAAO,CAAC,WAAa,WAAW,UAAU;IAChD,MAAM,eAAe,CAAC,WAAa,MAAM,UAAU,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,QAAU,WAAW,OAAO;IAEnG,MAAM,eAAe,MAAM;IAC3B,MAAM,mBAAmB,CAAC,aAAa;QACnC,IAAI,eAAe,eAAe;YAC9B,OAAO;QACX,OACK,IAAI,aAAa;YAClB,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC;QAClB,kGAAkG;QAClG,MAAM,cAAc,IAAI,OAAO,KAAK;QACpC,MAAM,gBAAgB,KAAK,mBAAmB,IAAI,KAAK,GAAG;QAC1D,IAAI,IAAI,OAAO,KAAK,SAAS;YACzB,OAAO;gBAAE,MAAM;YAAS;QAC5B,OACK,IAAI,eAAe,eAAe;YACnC,OAAO;gBAAE,MAAM;gBAAU,SAAS,iBAAiB,aAAa;YAAe;QACnF,OACK;YACD,OAAO;gBAAE,MAAM;YAAO;QAC1B;IACJ;IACA,MAAM,qBAAqB,CAAC;QACxB,MAAM,cAAc,SAAS,OAAO,CAAC,OAAS,aAAa,KAAK,OAAO;QACvE,IAAI,YAAY,MAAM,KAAK,GAAG;YAC1B,OAAO,SAAS,IAAI,CAAC;QACzB,OACK,IAAI,YAAY,MAAM,KAAK,MAAM,MAAM,EAAE;YAC1C,OAAO,SAAS,IAAI,CAAC;QACzB,OACK;YACD,OAAO,SAAS,IAAI;QACxB;IACJ;IACA,MAAM,oBAAoB,CAAC;QACvB,MAAM,WAAW,IAAI,MAAM,CAAC,MAAQ,aAAa,KAAK,IAAI;QAC1D,MAAM,YAAY,SAAS,UAAU;QACrC,MAAM,YAAY,SAAS,UAAU;QACrC,IAAI,CAAC,aAAa,CAAC,WAAW;YAC1B,OAAO,SAAS,IAAI,CAAC;QACzB,OACK;YACD,MAAM,UAAU,SAAS,UAAU;YACnC,IAAI,aAAa,CAAC,WAAW,CAAC,WAAW;gBACrC,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,CAAC,aAAa,CAAC,WAAW,WAAW;gBAC1C,OAAO,SAAS,IAAI,CAAC;YACzB,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;IACJ;IAEA,MAAM,sBAAsB,CAAC,OAAO,aAAe,IAAI,OAAO,CAAC;YAC3D,IAAI,KAAK,SAAS,YAAY;gBAC1B,MAAM,QAAQ,IAAI,QAAQ,MAAM,CAAC;oBAC7B,MAAM,UAAU,aAAa,QAAQ,QAAQ;oBAC7C,OAAO,OAAO,QAAQ,GAAG;gBAC7B;gBACA,OAAO,UAAU,KAAK,OAAO;YACjC,OACK;gBACD,MAAM,UAAU,IAAI,MAAM,MAAM,CAAC;oBAC7B,MAAM,UAAU,aAAa,MAAM,WAAW;oBAC9C,MAAM,UAAU,aAAa,MAAM,WAAW;oBAC9C,OAAO,OAAO,MAAM,SAAS;gBACjC;gBACA,OAAO,UAAU,KAAK,SAAS,WAAW;YAC9C;QACJ;IACA,MAAM,mBAAmB,CAAC,QAAU,OAAO,OAAO,GAAG,CAAC,CAAC;YACnD,MAAM,aAAa,KAAK;YACxB,OAAO,eAAe,cAAc,aAAa;QACrD,GAAG,KAAK,CAAC;IACT;;;;KAIC,GACD,MAAM,cAAc,CAAC;QACjB,MAAM,SAAS,KAAK;QACpB,MAAM,iBAAiB,aAAa;QACpC,MAAM,QAAQ;eAAI;eAAmB;SAAO;QAC5C,OAAO,oBAAoB,OAAO;IACtC;IAEA,MAAM,kBAAkB;IACxB,MAAM,4BAA4B,CAAC,QAAU,OAAO,OAAO,iBACtD,IAAI,CAAC,CAAC,eAAiB,SAAS,IAAI,CAAC,aAAa,KAAK,CAAC,UACxD,GAAG,CAAC,CAAC,aAAe,YAAY,YAAY;IAEjD,MAAM,MAAM,CAAC,KAAK;QACd,OAAO,MAAM,MAAM;IACvB;IACA,MAAM,QAAQ,CAAC,WAAW,KAAK,SAAW,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,KAAK,QAAQ;IAC1F,MAAM,WAAW,CAAC,WAAW,MAAM;QAC/B,MAAM,WAAW,YAAY,WAAW,CAAC;YACrC,OAAO,WAAW,MAAM,OAAO,OAAO;QAC1C;QACA,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,SAAS,IAAI;IAC3E;IACA,MAAM,cAAc,CAAC,WAAW;QAC5B,MAAM,MAAM,KAAK,UAAU,GAAG,EAAE,CAAC;YAC7B,OAAO,EAAE,KAAK;QAClB;QACA,OAAO,SAAS,KAAK;IACzB;IACA,MAAM,kBAAkB,CAAC;QACrB,MAAM,eAAe,CAAC;QACtB,IAAI,QAAQ;QACZ,OAAO,QAAQ,KAAK,EAAE,CAAC;YACnB,MAAM,UAAU,OAAO,OAAO;YAC9B,MAAM,SAAS,CAAC;gBACZ,MAAM,WAAW,QAAQ;gBACzB,YAAY,CAAC,SAAS,GAAG,UAAU,OAAO,OAAO,EAAE,SAAS;YAChE;YACA,SAAS;QACb;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,MAAM,WAAW,CAAC;QACd,0DAA0D;QAC1D,sBAAsB;QACtB,mEAAmE;QACnE,wBAAwB;QACxB,gCAAgC;QAChC,mBAAmB;QACnB,gCAAgC;QAChC,MAAM,SAAS,CAAC;QAChB,MAAM,QAAQ,EAAE;QAChB,MAAM,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC,UAAY,QAAQ,OAAO,EAAE,IAAI,CAAC;QACnE,MAAM,gBAAgB,SAAS,IAAI,CAAC,2BAA2B,KAAK,CAAC,CAAC;QACtE,IAAI,UAAU;QACd,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,MAAM,EAAE,MAAM,YAAY,EAAE,MAAM,IAAI,EAAE,GAAG,UAAU,MAAM,CAAC,UAAY,QAAQ,OAAO,KAAK;QAC5F,oBAAoB;QACpB,OAAO,MAAM,CAAC;YACV,MAAM,aAAa,EAAE;YACrB,OAAO,QAAQ,KAAK,EAAE,CAAC;gBACnB,IAAI,QAAQ;gBACZ,8DAA8D;gBAC9D,MAAO,MAAM,CAAC,IAAI,UAAU,OAAO,KAAK,UAAW;oBAC/C;gBACJ;gBACA,MAAM,WAAW,kBAAkB,eAAe,MAAM,QAAQ;gBAChE,MAAM,UAAU,SAAS,QAAQ,OAAO,EAAE,QAAQ,OAAO,EAAE,QAAQ,OAAO,EAAE,UAAU,OAAO;gBAC7F,mEAAmE;gBACnE,IAAK,IAAI,yBAAyB,GAAG,yBAAyB,QAAQ,OAAO,EAAE,yBAA0B;oBACrG,IAAK,IAAI,sBAAsB,GAAG,sBAAsB,QAAQ,OAAO,EAAE,sBAAuB;wBAC5F,MAAM,cAAc,WAAW;wBAC/B,MAAM,iBAAiB,QAAQ;wBAC/B,MAAM,SAAS,IAAI,aAAa;wBAChC,MAAM,CAAC,OAAO,GAAG;wBACjB,aAAa,KAAK,GAAG,CAAC,YAAY,iBAAiB;oBACvD;gBACJ;gBACA,WAAW,IAAI,CAAC;YACpB;YACA;YACA,MAAM,IAAI,CAAC,UAAU,QAAQ,OAAO,EAAE,YAAY,QAAQ,OAAO;YACjE;QACJ;QACA,mBAAmB;QACnB,+EAA+E;QAC/E,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,KAAK,cAAc,GAAG,CAAC,CAAC;YACnD,MAAM,UAAU,gBAAgB;YAChC,MAAM,aAAa,SAAS,QAAQ,OAAO,EAAE,OAAO;YACpD,OAAO;gBACH,WAAW;oBAAC;iBAAW;gBACvB;YACJ;QACJ,GAAG,UAAU,CAAC,IAAM,CAAC;gBACjB,WAAW,EAAE;gBACb,SAAS,CAAC;YACd,CAAC;QACD,MAAM,SAAS,KAAK,SAAS;QAC7B,OAAO;YACH,MAAM;YACN;YACA,KAAK;YACL;YACA;QACJ;IACJ;IACA,MAAM,YAAY,CAAC;QACf,MAAM,OAAO,YAAY;QACzB,OAAO,SAAS;IACpB;IACA,MAAM,YAAY,CAAC,YAAc,KAAK,UAAU,GAAG,EAAE,CAAC,IAAM,EAAE,KAAK;IACnE,MAAM,cAAc,CAAC,YAAc,OAAO,UAAU,OAAO;IAC3D,MAAM,aAAa,CAAC,YAAc,KAAK,UAAU,OAAO,EAAE,MAAM,GAAG;IACnE,MAAM,cAAc,CAAC,WAAW,cAAgB,SAAS,IAAI,CAAC,UAAU,OAAO,CAAC,YAAY;IAC5F,MAAM,YAAY;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IAEA,MAAM,kBAAkB,CAAC,WAAW,UAAY,QAAQ,UAAU,GAAG,EAAE,CAAC,IAAM,KAAK,EAAE,KAAK,EAAE,CAAC,IAAM,GAAG,SAAS,EAAE,OAAO;IACxH,MAAM,eAAe,CAAC,WAAW,QAAQ;QACrC,MAAM,UAAU,IAAI,OAAO,SAAS,EAAE,CAAC;YACnC,OAAO,KAAK,QACP,IAAI,CAAC,CAAC,KAAO,gBAAgB,WAAW,KACxC,MAAM,CAAC;QAChB;QACA,MAAM,QAAQ,IAAI;QAClB,OAAO,OAAO,MAAM,MAAM,GAAG,GAAG;IACpC;IACA,MAAM,aAAa,CAAC,YAAY,SAAW,OAAO,QAAQ;IAC1D,MAAM,eAAe,CAAC,YAAY,SAAW,OAAO,UAAU;IAC9D,MAAM,UAAU,CAAC,WAAW,SAAW,aAAa,WAAW,QAAQ;IACvE,MAAM,sBAAsB,CAAC,WAAW,OAAS,gBAAgB,WAAW,MAAM,MAAM,CAAC,CAAC,SAAW,CAAC,OAAO,QAAQ;IACrH,MAAM,cAAc,CAAC,WAAW,QAAU,OAAO,OAAO,CAAC,OAAS,oBAAoB,WAAW;IACjG,0FAA0F;IAC1F,MAAM,qBAAqB,CAAC,WAAW,SAAW,WAAW,WAAW,QAAQ,MAAM,CAAC,CAAC,YAAc,YAAY,WAAW,UAAU,KAAK;IAC5I,4FAA4F;IAC5F,MAAM,uBAAuB,CAAC,WAAW,SAAW,aAAa,WAAW,QAAQ,MAAM,CAAC,CAAC,QAAU,YAAY,WAAW;IAE7H,MAAM,QAAQ,MAAM;IACpB,MAAM,aAAa,MAAM;IACzB,MAAM,QAAQ,CAAC,UAAY,KAAK,aAAa,QAAQ,WAAW;IAChE,MAAM,gBAAgB,CAAC;QACnB,MAAM,UAAU,aAAa,SAAS,WAAW;QACjD,MAAM,UAAU,aAAa,SAAS,WAAW;QACjD,OAAO;YACH;YACA;YACA;QACJ;IACJ;IACA,kDAAkD;IAClD,MAAM,eAAe,SAAC;YAAY,0EAAS;QACvC,MAAM,SAAS,CAAC,OAAS,MAAM,KAAK,OAAO,IAAI,WAAW,GAAG,CAAC,QAAQ,WAAW,IAAI,CAAC;QACtF,MAAM,QAAQ,CAAC,OAAS,WAAW,KAAK,OAAO,IAAI,WAAW,QAAQ,CAAC,QAAQ,WAAW,GAAG,CAAC;QAC9F,MAAM,MAAM,CAAC;YACT,IAAI,MAAM,UAAU;gBAChB,OAAO,MAAM;oBAAE;gBAAQ;YAC3B,OACK;gBACD,MAAM,OAAO;gBACb,MAAM,cAAc,OAAO,OAAO;gBAClC,SAAS,SAAS,IAAI,CAAC;oBAAE,MAAM;oBAAM;gBAAY;gBACjD,OAAO;YACX;QACJ;QACA,IAAI,SAAS,SAAS,IAAI;QAC1B,MAAM,YAAY,CAAC,SAAS;YACxB,OAAO,OAAO,IAAI,CAAC;gBACf,OAAO,IAAI;YACf,GAAG,CAAC;gBACA,OAAO,WAAW,SAAS,EAAE,IAAI,IAAI,EAAE,WAAW,GAAG,IAAI;YAC7D;QACJ;QACA,OAAO;YACH;QACJ;IACJ;IACA,MAAM,YAAY,CAAC;QACf,OAAO,CAAC;YACJ,MAAM,OAAO,EAAE;YACf,MAAM,SAAS,CAAC,SAAS;gBACrB,OAAO,KAAK,MAAM,CAAC;oBACf,OAAO,WAAW,EAAE,IAAI,EAAE;gBAC9B;YACJ;YACA,MAAM,UAAU,CAAC;gBACb,2EAA2E;gBAC3E,MAAM,QAAQ,QAAQ,OAAO;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAChD,MAAM,OAAO,WAAW,OAAO,CAAC,SAAS,KAAK;gBAC9C,KAAK,IAAI,CAAC;oBACN,MAAM;oBACN,KAAK;gBACT;gBACA,OAAO;YACX;YACA,MAAM,gBAAgB,CAAC,SAAS;gBAC5B,IAAI,MAAM,YAAY,MAAM,UAAU;oBAClC,OAAO;gBACX,OACK;oBACD,MAAM,OAAO;oBACb,OAAO,OAAO,MAAM,YAAY,IAAI,CAAC;wBACjC,OAAO,QAAQ;oBACnB,GAAG,CAAC;wBACA,OAAO,WAAW,SAAS,EAAE,IAAI,IAAI,EAAE,GAAG,GAAG,QAAQ;oBACzD;gBACJ;YACJ;YACA,OAAO;gBACH;YACJ;QACJ;IACJ;IACA,MAAM,oBAAoB,CAAC,OAAS,OAAO,MAAM,SAAS,GAAG,CAC7D,qDAAqD;QACrD,iJAAiJ;QACjJ,CAAC,YAAc,UAAU,MAAM,CAAC,GAAG;IACnC,MAAM,UAAU,CAAC;QACb,MAAM,UAAU,CAAC;YACb,MAAM,QAAQ,kBAAkB;YAChC,MAAM,IAAI,CAAC,CAAC,YAAc,MAAM,MAAM,SAAS;YAC/C,OAAO;gBACH,MAAM,MAAM,WAAW,IAAI,CAAC;oBACxB,SAAS;oBACT,SAAS;oBACT,SAAS;gBACb;gBACA,qEAAqE;gBACrE,OAAO,KAAK;gBACZ,OAAO,MAAM;gBACb,MAAM,IAAI,CAAC,CAAC,YAAc,MAAM,KAAK,SAAS;gBAC9C,OAAO;YACX;QACJ;QACA,MAAM,QAAQ,CAAC;YACX,MAAM,mBAAmB;gBACrB,MAAM,mBAAmB,IAAI,IAAI,OAAO;gBACxC,IAAI,iBAAiB,MAAM,KAAK,GAAG;oBAC/B,OAAO,SAAS,IAAI;gBACxB,OACK;oBACD,MAAM,YAAY,gBAAgB,CAAC,EAAE;oBACrC,MAAM,SAAS;wBAAC;wBAAO;qBAAM;oBAC7B,MAAM,UAAU,OAAO,kBAAkB,CAAC;wBACtC,OAAO,cAAc,aAAa,SAAS,QAAQ;oBACvD;oBACA,OAAO,UAAU,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC;gBACrD;YACJ;YACA,OAAO,KAAK,CAAC,EAAE,EAAE;YACjB,mBAAmB,IAAI,CAAC,IAAM,SAAS,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,YAAc,MAAM,KAAK,CAAC,EAAE,EAAE,SAAS,YAAY;YAC/G,OAAO,SAAS,KAAK,CAAC,EAAE;QAC5B;QACA,OAAO;YACH;YACA;QACJ;IACJ;IACA,MAAM,aAAa;QACf;QACA;QACA;IACJ;IAEA,IAAI,gBAAgB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IAED,IAAI,cAAc;QACd,MAAM,UAAU,CAAC;YACb,OAAO,aAAa,OAAO,CAAC,QAAQ,GAAG,CAAC,SAAS,CAAC;QACtD;QACA,MAAM,YAAW,CAAC,UAAY,gBAAgB,SAAS,GAAG;QAC1D,MAAM,aAAa,CAAC;YAChB,IAAI,CAAC,UAAU,UAAU;gBACrB,OAAO;YACX;YACA,IAAI,KAAK,aAAa,QAAQ;gBAC1B,OAAO;YACX;YACA,OAAO,SAAS,eAAe,KAAK;QACxC;QACA,MAAM,aAAa,CAAC;YAChB,IAAI,CAAC,UAAU,UAAU;gBACrB,OAAO;YACX;YACA,OAAO,SAAS;gBAAC;gBAAM;gBAAO;gBAAM;aAAQ,EAAE,KAAK;QACvD;QACA,MAAM,gBAAgB,CAAC,UAAY,UAAU,YAAY,MAAM,SAAS,uBAAuB;QAC/F,MAAM,kBAAkB,CAAC,SAAS;YAC9B,OAAO,QAAQ,GAAG,CAAC,uBAAuB,CAAC,MAAM,GAAG;QACxD;QACA,MAAM,mBAAmB,CAAC,QAAQ;YAC9B,MAAM,KAAK,MAAM;YACjB,OAAO,aAAa;QACxB;QACA,MAAM,YAAY,CAAC;YACf,MAAM,MAAM,KAAK;YACjB,OAAO,SAAS;gBACZ;gBAAU;gBAAY;gBAAU;gBAAY;gBAAW;gBAAS;gBAAS;gBAAY;aACxF,EAAE;QACP;QACA,MAAM,cAAc,CAAC,UAAY,UAAU,WAAW,OAAO,SAAS,UAAU,SAAS,IAAI;QAC7F,OAAO;YACH,IAAI,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,KAAK;YACT;YACA,MAAM,SAAS;gBACX,UAAU;gBACV,WAAW;YACf;YACA,QAAQ,SAAS;gBACb,KAAK;gBACL,QAAQ;gBACR,KAAK;gBACL,QAAQ;YACZ;YACA,OAAO,SAAS;gBACZ,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,QAAQ;YACZ;YACA,QAAQ,SAAS;gBACb,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,MAAM;YACV;YACA,QAAQ,SAAS;gBACb,QAAQ;gBACR,QAAQ;YACZ;YACA,QAAQ,SAAS;gBACb,IAAI,aAAa,OAAO;gBACxB,OAAO;gBACP,MAAM,aAAa,QAAQ;YAC/B;YACA,OAAO,SAAS;gBACZ;gBACA,aAAa;gBACb,aAAa;YACjB;YACA,UAAU,SAAS;gBACf,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,UAAA;gBACA,QAAQ;gBACR,WAAW;gBACX,WAAW;gBACX;gBACA;gBACA,SAAS;gBACT,SAAS;gBACT;gBACA;gBACA;YACJ;YACA,IAAI;YACJ,IAAI;QACR;IACJ;IAEA,MAAM,YAAY,CAAC,MAAM,QAAU,CAAC;YAChC;YACA;QACJ,CAAC;IACD,MAAM,aAAa,CAAC,OAAO,QAAQ,SAAW,CAAC;YAC3C;YACA;YACA;QACJ,CAAC;IACD,MAAM,SAAS,CAAC,UAAU,QAAQ;QAC9B,MAAM,WAAW,SAAS,QAAQ,GAAG,QAAQ,CAAC;QAC9C,MAAM,QAAQ,UAAU,UAAU,MAAM,SAAS,EAAE,EAAE;QACrD,OAAO,MAAM,GAAG,CAAC,CAAC;YACd,OAAO;gBACH,QAAQ,SAAS,KAAK,CAAC,GAAG;gBAC1B,OAAO,SAAS,KAAK,CAAC,MAAM;YAChC;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,eAAe,CAAC,UAAU,QAAQ;QACpC,OAAO,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;YACxC,MAAM,SAAS,SAAS,MAAM,GAAG,KAAK,CAAC;YACvC,SAAS,MAAM,GAAG,SAAS,CAAC,QAAQ,MAAM,KAAK;YAC/C,SAAS,MAAM,GAAG,KAAK,CAAC,QAAQ;YAChC,OAAO,UAAU,QAAQ;QAC7B;IACJ;IACA;;;KAGC,GACD,MAAM,cAAc,CAAC,UAAU,QAAQ;QACnC,OAAO,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,SAAS,MAAM,GAAG,KAAK,CAAC;YACtC,SAAS,MAAM,GAAG,SAAS,CAAC,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC;gBAAC;aAAM;YAC9D,SAAS,MAAM,GAAG,SAAS,CAAC,QAAQ,MAAM,KAAK;YAC/C,SAAS,MAAM,GAAG,MAAM,CAAC,QAAQ;YACjC,OAAO,UAAU,OAAO;QAC5B;IACJ;IACA;;;;;;KAMC,GACD,MAAM,YAAY,CAAC,UAAU,MAAM,OAAO;QACtC,MAAM,OAAO,CAAC,OAAO,OAAO;YACxB,MAAM,WAAW,WAAW,OAAO,SAAS,IAAI,IAAI;YACpD,0BAA0B;YAC1B,IAAI,MAAM,QAAQ;gBACd,OAAO,WAAW,OAAO,OAAO;YACpC,OACK;gBACD,4CAA4C;gBAC5C,OAAO,SAAS,QAAQ,GAAG,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC;oBAC3C,OAAO,QAAQ,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;wBACzC,MAAM,QAAQ;4BAAC;gCAAE,OAAO,SAAS,IAAI;gCAAE,QAAQ,SAAS,KAAK;4BAAC;yBAAE;wBAChE,8EAA8E;wBAC9E,MAAM,YAAY,MAAM,UAAU,SAAS,SAAS,IAAI;wBACxD,OAAO,KAAK,WAAW,SAAS,IAAI,CAAC,SAAS,KAAK,GAAG,OAAO,MAAM,CAAC;oBACxE;gBACJ,GAAG,KAAK,CAAC;YACb;QACJ;QACA,OAAO,KAAK,MAAM,SAAS,IAAI,IAAI,EAAE;IACzC;IAEA,MAAM,MAAM,CAAC,UAAU,MAAM,UAAU;QACnC,MAAM,OAAO,QAAQ,CAAC,EAAE;QACxB,MAAM,OAAO,SAAS,KAAK,CAAC;QAC5B,OAAO,EAAE,UAAU,MAAM,MAAM;IACnC;IACA;;KAEC,GACD,MAAM,SAAS,CAAC,UAAU,MAAM;QAC5B,OAAO,SAAS,MAAM,GAAG,IACrB,IAAI,UAAU,MAAM,UAAU,aAC9B,SAAS,IAAI;IACrB;IACA,MAAM,YAAY,CAAC,UAAU,MAAM,MAAM;QACrC,MAAM,QAAQ,KAAK,UAAU;QAC7B,OAAO,MAAM,MAAM,CAAC,GAAG;YACnB,MAAM,UAAU,KAAK,UAAU;YAC/B,OAAO,cAAc,UAAU,GAAG;QACtC,GAAG;IACP;IACA,MAAM,gBAAgB,CAAC,UAAU,OAAO;QACpC,OAAO,MAAM,IAAI,CAAC,CAAC;YACf,OAAO,IAAI,MAAM,CAAC,MAAM,SAAS,EAAE,EAAE;QACzC;IACJ;IAEA,MAAM,cAAc;IACpB;IACA;IACA;IAEA,MAAM,WAAW;IACjB,MAAM,YAAY,CAAC,MAAM;QACrB,OAAO,YAAY,UAAU,CAAC,WAAW;YACrC,OAAO,KAAK;QAChB,GAAG;IACP;IAEA,MAAM,gBAAgB,CAAC,OAAO;QAC1B,MAAM,QAAQ,UAAU,SAAS,CAAC;QAClC,MAAM,UAAU,QAAQ,OAAO;QAC/B,OAAO,QAAQ,IAAI,CAAC,CAAC;YACjB,MAAM,mBAAmB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;YAChE,MAAM,cAAc,aAAa,CAAC,EAAE,CAAC,GAAG;YACxC,MAAM,cAAc,iBAAiB,GAAG,GAAG,iBAAiB,OAAO;YACnE,MAAM,eAAe,MAAM,GAAG,CAAC,KAAK,CAAC,aAAa;YAClD,OAAO,kBAAkB;QAC7B,GAAG,KAAK,CAAC;IACb;IACA,WAAW,SAAS,CAAC;IACrB,WAAW,SAAS,CAAC;IACrB,MAAM,cAAc;IAEpB,+EAA+E;IAC/E,MAAM,WAAW,CAAC,QAAQ;QACtB,OAAQ,OAAO,MAAM,IAAI,OAAO,QAAQ,IACpC,AAAC,OAAO,MAAM,GAAG,OAAO,OAAO,GAAG,KAAM,OAAO,SAAS,IACxD,OAAO,GAAG,IAAI,OAAO,QAAQ,IAC7B,AAAC,OAAO,GAAG,GAAG,OAAO,OAAO,GAAG,KAAM,OAAO,SAAS;IAC7D;IACA,MAAM,gBAAgB,CAAC,WAAW;QAC9B,IAAI,SAAS;QACb,MAAM,iBAAiB,MAAM,UAAU;QACvC,IAAK,IAAI,IAAI,OAAO,QAAQ,EAAE,KAAK,OAAO,SAAS,EAAE,IAAK;YACtD,IAAK,IAAI,IAAI,OAAO,QAAQ,EAAE,KAAK,OAAO,SAAS,EAAE,IAAK;gBACtD,SAAS,UAAU,UAAU,KAAK,CAAC,WAAW,GAAG,GAAG,MAAM,CAAC;YAC/D;QACJ;QACA,OAAO,SAAS,SAAS,IAAI,CAAC,UAAU,SAAS,IAAI;IACzD;IAEA,MAAM,YAAY,CAAC,SAAS;QACxB,OAAO,OAAO,KAAK,GAAG,CAAC,QAAQ,GAAG,EAAE,QAAQ,GAAG,GAAG,KAAK,GAAG,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,KAAK,GAAG,CAAC,QAAQ,GAAG,GAAG,QAAQ,OAAO,GAAG,GAAG,QAAQ,GAAG,GAAG,QAAQ,OAAO,GAAG,IAAI,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG;IAClQ;IACA,MAAM,YAAY,CAAC,WAAW,WAAW;QACrC,MAAM,cAAc,UAAU,QAAQ,CAAC,WAAW,WAAW;QAC7D,MAAM,eAAe,UAAU,QAAQ,CAAC,WAAW,YAAY;QAC/D,OAAO,YAAY,IAAI,CAAC,CAAC;YACrB,OAAO,aAAa,GAAG,CAAC,CAAC;gBACrB,OAAO,UAAU,IAAI;YACzB;QACJ;IACJ;IACA,MAAM,WAAW,CAAC,WAAW,WAAW;QACpC,OAAO,UAAU,WAAW,WAAW,YAAY,IAAI,CAAC,CAAC;YACrD,OAAO,cAAc,WAAW;QACpC;IACJ;IAEA,MAAM,SAAS,CAAC,OAAO,OAAO;QAC1B,MAAM,YAAY,aAAa;QAC/B,OAAO,SAAS,WAAW,OAAO;IACtC;IACA,wDAAwD;IACxD,MAAM,eAAe,UAAU,SAAS;IAExC,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,sBAAsB,CAAC,KAAK,KAAK;QACnC,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS;QAClC,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,MAAM,eAAe,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE;YAC5C,IAAI,YAAY,kBAAkB;gBAC9B,kBAAkB;YACtB;YACA,IAAI,oBAAoB,cAAc;gBAClC,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,WAAW,CAAC,QAAQ,KAAK;QAC3B,mHAAmH;QACnH,SAAS,IAAI,CAAC,oBAAoB,KAAK,CAAC,MAAM,CAAC;YAC3C,IAAI,UAAU,MAAM;gBAChB,OAAO,SAAS,CAAC,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG;YACjD;QACJ;QACA,IAAI,MAAM;YACN,OAAO,SAAS,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,GAAG;QAC/C;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,KAAK;QAC5B,iHAAiH;QACjH,SAAS,IAAI,CAAC,oBAAoB,KAAK,CAAC,MAAM,CAAC;YAC3C,IAAI,UAAU,MAAM;gBAChB,OAAO,SAAS,CAAC,MAAM,CAAC,WAAW,OAAO,CAAC,GAAG;YAClD;QACJ;QACA,IAAI,MAAM;YACN,OAAO,SAAS,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,GAAG;QAChD;IACJ;IAEA;;;;;;KAMC,GACD,MAAM,oBAAoB,CAAC,QAAQ,OAAO;QACtC,OAAO,QAAQ,CAAC,iBAAiB;YAAE,GAAG,IAAI;YAAE;QAAM;IACtD;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,sBAAsB;IAC5B,MAAM,0BAA0B,MAAM,GAAG,CAAC;QACtC,MAAM,OAAO,AAAC,GAAQ,OAAN,IAAI,GAAE;QACtB,OAAO;YAAE,OAAO;YAAM,OAAO;QAAK;IACtC;IACA,MAAM,0BAA0B,IAAI;QAAC;QAAS;QAAU;QAAU;QAAU;QAAU;QAAS;QAAS;QAAU;QAAQ;KAAS,EAAE,CAAC;QAClI,OAAO;YAAE,OAAO;YAAM,OAAO,KAAK,WAAW;QAAG;IACpD;IACA,2DAA2D;IAC3D,MAAM,eAAe;IACrB,MAAM,sBAAsB,CAAC;QACzB,IAAI;QACJ,wFAAwF;QACxF,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,cAAc,CAAC,KAAK,IAAI,SAAS,CAAC,OAAO,SAAS,CAAC,QAAQ,IAAI,IAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,OAAO;QAClI,OAAO,SAAS,aAAa,OAAO,CAAC,gBAAgB;IACzD;IACA,2DAA2D;IAC3D,MAAM,yBAAyB,CAAC,QAAQ;QACpC,IAAI,mBAAmB,WAAW,CAAC,mBAAmB,SAAS;YAC3D,OAAO;QACX,OACK,IAAI,eAAe,SAAS;YAC7B,OAAO;gBAAE,GAAG,aAAa;gBAAE,OAAO,oBAAoB;YAAQ;QAClE,OACK;YACD,OAAO;gBAAE,GAAG,aAAa;gBAAE,OAAO;YAAa;QACnD;IACJ;IACA,2DAA2D;IAC3D,MAAM,6BAA6B,CAAC,QAAQ;QACxC,IAAI,mBAAmB,WAAW,mBAAmB,SAAS;YAC1D,OAAO;QACX,OACK,IAAI,eAAe,SAAS;YAC7B,OAAO;gBAAE,GAAG,iBAAiB;gBAAE,OAAO,oBAAoB;YAAQ;QACtE,OACK;YACD,OAAO;gBAAE,GAAG,iBAAiB;gBAAE,OAAO;YAAa;QACvD;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,WAAW,CAAC;QACd,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,uBAAuB;YAClC,WAAW;YACX,SAAS;QACb;QACA,eAAe,uBAAuB;YAClC,WAAW;YACX,SAAS;QACb;QACA,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;QACA,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS;QACb;QACA,eAAe,gBAAgB;YAC3B,WAAW;YACX,SAAS;QACb;QACA,eAAe,4BAA4B;YACvC,WAAW;YACX,SAAS;QACb;QACA,eAAe,cAAc;YACzB,WAAW;YACX,+FAA+F;YAC/F,SAAS,CAAC,SAAS,UAAU,CAAC,OAAO;QACzC;QACA,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,wBAAwB;YACnC,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,iBAAiB;YAC5B,WAAW;YACX,SAAS;QACb;QACA,eAAe,8BAA8B;YACzC,WAAW;YACX,SAAS,EAAE;QACf;QACA,eAAe,0BAA0B;YACrC,WAAW;YACX,SAAS,EAAE;QACf;IACJ;IACA,MAAM,qBAAqB,OAAO;IAClC,MAAM,uBAAuB,OAAO;IACpC,MAAM,uBAAuB,OAAO;IACpC,MAAM,qBAAqB,OAAO;IAClC,MAAM,oBAAoB,OAAO;IACjC,MAAM,sBAAsB,OAAO;IACnC,MAAM,uBAAuB,OAAO;IACpC,MAAM,eAAe,OAAO;IAC5B,MAAM,qBAAqB,OAAO;IAClC,MAAM,mBAAmB,OAAO;IAChC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,oBAAoB,OAAO;IACjC,MAAM,aAAa,OAAO;IAC1B,MAAM,6BAA6B,OAAO;IAC1C,MAAM,yBAAyB,OAAO;IACtC,MAAM,iBAAiB,CAAC,SAAW,mBAAmB,YAAY;IAClE,MAAM,qBAAqB,CAAC,SAAW,mBAAmB,YAAY;IACtE,MAAM,mBAAmB,CAAC;QACtB,4HAA4H;QAC5H,MAAM,UAAU,OAAO,OAAO;QAC9B,MAAM,gBAAgB,QAAQ,GAAG,CAAC;QAClC,OAAO,QAAQ,KAAK,CAAC,0BAA0B,gBAAgB,uBAAuB,QAAQ;IAClG;IACA,MAAM,uBAAuB,CAAC;QAC1B,4HAA4H;QAC5H,MAAM,UAAU,OAAO,OAAO;QAC9B,MAAM,oBAAoB,QAAQ,GAAG,CAAC;QACtC,OAAO,QAAQ,KAAK,CAAC,8BAA8B,oBAAoB,2BAA2B,QAAQ;IAC9G;IAEA,MAAM,cAAc,CAAC;QACjB,OAAO,SAAS,WAAW;IAC/B;IACA,MAAM,aAAa,CAAC,WAAW;QAC3B,MAAM,OAAO,YAAY,WAAW;QACpC,OAAO,KAAK,MAAM,GAAG,IAAI,SAAS,IAAI,CAAC,QAAQ,SAAS,IAAI;IAChE;IACA,MAAM,WAAW,CAAC,WAAW,uBAAuB;QAChD,OAAO,WAAW,WAAW,uBAAuB,IAAI,CAAC,CAAC;YACtD,OAAO,WAAW,WAAW,sBAAsB,IAAI,CAAC,CAAC;gBACrD,OAAO,UAAU,aAAa;oBAAC;oBAAO;iBAAK,EAAE,GAAG,CAAC,CAAC;oBAC9C,OAAO;wBACH;wBACA;wBACA;oBACJ;gBACJ;YACJ;QACJ;IACJ;IAEA,kFAAkF;IAClF,MAAM,WAAW,CAAC,WAAW;QACzB,OAAO,WAAW,WAAW;IACjC;IACA,MAAM,cAAc,CAAC,WAAW,uBAAuB;QACnD,OAAO,SAAS,WAAW,uBAAuB,sBAAsB,IAAI,CAAC,CAAC;YAC1E,MAAM,SAAS,CAAC;gBACZ,OAAO,GAAG,WAAW;YACzB;YACA,MAAM,kBAAkB;YACxB,MAAM,gBAAgB,SAAS,MAAM,KAAK,EAAE,iBAAiB;YAC7D,MAAM,eAAe,SAAS,MAAM,IAAI,EAAE,iBAAiB;YAC3D,OAAO,cAAc,IAAI,CAAC,CAAC;gBACvB,OAAO,aAAa,IAAI,CAAC,CAAC;oBACtB,OAAO,GAAG,IAAI,MAAM,OAAO,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,IAAI,SAAS,IAAI;gBACpF;YACJ;QACJ;IACJ;IAEA,MAAM,YAAY;IAClB,MAAM,aAAa,CAAC;QAChB,MAAM,UAAU,CAAC,MAAM,OAAS,OAAO,MAAM,MAAM,MAAM,CAAC,CAAC,OAAS,SAAS,MAAM,MAAM;QACzF,MAAM,kBAAkB,CAAC,OAAS,QAAQ,MAAM,cAAc,QAAQ,MAAM;QAC5E,OAAO,cAAc,MAAM,GAAG,KAAK,OAAO,eAAe,mBAAmB,SAAS,IAAI,CAAC,iBAAiB,SAAS,IAAI;IAC5H;IACA,MAAM,WAAW,CAAC,OAAO,eAAe;QACpC,IAAI,cAAc,MAAM,IAAI,GAAG;YAC3B,OAAO,SAAS,IAAI;QACxB,OACK;YACD,OAAO,YAAY,OAAO,SAAS,qBAAqB,EAAE,SAAS,oBAAoB,EAClF,GAAG,CAAC,CAAC,SAAW,CAAC;oBAAE;oBAAQ,OAAO;gBAAc,CAAC;QAC1D;IACJ;IAEA;;;;;KAKC,GACD,MAAM,cAAc;IACpB,MAAM,sBAAsB,QAAQ,cAAc,UAAU,cAAc;IAC1E,MAAM,mBAAmB;IACzB,MAAM,2BAA2B,QAAQ,mBAAmB,UAAU,mBAAmB;IACzF,MAAM,kBAAkB;IACxB,MAAM,0BAA0B,QAAQ,kBAAkB,UAAU,kBAAkB;IACtF,MAAM,WAAW;QACb,UAAU;QACV,kBAAkB;QAClB,eAAe;QACf,uBAAuB;QACvB,cAAc;QACd,sBAAsB;IAC1B;IAEA;;;;;KAKC,GACD,MAAM,2BAA2B,CAAC,UAAY,MAAM,SAAS,IAAI,CAAC,CAAC,QAAU,SAAS,OAAO,SAAS,qBAAqB,GAAG,IAAI,CAAC,SAAS,UAAU,CAAC,QAAU,KAAK,CAAC,EAAE;IACzK,MAAM,2BAA2B,CAAC,WAAa,CAAC,UAAU;YACtD,MAAM,WAAW,KAAK;YACtB,MAAM,OAAO,aAAa,SAAS,aAAa,aAAa,yBAAyB,YAAY;YAClG,OAAO,UAAU,MAAM,UAAU;QACrC;IACA,MAAM,4BAA4B,yBAAyB;IAC3D,MAAM,mBAAmB,yBAAyB;IAClD,MAAM,wBAAwB,CAAC,SAAW,QAAQ,OAAO,KAAK,CAAC,KAAK,CAAC,gBAAgB;IACrF,MAAM,uBAAuB,CAAC,UAAU;QACpC,MAAM,UAAU,iBAAiB;QACjC,MAAM,UAAU,QAAQ,IAAI,CAAC,CAAC,OAAS,MAAM,OACxC,GAAG,CAAC,CAAC,QAAU,KAAK;QACzB,OAAO,MAAM,SAAS,SAAS,CAAC,MAAM,OAAS,SAAS,MAAM,CAAC,MAAQ,OAAO,QAAQ,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,UAAY,MAAM,SAAS,cAAc,OAAO,GAAG,SAAS,SAAS,KAAK,CAAC,EAAE;IACxL;IAEA,MAAM,sBAAsB;QACxB;YACI,MAAM;YACN,OAAO;QACX;QACA;YACI,MAAM;YACN,OAAO;QACX;QACA;YACI,MAAM;YACN,OAAO;QACX;QACA;YACI,MAAM;YACN,OAAO;QACX;KACH;IAED,MAAM,YAAY,CAAC,QAAU,CAAC;YAC1B,OAAO,aAAa;QACxB,CAAC;IACD,MAAM,iBAAiB;IACvB,MAAM,gBAAgB;IACtB,MAAM,cAAc,CAAC,MAAQ,eAAe,IAAI,CAAC,QAAQ,cAAc,IAAI,CAAC;IAC5E,MAAM,eAAe,CAAC,MAAQ,cAAc,KAAK,KAAK,WAAW;IACjE,MAAM,eAAe,CAAC,MAAQ,YAAY,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO,aAAa;QAAK,KAAK,SAAS,IAAI;IAC5G,MAAM,QAAQ,CAAC;QACX,MAAM,MAAM,UAAU,QAAQ,CAAC;QAC/B,OAAO,CAAC,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,EAAE,WAAW;IAC3D;IACA,MAAM,WAAW,CAAC;QACd,MAAM,QAAQ,MAAM,WAAW,GAAG,IAAI,MAAM,WAAW,KAAK,IAAI,MAAM,WAAW,IAAI;QACrF,OAAO,UAAU;IACrB;IAEA,6BAA6B,GAC7B,MAAM,WAAW;IACjB,qFAAqF;IACrF,MAAM,YAAY;IAClB,MAAM,aAAa,CAAC,KAAK,OAAO,MAAM,QAAU,CAAC;YAC7C;YACA;YACA;YACA;QACJ,CAAC;IACD,MAAM,mBAAmB,CAAC,KAAK,OAAO,MAAM;QACxC,MAAM,IAAI,SAAS,KAAK;QACxB,MAAM,IAAI,SAAS,OAAO;QAC1B,MAAM,IAAI,SAAS,MAAM;QACzB,MAAM,IAAI,WAAW;QACrB,OAAO,WAAW,GAAG,GAAG,GAAG;IAC/B;IACA,MAAM,aAAa,CAAC;QAChB,MAAM,WAAW,SAAS,IAAI,CAAC;QAC/B,IAAI,aAAa,MAAM;YACnB,OAAO,SAAS,IAAI,CAAC,iBAAiB,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;QACjF;QACA,MAAM,YAAY,UAAU,IAAI,CAAC;QACjC,IAAI,cAAc,MAAM;YACpB,OAAO,SAAS,IAAI,CAAC,iBAAiB,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE;QAChG;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,MAAM,WAAW,CAAC,QAAU,aAAa,OACpC,OAAO,CAAC,IAAM,WAAW,OAAO,GAAG,CAAC,WACpC,UAAU,CAAC;YACZ,kEAAkE;YAClE,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,MAAM,GAAG;YAChB,OAAO,KAAK,GAAG;YACf,MAAM,gBAAgB,OAAO,UAAU,CAAC;YACxC,oCAAoC;YACpC,cAAc,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACzD,uGAAuG;YACvG,cAAc,SAAS,GAAG;YAC1B,cAAc,SAAS,GAAG;YAC1B,cAAc,QAAQ,CAAC,GAAG,GAAG,GAAG;YAChC,MAAM,OAAO,cAAc,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI;YACxD,MAAM,IAAI,IAAI,CAAC,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC,EAAE;YACjB,OAAO,SAAS,WAAW,GAAG,GAAG,GAAG;QACxC;IACA,MAAM,kBAAkB,CAAC,QAAU,WAAW,OACzC,GAAG,CAAC,UACJ,GAAG,CAAC,CAAC,IAAM,MAAM,EAAE,KAAK,EACxB,KAAK,CAAC;IAEX,MAAM,gBAAgB,CAAC,QAAQ,YAAY;QACvC,OAAO,CAAC;YACJ,MAAM,gBAAgB;YACtB,MAAM,SAAS,QAAQ;YACvB,MAAM,OAAO;gBACT,MAAM,gBAAgB,sBAAsB;gBAC5C,MAAM,YAAY,CAAC,OAAS,OAAO,SAAS,CAAC,KAAK,CAAC,YAAY;wBAAE,OAAO;oBAAY,GAAG,KAAK,GAAG,EAAE;gBACjG,mJAAmJ;gBACnJ,IAAI,QAAQ;oBACR,IAAI,SAAS,CAAC,CAAC,OAAO,eAAe;oBACrC,cAAc,GAAG,CAAC,OAAO,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,QAAU,IAAI,SAAS,CAAC,CAAC,QAAQ;gBACnG,OACK;oBACD,IAAI,SAAS,CAAC,OAAO,eAAe;oBACpC,cAAc,GAAG,CAAC,OAAO,SAAS,CAAC,aAAa,CAAC,YAAY,IAAI,SAAS,EAAE,OAAO;wBAAE,OAAO;oBAAY;gBAC5G;YACJ;YACA,mEAAmE;YACnE,OAAO,WAAW,GAAG,SAAS,OAAO,EAAE,CAAC,QAAQ;YAChD,OAAO,cAAc,KAAK;QAC9B;IACJ;IACA,MAAM,cAAc,CAAC,OAAS,kBAAkB,MAAM;IACtD,MAAM,iBAAiB,CAAC,QAAU,IAAI,OAAO,CAAC;YAC1C,wEAAwE;YACxE,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI;YACxC,IAAI,YAAY,OAAO;gBACnB,OAAO;oBACH;oBACA,OAAO,eAAe,KAAK,IAAI;gBACnC;YACJ,OACK;gBACD,OAAO;oBACH;oBACA,OAAO,KAAK,KAAK;gBACrB;YACJ;QACJ;IACA,MAAM,iBAAiB,CAAC;QACpB,IAAI,CAAC,UAAU,MAAM,EAAE;YACnB,OAAO,SAAS,IAAI;QACxB;QACA,OAAO,SAAS,IAAI,CAAC,eAAe;YAAC;gBAAE,MAAM;gBAAa,OAAO;YAAe;eAAM;SAAU;IACpG;IACA,MAAM,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,WAAa,IAAI,OAAO,CAAC;YACpE,wEAAwE;YACxE,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;YACpC,IAAI,YAAY,OAAO;gBACnB,OAAO;oBACH,MAAM;oBACN;oBACA,iBAAiB,IAAM,eAAe,QAAQ,KAAK,IAAI,EAAE,QAAQ;gBACrE;YACJ,OACK;gBACD,OAAO;oBACH;oBACA,MAAM;oBACN,UAAU,IAAM,SAAS,KAAK,KAAK;oBACnC,SAAS,cAAc,QAAQ,QAAQ,KAAK,KAAK;gBACrD;YACJ;QACJ;IACA,MAAM,sBAAsB,CAAC,QAAQ,QAAU,CAAC;YAC5C,OAAO,WAAW,CAAC,0BAA0B,OAAO;gBAAE,CAAC,MAAM,EAAE;YAAM;QACzE;IACA,MAAM,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC;YACzC,IAAI,YAAY,OAAO;gBACnB,OAAO;oBAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM,eAAe,KAAK,IAAI;oBAAE;iBAAE;YACzD,OACK;gBACD,OAAO,WAAW,KAAK,KAAK,IAAI;oBAAC;iBAAK,GAAG,EAAE;YAC/C;QACJ;IACA,MAAM,4BAA4B,CAAC,QAAQ,OAAO,QAAQ,WAAa,CAAC,WAAa,SAAS,eAAe,QAAQ,OAAO,QAAQ;IACpI,MAAM,iBAAiB,CAAC,QAAQ,WAAW;QACvC,MAAM,WAAW,IAAI,WAAW,CAAC,QAAU,CAAC;gBACxC,MAAM,MAAM,KAAK;gBACjB,OAAO,MAAM,SAAS,MAAM,KAAK,EAAE,KAAK;gBACxC,MAAM;YACV,CAAC;QACD,OAAO;YAAC;gBACA,MAAM;gBACN,WAAW;gBACX,UAAU;oBACN,QAAQ,SAAS,MAAM,GAAG,IAAI,WAAW;oBACzC,mBAAmB;gBACvB;gBACA,UAAU,CAAC;oBACP,MAAM,QAAQ,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK;oBACvD,OAAO,WAAW,CAAC,0BAA0B,OAAO;wBAAE,CAAC,MAAM,EAAE;oBAAM;gBACzE;YACJ;SAAE;IACV;IACA,MAAM,kBAAkB,CAAC,SAAW;YAChC,MAAM,cAAc,OAAO,iBAAiB,CAAC;YAC7C,MAAM,UAAU,gBAAgB,WAAW,SAAS;YACpD,OAAO,WAAW,CAAC,mBAAmB,OAAO;gBAAE,MAAM;YAAQ;QACjE;IACA,MAAM,qBAAqB,CAAC,SAAW;YACnC,MAAM,cAAc,OAAO,iBAAiB,CAAC;YAC7C,MAAM,UAAU,gBAAgB,OAAO,OAAO;YAC9C,OAAO,WAAW,CAAC,mBAAmB,OAAO;gBAAE,MAAM;YAAQ;QACjE;IAEA,MAAM,iBAAiB,CAAC,SAAW,eAAe,iBAAiB,SAC9D,GAAG,CAAC,CAAC,QAAU,CAAC;gBACjB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP;YACJ,CAAC;IACD,MAAM,WAAW;QACb;YACI,MAAM;YACN,MAAM;YACN,OAAO;QACX;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBAAE,MAAM;oBAAQ,OAAO;gBAAK;gBAC5B;oBAAE,MAAM;oBAAe,OAAO;gBAAK;aACtC;QACL;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBAAE,MAAM;oBAAQ,OAAO;gBAAG;gBAC1B;oBAAE,MAAM;oBAAO,OAAO;gBAAM;gBAC5B;oBAAE,MAAM;oBAAU,OAAO;gBAAM;gBAC/B;oBAAE,MAAM;oBAAa,OAAO;gBAAW;gBACvC;oBAAE,MAAM;oBAAgB,OAAO;gBAAW;aAC7C;QACL;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBAAE,MAAM;oBAAQ,OAAO;gBAAG;gBAC1B;oBAAE,MAAM;oBAAQ,OAAO;gBAAO;gBAC9B;oBAAE,MAAM;oBAAU,OAAO;gBAAS;gBAClC;oBAAE,MAAM;oBAAS,OAAO;gBAAQ;aACnC;QACL;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;QACX;KACH;IACD,MAAM,aAAa,CAAC,SAAW,SAAS,MAAM,CAAC,eAAe,QAAQ,OAAO;IAE7E,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,MAAM,mBAAmB;YAAC;gBAAE,MAAM;gBAAa,OAAO;YAAG;SAAE;QAC3D,MAAM,cAAc;YAChB;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,OAAO,iBAAiB,MAAM,CAAC,eAAe,qBAAqB;YACvE;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;SACH;QACD,MAAM,cAAc;YAChB,MAAM;YACN,MAAM;YACN,OAAO;QACX;QACA,MAAM,QAAQ,eAAe,SAAS;YAAC;SAAY,CAAC,MAAM,CAAC,eAAe;QAC1E,OAAO;YACH,OAAO;YACP,MAAM;YACN;QACJ;IACJ;IAEA,8DAA8D;IAC9D,+CAA+C;IAC/C,MAAM,SAAS,CAAC,QAAQ;QACpB,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,YAAY,CAAC,MAAM;YACrB,IAAI,SAAS,CAAC,SAAS,MAAM;QACjC;QACA,MAAM,WAAW,CAAC,MAAM;YACpB,IAAI,QAAQ,CAAC,SAAS,MAAM;QAChC;QACA,MAAM,YAAY,CAAC,YAAY;YAC3B,yCAAyC;YACzC,IAAI,UAAU,IAAI;gBACd,OAAO,SAAS,CAAC,MAAM,CAAC,YAAY;oBAAE,OAAO;gBAAK,GAAG,SAAS;YAClE,OACK;gBACD,OAAO,SAAS,CAAC,KAAK,CAAC,YAAY;oBAAE;gBAAM,GAAG;YAClD;QACJ;QACA,OAAO;YACH;YACA;YACA;QACJ;IACJ;IACA,MAAM,cAAc;QAChB;IACJ;IAEA,MAAM,WAAW,CAAC,QAAU,WAAW,OAAO,SAAS,gBAAgB,SAAS;IAChF,MAAM,wBAAwB,CAAC;QAC3B,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,OAAO;YACH,aAAa,SAAS,SAAS,gBAAgB,KAAK,CAAC;YACrD,aAAa,SAAS,SAAS,gBAAgB,KAAK,CAAC;YACrD,aAAa,SAAS,SAAS,gBAAgB,GAAG,CAAC,UAAU,KAAK,CAAC;YACnE,iBAAiB,SAAS,SAAS,oBAAoB,GAAG,CAAC,UAAU,KAAK,CAAC;QAC/E;IACJ;IACA,MAAM,kBAAkB,CAAC;QACrB,gDAAgD;QAChD,qEAAqE;QACrE,gDAAgD;QAChD,MAAM,WAAW,IAAI,CAAC,EAAE;QACxB,MAAM,iBAAiB,KAAK,KAAK,CAAC;QAClC,OAAO,gBAAgB,CAAC;YACpB,OAAO,KAAK,WAAW,CAAC;gBACpB,KAAK,OAAO,CAAC,WAAW;oBACpB,MAAM,kBAAkB,QAAQ,CAAC,IAAI;oBACrC,IAAI,oBAAoB,MAAM,QAAQ,SAAS;wBAC3C,IAAI,oBAAoB,WAAW;4BAC/B,QAAQ,CAAC,IAAI,GAAG,QAAQ,UAAU,iBAAiB;wBACvD;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,0GAA0G;IAC1G,8CAA8C;IAC9C,MAAM,eAAe,CAAC,SAAS,YAAY,QAAQ,MAAQ,KAAK,SAAS,CAAC,OAAS,CAAC,YAAY,OAAO,SAAS,CAAC,SAAS,CAAC,KAAK,aAAa,QAAQ,KAAK,CAAC;IAC3J,MAAM,gBAAgB,MAAM,cAAc;QAAC;QAAQ;QAAU;KAAQ,EAAE;IACvE,MAAM,gBAAgB,MAAM,cAAc;QAAC;QAAO;QAAU;KAAS,EAAE;IACvE,MAAM,0BAA0B,CAAC,QAAQ;QACrC,MAAM,QAAQ,iBAAiB;QAC/B,MAAM,QAAQ,qBAAqB;QACnC,MAAM,2BAA2B,IAAM,CAAC;gBACpC,aAAa,MAAM,OAAO,gBAAgB,KAAK,CAAC;gBAChD,aAAa,SAAS,MAAM,OAAO,gBAAgB,KAAK,CAAC;gBACzD,iBAAiB,SAAS,MAAM,OAAO,oBAAoB,KAAK,CAAC;YACrE,CAAC;QACD,MAAM,cAAc;YAChB,QAAQ;YACR,OAAO;YACP,aAAa;YACb,aAAa;YACb,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;QACZ;QACA,MAAM,YAAY;YACd,MAAM,cAAc,KAAK,CAAC,eAAe;YACzC,IAAI,mBAAmB,WAAW,aAAa;gBAC3C,OAAO;oBAAE,QAAQ;gBAAY;YACjC;YACA,OAAO,MAAM,OAAO,UAAU,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,GAAG,CAAC,SAAW,CAAC;oBAAE;gBAAO,CAAC;QAC1E;QACA,MAAM,WAAY,iBAAiB,6BAA6B,CAAC;QACjE,MAAM,4BAA4B;YAC9B,MAAM,UAAU,MAAM,OAAO,kBAAkB,EAAE,CAAC,MAAM,OAAO,gBAAgB,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,GAAG,CAAC,cAAgB,CAAC;oBAAE;gBAAY,CAAC;YACjI,MAAM,UAAU,MAAM,OAAO,kBAAkB,EAAE,CAAC,MAAM,OAAO,gBAAgB,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,GAAG,CAAC,cAAgB,CAAC;oBAAE;gBAAY,CAAC;YACjI,OAAO;gBACH,GAAG,OAAO;gBACV,GAAG,OAAO;YACd;QACJ;QACA,MAAM,OAAO;YACT,GAAG,WAAW;YACd,GAAG,KAAK;YACR,GAAG,KAAK;YACR,GAAG,QAAQ;YACX,GAAG,WAAW;YACd,GAAG,2BAA2B;QAClC;QACA,OAAO;IACX;IACA,MAAM,aAAa,CAAC,MAAQ,MAAM,aAAa,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAC9D,MAAM,SAAS;gBAAE,WAAW,QAAQ,IAAI,KAAK;YAAE;YAC/C,OAAO,YAAY,OAAO;QAC9B,GAAG,KAAK,CAAC;IACT,MAAM,8BAA8B,CAAC,QAAQ,KAAK;QAC9C,MAAM,YAAY,CAAC,KAAK;YACpB,6BAA6B;YAC7B,kEAAkE;YAClE,6EAA6E;YAC7E,uFAAuF;YACvF,MAAM,iBAAiB,SAAS,aAAa,OAAO,CAAC,MAAM;YAC3D,IAAI,mBAAmB,WAAW,eAAe,MAAM,IAAI;gBACvD,OAAO,eAAe,KAAK,CAAC;YAChC;YACA,OAAO,IAAI,SAAS,CAAC,KAAK,aAAa,oBAAoB,OAAO,GAAG,EAAE,KAAK,mBACrE,oBAAoB,OAAO,GAAG,EAAE,KAAK,aAAa;QAC7D;QACA,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,cAAc,mBAAmB,UACnC,IAAI,QAAQ,CAAC,KAAK,qBAAqB,IAAI,SAAS,CAAC,KAAK,iBAC1D,IAAI,SAAS,CAAC,KAAK,kBAAkB,IAAI,QAAQ,CAAC,KAAK;QAC3D,MAAM,cAAc,mBAAmB,UACnC,oBAAoB,KAAK,KAAK,cAAc,IAAI,SAAS,CAAC,KAAK,iBAC/D,IAAI,SAAS,CAAC,KAAK,kBAAkB,oBAAoB,KAAK,KAAK;QACvE,OAAO;YACH,OAAO,IAAI,QAAQ,CAAC,KAAK,YAAY,IAAI,SAAS,CAAC,KAAK;YACxD,QAAQ,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,SAAS,CAAC,KAAK;YAC1D,aAAa,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,cAAc;YAC5E,aAAa,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,cAAc;YAC5E,QAAQ,UAAU,KAAK;YACvB,SAAS,CAAC,CAAC,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,EAAE;YACxC,OAAO,IAAI,SAAS,CAAC,KAAK,SAAS;YACnC,OAAO,cAAc,QAAQ;YAC7B,GAAI,iBAAiB,sBAAsB,OAAO,CAAC,CAAC;QACxD;IACJ;IACA,MAAM,4BAA4B,CAAC,QAAQ,KAAK;QAC5C,MAAM,MAAM,OAAO,GAAG;QACtB,OAAO;YACH,QAAQ,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,SAAS,CAAC,KAAK;YAC1D,OAAO,IAAI,SAAS,CAAC,KAAK,SAAS;YACnC,MAAM,WAAW;YACjB,OAAO,cAAc,QAAQ;YAC7B,GAAI,oBAAoB,sBAAsB,OAAO,CAAC,CAAC;QAC3D;IACJ;IACA,MAAM,6BAA6B,CAAC,QAAQ,MAAM,oBAAoB;QAClE,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,SAAS,OAAO,KAAK,CAAC;QAC5B,MAAM,WAAW,CAAC,SAAS,QAAU,IAAI,QAAQ,CAAC,SAAS,UAAU,IAAI,SAAS,CAAC,SAAS;QAC5F,OAAO;YACH,OAAO,SAAS,QAAQ;YACxB,OAAO,IAAI,SAAS,CAAC,MAAM;YAC3B,UAAU,YAAY;YACtB,OAAO,IAAI,SAAS,CAAC,MAAM,SAAS;YACpC,QAAQ,cAAc,QAAQ;YAC9B,QAAQ,cAAc,QAAQ;YAC9B,GAAI,qBAAqB,sBAAsB,QAAQ,CAAC,CAAC;QAC7D;IACJ;IAEA,MAAM,mBAAmB,CAAC,OAAO;QAC7B,MAAM,YAAY,UAAU,SAAS,CAAC;QACtC,MAAM,WAAW,UAAU,SAAS,CAAC;QACrC,MAAM,WAAW,SAAS,UAAU,CAAC,QAAU,OAAO,OAAO,CAAC,QAAU,GAAG,MAAM,OAAO,EAAE;QAC1F,OAAO,IAAI,UAAU,CAAC,OAAS,CAAC;gBAC5B,SAAS,KAAK,OAAO,CAAC,GAAG;gBACzB,QAAQ,UAAU,WAAW,CAAC,WAAW,KAAK,MAAM,EAAE,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,CAAC,GAAG;YACtF,CAAC;IACL;IACA,MAAM,sBAAsB,CAAC,UAAU,aAAa,MAAM;QACtD,IAAI,aAAa,UAAU;YACvB,SAAS,SAAS,CAAC,SAAS,KAAK,KAAK;QAC1C;QACA,IAAI,aAAa,YAAY,KAAK,KAAK,KAAK,gBAAgB;YACxD,SAAS,SAAS,CAAC,SAAS,KAAK,KAAK;QAC1C;QACA,IAAI,aAAa,UAAU;YACvB,YAAY,QAAQ,CAAC,SAAS,YAAY,KAAK,KAAK;QACxD;IACJ;IACA,MAAM,wBAAwB,CAAC,UAAU,MAAM;QAC3C,IAAI,aAAa,oBAAoB;YACjC,SAAS,SAAS,CAAC,4BAA4B,KAAK,eAAe;QACvE;QACA,IAAI,aAAa,gBAAgB;YAC7B,SAAS,SAAS,CAAC,wBAAwB,KAAK,WAAW;QAC/D;QACA,IAAI,aAAa,gBAAgB;YAC7B,SAAS,SAAS,CAAC,wBAAwB,KAAK,WAAW;QAC/D;QACA,IAAI,aAAa,gBAAgB;YAC7B,SAAS,SAAS,CAAC,wBAAwB,YAAY,KAAK,WAAW;QAC3E;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ,OAAO,MAAM;QAC3C,MAAM,eAAe,MAAM,MAAM,KAAK;QACtC,OAAO,OAAO,CAAC;YACX,MAAM,UAAU,KAAK,OAAO;YAC5B,MAAM,6BAA6B,eAAe,SAAS;YAC3D,MAAM,WAAW,YAAY,MAAM,CAAC,QAAQ;YAC5C,MAAM,cAAc,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,YAAY,MAAM,CAAC,QAAQ,MAAM,KAAK,CAAC;YACpF,oBAAoB,UAAU,aAAa,MAAM;YACjD,IAAI,mBAAmB,SAAS;gBAC5B,sBAAsB,UAAU,MAAM;YAC1C;YACA,kBAAkB;YAClB,IAAI,WAAW,WAAW;gBACtB,SAAS,QAAQ,SAAS,KAAK,MAAM;YACzC;YACA,2BAA2B;YAC3B,IAAI,WAAW,WAAW;gBACtB,UAAU,QAAQ,SAAS,KAAK,MAAM;YAC1C;QACJ;IACJ;IACA,MAAM,uBAAuB,CAAC,QAAQ;QAClC,gGAAgG;QAChG,wFAAwF;QACxF,OAAO,WAAW,CAAC,oBAAoB,OAAO;YAAE,MAAM,KAAK,QAAQ;YAAE,WAAW;QAAK;IACzF;IACA,MAAM,gBAAgB,CAAC,QAAQ,OAAO,SAAS;QAC3C,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,MAAQ,OAAO,CAAC,IAAI,KAAK;QACnE,IAAI,KAAK,gBAAgB,KAAK,MAAM,MAAM,IAAI,GAAG;YAC7C,kFAAkF;YAClF,0EAA0E;YAC1E,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAClB,MAAM,gBAAgB,iBAAiB,OAAO;gBAC9C,wFAAwF;gBACxF,MAAM,gBAAgB,KAAK,OAAO,cAAc,CAAC,QAAQ,MAAQ,QAAQ,WAAW,QAAQ,eAAe;gBAC3G,MAAM,oBAAoB,IAAI,cAAc;gBAC5C,iDAAiD;gBACjD,IAAI,iBAAiB,IAAI,cAAc,UAAU;oBAC7C,iBAAiB,QAAQ,eAAe,MAAM,MAAM,KAAK;gBAC7D;gBACA,mDAAmD;gBACnD,IAAI,mBAAmB;oBACnB,qBAAqB,QAAQ;gBACjC;gBACA,kBAAkB,QAAQ,MAAM,GAAG,EAAE;oBACjC,WAAW;oBACX,OAAO;gBACX;YACJ;QACJ;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ,OAAO,SAAS;QAC9C,MAAM,OAAO,IAAI,OAAO;QACxB,IAAI,KAAK;QACT,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,cAAc,QAAQ,OAAO,SAAS;YACtC,OAAO,KAAK;QAChB;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ;QACvB,MAAM,YAAY,MAAM,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,QAAU,IAAI,iBAAiB,OAAO,QAAQ,CAAC,OAAS,2BAA2B,QAAQ,KAAK,OAAO,EAAE,mBAAmB,SAAS,KAAK,MAAM;QACvL,OAAO,gBAAgB,UAAU,QAAQ;IAC7C;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,QAAQ,sBAAsB;QACpC,6CAA6C;QAC7C,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB;QACJ;QACA,MAAM,OAAO,UAAU,QAAQ;QAC/B,MAAM,iBAAiB;YACnB,MAAM;YACN,MAAM;gBACF;oBACI,OAAO;oBACP,MAAM;oBACN,OAAO,WAAW;gBACtB;gBACA,eAAe,QAAQ;aAC1B;QACL;QACA,MAAM,cAAc;YAChB,MAAM;YACN,OAAO;gBACH;oBACI,MAAM;oBACN,SAAS;oBACT,OAAO,WAAW;gBACtB;aACH;QACL;QACA,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM,mBAAmB,UAAU,iBAAiB;YACpD,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;YACb,UAAU,MAAM,kBAAkB,QAAQ,OAAO;QACrD;IACJ;IAEA,MAAM,eAAe,CAAC,SAAW,eAAe,gBAAgB,SAC3D,GAAG,CAAC,CAAC,QAAU,CAAC;gBACjB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP;YACJ,CAAC;IACD,MAAM,eAAe;QACjB;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBAAE,MAAM;oBAAU,OAAO;gBAAS;gBAClC;oBAAE,MAAM;oBAAQ,OAAO;gBAAO;gBAC9B;oBAAE,MAAM;oBAAU,OAAO;gBAAS;aACrC;QACL;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBAAE,MAAM;oBAAQ,OAAO;gBAAG;gBAC1B;oBAAE,MAAM;oBAAQ,OAAO;gBAAO;gBAC9B;oBAAE,MAAM;oBAAU,OAAO;gBAAS;gBAClC;oBAAE,MAAM;oBAAS,OAAO;gBAAQ;aACnC;QACL;QACA;YACI,OAAO;YACP,MAAM;YACN,MAAM;QACV;KACH;IACD,MAAM,aAAa,CAAC,SAAW,aAAa,MAAM,CAAC,aAAa,QAAQ,OAAO;IAE/E,MAAM,oBAAoB,CAAC,UAAU,MAAM;QACvC,IAAI,aAAa,YAAY,KAAK,KAAK,KAAK,gBAAgB;YACxD,SAAS,SAAS,CAAC,SAAS,KAAK,KAAK;QAC1C;QACA,IAAI,aAAa,WAAW;YACxB,SAAS,QAAQ,CAAC,UAAU,YAAY,KAAK,MAAM;QACvD;IACJ;IACA,MAAM,sBAAsB,CAAC,UAAU,MAAM;QACzC,IAAI,aAAa,oBAAoB;YACjC,SAAS,QAAQ,CAAC,oBAAoB,KAAK,eAAe;QAC9D;QACA,IAAI,aAAa,gBAAgB;YAC7B,SAAS,QAAQ,CAAC,gBAAgB,KAAK,WAAW;QACtD;QACA,IAAI,aAAa,gBAAgB;YAC7B,SAAS,QAAQ,CAAC,gBAAgB,KAAK,WAAW;QACtD;IACJ;IACA,MAAM,iBAAiB,CAAC,QAAQ,MAAM,MAAM;QACxC,MAAM,cAAc,KAAK,MAAM,KAAK;QACpC,MAAM,6BAA6B,cAAc,SAAS;QAC1D,OAAO,MAAM,CAAC;YACV,MAAM,WAAW,WAAW,aAAa,OAAO,CAAC,SAAS;YAC1D,MAAM,WAAW,YAAY,MAAM,CAAC,QAAQ;YAC5C,kBAAkB,UAAU,MAAM;YAClC,IAAI,kBAAkB,SAAS;gBAC3B,oBAAoB,UAAU,MAAM;YACxC;YACA,0EAA0E;YAC1E,IAAI,WAAW,WAAW;gBACtB,OAAO,UAAU,CAAC;oBACd,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,EAAE,UAAU;gBAC5C;YACJ;YACA,IAAI,WAAW,UAAU;gBACrB,SAAS,QAAQ,QAAQ,KAAK,KAAK;YACvC;QACJ;IACJ;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,gGAAgG;QAChG,wFAAwF;QACxF,OAAO,WAAW,CAAC,mBAAmB,OAAO;YAAE,MAAM,KAAK,IAAI;YAAE,WAAW;QAAK;IACpF;IACA,MAAM,eAAe,CAAC,QAAQ,MAAM,SAAS;QACzC,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,MAAQ,OAAO,CAAC,IAAI,KAAK;QACnE,IAAI,KAAK,gBAAgB,GAAG;YACxB,MAAM,eAAe,IAAI,cAAc;YACvC,wEAAwE;YACxE,MAAM,gBAAgB,eAAe,KAAK,gBAAgB,IAAI;YAC9D,gDAAgD;YAChD,IAAI,eAAe;gBACf,eAAe,QAAQ,MAAM,MAAM,MAAM,KAAK;YAClD;YACA,kDAAkD;YAClD,IAAI,cAAc;gBACd,mBAAmB,QAAQ;YAC/B;YACA,MAAM,aAAa,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,QAAU,kBAAkB,QAAQ,MAAM,GAAG,EAAE;oBACtF,WAAW;oBACX,OAAO;gBACX;QACJ;IACJ;IACA,MAAM,kBAAkB,CAAC,QAAQ,MAAM,SAAS;QAC5C,MAAM,OAAO,IAAI,OAAO;QACxB,IAAI,KAAK;QACT,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,aAAa,QAAQ,MAAM,SAAS;YACpC,OAAO,KAAK;QAChB;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,OAAO,qBAAqB,kBAAkB,SAAS,SAAS,QAAQ;QAC9E,4CAA4C;QAC5C,IAAI,KAAK,MAAM,KAAK,GAAG;YACnB;QACJ;QACA,uDAAuD;QACvD,MAAM,WAAW,IAAI,MAAM,CAAC,SAAW,0BAA0B,QAAQ,OAAO,GAAG,EAAE,kBAAkB;QACvG,MAAM,OAAO,gBAAgB;QAC7B,MAAM,iBAAiB;YACnB,MAAM;YACN,MAAM;gBACF;oBACI,OAAO;oBACP,MAAM;oBACN,OAAO,WAAW;gBACtB;gBACA,eAAe,QAAQ;aAC1B;QACL;QACA,MAAM,cAAc;YAChB,MAAM;YACN,OAAO;gBACH;oBACI,MAAM;oBACN,SAAS;oBACT,OAAO,WAAW;gBACtB;aACH;QACL;QACA,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM,kBAAkB,UAAU,iBAAiB;YACnD,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;YACb,UAAU,MAAM,iBAAiB,QAAQ,IAAI,MAAM,CAAC,IAAM,EAAE,GAAG,GAAG;QACtE;IACJ;IAEA,MAAM,WAAW,CAAC,QAAQ,SAAS;QAC/B,MAAM,mBAAmB,CAAC,iBAAiB,EAAE,GAAG;YAC5C;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;YACf;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;YACf;SACH;QACD,MAAM,cAAc;YAChB;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;SACH;QACD,MAAM,kBAAkB,qBAAqB,UAAU;YACnD;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;YACf;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;YACf;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;YACX;YACA;gBACI,MAAM;gBACN,OAAO;gBACP,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;wBACN,OAAO;oBACX;iBACH;YACL;SACH,GAAG,EAAE;QACN,MAAM,gBAAgB;YAClB;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,OAAO;oBACH;wBAAE,MAAM;wBAAQ,OAAO;oBAAG;oBAC1B;wBAAE,MAAM;wBAAQ,OAAO;oBAAO;oBAC9B;wBAAE,MAAM;wBAAU,OAAO;oBAAS;oBAClC;wBAAE,MAAM;wBAAS,OAAO;oBAAQ;iBACnC;YACL;SACH;QACD,MAAM,gBAAgB,QAAQ,MAAM,GAAG,IAAI;YACvC;gBACI,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,OAAO;YACX;SACH,GAAG,EAAE;QACN,OAAO,iBAAiB,MAAM,CAAC,aAAa,MAAM,CAAC,iBAAiB,MAAM,CAAC,eAAe,MAAM,CAAC;IACrG;IAEA,6EAA6E;IAC7E,MAAM,YAAY,CAAC,KAAK,KAAK,MAAM;QAC/B,IAAI,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,MAAM;YAC9C,IAAI,SAAS,SAAS,cAAc,QAAQ;gBACxC,IAAI,QAAQ,CAAC,KAAK,MAAM;YAC5B,OACK;gBACD,IAAI,SAAS,CAAC,KAAK;YACvB;QACJ,OACK;YACD,IAAI,IAAI,QAAQ,EAAE;gBACd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAK;oBAC1C,UAAU,KAAK,IAAI,QAAQ,CAAC,EAAE,EAAE,MAAM;gBAC1C;YACJ;QACJ;IACJ;IACA,MAAM,qBAAqB,CAAC,QAAQ,UAAU,MAAM;QAChD,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,QAAQ,CAAC;QACf,MAAM,SAAS,CAAC;QAChB,MAAM,uBAAuB,mBAAmB;QAChD,MAAM,wBAAwB,oBAAoB;QAClD,MAAM,eAAe,WAAW,KAAK,MAAM,MAAM;QACjD,IAAI,CAAC,YAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,gBAAgB;YAC3D,MAAM,KAAK,GAAG,KAAK,KAAK;QAC5B;QACA,OAAO,MAAM,GAAG,YAAY,KAAK,MAAM;QACvC,IAAI,sBAAsB;YACtB,OAAO,KAAK,GAAG,YAAY,KAAK,KAAK;QACzC,OACK,IAAI,IAAI,SAAS,CAAC,UAAU,UAAU;YACvC,MAAM,KAAK,GAAG,eAAe,KAAK,KAAK;QAC3C;QACA,IAAI,sBAAsB;YACtB,IAAI,cAAc;gBACd,MAAM,MAAM,GAAG;gBACf,MAAM,CAAC,eAAe,GAAG;YAC7B,OACK;gBACD,MAAM,CAAC,eAAe,GAAG,YAAY,KAAK,MAAM;gBAChD,MAAM,MAAM,GAAG;YACnB;YACA,MAAM,CAAC,iBAAiB,GAAG,YAAY,KAAK,WAAW;QAC3D,OACK;YACD,MAAM,MAAM,GAAG,eAAe,IAAI,KAAK,MAAM;YAC7C,MAAM,WAAW,GAAG,KAAK,WAAW;YACpC,MAAM,WAAW,GAAG,KAAK,WAAW;QACxC;QACA,4HAA4H;QAC5H,IAAI,wBAAwB,SAAS,QAAQ,EAAE;YAC3C,MAAM,aAAa,CAAC;YACpB,IAAI,cAAc;gBACd,UAAU,CAAC,eAAe,GAAG;YACjC,OACK,IAAI,kBAAkB,MAAM,EAAE;gBAC/B,UAAU,CAAC,eAAe,GAAG,YAAY,KAAK,MAAM;YACxD;YACA,IAAI,kBAAkB,WAAW,EAAE;gBAC/B,WAAW,OAAO,GAAG,YAAY,KAAK,WAAW;YACrD;YACA,IAAI,yBAAyB,kBAAkB,WAAW,EAAE;gBACxD,UAAU,CAAC,eAAe,GAAG,KAAK,WAAW;YACjD;YACA,IAAI,CAAC,UAAU,aAAa;gBACxB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,IAAK;oBAC/C,UAAU,KAAK,SAAS,QAAQ,CAAC,EAAE,EAAE;gBACzC;YACJ;QACJ;QACA,IAAI,uBAAuB;YACvB,MAAM,UAAU;YAChB,MAAM,CAAC,mBAAmB,GAAG,QAAQ,eAAe;YACpD,MAAM,CAAC,eAAe,GAAG,QAAQ,WAAW;YAC5C,MAAM,CAAC,eAAe,GAAG,QAAQ,WAAW;QAChD;QACA,IAAI,SAAS,CAAC,UAAU;YAAE,GAAG,iBAAiB,OAAO;YAAE,GAAG,MAAM;QAAC;QACjE,IAAI,UAAU,CAAC,UAAU;YAAE,GAAG,qBAAqB,OAAO;YAAE,GAAG,KAAK;QAAC;IACzE;IACA,MAAM,oBAAoB,CAAC,QAAQ,UAAU,SAAS;QAClD,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,OAAO,IAAI,OAAO;QACxB,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,MAAQ,OAAO,CAAC,IAAI,KAAK;QACnE,IAAI,KAAK;QACT,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,IAAI,CAAC,UAAU;gBACX,MAAM,OAAO,MAAM,KAAK,IAAI,EAAE,KAAK,CAAC;gBACpC,MAAM,OAAO,MAAM,KAAK,IAAI,EAAE,KAAK,CAAC;gBACpC,kCAAkC;gBAClC,OAAO,WAAW,CAAC,kBAAkB,OAAO;oBAAE;oBAAM,SAAS;gBAAK;gBAClE,WAAW,iBAAiB,kBAAkB,SAAS,UAAU,SAC5D,IAAI,CAAC,CAAC,OAAS,MAAM,MAAM,UAAU,UACrC,GAAG,CAAC,CAAC,QAAU,MAAM,GAAG,EACxB,QAAQ;YACjB;YACA,IAAI,KAAK,gBAAgB,GAAG;gBACxB,MAAM,2BAA2B;oBAC7B,QAAQ,IAAI,cAAc;oBAC1B,aAAa,IAAI,cAAc;oBAC/B,aAAa,IAAI,cAAc;gBACnC;gBACA,mBAAmB,QAAQ,UAAU,MAAM;gBAC3C,wBAAwB;gBACxB,MAAM,aAAa,IAAI,MAAM,CAAC,WAAW,SAAS,CAAC,EAAE;gBACrD,IAAI,cAAc,CAAC,KAAK,OAAO,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;oBAC5D,OAAO,WAAW,CAAC;gBACvB;gBACA,SAAS,QAAQ,UAAU,KAAK,KAAK;YACzC;YACA,OAAO,KAAK;YACZ,OAAO,SAAS;YAChB,IAAI,KAAK,gBAAgB,GAAG;gBACxB,MAAM,kBAAkB,IAAI,cAAc;gBAC1C,2EAA2E;gBAC3E,MAAM,gBAAgB,kBAAkB,KAAK,gBAAgB,IAAI;gBACjE,kBAAkB,QAAQ,UAAU;oBAAE,WAAW;oBAAiB,OAAO;gBAAc;YAC3F;QACJ;IACJ;IACA,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,MAAM,OAAO,GAAG;QACtB,IAAI;QACJ,IAAI,OAAO,wBAAwB,QAAQ,oBAAoB;QAC/D,uCAAuC;QACvC,8HAA8H;QAC9H,iEAAiE;QACjE,sEAAsE;QACtE,2GAA2G;QAC3G,IAAI,gBAAgB;YAChB,8GAA8G;YAC9G,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,IAAI,oBAAoB,SAAS;gBAC7B,KAAK,WAAW,GAAG;gBACnB,KAAK,WAAW,GAAG;gBACnB,KAAK,eAAe,GAAG;YAC3B;QACJ,OACK;YACD,WAAW,IAAI,SAAS,CAAC,OAAO,SAAS,CAAC,QAAQ,IAAI,SAAS,OAAO,OAAO;YAC7E,IAAI,UAAU;gBACV,0CAA0C;gBAC1C,OAAO,4BAA4B,QAAQ,UAAU,oBAAoB;YAC7E,OACK;gBACD,sHAAsH;gBACtH,IAAI,oBAAoB,SAAS;oBAC7B,KAAK,WAAW,GAAG;oBACnB,KAAK,WAAW,GAAG;oBACnB,KAAK,eAAe,GAAG;gBAC3B;YACJ;QACJ;QACA,MAAM,UAAU,eAAe,kBAAkB;QACjD,IAAI,QAAQ,MAAM,IAAI;YAClB,IAAI,KAAK,KAAK,EAAE;gBACZ,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,2BAA2B;YAC/D;QACJ;QACA,MAAM,eAAe;YACjB,MAAM;YACN,SAAS;YACT,OAAO,SAAS,QAAQ,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC/C;QACA,MAAM,kBAAkB,IAAM,CAAC;gBAC3B,MAAM;gBACN,OAAO;oBAAC;iBAAa;YACzB,CAAC;QACD,MAAM,eAAe,IAAM,CAAC;gBACxB,MAAM;gBACN,MAAM;oBACF;wBACI,OAAO;wBACP,MAAM;wBACN,OAAO;4BAAC;yBAAa;oBACzB;oBACA,eAAe,QAAQ;iBAC1B;YACL,CAAC;QACD,MAAM,aAAa,oBAAoB,UAAU,iBAAiB;QAClE,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU,MAAM,mBAAmB,QAAQ,UAAU;YACrD,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;QACjB;IACJ;IAEA,MAAM,mBAAmB,CAAC;QACtB,MAAM,YAAY,CAAC;YACf,IAAI,oBAAoB,kBAAkB,UAAU;gBAChD;YACJ;QACJ;QACA,2BAA2B;QAC3B,KAAK;YACD,+EAA+E;YAC/E,eAAe,MAAM,MAAM,QAAQ;YACnC,kBAAkB,MAAM,QAAQ;YAChC,mBAAmB,MAAM,QAAQ;YACjC,sBAAsB,MAAM,MAAM,QAAQ;QAC9C,GAAG,CAAC,MAAM,OAAS,OAAO,UAAU,CAAC,MAAM,IAAM,UAAU;IAC/D;IAEA;;;;;KAKC,GACD,MAAM,SAAS,CAAC,OAAS,CAAC;YACtB,SAAS;YACT,UAAU,SAAS,IAAI;YACvB,YAAY,SAAS,IAAI;YACzB,WAAW;gBAAC;aAAK;QACrB,CAAC;IACD,MAAM,UAAU,CAAC,eAAe,OAAO,OAAS,CAAC;YAC7C,SAAS;YACT,UAAU,SAAS,OAAO,eAAe;YACzC,YAAY,WAAW;YACvB,WAAW,UAAU;QACzB,CAAC;IAED,MAAM,sBAAsB,CAAC;QACzB,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,iBAAiB,KAAK,EAAE;QAC9B,IAAI,mBAAmB,SAAS,IAAI;QACpC,MAAM,YAAY,MAAM;QACxB,MAAM,yBAAyB,CAAC,MAAQ,iBAAiB,MAAM,CAAC,CAAC,UAAY,CAAC,OAAO,CAAC,IAAI;QAC1F,MAAM,WAAW,IAAM,0BAA0B,kBAAkB,SAAS,UAAU;QACtF,MAAM,SAAS,IAAM,0BAA0B,gBAAgB,SAAS,UAAU;QAClF,MAAM,cAAc,IAAM,WAAW,IAAI,CAAC,CAAC,qBAAuB,QAAQ,MAAM,MAAM,qBAAqB,SAAS,IAAI,CAAC,QAAQ,CAAC,YAAY;oBAC1I,IAAI,GAAG,YAAY,WAAW;wBAC1B,IAAI,UAAU,qBAAqB;4BAC/B,OAAO,SAAS,IAAI,CAAC,OAAO;wBAChC,OACK;4BACD,OAAO,SAAS,IAAI,CAAC,QAAQ,sBAAsB,SAAS,YAAY;wBAC5E;oBACJ;oBACA,OAAO,SAAS,IAAI;gBACxB;QACA,MAAM,sBAAsB,CAAC;YACzB,MAAM,WAAW,MAAM,QAAQ,OAAO;YACtC,OAAO,SAAS,GAAG,CAAC,CAAC;gBACjB,MAAM,YAAY,UAAU,SAAS,CAAC;gBACtC,MAAM,gBAAgB,QAAQ,WAAW,SAAS,KAAK,CAAC,EAAE;gBAC1D,MAAM,SAAS,MAAM,eAAe,CAAC,KAAK;oBACtC,IAAI,KAAK,QAAQ,EAAE;wBACf,IAAI,KAAK,GAAG;wBACZ,IAAI,KAAK,MAAM,KAAK,GAAG;4BACnB,IAAI,OAAO,GAAG;wBAClB,OACK,IAAI,KAAK,MAAM,GAAG,KAAK,OAAO,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE;4BAC3D,IAAI,MAAM,GAAG;wBACjB;oBACJ;oBACA,OAAO;gBACX,GAAG;oBAAE,OAAO;oBAAO,SAAS;oBAAO,QAAQ;gBAAM;gBACjD,OAAO;oBACH,WAAW,mBAAmB,WAAW,SAAS,MAAM;oBACxD,aAAa,qBAAqB,WAAW,SAAS,MAAM;oBAC5D;gBACJ;YACJ;QACJ;QACA,MAAM,eAAe;YACjB,oBAAoB;YACpB,QAAQ,GAAG,CAAC,OAAO;YACnB,8BAA8B;YAC9B,mBAAmB,QAAQ,GAAG,GAAG,IAAI,CAAC;YACtC,0BAA0B;YAC1B,OAAO,eAAe,GAAG,IAAI;QACjC;QACA,MAAM,eAAe,CAAC;YAClB,+CAA+C;YAC/C;YACA,yEAAyE;YACzE,eAAe,GAAG,CAAC,eAAe,GAAG,GAAG,MAAM,CAAC;gBAAC;aAAQ;YACxD,OAAO;gBACH,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,IAAI,CAAC,IAAM,MAAM;YACnE;QACJ;QACA,MAAM,UAAU,CAAC,KAAK,aAAe,aAAa,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;oBACvE,IAAI,UAAU,CAAC;gBACnB,GAAG,CAAC;oBACA,IAAI,UAAU,CAAC,CAAC,WAAW,YAAY,OAAO,SAAS,CAAC,UAAU;gBACtE;QACA,MAAM,oBAAoB,CAAC,KAAK,YAAY,WAAa,aAAa,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;oBAC3F,IAAI,UAAU,CAAC;oBACf,IAAI,SAAS,CAAC;gBAClB,GAAG,CAAC;oBACA,IAAI,UAAU,CAAC,CAAC,WAAW,YAAY,OAAO,SAAS,CAAC,UAAU;oBAClE,IAAI,SAAS,CAAC,SAAS;gBAC3B;QACA,MAAM,uBAAuB,CAAC,gBAAkB,iBAAiB,MAAM,CAAC,CAAC,UAAY,QAAQ,MAAM,CAAC,cAAc;QAClH,MAAM,eAAe,CAAC,MAAQ,QAAQ,KAAK,CAAC,IAAM;QAClD,MAAM,mBAAmB,CAAC,MAAQ,QAAQ,KAAK,CAAC,UAAY,UAAU,QAAQ,OAAO;QACrF,MAAM,gBAAgB,CAAC,gBAAkB,CAAC,MAAQ,QAAQ,KAAK,CAAC,UAAY,UAAU,QAAQ,OAAO,KAAK,qBAAqB;QAC/H,MAAM,mBAAmB,CAAC,mBAAqB,CAAC,MAAQ,QAAQ,KAAK,CAAC,UAAY,UAAU,QAAQ,OAAO,KAAK,mBAAmB,MAAM;QACzI,MAAM,yBAAyB,CAAC,kBAAkB,gBAAkB,CAAC,MAAQ,QAAQ,KAAK,CAAC,UAAY,UAAU,QAAQ,OAAO,KAAK,mBAAmB,MAAM,MAAM,qBAAqB;QACzL,MAAM,mBAAmB,CAAC,MAAQ,QAAQ,KAAK,CAAC,WAAa,uBAAuB;QACpF,MAAM,qBAAqB,CAAC,MAAQ,QAAQ,KAAK,CAAC,WAAa,uBAAuB;QACtF,MAAM,0BAA0B,CAAC;YAC7B,OAAO,kBAAkB,KAAK,OAAO,CAAC;gBAClC,MAAM,WAAW,MAAM,QAAQ,OAAO,EAAE,UAAU;gBAClD,OAAO,SAAS,MAAM,CAAC,CAAC,QAAU,MAAM,OAAO;YACnD;QACJ;QACA,MAAM,sBAAsB,CAAC,SAAS,aAAe,CAAC;gBAClD,OAAO,kBAAkB,KAAK,CAAC,UAAY,UAAU,QAAQ,OAAO,GAAG,IAAM,OAAO,iBAAiB,CAAC,aAAa;YACvH;QACA,MAAM,yBAAyB,oBAAoB,mBAAmB;QACtE,MAAM,4BAA4B,oBAAoB,mBAAmB;QACzE,OAAO,EAAE,CAAC,8CAA8C;QACxD,OAAO;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,SAAS,QAAQ,GAAG;QACxB;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC;;;;KAIC,GACD,MAAM,gBAAgB;IACtB,MAAM,eAAe,gBAAgB;IACrC,MAAM,kBAAkB,gBAAgB;IACxC,MAAM,UAAU,CAAC;QACb,IAAI;QACJ,MAAM,QAAQ,CAAC,KAAK,OAAO,IAAI,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACtE,OAAO,QAAQ,OAAO,CAAC,OAAS,SAAS,IAAI,CAAC,KAAK,OAAO,CAAC;IAC/D;IACA,MAAM,UAAU,IAAM,QAAQ;IAC9B,MAAM,aAAa,IAAM,QAAQ;IAEjC,MAAM,oBAAoB,CAAC,SAAW,CAAC;YACnC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,SAAS,kBAAkB;YAC3B,OAAO,CAAC,WAAa,SAAS;QAClC;QACA,MAAM,MAAM,CAAC,UAAY,IAAM,OAAO,WAAW,CAAC;QAClD,oEAAoE;QACpE,MAAM,wBAAwB,CAAC,MAAM;YACjC,IAAI,OAAO,qBAAqB,CAAC,KAAK,OAAO,GAAG;gBAC5C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM;oBAC/B,GAAG,IAAI;oBACP,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,OAAO;gBAC1E;YACJ;QACJ;QACA,oEAAoE;QACpE,MAAM,8BAA8B,CAAC,MAAM;YACvC,IAAI,OAAO,qBAAqB,CAAC,KAAK,OAAO,GAAG;gBAC5C,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM;oBACrC,GAAG,IAAI;oBACP,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,OAAO;gBAC1E;YACJ;QACJ;QACA,sBAAsB,cAAc;YAChC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,YAAY;QAC1C;QACA,sBAAsB,eAAe;YACjC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,YAAY;QAC1C;QACA,sBAAsB,kBAAkB;YACpC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,mBAAmB;YACrC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,mBAAmB;YACrC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,kBAAkB;QAChD;QACA,sBAAsB,wBAAwB;YAC1C,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,uBAAuB;YACzC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,kBAAkB;YACpC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,iBAAiB;YACnC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,wBAAwB;YAC1C,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,aAAa,CAAC,UAAU,yBAAyB;QAC/E;QACA,sBAAsB,uBAAuB;YACzC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,aAAa,CAAC,SAAS,wBAAwB;QAC7E;QACA,sBAAsB,kBAAkB;YACpC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;QAC3E;QACA,sBAAsB,eAAe;YACjC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,gBAAgB;YAClC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB;QAC9C;QACA,sBAAsB,uBAAuB;YACzC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB,CAAC;QAC/C;QACA,sBAAsB,sBAAsB;YACxC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,gBAAgB,CAAC;QAC/C;QACA,sBAAsB,eAAe;YACjC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;QAC3E;QACA,sBAAsB,gBAAgB;YAClC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;QAC3E;QACA,sBAAsB,uBAAuB;YACzC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,sBAAsB,CAAC,YAAY,UAAU,yBAAyB;QACpG;QACA,sBAAsB,sBAAsB;YACxC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,sBAAsB,CAAC,YAAY,SAAS,wBAAwB;QAClG;QACA,sBAAsB,qBAAqB;YACvC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,kBAAkB;QAC/B;QACA,MAAM,iBAAiB,eAAe,kBAAkB;QACxD,IAAI,eAAe,MAAM,KAAK,KAAK,OAAO,qBAAqB,CAAC,wBAAwB;YACpF,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc;gBAC3C,MAAM;gBACN,SAAS;gBACT,OAAO,0BAA0B,QAAQ,gBAAgB,cAAc,CAAC,QAAU,OAAO,WAAW,CAAC,uBAAuB,OAAO;gBACnI,SAAS,iBAAiB,YAAY;YAC1C;QACJ;QACA,MAAM,qBAAqB,eAAe,iBAAiB;QAC3D,IAAI,mBAAmB,MAAM,KAAK,KAAK,OAAO,qBAAqB,CAAC,4BAA4B;YAC5F,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB;gBAC/C,MAAM;gBACN,SAAS;gBACT,OAAO,0BAA0B,QAAQ,oBAAoB,kBAAkB,CAAC,QAAU,OAAO,WAAW,CAAC,2BAA2B,OAAO;gBAC/I,SAAS,iBAAiB,gBAAgB;YAC9C;QACJ;QACA,oEAAoE;QACpE,IAAI,OAAO,qBAAqB,CAAC,2BAA2B;YACxD,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,mBAAmB;gBAChD,MAAM;gBACN,SAAS;gBACT,OAAO,0BAA0B,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ;gBACpH,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,wBAAwB;gBACrD,MAAM;gBACN,SAAS;gBACT,OAAO,0BAA0B,QAAQ,qBAAqB,SAAS,wBAAwB,oBAAoB,QAAQ;gBAC3H,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,wBAAwB;gBACrD,MAAM;gBACN,SAAS;gBACT,OAAO,0BAA0B,QAAQ,qBAAqB,SAAS,wBAAwB,oBAAoB,QAAQ;gBAC3H,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,4BAA4B;gBACzD,MAAM;gBACN,SAAS;gBACT,OAAO,CAAC,WAAa,SAAS,eAAe,QAAQ,2BAA2B,SAAS;gBACzF,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,wBAAwB;gBACrD,MAAM;gBACN,SAAS;gBACT,OAAO,CAAC,WAAa,SAAS,eAAe,QAAQ,uBAAuB,SAAS;gBACrF,SAAS,iBAAiB,gBAAgB;YAC9C;QACJ;QACA,4BAA4B,gBAAgB;YACxC,SAAS;YACT,MAAM;YACN,SAAS;YACT,SAAS,iBAAiB,uBAAuB;QACrD;QACA,4BAA4B,kBAAkB;YAC1C,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU,gBAAgB;YAC1B,SAAS,iBAAiB,sBAAsB;QACpD;QACA,4BAA4B,kBAAkB;YAC1C,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU,mBAAmB;YAC7B,SAAS,iBAAiB,yBAAyB;QACvD;IACJ;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,kBAAkB,CAAC,QAAU,OAAO,GAAG,CAAC,EAAE,CAAC,OAAO,YAAY,OAAO,OAAO,GAAG,QAAQ,CAAC,UAAU,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,UAAU;QAC9I,MAAM,UAAU,WAAW;QAC3B,IAAI,QAAQ,MAAM,GAAG,GAAG;YACpB,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS;gBAC1C,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,UAAU;YACd;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,eAAe,CAAC,QAAQ;QAC1B,MAAM,MAAM,CAAC,UAAY,IAAM,OAAO,WAAW,CAAC;QAClD,oEAAoE;QACpE,MAAM,sBAAsB,CAAC,MAAM;YAC/B,IAAI,OAAO,qBAAqB,CAAC,KAAK,OAAO,GAAG;gBAC5C,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM;oBACjC,GAAG,IAAI;oBACP,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,OAAO;gBAC1E;gBACA,OAAO;YACX,OACK;gBACD,OAAO;YACX;QACJ;QACA,oEAAoE;QACpE,MAAM,4BAA4B,CAAC,MAAM;YACrC,IAAI,OAAO,qBAAqB,CAAC,KAAK,OAAO,GAAG;gBAC5C,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM;oBACvC,GAAG,IAAI;oBACP,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,OAAO;gBAC1E;YACJ;QACJ;QACA,MAAM,oBAAoB,CAAC;YACvB,OAAO,WAAW,CAAC,kBAAkB,OAAO;gBACxC,MAAM,KAAK,OAAO;gBAClB,SAAS,KAAK,UAAU;YAC5B;QACJ;QACA,MAAM,kBAAkB;YACpB,oBAAoB,wBAAwB;gBACxC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,uBAAuB;gBACvC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,kBAAkB;gBAClC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,iBAAiB;gBACjC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,eAAe;gBAC/B,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,gBAAgB;gBAChC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,uBAAuB;gBACvC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB,CAAC;YAC/C;YACA,oBAAoB,sBAAsB;gBACtC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB,CAAC;YAC/C;SACH;QACD,MAAM,qBAAqB;YACvB,oBAAoB,2BAA2B;gBAC3C,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,aAAa,CAAC,UAAU,yBAAyB;YAC/E;YACA,oBAAoB,0BAA0B;gBAC1C,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,aAAa,CAAC,SAAS,wBAAwB;YAC7E;YACA,oBAAoB,qBAAqB;gBACrC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;YAC3E;YACA,oBAAoB,kBAAkB;gBAClC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;YAC3E;YACA,oBAAoB,mBAAmB;gBACnC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,aAAa,CAAC,QAAQ,uBAAuB;YAC3E;YACA,oBAAoB,0BAA0B;gBAC1C,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,sBAAsB,CAAC,YAAY,UAAU,yBAAyB;YACpG;YACA,oBAAoB,yBAAyB;gBACzC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,sBAAsB,CAAC,YAAY,SAAS,wBAAwB;YAClG;SACH;QACD,MAAM,mBAAmB;YACrB,oBAAoB,kBAAkB;gBAClC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,mBAAmB;gBACnC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,oBAAoB,mBAAmB;gBACnC,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,SAAS,iBAAiB,kBAAkB;YAChD;SACH;QACD,IAAI,CAAC,aAAa,SAAS;YACvB,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe;gBAC1C,MAAM;gBACN,MAAM;gBACN,UAAU,IAAI;gBACd,SAAS,gBAAgB;YAC7B;QACJ,OACK;YACD,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,eAAe;gBAChD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM;wBAAC;4BAAE,MAAM;4BAAiB,WAAW;4BAAe,UAAU;wBAAkB;qBAAE;gBACzG,SAAS,gBAAgB;YAC7B;QACJ;QACA,uEAAuE;QACvE,0EAA0E;QAC1E,2DAA2D;QAC3D,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,qBAAqB;YAChD,MAAM;YACN,MAAM;YACN,UAAU,IAAI;YACd,SAAS,gBAAgB;QAC7B;QACA,oBAAoB,cAAc;YAC9B,MAAM;YACN,SAAS,iBAAiB,YAAY;YACtC,SAAS;QACb;QACA,oBAAoB,eAAe;YAC/B,MAAM;YACN,MAAM;YACN,SAAS,iBAAiB,YAAY;YACtC,SAAS;QACb;QACA,6CAA6C;QAC7C,IAAI,SAAS,iBAAiB,OAAO;YACjC,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO;gBACxC,MAAM;gBACN,MAAM;gBACN,iBAAiB,SAAS;YAC9B;QACJ;QACA,IAAI,SAAS,oBAAoB,OAAO;YACpC,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU;gBAC3C,MAAM;gBACN,MAAM;gBACN,iBAAiB,SAAS;YAC9B;QACJ;QACA,IAAI,SAAS,kBAAkB,OAAO;YAClC,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ;gBACzC,MAAM;gBACN,MAAM;gBACN,iBAAiB,SAAS;YAC9B;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS;YACvC,QAAQ;gBACJ,2EAA2E;gBAC3E,iBAAiB,YAAY;gBAC7B,kDAAkD;gBAClD,OAAO,iBAAiB,OAAO,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC;oBAClD,+EAA+E;oBAC/E,IAAI,KAAK,QAAQ,OAAO,MAAM,WAAW;wBACrC,OAAO;oBACX,OACK;wBACD,OAAO;oBACX;gBACJ;YACJ;QACJ;QACA,MAAM,iBAAiB,eAAe,kBAAkB;QACxD,IAAI,eAAe,MAAM,KAAK,KAAK,OAAO,qBAAqB,CAAC,wBAAwB;YACpF,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc;gBAC/C,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,gBAAgB,cAAc,CAAC,QAAU,OAAO,WAAW,CAAC,uBAAuB,OAAO;gBACxI,SAAS,iBAAiB,YAAY;YAC1C;QACJ;QACA,MAAM,qBAAqB,eAAe,iBAAiB;QAC3D,IAAI,mBAAmB,MAAM,KAAK,KAAK,OAAO,qBAAqB,CAAC,4BAA4B;YAC5F,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB;gBACnD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,oBAAoB,kBAAkB,CAAC,QAAU,OAAO,WAAW,CAAC,2BAA2B,OAAO;gBACpJ,SAAS,iBAAiB,gBAAgB;YAC9C;QACJ;QACA,oEAAoE;QACpE,IAAI,OAAO,qBAAqB,CAAC,2BAA2B;YACxD,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,mBAAmB;gBACpD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ;gBACzH,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,wBAAwB;gBACzD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,qBAAqB,SAAS,wBAAwB,oBAAoB,QAAQ;gBAChI,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,wBAAwB;gBACzD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,qBAAqB,SAAS,wBAAwB,oBAAoB,QAAQ;gBAChI,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,4BAA4B;gBAC7D,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,2BAA2B,SAAS;gBAClF,SAAS,iBAAiB,gBAAgB;YAC9C;YACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,wBAAwB;gBACzD,MAAM;gBACN,MAAM;gBACN,iBAAiB,IAAM,eAAe,QAAQ,uBAAuB,SAAS;gBAC9E,SAAS,iBAAiB,gBAAgB;YAC9C;QACJ;QACA,0BAA0B,gBAAgB;YACtC,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS,iBAAiB,uBAAuB;QACrD;QACA,0BAA0B,kBAAkB;YACxC,MAAM;YACN,MAAM;YACN,SAAS;YACT,UAAU,gBAAgB;YAC1B,SAAS,iBAAiB,sBAAsB;QACpD;QACA,0BAA0B,kBAAkB;YACxC,MAAM;YACN,MAAM;YACN,SAAS;YACT,UAAU,mBAAmB;YAC7B,SAAS,iBAAiB,sBAAsB;QACpD;IACJ;IAEA,MAAM,SAAS,CAAC;QACZ,MAAM,mBAAmB,oBAAoB;QAC7C,SAAS;QACT,iBAAiB;QACjB,aAAa,QAAQ;QACrB,WAAW,QAAQ;QACnB,YAAY;IAChB;IACA,IAAI,WAAW;QACX,SAAS,GAAG,CAAC,SAAS;IAC1B;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/table/index.js"], "sourcesContent": ["// Exports the \"table\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/table')\n//   ES2015:\n//     import 'tinymce/plugins/table'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,SAAS;AACT,cAAc;AACd,uCAAuC;AACvC,YAAY;AACZ,qCAAqC", "ignoreList": [0], "debugId": null}}]}