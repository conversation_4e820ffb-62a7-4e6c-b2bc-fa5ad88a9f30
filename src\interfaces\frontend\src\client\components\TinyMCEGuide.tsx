'use client';

import { useState } from 'react';
import { Card } from '@/client/components/ui/card';
import { Button } from '@/client/components/ui/button';
import { ChevronDown, ChevronRight, HelpCircle } from 'lucide-react';

export default function TinyMCEGuide() {
  const [isOpen, setIsOpen] = useState(false);

  const features = [
    {
      title: "📝 Templates prédéfinis",
      description: "Utilisez le menu 'Templates' pour insérer des mises en page toutes prêtes (Titre+CTA, Article avec image, etc.)"
    },
    {
      title: "🎨 Formatage avancé",
      description: "Police, taille, couleurs, alignement - tout comme dans Word ! Les couleurs sont automatiquement optimisées pour l'email."
    },
    {
      title: "🔗 Bouton CTA magique",
      description: "Le bouton 'CTA' dans la barre d'outils crée automatiquement des boutons d'action optimisés pour les newsletters."
    },
    {
      title: "📧 Mode Email",
      description: "Le bouton 'Email' convertit automatiquement votre contenu avec des styles inline compatibles avec tous les clients email."
    },
    {
      title: "🖼️ Images et médias",
      description: "Insérez facilement des images, tableaux et liens. Les images sont automatiquement redimensionnées."
    },
    {
      title: "👀 Prévisualisation",
      description: "Utilisez 'Preview' pour voir exactement à quoi ressemblera votre newsletter avant de l'envoyer."
    }
  ];

  const tips = [
    "Commencez par choisir un template pour gagner du temps",
    "Utilisez les titres H1/H2 pour structurer votre contenu",
    "Ajoutez des boutons CTA colorés pour encourager l'action",
    "Prévisualisez toujours avant de sauvegarder",
    "Le bouton 'Email' optimise automatiquement pour l'envoi"
  ];

  return (
    <Card className="mb-4">
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between p-4 h-auto"
      >
        <div className="flex items-center gap-2">
          <HelpCircle className="w-5 h-5 text-blue-600" />
          <span className="font-medium">Guide d'utilisation TinyMCE</span>
        </div>
        {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
      </Button>
      
      {isOpen && (
        <div className="px-4 pb-4 space-y-4">
          <div>
            <h4 className="font-semibold text-green-700 mb-2">✨ Fonctionnalités principales</h4>
            <div className="grid gap-3">
              {features.map((feature, index) => (
                <div key={index} className="p-3 bg-green-50 rounded-lg">
                  <div className="font-medium text-sm text-green-800">{feature.title}</div>
                  <div className="text-xs text-green-700 mt-1">{feature.description}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-blue-700 mb-2">💡 Conseils pratiques</h4>
            <ul className="space-y-2">
              {tips.map((tip, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-blue-700">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-start gap-2">
              <span className="text-yellow-600">⚠️</span>
              <div className="text-sm">
                <div className="font-medium text-yellow-800">Important pour les emails</div>
                <div className="text-yellow-700">Cliquez sur le bouton 'Email' avant de sauvegarder pour garantir la compatibilité avec tous les clients email (Gmail, Outlook, etc.)</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}
