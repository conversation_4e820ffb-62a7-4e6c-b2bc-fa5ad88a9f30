{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/fullscreen/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType$1 = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq$1 = (t) => (a) => t === a;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isArray = isType$1('array');\n    const isNull = eq$1(null);\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => { };\n    /** Compose a unary function with an n-ary function */\n    const compose = (fa, fb) => {\n        return (...args) => {\n            return fa(fb.apply(null, args));\n        };\n    };\n    /** Compose two unary functions. Similar to compose, but avoids using Function.prototype.apply. */\n    const compose1 = (fbc, fab) => (a) => fbc(fab(a));\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    // eslint-disable-next-line prefer-arrow/prefer-arrow-functions\n    function curry(fn, ...initialArgs) {\n        return (...restArgs) => {\n            const all = initialArgs.concat(restArgs);\n            return fn.apply(null, all);\n        };\n    }\n    const never = constant(false);\n    const always = constant(true);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const filter$1 = (xs, pred) => {\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                r.push(x);\n            }\n        }\n        return r;\n    };\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find$1 = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const bind$3 = (xs, f) => flatten(map(xs, f));\n    const get$5 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = (xs) => get$5(xs, 0);\n    const findMap = (arr, f) => {\n        for (let i = 0; i < arr.length; i++) {\n            const r = f(arr[i], i);\n            if (r.isSome()) {\n                return r;\n            }\n        }\n        return Optional.none();\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    // Use window object as the global if it's available since CSP will block script evals\n    // eslint-disable-next-line @typescript-eslint/no-implied-eval\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    /*\n    Notes on the lift functions:\n    - We used to have a generic liftN, but we were concerned about its type-safety, and the below variants were faster in microbenchmarks.\n    - The getOrDie calls are partial functions, but are checked beforehand. This is faster and more convenient (but less safe) than folds.\n    - && is used instead of a loop for simplicity and performance.\n    */\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n\n    /** path :: ([String], JsObj?) -> JsObj */\n    const path = (parts, scope) => {\n        let o = scope !== undefined && scope !== null ? scope : Global;\n        for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n            o = o[parts[i]];\n        }\n        return o;\n    };\n    /** resolve :: (String, JsObj?) -> JsObj */\n    const resolve = (p, scope) => {\n        const parts = p.split('.');\n        return path(parts, scope);\n    };\n\n    const singleton = (doRevoke) => {\n        const subject = Cell(Optional.none());\n        const revoke = () => subject.get().each(doRevoke);\n        const clear = () => {\n            revoke();\n            subject.set(Optional.none());\n        };\n        const isSet = () => subject.get().isSome();\n        const get = () => subject.get();\n        const set = (s) => {\n            revoke();\n            subject.set(Optional.some(s));\n        };\n        return {\n            clear,\n            isSet,\n            get,\n            set\n        };\n    };\n    const unbindable = () => singleton((s) => s.unbind());\n    const value = () => {\n        const subject = singleton(noop);\n        const on = (f) => subject.get().each(f);\n        return {\n            ...subject,\n            on\n        };\n    };\n\n    const contains = (str, substr, start = 0, end) => {\n        const idx = str.indexOf(substr, start);\n        if (idx !== -1) {\n            return isUndefined(end) ? true : idx + substr.length <= end;\n        }\n        else {\n            return false;\n        }\n    };\n\n    // Run a function fn after rate ms. If another invocation occurs\n    // during the time it is waiting, ignore it completely.\n    const first = (fn, rate) => {\n        let timer = null;\n        const cancel = () => {\n            if (!isNull(timer)) {\n                clearTimeout(timer);\n                timer = null;\n            }\n        };\n        const throttle = (...args) => {\n            if (isNull(timer)) {\n                timer = setTimeout(() => {\n                    timer = null;\n                    fn.apply(null, args);\n                }, rate);\n            }\n        };\n        return {\n            cancel,\n            throttle\n        };\n    };\n\n    const cached = (f) => {\n        let called = false;\n        let r;\n        return (...args) => {\n            if (!called) {\n                called = true;\n                r = f.apply(null, args);\n            }\n            return r;\n        };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const get$4 = (fullscreenState) => ({\n        isFullscreen: () => fullscreenState.get() !== null\n    });\n\n    const fromHtml = (html, scope) => {\n        const doc = scope || document;\n        const div = doc.createElement('div');\n        div.innerHTML = html;\n        if (!div.hasChildNodes() || div.childNodes.length > 1) {\n            const message = 'HTML does not have a single root node';\n            // eslint-disable-next-line no-console\n            console.error(message, html);\n            throw new Error(message);\n        }\n        return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n        const doc = scope || document;\n        const node = doc.createElement(tag);\n        return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n        const doc = scope || document;\n        const node = doc.createTextNode(text);\n        return fromDom(node);\n    };\n    const fromDom = (node) => {\n        // TODO: Consider removing this check, but left atm for safety\n        if (node === null || node === undefined) {\n            throw new Error('Node cannot be null or undefined');\n        }\n        return {\n            dom: node\n        };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    // tslint:disable-next-line:variable-name\n    const SugarElement = {\n        fromHtml,\n        fromTag,\n        fromText,\n        fromDom,\n        fromPoint\n    };\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const is = (element, selector) => {\n        const dom = element.dom;\n        if (dom.nodeType !== ELEMENT) {\n            return false;\n        }\n        else {\n            const elem = dom;\n            if (elem.matches !== undefined) {\n                return elem.matches(selector);\n            }\n            else if (elem.msMatchesSelector !== undefined) {\n                return elem.msMatchesSelector(selector);\n            }\n            else if (elem.webkitMatchesSelector !== undefined) {\n                return elem.webkitMatchesSelector(selector);\n            }\n            else if (elem.mozMatchesSelector !== undefined) {\n                // cast to any as mozMatchesSelector doesn't exist in TS DOM lib\n                return elem.mozMatchesSelector(selector);\n            }\n            else {\n                throw new Error('Browser lacks native selectors');\n            } // unfortunately we can't throw this on startup :(\n        }\n    };\n    const bypassSelector = (dom) => \n    // Only elements, documents and shadow roots support querySelector\n    // shadow root element type is DOCUMENT_FRAGMENT\n    dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT ||\n        // IE fix for complex queries on empty nodes: http://jsfiddle.net/spyder/fv9ptr5L/\n        dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n        const base = scope === undefined ? document : scope.dom;\n        return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n\n    const DeviceType = (os, browser, userAgent, mediaMatch) => {\n        const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n        const isiPhone = os.isiOS() && !isiPad;\n        const isMobile = os.isiOS() || os.isAndroid();\n        const isTouch = isMobile || mediaMatch('(pointer:coarse)');\n        const isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n        const isPhone = isiPhone || isMobile && !isTablet;\n        const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n        const isDesktop = !isPhone && !isTablet && !iOSwebview;\n        return {\n            isiPad: constant(isiPad),\n            isiPhone: constant(isiPhone),\n            isTablet: constant(isTablet),\n            isPhone: constant(isPhone),\n            isTouch: constant(isTouch),\n            isAndroid: os.isAndroid,\n            isiOS: os.isiOS,\n            isWebView: constant(iOSwebview),\n            isDesktop: constant(isDesktop)\n        };\n    };\n\n    const firstMatch = (regexes, s) => {\n        for (let i = 0; i < regexes.length; i++) {\n            const x = regexes[i];\n            if (x.test(s)) {\n                return x;\n            }\n        }\n        return undefined;\n    };\n    const find = (regexes, agent) => {\n        const r = firstMatch(regexes, agent);\n        if (!r) {\n            return { major: 0, minor: 0 };\n        }\n        const group = (i) => {\n            return Number(agent.replace(r, '$' + i));\n        };\n        return nu$2(group(1), group(2));\n    };\n    const detect$3 = (versionRegexes, agent) => {\n        const cleanedAgent = String(agent).toLowerCase();\n        if (versionRegexes.length === 0) {\n            return unknown$2();\n        }\n        return find(versionRegexes, cleanedAgent);\n    };\n    const unknown$2 = () => {\n        return nu$2(0, 0);\n    };\n    const nu$2 = (major, minor) => {\n        return { major, minor };\n    };\n    const Version = {\n        nu: nu$2,\n        detect: detect$3,\n        unknown: unknown$2\n    };\n\n    const detectBrowser$1 = (browsers, userAgentData) => {\n        return findMap(userAgentData.brands, (uaBrand) => {\n            const lcBrand = uaBrand.brand.toLowerCase();\n            return find$1(browsers, (browser) => { var _a; return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase()); })\n                .map((info) => ({\n                current: info.name,\n                version: Version.nu(parseInt(uaBrand.version, 10), 0)\n            }));\n        });\n    };\n\n    const detect$2 = (candidates, userAgent) => {\n        const agent = String(userAgent).toLowerCase();\n        return find$1(candidates, (candidate) => {\n            return candidate.search(agent);\n        });\n    };\n    // They (browser and os) are the same at the moment, but they might\n    // not stay that way.\n    const detectBrowser = (browsers, userAgent) => {\n        return detect$2(browsers, userAgent).map((browser) => {\n            const version = Version.detect(browser.versionRegexes, userAgent);\n            return {\n                current: browser.name,\n                version\n            };\n        });\n    };\n    const detectOs = (oses, userAgent) => {\n        return detect$2(oses, userAgent).map((os) => {\n            const version = Version.detect(os.versionRegexes, userAgent);\n            return {\n                current: os.name,\n                version\n            };\n        });\n    };\n\n    const normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    const checkContains = (target) => {\n        return (uastring) => {\n            return contains(uastring, target);\n        };\n    };\n    const browsers = [\n        // This is legacy Edge\n        {\n            name: 'Edge',\n            versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n            search: (uastring) => {\n                return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n            }\n        },\n        // This is Google Chrome and Chromium Edge\n        {\n            name: 'Chromium',\n            brand: 'Chromium',\n            versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/, normalVersionRegex],\n            search: (uastring) => {\n                return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n            }\n        },\n        {\n            name: 'IE',\n            versionRegexes: [/.*?msie\\ ?([0-9]+)\\.([0-9]+).*/, /.*?rv:([0-9]+)\\.([0-9]+).*/],\n            search: (uastring) => {\n                return contains(uastring, 'msie') || contains(uastring, 'trident');\n            }\n        },\n        // INVESTIGATE: Is this still the Opera user agent?\n        {\n            name: 'Opera',\n            versionRegexes: [normalVersionRegex, /.*?opera\\/([0-9]+)\\.([0-9]+).*/],\n            search: checkContains('opera')\n        },\n        {\n            name: 'Firefox',\n            versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n            search: checkContains('firefox')\n        },\n        {\n            name: 'Safari',\n            versionRegexes: [normalVersionRegex, /.*?cpu os ([0-9]+)_([0-9]+).*/],\n            search: (uastring) => {\n                return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n            }\n        }\n    ];\n    const oses = [\n        {\n            name: 'Windows',\n            search: checkContains('win'),\n            versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n        },\n        {\n            name: 'iOS',\n            search: (uastring) => {\n                return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n            },\n            versionRegexes: [/.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/, /.*cpu os ([0-9]+)_([0-9]+).*/, /.*cpu iphone os ([0-9]+)_([0-9]+).*/]\n        },\n        {\n            name: 'Android',\n            search: checkContains('android'),\n            versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n        },\n        {\n            name: 'macOS',\n            search: checkContains('mac os x'),\n            versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n        },\n        {\n            name: 'Linux',\n            search: checkContains('linux'),\n            versionRegexes: []\n        },\n        { name: 'Solaris',\n            search: checkContains('sunos'),\n            versionRegexes: []\n        },\n        {\n            name: 'FreeBSD',\n            search: checkContains('freebsd'),\n            versionRegexes: []\n        },\n        {\n            name: 'ChromeOS',\n            search: checkContains('cros'),\n            versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n        }\n    ];\n    const PlatformInfo = {\n        browsers: constant(browsers),\n        oses: constant(oses)\n    };\n\n    const edge = 'Edge';\n    const chromium = 'Chromium';\n    const ie = 'IE';\n    const opera = 'Opera';\n    const firefox = 'Firefox';\n    const safari = 'Safari';\n    const unknown$1 = () => {\n        return nu$1({\n            current: undefined,\n            version: Version.unknown()\n        });\n    };\n    const nu$1 = (info) => {\n        const current = info.current;\n        const version = info.version;\n        const isBrowser = (name) => () => current === name;\n        return {\n            current,\n            version,\n            isEdge: isBrowser(edge),\n            isChromium: isBrowser(chromium),\n            // NOTE: isIe just looks too weird\n            isIE: isBrowser(ie),\n            isOpera: isBrowser(opera),\n            isFirefox: isBrowser(firefox),\n            isSafari: isBrowser(safari)\n        };\n    };\n    const Browser = {\n        unknown: unknown$1,\n        nu: nu$1,\n        edge: constant(edge),\n        chromium: constant(chromium),\n        ie: constant(ie),\n        opera: constant(opera),\n        firefox: constant(firefox),\n        safari: constant(safari)\n    };\n\n    const windows = 'Windows';\n    const ios = 'iOS';\n    const android = 'Android';\n    const linux = 'Linux';\n    const macos = 'macOS';\n    const solaris = 'Solaris';\n    const freebsd = 'FreeBSD';\n    const chromeos = 'ChromeOS';\n    // Though there is a bit of dupe with this and Browser, trying to\n    // reuse code makes it much harder to follow and change.\n    const unknown = () => {\n        return nu({\n            current: undefined,\n            version: Version.unknown()\n        });\n    };\n    const nu = (info) => {\n        const current = info.current;\n        const version = info.version;\n        const isOS = (name) => () => current === name;\n        return {\n            current,\n            version,\n            isWindows: isOS(windows),\n            // TODO: Fix capitalisation\n            isiOS: isOS(ios),\n            isAndroid: isOS(android),\n            isMacOS: isOS(macos),\n            isLinux: isOS(linux),\n            isSolaris: isOS(solaris),\n            isFreeBSD: isOS(freebsd),\n            isChromeOS: isOS(chromeos)\n        };\n    };\n    const OperatingSystem = {\n        unknown,\n        nu,\n        windows: constant(windows),\n        ios: constant(ios),\n        android: constant(android),\n        linux: constant(linux),\n        macos: constant(macos),\n        solaris: constant(solaris),\n        freebsd: constant(freebsd),\n        chromeos: constant(chromeos)\n    };\n\n    const detect$1 = (userAgent, userAgentDataOpt, mediaMatch) => {\n        const browsers = PlatformInfo.browsers();\n        const oses = PlatformInfo.oses();\n        const browser = userAgentDataOpt.bind((userAgentData) => detectBrowser$1(browsers, userAgentData))\n            .orThunk(() => detectBrowser(browsers, userAgent))\n            .fold(Browser.unknown, Browser.nu);\n        const os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n        const deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n        return {\n            browser,\n            os,\n            deviceType\n        };\n    };\n    const PlatformDetection = {\n        detect: detect$1\n    };\n\n    const mediaMatch = (query) => window.matchMedia(query).matches;\n    // IMPORTANT: Must be in a thunk, otherwise rollup thinks calling this immediately\n    // causes side effects and won't tree shake this away\n    // Note: navigator.userAgentData is not part of the native typescript types yet\n    let platform = cached(() => PlatformDetection.detect(window.navigator.userAgent, Optional.from((window.navigator.userAgentData)), mediaMatch));\n    const detect = () => platform();\n\n    const unsafe = (name, scope) => {\n        return resolve(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n        const actual = unsafe(name, scope);\n        if (actual === undefined || actual === null) {\n            throw new Error(name + ' not available on this browser');\n        }\n        return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    /*\n     * IE9 and above\n     *\n     * MDN no use on this one, but here's the link anyway:\n     * https://developer.mozilla.org/en/docs/Web/API/HTMLElement\n     */\n    const sandHTMLElement = (scope) => {\n        return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = (x) => {\n        // use Resolve to get the window object for x and just return undefined if it can't find it.\n        // undefined scope later triggers using the global window.\n        const scope = resolve('ownerDocument.defaultView', x);\n        // TINY-7374: We can't rely on looking at the owner window HTMLElement as the element may have\n        // been constructed in a different window and then appended to the current window document.\n        return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const type = (element) => element.dom.nodeType;\n    const isType = (t) => (element) => type(element) === t;\n    const isHTMLElement = (element) => isElement(element) && isPrototypeOf(element.dom);\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n\n    /**\n     * The document associated with the current element\n     * NOTE: this will throw if the owner is null.\n     */\n    const owner = (element) => SugarElement.fromDom(element.dom.ownerDocument);\n    const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n        const stop = isFunction(isRoot) ? isRoot : never;\n        // This is used a *lot* so it needs to be performant, not recursive\n        let dom = element.dom;\n        const ret = [];\n        while (dom.parentNode !== null && dom.parentNode !== undefined) {\n            const rawParent = dom.parentNode;\n            const p = SugarElement.fromDom(rawParent);\n            ret.push(p);\n            if (stop(p) === true) {\n                break;\n            }\n            else {\n                dom = rawParent;\n            }\n        }\n        return ret;\n    };\n    const siblings$2 = (element) => {\n        // TODO: Refactor out children so we can just not add self instead of filtering afterwards\n        const filterSelf = (elements) => filter$1(elements, (x) => !eq(element, x));\n        return parent(element).map(children).map(filterSelf).getOr([]);\n    };\n    const nextSibling = (element) => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children = (element) => map(element.dom.childNodes, SugarElement.fromDom);\n\n    /**\n     * Is the element a ShadowRoot?\n     *\n     * Note: this is insufficient to test if any element is a shadow root, but it is sufficient to differentiate between\n     * a Document and a ShadowRoot.\n     */\n    const isShadowRoot = (dos) => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const getRootNode = (e) => SugarElement.fromDom(e.dom.getRootNode());\n    /** If this element is in a ShadowRoot, return it. */\n    const getShadowRoot = (e) => {\n        const r = getRootNode(e);\n        return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    /** Return the host of a ShadowRoot.\n     *\n     * This function will throw if Shadow DOM is unsupported in the browser, or if the host is null.\n     * If you actually have a ShadowRoot, this shouldn't happen.\n     */\n    const getShadowHost = (e) => SugarElement.fromDom(e.dom.host);\n    /**\n     * When Events bubble up through a ShadowRoot, the browser changes the target to be the shadow host.\n     * This function gets the \"original\" event target if possible.\n     * This only works if the shadow tree is open - if the shadow tree is closed, event.target is returned.\n     * See: https://developers.google.com/web/fundamentals/web-components/shadowdom#events\n     */\n    const getOriginalEventTarget = (event) => {\n        if (isNonNullable(event.target)) {\n            const el = SugarElement.fromDom(event.target);\n            if (isElement(el) && isOpenShadowHost(el)) {\n                // When target element is inside Shadow DOM we need to take first element from composedPath\n                // otherwise we'll get Shadow Root parent, not actual target element.\n                if (event.composed && event.composedPath) {\n                    const composedPath = event.composedPath();\n                    if (composedPath) {\n                        return head(composedPath);\n                    }\n                }\n            }\n        }\n        return Optional.from(event.target);\n    };\n    /** Return true if the element is a host of an open shadow root.\n     *  Return false if the element is a host of a closed shadow root, or if the element is not a host.\n     */\n    const isOpenShadowHost = (element) => isNonNullable(element.dom.shadowRoot);\n\n    const mkEvent = (target, x, y, stop, prevent, kill, raw) => ({\n        target,\n        x,\n        y,\n        stop,\n        prevent,\n        kill,\n        raw\n    });\n    /** Wraps an Event in an EventArgs structure.\n     * The returned EventArgs structure has its target set to the \"original\" target if possible.\n     * See SugarShadowDom.getOriginalEventTarget\n     */\n    const fromRawEvent = (rawEvent) => {\n        const target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));\n        const stop = () => rawEvent.stopPropagation();\n        const prevent = () => rawEvent.preventDefault();\n        const kill = compose(prevent, stop); // more of a sequence than a compose, but same effect\n        // FIX: Don't just expose the raw event. Need to identify what needs standardisation.\n        return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);\n    };\n    const handle = (filter, handler) => (rawEvent) => {\n        if (filter(rawEvent)) {\n            handler(fromRawEvent(rawEvent));\n        }\n    };\n    const binder = (element, event, filter, handler, useCapture) => {\n        const wrapped = handle(filter, handler);\n        // IE9 minimum\n        element.dom.addEventListener(event, wrapped, useCapture);\n        return {\n            unbind: curry(unbind, element, event, wrapped, useCapture)\n        };\n    };\n    const bind$2 = (element, event, filter, handler) => binder(element, event, filter, handler, false);\n    const unbind = (element, event, handler, useCapture) => {\n        // IE9 minimum\n        element.dom.removeEventListener(event, handler, useCapture);\n    };\n\n    const filter = always; // no filter on plain DomEvents\n    const bind$1 = (element, event, handler) => bind$2(element, event, filter, handler);\n\n    const rawSet = (dom, key, value) => {\n        /*\n         * JQuery coerced everything to a string, and silently did nothing on text node/null/undefined.\n         *\n         * We fail on those invalid cases, only allowing numbers and booleans.\n         */\n        if (isString(value) || isBoolean(value) || isNumber(value)) {\n            dom.setAttribute(key, value + '');\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n            throw new Error('Attribute value was not simple');\n        }\n    };\n    const set$1 = (element, key, value) => {\n        rawSet(element.dom, key, value);\n    };\n    const get$3 = (element, key) => {\n        const v = element.dom.getAttribute(key);\n        // undefined is the more appropriate value for JS, and this matches JQuery\n        return v === null ? undefined : v;\n    };\n    const remove = (element, key) => {\n        element.dom.removeAttribute(key);\n    };\n\n    // some elements, such as mathml, don't have style attributes\n    // others, such as angular elements, have style attributes that aren't a CSSStyleDeclaration\n    const isSupported = (dom) => \n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    // Node.contains() is very, very, very good performance\n    // http://jsperf.com/closest-vs-contains/5\n    const inBody = (element) => {\n        // Technically this is only required on IE, where contains() returns false for text nodes.\n        // But it's cheap enough to run everywhere and Sugar doesn't have platform detection (yet).\n        const dom = isText(element) ? element.dom.parentNode : element.dom;\n        // use ownerDocument.body to ensure this works inside iframes.\n        // Normally contains is bad because an element \"contains\" itself, but here we want that.\n        if (dom === undefined || dom === null || dom.ownerDocument === null) {\n            return false;\n        }\n        const doc = dom.ownerDocument;\n        return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n    const getBody = (doc) => {\n        const b = doc.dom.body;\n        if (b === null || b === undefined) {\n            throw new Error('Body is not available yet');\n        }\n        return SugarElement.fromDom(b);\n    };\n\n    const internalSet = (dom, property, value) => {\n        // This is going to hurt. Apologies.\n        // JQuery coerces numbers to pixels for certain property names, and other times lets numbers through.\n        // we're going to be explicit; strings only.\n        if (!isString(value)) {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n            throw new Error('CSS value must be a string: ' + value);\n        }\n        // removed: support for dom().style[property] where prop is camel case instead of normal property name\n        if (isSupported(dom)) {\n            dom.style.setProperty(property, value);\n        }\n    };\n    const set = (element, property, value) => {\n        const dom = element.dom;\n        internalSet(dom, property, value);\n    };\n    const setAll = (element, css) => {\n        const dom = element.dom;\n        each(css, (v, k) => {\n            internalSet(dom, k, v);\n        });\n    };\n    /*\n     * NOTE: For certain properties, this returns the \"used value\" which is subtly different to the \"computed value\" (despite calling getComputedStyle).\n     * Blame CSS 2.0.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n     */\n    const get$2 = (element, property) => {\n        const dom = element.dom;\n        /*\n         * IE9 and above per\n         * https://developer.mozilla.org/en/docs/Web/API/window.getComputedStyle\n         *\n         * Not in numerosity, because it doesn't memoize and looking this up dynamically in performance critical code would be horrendous.\n         *\n         * JQuery has some magic here for IE popups, but we don't really need that.\n         * It also uses element.ownerDocument.defaultView to handle iframes but that hasn't been required since FF 3.6.\n         */\n        const styles = window.getComputedStyle(dom);\n        const r = styles.getPropertyValue(property);\n        // jquery-ism: If r is an empty string, check that the element is not in a document. If it isn't, return the raw value.\n        // Turns out we do this a lot.\n        return (r === '' && !inBody(element)) ? getUnsafeProperty(dom, property) : r;\n    };\n    // removed: support for dom().style[property] where prop is camel case instead of normal property name\n    // empty string is what the browsers (IE11 and Chrome) return when the propertyValue doesn't exists.\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n\n    const r = (left, top) => {\n        const translate = (x, y) => r(left + x, top + y);\n        return {\n            left,\n            top,\n            translate\n        };\n    };\n    // tslint:disable-next-line:variable-name\n    const SugarPosition = r;\n\n    // get scroll position (x,y) relative to document _doc (or global if not supplied)\n    const get$1 = (_DOC) => {\n        const doc = _DOC !== undefined ? _DOC.dom : document;\n        // ASSUMPTION: This is for cross-browser support, body works for Safari & EDGE, and when we have an iframe body scroller\n        const x = doc.body.scrollLeft || doc.documentElement.scrollLeft;\n        const y = doc.body.scrollTop || doc.documentElement.scrollTop;\n        return SugarPosition(x, y);\n    };\n\n    // IE11 Can return undefined for a classList on elements such as math, so we make sure it's not undefined before attempting to use it.\n    const supports = (element) => element.dom.classList !== undefined;\n\n    const has = (element, clazz) => supports(element) && element.dom.classList.contains(clazz);\n\n    const ancestors$1 = (scope, predicate, isRoot) => filter$1(parents(scope, isRoot), predicate);\n    const siblings$1 = (scope, predicate) => filter$1(siblings$2(scope), predicate);\n\n    const all = (selector) => all$1(selector);\n    // For all of the following:\n    //\n    // jQuery does siblings of firstChild. IE9+ supports scope.dom.children (similar to Traverse.children but elements only).\n    // Traverse should also do this (but probably not by default).\n    //\n    const ancestors = (scope, selector, isRoot) => \n    // It may surprise you to learn this is exactly what JQuery does\n    // TODO: Avoid all this wrapping and unwrapping\n    ancestors$1(scope, (e) => is(e, selector), isRoot);\n    const siblings = (scope, selector) => \n    // It may surprise you to learn this is exactly what JQuery does\n    // TODO: Avoid all the wrapping and unwrapping\n    siblings$1(scope, (e) => is(e, selector));\n\n    const get = (_win) => {\n        const win = _win === undefined ? window : _win;\n        if (detect().browser.isFirefox()) {\n            // TINY-7984: Firefox 91 is returning incorrect values for visualViewport.pageTop, so disable it for now\n            return Optional.none();\n        }\n        else {\n            return Optional.from(win.visualViewport);\n        }\n    };\n    const bounds = (x, y, width, height) => ({\n        x,\n        y,\n        width,\n        height,\n        right: x + width,\n        bottom: y + height\n    });\n    const getBounds = (_win) => {\n        const win = _win === undefined ? window : _win;\n        const doc = win.document;\n        const scroll = get$1(SugarElement.fromDom(doc));\n        return get(win).fold(() => {\n            const html = win.document.documentElement;\n            // Don't use window.innerWidth/innerHeight here, as we don't want to include scrollbars\n            // since the right/bottom position is based on the edge of the scrollbar not the window\n            const width = html.clientWidth;\n            const height = html.clientHeight;\n            return bounds(scroll.left, scroll.top, width, height);\n        }, (visualViewport) => \n        // iOS doesn't update the pageTop/pageLeft when element.scrollIntoView() is called, so we need to fallback to the\n        // scroll position which will always be less than the page top/left values when page top/left are accurate/correct.\n        bounds(Math.max(visualViewport.pageLeft, scroll.left), Math.max(visualViewport.pageTop, scroll.top), visualViewport.width, visualViewport.height));\n    };\n    const bind = (name, callback, _win) => get(_win).map((visualViewport) => {\n        const handler = (e) => callback(fromRawEvent(e));\n        visualViewport.addEventListener(name, handler);\n        return {\n            unbind: () => visualViewport.removeEventListener(name, handler)\n        };\n    }).getOrThunk(() => ({\n        unbind: noop\n    }));\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const fireFullscreenStateChanged = (editor, state) => {\n        editor.dispatch('FullscreenStateChanged', { state });\n        editor.dispatch('ResizeEditor');\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('fullscreen_native', {\n            processor: 'boolean',\n            default: false\n        });\n    };\n    const getFullscreenNative = option('fullscreen_native');\n\n    const getFullscreenRoot = (editor) => {\n        const elem = SugarElement.fromDom(editor.getElement());\n        return getShadowRoot(elem).map(getShadowHost)\n            .getOrThunk(() => getBody(owner(elem)));\n    };\n    const getFullscreenElement = (root) => {\n        if (root.fullscreenElement !== undefined) {\n            return root.fullscreenElement;\n        }\n        else if (root.msFullscreenElement !== undefined) {\n            return root.msFullscreenElement;\n        }\n        else if (root.webkitFullscreenElement !== undefined) {\n            return root.webkitFullscreenElement;\n        }\n        else {\n            return null;\n        }\n    };\n    const getFullscreenchangeEventName = () => {\n        if (document.fullscreenElement !== undefined) {\n            return 'fullscreenchange';\n        }\n        else if (document.msFullscreenElement !== undefined) {\n            return 'MSFullscreenChange'; // warning, seems to be case sensitive\n        }\n        else if (document.webkitFullscreenElement !== undefined) {\n            return 'webkitfullscreenchange';\n        }\n        else {\n            return 'fullscreenchange';\n        }\n    };\n    const requestFullscreen = (sugarElem) => {\n        const elem = sugarElem.dom;\n        if (elem.requestFullscreen) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            elem.requestFullscreen();\n        }\n        else if (elem.msRequestFullscreen) {\n            elem.msRequestFullscreen();\n        }\n        else if (elem.webkitRequestFullScreen) {\n            elem.webkitRequestFullScreen();\n        }\n    };\n    const exitFullscreen = (sugarDoc) => {\n        const doc = sugarDoc.dom;\n        if (doc.exitFullscreen) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            doc.exitFullscreen();\n        }\n        else if (doc.msExitFullscreen) {\n            doc.msExitFullscreen();\n        }\n        else if (doc.webkitCancelFullScreen) {\n            doc.webkitCancelFullScreen();\n        }\n    };\n    const isFullscreenElement = (elem) => elem.dom === getFullscreenElement(owner(elem).dom);\n\n    const attr = 'data-ephox-mobile-fullscreen-style';\n    const siblingStyles = 'display:none!important;';\n    const ancestorPosition = 'position:absolute!important;';\n    // TINY-3407 ancestors need 'height:100%!important;overflow:visible!important;' to prevent collapsed ancestors hiding the editor\n    const ancestorStyles = 'top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;';\n    const bgFallback = 'background-color:rgb(255,255,255)!important;';\n    const isAndroid = global$1.os.isAndroid();\n    const matchColor = (editorBody) => {\n        // in iOS you can overscroll, sometimes when you overscroll you can reveal the bgcolor of an element beneath,\n        // by matching the bg color and clobbering ensures any reveals are 'camouflaged' the same color\n        const color = get$2(editorBody, 'background-color');\n        return (color !== undefined && color !== '') ? 'background-color:' + color + '!important' : bgFallback;\n    };\n    // We clobber all tags, direct ancestors to the editorBody get ancestorStyles, everything else gets siblingStyles\n    const clobberStyles = (dom, container, editorBody) => {\n        const gatherSiblings = (element) => {\n            return siblings(element, '*:not(.tox-silver-sink)');\n        };\n        const clobber = (clobberStyle) => (element) => {\n            const styles = get$3(element, 'style');\n            const backup = styles === undefined ? 'no-styles' : styles.trim();\n            if (backup === clobberStyle) {\n                return;\n            }\n            else {\n                set$1(element, attr, backup);\n                setAll(element, dom.parseStyle(clobberStyle));\n            }\n        };\n        const ancestors$1 = ancestors(container, '*');\n        const siblings$1 = bind$3(ancestors$1, gatherSiblings);\n        const bgColor = matchColor(editorBody);\n        /* NOTE: This assumes that container has no siblings itself */\n        each$1(siblings$1, clobber(siblingStyles));\n        each$1(ancestors$1, clobber(ancestorPosition + ancestorStyles + bgColor));\n        // position absolute on the outer-container breaks Android flex layout\n        const containerStyles = isAndroid === true ? '' : ancestorPosition;\n        clobber(containerStyles + ancestorStyles + bgColor)(container);\n    };\n    const restoreStyles = (dom) => {\n        const clobberedEls = all('[' + attr + ']');\n        each$1(clobberedEls, (element) => {\n            const restore = get$3(element, attr);\n            if (restore && restore !== 'no-styles') {\n                setAll(element, dom.parseStyle(restore));\n            }\n            else {\n                remove(element, 'style');\n            }\n            remove(element, attr);\n        });\n    };\n\n    const DOM = global$2.DOM;\n    const getScrollPos = () => getBounds(window);\n    const setScrollPos = (pos) => window.scrollTo(pos.x, pos.y);\n    const viewportUpdate = get().fold(() => ({ bind: noop, unbind: noop }), (visualViewport) => {\n        const editorContainer = value();\n        const resizeBinder = unbindable();\n        const scrollBinder = unbindable();\n        const refreshScroll = () => {\n            document.body.scrollTop = 0;\n            document.documentElement.scrollTop = 0;\n        };\n        const refreshVisualViewport = () => {\n            window.requestAnimationFrame(() => {\n                editorContainer.on((container) => setAll(container, {\n                    top: visualViewport.offsetTop + 'px',\n                    left: visualViewport.offsetLeft + 'px',\n                    height: visualViewport.height + 'px',\n                    width: visualViewport.width + 'px'\n                }));\n            });\n        };\n        const update = first(() => {\n            refreshScroll();\n            refreshVisualViewport();\n        }, 50);\n        const bind$1 = (element) => {\n            editorContainer.set(element);\n            update.throttle();\n            resizeBinder.set(bind('resize', update.throttle));\n            scrollBinder.set(bind('scroll', update.throttle));\n        };\n        const unbind = () => {\n            editorContainer.on(() => {\n                resizeBinder.clear();\n                scrollBinder.clear();\n            });\n            editorContainer.clear();\n        };\n        return {\n            bind: bind$1,\n            unbind\n        };\n    });\n    const toggleFullscreen = (editor, fullscreenState) => {\n        const body = document.body;\n        const documentElement = document.documentElement;\n        const editorContainer = editor.getContainer();\n        const editorContainerS = SugarElement.fromDom(editorContainer);\n        const sinkContainerS = nextSibling(editorContainerS)\n            .filter((elm) => isHTMLElement(elm) && has(elm, 'tox-silver-sink'));\n        const fullscreenRoot = getFullscreenRoot(editor);\n        const fullscreenInfo = fullscreenState.get();\n        const editorBody = SugarElement.fromDom(editor.getBody());\n        const isTouch = global$1.deviceType.isTouch();\n        const editorContainerStyle = editorContainer.style;\n        const iframe = editor.iframeElement;\n        const iframeStyle = iframe === null || iframe === void 0 ? void 0 : iframe.style;\n        const handleClasses = (handler) => {\n            handler(body, 'tox-fullscreen');\n            handler(documentElement, 'tox-fullscreen');\n            handler(editorContainer, 'tox-fullscreen');\n            getShadowRoot(editorContainerS)\n                .map((root) => getShadowHost(root).dom)\n                .each((host) => {\n                handler(host, 'tox-fullscreen');\n                handler(host, 'tox-shadowhost');\n            });\n        };\n        const cleanup = () => {\n            if (isTouch) {\n                restoreStyles(editor.dom);\n            }\n            handleClasses(DOM.removeClass);\n            viewportUpdate.unbind();\n            Optional.from(fullscreenState.get()).each((info) => info.fullscreenChangeHandler.unbind());\n        };\n        if (!fullscreenInfo) {\n            const fullscreenChangeHandler = bind$1(owner(fullscreenRoot), getFullscreenchangeEventName(), (_evt) => {\n                if (getFullscreenNative(editor)) {\n                    // if we have exited browser fullscreen with Escape then exit editor fullscreen too\n                    if (!isFullscreenElement(fullscreenRoot) && fullscreenState.get() !== null) {\n                        toggleFullscreen(editor, fullscreenState);\n                    }\n                }\n            });\n            const newFullScreenInfo = {\n                scrollPos: getScrollPos(),\n                containerWidth: editorContainerStyle.width,\n                containerHeight: editorContainerStyle.height,\n                containerTop: editorContainerStyle.top,\n                containerLeft: editorContainerStyle.left,\n                iframeWidth: iframeStyle.width,\n                iframeHeight: iframeStyle.height,\n                fullscreenChangeHandler,\n                sinkCssPosition: sinkContainerS.map((elm) => get$2(elm, 'position'))\n            };\n            if (isTouch) {\n                clobberStyles(editor.dom, editorContainerS, editorBody);\n            }\n            iframeStyle.width = iframeStyle.height = '100%';\n            editorContainerStyle.width = editorContainerStyle.height = '';\n            handleClasses(DOM.addClass);\n            sinkContainerS.each((elm) => {\n                set(elm, 'position', 'fixed');\n            });\n            viewportUpdate.bind(editorContainerS);\n            editor.on('remove', cleanup);\n            fullscreenState.set(newFullScreenInfo);\n            if (getFullscreenNative(editor)) {\n                requestFullscreen(fullscreenRoot);\n            }\n            fireFullscreenStateChanged(editor, true);\n        }\n        else {\n            fullscreenInfo.fullscreenChangeHandler.unbind();\n            if (getFullscreenNative(editor) && isFullscreenElement(fullscreenRoot)) {\n                exitFullscreen(owner(fullscreenRoot));\n            }\n            iframeStyle.width = fullscreenInfo.iframeWidth;\n            iframeStyle.height = fullscreenInfo.iframeHeight;\n            editorContainerStyle.width = fullscreenInfo.containerWidth;\n            editorContainerStyle.height = fullscreenInfo.containerHeight;\n            editorContainerStyle.top = fullscreenInfo.containerTop;\n            editorContainerStyle.left = fullscreenInfo.containerLeft;\n            lift2(sinkContainerS, fullscreenInfo.sinkCssPosition, (elm, val) => {\n                set(elm, 'position', val);\n            });\n            cleanup();\n            setScrollPos(fullscreenInfo.scrollPos);\n            fullscreenState.set(null);\n            fireFullscreenStateChanged(editor, false);\n            editor.off('remove', cleanup);\n        }\n    };\n\n    const register$1 = (editor, fullscreenState) => {\n        editor.addCommand('mceFullScreen', () => {\n            toggleFullscreen(editor, fullscreenState);\n        });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const setup = (editor, fullscreenState) => {\n        editor.on('init', () => {\n            editor.on('keydown', (e) => {\n                if (e.keyCode === global.TAB && !(e.metaKey || e.ctrlKey) && fullscreenState.get()) {\n                    e.preventDefault();\n                }\n            });\n        });\n    };\n\n    const makeSetupHandler = (editor, fullscreenState) => (api) => {\n        api.setActive(fullscreenState.get() !== null);\n        const editorEventCallback = (e) => api.setActive(e.state);\n        editor.on('FullscreenStateChanged', editorEventCallback);\n        return () => editor.off('FullscreenStateChanged', editorEventCallback);\n    };\n    const register = (editor, fullscreenState) => {\n        const onAction = () => editor.execCommand('mceFullScreen');\n        editor.ui.registry.addToggleMenuItem('fullscreen', {\n            text: 'Fullscreen',\n            icon: 'fullscreen',\n            shortcut: 'Meta+Shift+F',\n            onAction,\n            onSetup: makeSetupHandler(editor, fullscreenState),\n            context: 'any'\n        });\n        editor.ui.registry.addToggleButton('fullscreen', {\n            tooltip: 'Fullscreen',\n            icon: 'fullscreen',\n            onAction,\n            onSetup: makeSetupHandler(editor, fullscreenState),\n            shortcut: 'Meta+Shift+F',\n            context: 'any'\n        });\n    };\n\n    var Plugin = () => {\n        global$3.add('fullscreen', (editor) => {\n            const fullscreenState = Cell(null);\n            if (editor.inline) {\n                return get$4(fullscreenState);\n            }\n            register$2(editor);\n            register$1(editor, fullscreenState);\n            register(editor, fullscreenState);\n            setup(editor, fullscreenState);\n            editor.addShortcut('Meta+Shift+F', '', 'mceFullScreen');\n            return get$4(fullscreenState);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,WAAW,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACxD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,OAAO,CAAC,IAAM,CAAC,IAAM,MAAM;IACjC,MAAM,WAAW,SAAS;IAC1B,MAAM,WAAW,SAAS;IAC1B,MAAM,UAAU,SAAS;IACzB,MAAM,SAAS,KAAK;IACpB,MAAM,YAAY,aAAa;IAC/B,MAAM,cAAc,KAAK;IACzB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAChC,MAAM,WAAW,aAAa;IAE9B,MAAM,OAAO,KAAQ;IACrB,oDAAoD,GACpD,MAAM,UAAU,CAAC,IAAI;QACjB,OAAO;6CAAI;gBAAA;;YACP,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM;QAC7B;IACJ;IACA,gGAAgG,GAChG,MAAM,WAAW,CAAC,KAAK,MAAQ,CAAC,IAAM,IAAI,IAAI;IAC9C,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,+DAA+D;IAC/D,SAAS,MAAM,EAAE;QAAE,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,cAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YAAG,YAAH,OAAA,KAAA,SAAA,CAAA,KAAc;;QAC7B,OAAO;6CAAI;gBAAA;;YACP,MAAM,MAAM,YAAY,MAAM,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,QAAQ,SAAS;IACvB,MAAM,SAAS,SAAS;IAExB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,WAAW,CAAC,IAAI;QAClB,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,EAAE,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,SAAS,CAAC,IAAI;QAChB,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,SAAS,CAAC,IAAI,IAAM,QAAQ,IAAI,IAAI;IAC1C,MAAM,QAAQ,CAAC,IAAI,IAAM,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI;IACvF,MAAM,OAAO,CAAC,KAAO,MAAM,IAAI;IAC/B,MAAM,UAAU,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,MAAM,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,sFAAsF;IACtF,8DAA8D;IAC9D,MAAM,SAAS,uCAAgC,SAAS;IAExD;;;;;IAKA,GACA,MAAM,QAAQ,CAAC,IAAI,IAAI,IAAM,GAAG,MAAM,MAAM,GAAG,MAAM,KAAK,SAAS,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS,IAAI;IAExH,wCAAwC,GACxC,MAAM,OAAO,CAAC,OAAO;QACjB,IAAI,IAAI,UAAU,aAAa,UAAU,OAAO,QAAQ;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa,MAAM,MAAM,EAAE,EAAG;YACpE,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;QACnB;QACA,OAAO;IACX;IACA,yCAAyC,GACzC,MAAM,UAAU,CAAC,GAAG;QAChB,MAAM,QAAQ,EAAE,KAAK,CAAC;QACtB,OAAO,KAAK,OAAO;IACvB;IAEA,MAAM,YAAY,CAAC;QACf,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,SAAS,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC7B;QACA,MAAM,QAAQ,IAAM,QAAQ,GAAG,GAAG,MAAM;QACxC,MAAM,MAAM,IAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,CAAC;YACT;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC;QAC9B;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,aAAa,IAAM,UAAU,CAAC,IAAM,EAAE,MAAM;IAClD,MAAM,QAAQ;QACV,MAAM,UAAU,UAAU;QAC1B,MAAM,KAAK,CAAC,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,GAAG,OAAO;YACV;QACJ;IACJ;IAEA,MAAM,WAAW,SAAC,KAAK;YAAQ,yEAAQ,GAAG;QACtC,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ;QAChC,IAAI,QAAQ,CAAC,GAAG;YACZ,OAAO,YAAY,OAAO,OAAO,MAAM,OAAO,MAAM,IAAI;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IAEA,gEAAgE;IAChE,uDAAuD;IACvD,MAAM,QAAQ,CAAC,IAAI;QACf,IAAI,QAAQ;QACZ,MAAM,SAAS;YACX,IAAI,CAAC,OAAO,QAAQ;gBAChB,aAAa;gBACb,QAAQ;YACZ;QACJ;QACA,MAAM,WAAW;6CAAI;gBAAA;;YACjB,IAAI,OAAO,QAAQ;gBACf,QAAQ,WAAW;oBACf,QAAQ;oBACR,GAAG,KAAK,CAAC,MAAM;gBACnB,GAAG;YACP;QACJ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,SAAS,CAAC;QACZ,IAAI,SAAS;QACb,IAAI;QACJ,OAAO;6CAAI;gBAAA;;YACP,IAAI,CAAC,QAAQ;gBACT,SAAS;gBACT,IAAI,EAAE,KAAK,CAAC,MAAM;YACtB;YACA,OAAO;QACX;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,QAAQ,CAAC,kBAAoB,CAAC;YAChC,cAAc,IAAM,gBAAgB,GAAG,OAAO;QAClD,CAAC;IAED,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,IAAI,aAAa,CAAC;QAC9B,IAAI,SAAS,GAAG;QAChB,IAAI,CAAC,IAAI,aAAa,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,UAAU;YAChB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,QAAQ,IAAI,UAAU,CAAC,EAAE;IACpC;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,aAAa,CAAC;QAC/B,OAAO,QAAQ;IACnB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,cAAc,CAAC;QAChC,OAAO,QAAQ;IACnB;IACA,MAAM,UAAU,CAAC;QACb,8DAA8D;QAC9D,IAAI,SAAS,QAAQ,SAAS,WAAW;YACrC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;YACH,KAAK;QACT;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,GAAG,IAAM,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,CAAC;IACzF,yCAAyC;IACzC,MAAM,eAAe;QACjB;QACA;QACA;QACA;QACA;IACJ;IAEA,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAC1B,MAAM,UAAU;IAChB,MAAM,OAAO;IAEb,MAAM,KAAK,CAAC,SAAS;QACjB,MAAM,MAAM,QAAQ,GAAG;QACvB,IAAI,IAAI,QAAQ,KAAK,SAAS;YAC1B,OAAO;QACX,OACK;YACD,MAAM,OAAO;YACb,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,OAAO,KAAK,OAAO,CAAC;YACxB,OACK,IAAI,KAAK,iBAAiB,KAAK,WAAW;gBAC3C,OAAO,KAAK,iBAAiB,CAAC;YAClC,OACK,IAAI,KAAK,qBAAqB,KAAK,WAAW;gBAC/C,OAAO,KAAK,qBAAqB,CAAC;YACtC,OACK,IAAI,KAAK,kBAAkB,KAAK,WAAW;gBAC5C,gEAAgE;gBAChE,OAAO,KAAK,kBAAkB,CAAC;YACnC,OACK;gBACD,MAAM,IAAI,MAAM;YACpB,EAAE,kDAAkD;QACxD;IACJ;IACA,MAAM,iBAAiB,CAAC,MACxB,kEAAkE;QAClE,gDAAgD;QAChD,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,qBACtE,kFAAkF;QAClF,IAAI,iBAAiB,KAAK;IAC9B,MAAM,QAAQ,CAAC,UAAU;QACrB,MAAM,OAAO,UAAU,YAAY,WAAW,MAAM,GAAG;QACvD,OAAO,eAAe,QAAQ,EAAE,GAAG,IAAI,KAAK,gBAAgB,CAAC,WAAW,aAAa,OAAO;IAChG;IAEA,MAAM,KAAK,CAAC,IAAI,KAAO,GAAG,GAAG,KAAK,GAAG,GAAG;IAExC,MAAM,aAAa,CAAC,IAAI,SAAS,WAAW;QACxC,MAAM,SAAS,GAAG,KAAK,MAAM,QAAQ,IAAI,CAAC,eAAe;QACzD,MAAM,WAAW,GAAG,KAAK,MAAM,CAAC;QAChC,MAAM,WAAW,GAAG,KAAK,MAAM,GAAG,SAAS;QAC3C,MAAM,UAAU,YAAY,WAAW;QACvC,MAAM,WAAW,UAAU,CAAC,YAAY,YAAY,WAAW;QAC/D,MAAM,UAAU,YAAY,YAAY,CAAC;QACzC,MAAM,aAAa,QAAQ,QAAQ,MAAM,GAAG,KAAK,MAAM,UAAU,IAAI,CAAC,eAAe;QACrF,MAAM,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;QAC5C,OAAO;YACH,QAAQ,SAAS;YACjB,UAAU,SAAS;YACnB,UAAU,SAAS;YACnB,SAAS,SAAS;YAClB,SAAS,SAAS;YAClB,WAAW,GAAG,SAAS;YACvB,OAAO,GAAG,KAAK;YACf,WAAW,SAAS;YACpB,WAAW,SAAS;QACxB;IACJ;IAEA,MAAM,aAAa,CAAC,SAAS;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,IAAI,OAAO,CAAC,EAAE;YACpB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACX,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,OAAO,CAAC,SAAS;QACnB,MAAM,IAAI,WAAW,SAAS;QAC9B,IAAI,CAAC,GAAG;YACJ,OAAO;gBAAE,OAAO;gBAAG,OAAO;YAAE;QAChC;QACA,MAAM,QAAQ,CAAC;YACX,OAAO,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;QACzC;QACA,OAAO,KAAK,MAAM,IAAI,MAAM;IAChC;IACA,MAAM,WAAW,CAAC,gBAAgB;QAC9B,MAAM,eAAe,OAAO,OAAO,WAAW;QAC9C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC7B,OAAO;QACX;QACA,OAAO,KAAK,gBAAgB;IAChC;IACA,MAAM,YAAY;QACd,OAAO,KAAK,GAAG;IACnB;IACA,MAAM,OAAO,CAAC,OAAO;QACjB,OAAO;YAAE;YAAO;QAAM;IAC1B;IACA,MAAM,UAAU;QACZ,IAAI;QACJ,QAAQ;QACR,SAAS;IACb;IAEA,MAAM,kBAAkB,CAAC,UAAU;QAC/B,OAAO,QAAQ,cAAc,MAAM,EAAE,CAAC;YAClC,MAAM,UAAU,QAAQ,KAAK,CAAC,WAAW;YACzC,OAAO,OAAO,UAAU,CAAC;gBAAc,IAAI;gBAAI,OAAO,YAAY,CAAC,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,EAAE;YAAG,GAC3I,GAAG,CAAC,CAAC,OAAS,CAAC;oBAChB,SAAS,KAAK,IAAI;oBAClB,SAAS,QAAQ,EAAE,CAAC,SAAS,QAAQ,OAAO,EAAE,KAAK;gBACvD,CAAC;QACL;IACJ;IAEA,MAAM,WAAW,CAAC,YAAY;QAC1B,MAAM,QAAQ,OAAO,WAAW,WAAW;QAC3C,OAAO,OAAO,YAAY,CAAC;YACvB,OAAO,UAAU,MAAM,CAAC;QAC5B;IACJ;IACA,mEAAmE;IACnE,qBAAqB;IACrB,MAAM,gBAAgB,CAAC,UAAU;QAC7B,OAAO,SAAS,UAAU,WAAW,GAAG,CAAC,CAAC;YACtC,MAAM,UAAU,QAAQ,MAAM,CAAC,QAAQ,cAAc,EAAE;YACvD,OAAO;gBACH,SAAS,QAAQ,IAAI;gBACrB;YACJ;QACJ;IACJ;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,OAAO,SAAS,MAAM,WAAW,GAAG,CAAC,CAAC;YAClC,MAAM,UAAU,QAAQ,MAAM,CAAC,GAAG,cAAc,EAAE;YAClD,OAAO;gBACH,SAAS,GAAG,IAAI;gBAChB;YACJ;QACJ;IACJ;IAEA,MAAM,qBAAqB;IAC3B,MAAM,gBAAgB,CAAC;QACnB,OAAO,CAAC;YACJ,OAAO,SAAS,UAAU;QAC9B;IACJ;IACA,MAAM,WAAW;QACb,sBAAsB;QACtB;YACI,MAAM;YACN,gBAAgB;gBAAC;aAAiC;YAClD,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,YAAY,SAAS,UAAU,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU;YAC7H;QACJ;QACA,0CAA0C;QAC1C;YACI,MAAM;YACN,OAAO;YACP,gBAAgB;gBAAC;gBAAmC;aAAmB;YACvE,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,aAAa,CAAC,SAAS,UAAU;YAC/D;QACJ;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAkC;aAA6B;YAChF,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,WAAW,SAAS,UAAU;YAC5D;QACJ;QACA,mDAAmD;QACnD;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAoB;aAAiC;YACtE,QAAQ,cAAc;QAC1B;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;aAAsC;YACvD,QAAQ,cAAc;QAC1B;QACA;YACI,MAAM;YACN,gBAAgB;gBAAC;gBAAoB;aAAgC;YACrE,QAAQ,CAAC;gBACL,OAAO,CAAC,SAAS,UAAU,aAAa,SAAS,UAAU,UAAU,KAAK,SAAS,UAAU;YACjG;QACJ;KACH;IACD,MAAM,OAAO;QACT;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAwC;QAC7D;QACA;YACI,MAAM;YACN,QAAQ,CAAC;gBACL,OAAO,SAAS,UAAU,aAAa,SAAS,UAAU;YAC9D;YACA,gBAAgB;gBAAC;gBAAuC;gBAAgC;aAAsC;QAClI;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAoC;QACzD;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAsC;QAC3D;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YAAE,MAAM;YACJ,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB,EAAE;QACtB;QACA;YACI,MAAM;YACN,QAAQ,cAAc;YACtB,gBAAgB;gBAAC;aAAkC;QACvD;KACH;IACD,MAAM,eAAe;QACjB,UAAU,SAAS;QACnB,MAAM,SAAS;IACnB;IAEA,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,KAAK;IACX,MAAM,QAAQ;IACd,MAAM,UAAU;IAChB,MAAM,SAAS;IACf,MAAM,YAAY;QACd,OAAO,KAAK;YACR,SAAS;YACT,SAAS,QAAQ,OAAO;QAC5B;IACJ;IACA,MAAM,OAAO,CAAC;QACV,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,YAAY,CAAC,OAAS,IAAM,YAAY;QAC9C,OAAO;YACH;YACA;YACA,QAAQ,UAAU;YAClB,YAAY,UAAU;YACtB,kCAAkC;YAClC,MAAM,UAAU;YAChB,SAAS,UAAU;YACnB,WAAW,UAAU;YACrB,UAAU,UAAU;QACxB;IACJ;IACA,MAAM,UAAU;QACZ,SAAS;QACT,IAAI;QACJ,MAAM,SAAS;QACf,UAAU,SAAS;QACnB,IAAI,SAAS;QACb,OAAO,SAAS;QAChB,SAAS,SAAS;QAClB,QAAQ,SAAS;IACrB;IAEA,MAAM,UAAU;IAChB,MAAM,MAAM;IACZ,MAAM,UAAU;IAChB,MAAM,QAAQ;IACd,MAAM,QAAQ;IACd,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,WAAW;IACjB,iEAAiE;IACjE,wDAAwD;IACxD,MAAM,UAAU;QACZ,OAAO,GAAG;YACN,SAAS;YACT,SAAS,QAAQ,OAAO;QAC5B;IACJ;IACA,MAAM,KAAK,CAAC;QACR,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,UAAU,KAAK,OAAO;QAC5B,MAAM,OAAO,CAAC,OAAS,IAAM,YAAY;QACzC,OAAO;YACH;YACA;YACA,WAAW,KAAK;YAChB,2BAA2B;YAC3B,OAAO,KAAK;YACZ,WAAW,KAAK;YAChB,SAAS,KAAK;YACd,SAAS,KAAK;YACd,WAAW,KAAK;YAChB,WAAW,KAAK;YAChB,YAAY,KAAK;QACrB;IACJ;IACA,MAAM,kBAAkB;QACpB;QACA;QACA,SAAS,SAAS;QAClB,KAAK,SAAS;QACd,SAAS,SAAS;QAClB,OAAO,SAAS;QAChB,OAAO,SAAS;QAChB,SAAS,SAAS;QAClB,SAAS,SAAS;QAClB,UAAU,SAAS;IACvB;IAEA,MAAM,WAAW,CAAC,WAAW,kBAAkB;QAC3C,MAAM,WAAW,aAAa,QAAQ;QACtC,MAAM,OAAO,aAAa,IAAI;QAC9B,MAAM,UAAU,iBAAiB,IAAI,CAAC,CAAC,gBAAkB,gBAAgB,UAAU,gBAC9E,OAAO,CAAC,IAAM,cAAc,UAAU,YACtC,IAAI,CAAC,QAAQ,OAAO,EAAE,QAAQ,EAAE;QACrC,MAAM,KAAK,SAAS,MAAM,WAAW,IAAI,CAAC,gBAAgB,OAAO,EAAE,gBAAgB,EAAE;QACrF,MAAM,aAAa,WAAW,IAAI,SAAS,WAAW;QACtD,OAAO;YACH;YACA;YACA;QACJ;IACJ;IACA,MAAM,oBAAoB;QACtB,QAAQ;IACZ;IAEA,MAAM,aAAa,CAAC,QAAU,OAAO,UAAU,CAAC,OAAO,OAAO;IAC9D,kFAAkF;IAClF,qDAAqD;IACrD,+EAA+E;IAC/E,IAAI,WAAW,OAAO,IAAM,kBAAkB,MAAM,CAAC,OAAO,SAAS,CAAC,SAAS,EAAE,SAAS,IAAI,CAAE,OAAO,SAAS,CAAC,aAAa,GAAI;IAClI,MAAM,SAAS,IAAM;IAErB,MAAM,SAAS,CAAC,MAAM;QAClB,OAAO,QAAQ,MAAM;IACzB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,SAAS,OAAO,MAAM;QAC5B,IAAI,WAAW,aAAa,WAAW,MAAM;YACzC,MAAM,IAAI,MAAM,OAAO;QAC3B;QACA,OAAO;IACX;IAEA,MAAM,iBAAiB,OAAO,cAAc;IAC5C;;;;;KAKC,GACD,MAAM,kBAAkB,CAAC;QACrB,OAAO,SAAS,eAAe;IACnC;IACA,MAAM,gBAAgB,CAAC;QACnB,4FAA4F;QAC5F,0DAA0D;QAC1D,MAAM,QAAQ,QAAQ,6BAA6B;QACnD,8FAA8F;QAC9F,2FAA2F;QAC3F,OAAO,SAAS,MAAM,CAAC,gBAAgB,OAAO,SAAS,CAAC,aAAa,CAAC,MAAM,mBAAmB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC;IAC3I;IAEA,MAAM,OAAO,CAAC,UAAY,QAAQ,GAAG,CAAC,QAAQ;IAC9C,MAAM,SAAS,CAAC,IAAM,CAAC,UAAY,KAAK,aAAa;IACrD,MAAM,gBAAgB,CAAC,UAAY,UAAU,YAAY,cAAc,QAAQ,GAAG;IAClF,MAAM,YAAY,OAAO;IACzB,MAAM,SAAS,OAAO;IACtB,MAAM,qBAAqB,OAAO;IAElC;;;KAGC,GACD,MAAM,QAAQ,CAAC,UAAY,aAAa,OAAO,CAAC,QAAQ,GAAG,CAAC,aAAa;IACzE,MAAM,SAAS,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,OAAO;IAC1F,MAAM,UAAU,CAAC,SAAS;QACtB,MAAM,OAAO,WAAW,UAAU,SAAS;QAC3C,mEAAmE;QACnE,IAAI,MAAM,QAAQ,GAAG;QACrB,MAAM,MAAM,EAAE;QACd,MAAO,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,UAAW;YAC5D,MAAM,YAAY,IAAI,UAAU;YAChC,MAAM,IAAI,aAAa,OAAO,CAAC;YAC/B,IAAI,IAAI,CAAC;YACT,IAAI,KAAK,OAAO,MAAM;gBAClB;YACJ,OACK;gBACD,MAAM;YACV;QACJ;QACA,OAAO;IACX;IACA,MAAM,aAAa,CAAC;QAChB,0FAA0F;QAC1F,MAAM,aAAa,CAAC,WAAa,SAAS,UAAU,CAAC,IAAM,CAAC,GAAG,SAAS;QACxE,OAAO,OAAO,SAAS,GAAG,CAAC,UAAU,GAAG,CAAC,YAAY,KAAK,CAAC,EAAE;IACjE;IACA,MAAM,cAAc,CAAC,UAAY,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,OAAO;IAChG,MAAM,WAAW,CAAC,UAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,aAAa,OAAO;IAE9E;;;;;KAKC,GACD,MAAM,eAAe,CAAC,MAAQ,mBAAmB,QAAQ,cAAc,IAAI,GAAG,CAAC,IAAI;IACnF,MAAM,cAAc,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,WAAW;IACjE,mDAAmD,GACnD,MAAM,gBAAgB,CAAC;QACnB,MAAM,IAAI,YAAY;QACtB,OAAO,aAAa,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;IAC7D;IACA;;;;KAIC,GACD,MAAM,gBAAgB,CAAC,IAAM,aAAa,OAAO,CAAC,EAAE,GAAG,CAAC,IAAI;IAC5D;;;;;KAKC,GACD,MAAM,yBAAyB,CAAC;QAC5B,IAAI,cAAc,MAAM,MAAM,GAAG;YAC7B,MAAM,KAAK,aAAa,OAAO,CAAC,MAAM,MAAM;YAC5C,IAAI,UAAU,OAAO,iBAAiB,KAAK;gBACvC,2FAA2F;gBAC3F,qEAAqE;gBACrE,IAAI,MAAM,QAAQ,IAAI,MAAM,YAAY,EAAE;oBACtC,MAAM,eAAe,MAAM,YAAY;oBACvC,IAAI,cAAc;wBACd,OAAO,KAAK;oBAChB;gBACJ;YACJ;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,MAAM,MAAM;IACrC;IACA;;KAEC,GACD,MAAM,mBAAmB,CAAC,UAAY,cAAc,QAAQ,GAAG,CAAC,UAAU;IAE1E,MAAM,UAAU,CAAC,QAAQ,GAAG,GAAG,MAAM,SAAS,MAAM,MAAQ,CAAC;YACzD;YACA;YACA;YACA;YACA;YACA;YACA;QACJ,CAAC;IACD;;;KAGC,GACD,MAAM,eAAe,CAAC;QAClB,MAAM,SAAS,aAAa,OAAO,CAAC,uBAAuB,UAAU,KAAK,CAAC,SAAS,MAAM;QAC1F,MAAM,OAAO,IAAM,SAAS,eAAe;QAC3C,MAAM,UAAU,IAAM,SAAS,cAAc;QAC7C,MAAM,OAAO,QAAQ,SAAS,OAAO,qDAAqD;QAC1F,qFAAqF;QACrF,OAAO,QAAQ,QAAQ,SAAS,OAAO,EAAE,SAAS,OAAO,EAAE,MAAM,SAAS,MAAM;IACpF;IACA,MAAM,SAAS,CAAC,QAAQ,UAAY,CAAC;YACjC,IAAI,OAAO,WAAW;gBAClB,QAAQ,aAAa;YACzB;QACJ;IACA,MAAM,SAAS,CAAC,SAAS,OAAO,QAAQ,SAAS;QAC7C,MAAM,UAAU,OAAO,QAAQ;QAC/B,cAAc;QACd,QAAQ,GAAG,CAAC,gBAAgB,CAAC,OAAO,SAAS;QAC7C,OAAO;YACH,QAAQ,MAAM,QAAQ,SAAS,OAAO,SAAS;QACnD;IACJ;IACA,MAAM,SAAS,CAAC,SAAS,OAAO,QAAQ,UAAY,OAAO,SAAS,OAAO,QAAQ,SAAS;IAC5F,MAAM,SAAS,CAAC,SAAS,OAAO,SAAS;QACrC,cAAc;QACd,QAAQ,GAAG,CAAC,mBAAmB,CAAC,OAAO,SAAS;IACpD;IAEA,MAAM,SAAS,QAAQ,+BAA+B;IACtD,MAAM,SAAS,CAAC,SAAS,OAAO,UAAY,OAAO,SAAS,OAAO,QAAQ;IAE3E,MAAM,SAAS,CAAC,KAAK,KAAK;QACtB;;;;SAIC,GACD,IAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;YACxD,IAAI,YAAY,CAAC,KAAK,QAAQ;QAClC,OACK;YACD,sCAAsC;YACtC,QAAQ,KAAK,CAAC,uCAAuC,KAAK,aAAa,OAAO,eAAe;YAC7F,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,QAAQ,CAAC,SAAS,KAAK;QACzB,OAAO,QAAQ,GAAG,EAAE,KAAK;IAC7B;IACA,MAAM,QAAQ,CAAC,SAAS;QACpB,MAAM,IAAI,QAAQ,GAAG,CAAC,YAAY,CAAC;QACnC,0EAA0E;QAC1E,OAAO,MAAM,OAAO,YAAY;IACpC;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,QAAQ,GAAG,CAAC,eAAe,CAAC;IAChC;IAEA,6DAA6D;IAC7D,4FAA4F;IAC5F,MAAM,cAAc,CAAC,MACrB,6DAA6D;QAC7D,IAAI,KAAK,KAAK,aAAa,WAAW,IAAI,KAAK,CAAC,gBAAgB;IAEhE,uDAAuD;IACvD,0CAA0C;IAC1C,MAAM,SAAS,CAAC;QACZ,0FAA0F;QAC1F,2FAA2F;QAC3F,MAAM,MAAM,OAAO,WAAW,QAAQ,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG;QAClE,8DAA8D;QAC9D,wFAAwF;QACxF,IAAI,QAAQ,aAAa,QAAQ,QAAQ,IAAI,aAAa,KAAK,MAAM;YACjE,OAAO;QACX;QACA,MAAM,MAAM,IAAI,aAAa;QAC7B,OAAO,cAAc,aAAa,OAAO,CAAC,MAAM,IAAI,CAAC,IAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,QAAQ;IACxG;IACA,MAAM,UAAU,CAAC;QACb,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI;QACtB,IAAI,MAAM,QAAQ,MAAM,WAAW;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,aAAa,OAAO,CAAC;IAChC;IAEA,MAAM,cAAc,CAAC,KAAK,UAAU;QAChC,oCAAoC;QACpC,qGAAqG;QACrG,4CAA4C;QAC5C,IAAI,CAAC,SAAS,QAAQ;YAClB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,sCAAsC,UAAU,aAAa,OAAO,eAAe;YACjG,MAAM,IAAI,MAAM,iCAAiC;QACrD;QACA,sGAAsG;QACtG,IAAI,YAAY,MAAM;YAClB,IAAI,KAAK,CAAC,WAAW,CAAC,UAAU;QACpC;IACJ;IACA,MAAM,MAAM,CAAC,SAAS,UAAU;QAC5B,MAAM,MAAM,QAAQ,GAAG;QACvB,YAAY,KAAK,UAAU;IAC/B;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,MAAM,MAAM,QAAQ,GAAG;QACvB,KAAK,KAAK,CAAC,GAAG;YACV,YAAY,KAAK,GAAG;QACxB;IACJ;IACA;;;;;KAKC,GACD,MAAM,QAAQ,CAAC,SAAS;QACpB,MAAM,MAAM,QAAQ,GAAG;QACvB;;;;;;;;SAQC,GACD,MAAM,SAAS,OAAO,gBAAgB,CAAC;QACvC,MAAM,IAAI,OAAO,gBAAgB,CAAC;QAClC,uHAAuH;QACvH,8BAA8B;QAC9B,OAAO,AAAC,MAAM,MAAM,CAAC,OAAO,WAAY,kBAAkB,KAAK,YAAY;IAC/E;IACA,sGAAsG;IACtG,oGAAoG;IACpG,MAAM,oBAAoB,CAAC,KAAK,WAAa,YAAY,OAAO,IAAI,KAAK,CAAC,gBAAgB,CAAC,YAAY;IAEvG,MAAM,IAAI,CAAC,MAAM;QACb,MAAM,YAAY,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,MAAM;QAC9C,OAAO;YACH;YACA;YACA;QACJ;IACJ;IACA,yCAAyC;IACzC,MAAM,gBAAgB;IAEtB,kFAAkF;IAClF,MAAM,QAAQ,CAAC;QACX,MAAM,MAAM,SAAS,YAAY,KAAK,GAAG,GAAG;QAC5C,wHAAwH;QACxH,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,eAAe,CAAC,UAAU;QAC/D,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,eAAe,CAAC,SAAS;QAC7D,OAAO,cAAc,GAAG;IAC5B;IAEA,sIAAsI;IACtI,MAAM,WAAW,CAAC,UAAY,QAAQ,GAAG,CAAC,SAAS,KAAK;IAExD,MAAM,MAAM,CAAC,SAAS,QAAU,SAAS,YAAY,QAAQ,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;IAEpF,MAAM,cAAc,CAAC,OAAO,WAAW,SAAW,SAAS,QAAQ,OAAO,SAAS;IACnF,MAAM,aAAa,CAAC,OAAO,YAAc,SAAS,WAAW,QAAQ;IAErE,MAAM,MAAM,CAAC,WAAa,MAAM;IAChC,4BAA4B;IAC5B,EAAE;IACF,yHAAyH;IACzH,8DAA8D;IAC9D,EAAE;IACF,MAAM,YAAY,CAAC,OAAO,UAAU,SACpC,gEAAgE;QAChE,+CAA+C;QAC/C,YAAY,OAAO,CAAC,IAAM,GAAG,GAAG,WAAW;IAC3C,MAAM,WAAW,CAAC,OAAO,WACzB,gEAAgE;QAChE,8CAA8C;QAC9C,WAAW,OAAO,CAAC,IAAM,GAAG,GAAG;IAE/B,MAAM,MAAM,CAAC;QACT,MAAM,MAAM,SAAS,YAAY,SAAS;QAC1C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI;YAC9B,wGAAwG;YACxG,OAAO,SAAS,IAAI;QACxB,OACK;YACD,OAAO,SAAS,IAAI,CAAC,IAAI,cAAc;QAC3C;IACJ;IACA,MAAM,SAAS,CAAC,GAAG,GAAG,OAAO,SAAW,CAAC;YACrC;YACA;YACA;YACA;YACA,OAAO,IAAI;YACX,QAAQ,IAAI;QAChB,CAAC;IACD,MAAM,YAAY,CAAC;QACf,MAAM,MAAM,SAAS,YAAY,SAAS;QAC1C,MAAM,MAAM,IAAI,QAAQ;QACxB,MAAM,SAAS,MAAM,aAAa,OAAO,CAAC;QAC1C,OAAO,IAAI,KAAK,IAAI,CAAC;YACjB,MAAM,OAAO,IAAI,QAAQ,CAAC,eAAe;YACzC,uFAAuF;YACvF,uFAAuF;YACvF,MAAM,QAAQ,KAAK,WAAW;YAC9B,MAAM,SAAS,KAAK,YAAY;YAChC,OAAO,OAAO,OAAO,IAAI,EAAE,OAAO,GAAG,EAAE,OAAO;QAClD,GAAG,CAAC,iBACJ,iHAAiH;YACjH,mHAAmH;YACnH,OAAO,KAAK,GAAG,CAAC,eAAe,QAAQ,EAAE,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,eAAe,OAAO,EAAE,OAAO,GAAG,GAAG,eAAe,KAAK,EAAE,eAAe,MAAM;IACpJ;IACA,MAAM,OAAO,CAAC,MAAM,UAAU,OAAS,IAAI,MAAM,GAAG,CAAC,CAAC;YAClD,MAAM,UAAU,CAAC,IAAM,SAAS,aAAa;YAC7C,eAAe,gBAAgB,CAAC,MAAM;YACtC,OAAO;gBACH,QAAQ,IAAM,eAAe,mBAAmB,CAAC,MAAM;YAC3D;QACJ,GAAG,UAAU,CAAC,IAAM,CAAC;gBACjB,QAAQ;YACZ,CAAC;IAED,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,6BAA6B,CAAC,QAAQ;QACxC,OAAO,QAAQ,CAAC,0BAA0B;YAAE;QAAM;QAClD,OAAO,QAAQ,CAAC;IACpB;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,sBAAsB,OAAO;IAEnC,MAAM,oBAAoB,CAAC;QACvB,MAAM,OAAO,aAAa,OAAO,CAAC,OAAO,UAAU;QACnD,OAAO,cAAc,MAAM,GAAG,CAAC,eAC1B,UAAU,CAAC,IAAM,QAAQ,MAAM;IACxC;IACA,MAAM,uBAAuB,CAAC;QAC1B,IAAI,KAAK,iBAAiB,KAAK,WAAW;YACtC,OAAO,KAAK,iBAAiB;QACjC,OACK,IAAI,KAAK,mBAAmB,KAAK,WAAW;YAC7C,OAAO,KAAK,mBAAmB;QACnC,OACK,IAAI,KAAK,uBAAuB,KAAK,WAAW;YACjD,OAAO,KAAK,uBAAuB;QACvC,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,+BAA+B;QACjC,IAAI,SAAS,iBAAiB,KAAK,WAAW;YAC1C,OAAO;QACX,OACK,IAAI,SAAS,mBAAmB,KAAK,WAAW;YACjD,OAAO,sBAAsB,sCAAsC;QACvE,OACK,IAAI,SAAS,uBAAuB,KAAK,WAAW;YACrD,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,oBAAoB,CAAC;QACvB,MAAM,OAAO,UAAU,GAAG;QAC1B,IAAI,KAAK,iBAAiB,EAAE;YACxB,mEAAmE;YACnE,KAAK,iBAAiB;QAC1B,OACK,IAAI,KAAK,mBAAmB,EAAE;YAC/B,KAAK,mBAAmB;QAC5B,OACK,IAAI,KAAK,uBAAuB,EAAE;YACnC,KAAK,uBAAuB;QAChC;IACJ;IACA,MAAM,iBAAiB,CAAC;QACpB,MAAM,MAAM,SAAS,GAAG;QACxB,IAAI,IAAI,cAAc,EAAE;YACpB,mEAAmE;YACnE,IAAI,cAAc;QACtB,OACK,IAAI,IAAI,gBAAgB,EAAE;YAC3B,IAAI,gBAAgB;QACxB,OACK,IAAI,IAAI,sBAAsB,EAAE;YACjC,IAAI,sBAAsB;QAC9B;IACJ;IACA,MAAM,sBAAsB,CAAC,OAAS,KAAK,GAAG,KAAK,qBAAqB,MAAM,MAAM,GAAG;IAEvF,MAAM,OAAO;IACb,MAAM,gBAAgB;IACtB,MAAM,mBAAmB;IACzB,gIAAgI;IAChI,MAAM,iBAAiB;IACvB,MAAM,aAAa;IACnB,MAAM,YAAY,SAAS,EAAE,CAAC,SAAS;IACvC,MAAM,aAAa,CAAC;QAChB,6GAA6G;QAC7G,+FAA+F;QAC/F,MAAM,QAAQ,MAAM,YAAY;QAChC,OAAO,AAAC,UAAU,aAAa,UAAU,KAAM,sBAAsB,QAAQ,eAAe;IAChG;IACA,iHAAiH;IACjH,MAAM,gBAAgB,CAAC,KAAK,WAAW;QACnC,MAAM,iBAAiB,CAAC;YACpB,OAAO,SAAS,SAAS;QAC7B;QACA,MAAM,UAAU,CAAC,eAAiB,CAAC;gBAC/B,MAAM,SAAS,MAAM,SAAS;gBAC9B,MAAM,SAAS,WAAW,YAAY,cAAc,OAAO,IAAI;gBAC/D,IAAI,WAAW,cAAc;oBACzB;gBACJ,OACK;oBACD,MAAM,SAAS,MAAM;oBACrB,OAAO,SAAS,IAAI,UAAU,CAAC;gBACnC;YACJ;QACA,MAAM,cAAc,UAAU,WAAW;QACzC,MAAM,aAAa,OAAO,aAAa;QACvC,MAAM,UAAU,WAAW;QAC3B,4DAA4D,GAC5D,OAAO,YAAY,QAAQ;QAC3B,OAAO,aAAa,QAAQ,mBAAmB,iBAAiB;QAChE,sEAAsE;QACtE,MAAM,kBAAkB,cAAc,OAAO,KAAK;QAClD,QAAQ,kBAAkB,iBAAiB,SAAS;IACxD;IACA,MAAM,gBAAgB,CAAC;QACnB,MAAM,eAAe,IAAI,MAAM,OAAO;QACtC,OAAO,cAAc,CAAC;YAClB,MAAM,UAAU,MAAM,SAAS;YAC/B,IAAI,WAAW,YAAY,aAAa;gBACpC,OAAO,SAAS,IAAI,UAAU,CAAC;YACnC,OACK;gBACD,OAAO,SAAS;YACpB;YACA,OAAO,SAAS;QACpB;IACJ;IAEA,MAAM,MAAM,SAAS,GAAG;IACxB,MAAM,eAAe,IAAM,UAAU;IACrC,MAAM,eAAe,CAAC,MAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1D,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAM,CAAC;YAAE,MAAM;YAAM,QAAQ;QAAK,CAAC,GAAG,CAAC;QACrE,MAAM,kBAAkB;QACxB,MAAM,eAAe;QACrB,MAAM,eAAe;QACrB,MAAM,gBAAgB;YAClB,SAAS,IAAI,CAAC,SAAS,GAAG;YAC1B,SAAS,eAAe,CAAC,SAAS,GAAG;QACzC;QACA,MAAM,wBAAwB;YAC1B,OAAO,qBAAqB,CAAC;gBACzB,gBAAgB,EAAE,CAAC,CAAC,YAAc,OAAO,WAAW;wBAChD,KAAK,eAAe,SAAS,GAAG;wBAChC,MAAM,eAAe,UAAU,GAAG;wBAClC,QAAQ,eAAe,MAAM,GAAG;wBAChC,OAAO,eAAe,KAAK,GAAG;oBAClC;YACJ;QACJ;QACA,MAAM,SAAS,MAAM;YACjB;YACA;QACJ,GAAG;QACH,MAAM,SAAS,CAAC;YACZ,gBAAgB,GAAG,CAAC;YACpB,OAAO,QAAQ;YACf,aAAa,GAAG,CAAC,KAAK,UAAU,OAAO,QAAQ;YAC/C,aAAa,GAAG,CAAC,KAAK,UAAU,OAAO,QAAQ;QACnD;QACA,MAAM,SAAS;YACX,gBAAgB,EAAE,CAAC;gBACf,aAAa,KAAK;gBAClB,aAAa,KAAK;YACtB;YACA,gBAAgB,KAAK;QACzB;QACA,OAAO;YACH,MAAM;YACN;QACJ;IACJ;IACA,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,MAAM,OAAO,SAAS,IAAI;QAC1B,MAAM,kBAAkB,SAAS,eAAe;QAChD,MAAM,kBAAkB,OAAO,YAAY;QAC3C,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,MAAM,iBAAiB,YAAY,kBAC9B,MAAM,CAAC,CAAC,MAAQ,cAAc,QAAQ,IAAI,KAAK;QACpD,MAAM,iBAAiB,kBAAkB;QACzC,MAAM,iBAAiB,gBAAgB,GAAG;QAC1C,MAAM,aAAa,aAAa,OAAO,CAAC,OAAO,OAAO;QACtD,MAAM,UAAU,SAAS,UAAU,CAAC,OAAO;QAC3C,MAAM,uBAAuB,gBAAgB,KAAK;QAClD,MAAM,SAAS,OAAO,aAAa;QACnC,MAAM,cAAc,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK;QAChF,MAAM,gBAAgB,CAAC;YACnB,QAAQ,MAAM;YACd,QAAQ,iBAAiB;YACzB,QAAQ,iBAAiB;YACzB,cAAc,kBACT,GAAG,CAAC,CAAC,OAAS,cAAc,MAAM,GAAG,EACrC,IAAI,CAAC,CAAC;gBACP,QAAQ,MAAM;gBACd,QAAQ,MAAM;YAClB;QACJ;QACA,MAAM,UAAU;YACZ,IAAI,SAAS;gBACT,cAAc,OAAO,GAAG;YAC5B;YACA,cAAc,IAAI,WAAW;YAC7B,eAAe,MAAM;YACrB,SAAS,IAAI,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC,OAAS,KAAK,uBAAuB,CAAC,MAAM;QAC3F;QACA,IAAI,CAAC,gBAAgB;YACjB,MAAM,0BAA0B,OAAO,MAAM,iBAAiB,gCAAgC,CAAC;gBAC3F,IAAI,oBAAoB,SAAS;oBAC7B,mFAAmF;oBACnF,IAAI,CAAC,oBAAoB,mBAAmB,gBAAgB,GAAG,OAAO,MAAM;wBACxE,iBAAiB,QAAQ;oBAC7B;gBACJ;YACJ;YACA,MAAM,oBAAoB;gBACtB,WAAW;gBACX,gBAAgB,qBAAqB,KAAK;gBAC1C,iBAAiB,qBAAqB,MAAM;gBAC5C,cAAc,qBAAqB,GAAG;gBACtC,eAAe,qBAAqB,IAAI;gBACxC,aAAa,YAAY,KAAK;gBAC9B,cAAc,YAAY,MAAM;gBAChC;gBACA,iBAAiB,eAAe,GAAG,CAAC,CAAC,MAAQ,MAAM,KAAK;YAC5D;YACA,IAAI,SAAS;gBACT,cAAc,OAAO,GAAG,EAAE,kBAAkB;YAChD;YACA,YAAY,KAAK,GAAG,YAAY,MAAM,GAAG;YACzC,qBAAqB,KAAK,GAAG,qBAAqB,MAAM,GAAG;YAC3D,cAAc,IAAI,QAAQ;YAC1B,eAAe,IAAI,CAAC,CAAC;gBACjB,IAAI,KAAK,YAAY;YACzB;YACA,eAAe,IAAI,CAAC;YACpB,OAAO,EAAE,CAAC,UAAU;YACpB,gBAAgB,GAAG,CAAC;YACpB,IAAI,oBAAoB,SAAS;gBAC7B,kBAAkB;YACtB;YACA,2BAA2B,QAAQ;QACvC,OACK;YACD,eAAe,uBAAuB,CAAC,MAAM;YAC7C,IAAI,oBAAoB,WAAW,oBAAoB,iBAAiB;gBACpE,eAAe,MAAM;YACzB;YACA,YAAY,KAAK,GAAG,eAAe,WAAW;YAC9C,YAAY,MAAM,GAAG,eAAe,YAAY;YAChD,qBAAqB,KAAK,GAAG,eAAe,cAAc;YAC1D,qBAAqB,MAAM,GAAG,eAAe,eAAe;YAC5D,qBAAqB,GAAG,GAAG,eAAe,YAAY;YACtD,qBAAqB,IAAI,GAAG,eAAe,aAAa;YACxD,MAAM,gBAAgB,eAAe,eAAe,EAAE,CAAC,KAAK;gBACxD,IAAI,KAAK,YAAY;YACzB;YACA;YACA,aAAa,eAAe,SAAS;YACrC,gBAAgB,GAAG,CAAC;YACpB,2BAA2B,QAAQ;YACnC,OAAO,GAAG,CAAC,UAAU;QACzB;IACJ;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,iBAAiB;YAC/B,iBAAiB,QAAQ;QAC7B;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,QAAQ,CAAC,QAAQ;QACnB,OAAO,EAAE,CAAC,QAAQ;YACd,OAAO,EAAE,CAAC,WAAW,CAAC;gBAClB,IAAI,EAAE,OAAO,KAAK,OAAO,GAAG,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,gBAAgB,GAAG,IAAI;oBAChF,EAAE,cAAc;gBACpB;YACJ;QACJ;IACJ;IAEA,MAAM,mBAAmB,CAAC,QAAQ,kBAAoB,CAAC;YACnD,IAAI,SAAS,CAAC,gBAAgB,GAAG,OAAO;YACxC,MAAM,sBAAsB,CAAC,IAAM,IAAI,SAAS,CAAC,EAAE,KAAK;YACxD,OAAO,EAAE,CAAC,0BAA0B;YACpC,OAAO,IAAM,OAAO,GAAG,CAAC,0BAA0B;QACtD;IACA,MAAM,WAAW,CAAC,QAAQ;QACtB,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc;YAC/C,MAAM;YACN,MAAM;YACN,UAAU;YACV;YACA,SAAS,iBAAiB,QAAQ;YAClC,SAAS;QACb;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc;YAC7C,SAAS;YACT,MAAM;YACN;YACA,SAAS,iBAAiB,QAAQ;YAClC,UAAU;YACV,SAAS;QACb;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,cAAc,CAAC;YACxB,MAAM,kBAAkB,KAAK;YAC7B,IAAI,OAAO,MAAM,EAAE;gBACf,OAAO,MAAM;YACjB;YACA,WAAW;YACX,WAAW,QAAQ;YACnB,SAAS,QAAQ;YACjB,MAAM,QAAQ;YACd,OAAO,WAAW,CAAC,gBAAgB,IAAI;YACvC,OAAO,MAAM;QACjB;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/fullscreen/index.js"], "sourcesContent": ["// Exports the \"fullscreen\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/fullscreen')\n//   ES2015:\n//     import 'tinymce/plugins/fullscreen'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,SAAS;AACT,cAAc;AACd,4CAA4C;AAC5C,YAAY;AACZ,0CAA0C", "ignoreList": [0], "debugId": null}}]}