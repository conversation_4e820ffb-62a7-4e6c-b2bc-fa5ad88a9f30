{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/insertdatetime/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('insertdatetime_dateformat', {\n            processor: 'string',\n            default: editor.translate('%Y-%m-%d')\n        });\n        registerOption('insertdatetime_timeformat', {\n            processor: 'string',\n            default: editor.translate('%H:%M:%S')\n        });\n        registerOption('insertdatetime_formats', {\n            processor: 'string[]',\n            default: ['%H:%M:%S', '%Y-%m-%d', '%I:%M:%S %p', '%D']\n        });\n        registerOption('insertdatetime_element', {\n            processor: 'boolean',\n            default: false\n        });\n    };\n    const getDateFormat = option('insertdatetime_dateformat');\n    const getTimeFormat = option('insertdatetime_timeformat');\n    const getFormats = option('insertdatetime_formats');\n    const shouldInsertTimeElement = option('insertdatetime_element');\n    const getDefaultDateTime = (editor) => {\n        const formats = getFormats(editor);\n        return formats.length > 0 ? formats[0] : getTimeFormat(editor);\n    };\n\n    const daysShort = 'Sun Mon Tue Wed Thu Fri Sat Sun'.split(' ');\n    const daysLong = 'Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday'.split(' ');\n    const monthsShort = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec'.split(' ');\n    const monthsLong = 'January February March April May June July August September October November December'.split(' ');\n    const addZeros = (value, len) => {\n        value = '' + value;\n        if (value.length < len) {\n            for (let i = 0; i < (len - value.length); i++) {\n                value = '0' + value;\n            }\n        }\n        return value;\n    };\n    const getDateTime = (editor, fmt, date = new Date()) => {\n        fmt = fmt.replace('%D', '%m/%d/%Y');\n        fmt = fmt.replace('%r', '%I:%M:%S %p');\n        fmt = fmt.replace('%Y', '' + date.getFullYear());\n        fmt = fmt.replace('%y', '' + date.getYear());\n        fmt = fmt.replace('%m', addZeros(date.getMonth() + 1, 2));\n        fmt = fmt.replace('%d', addZeros(date.getDate(), 2));\n        fmt = fmt.replace('%H', '' + addZeros(date.getHours(), 2));\n        fmt = fmt.replace('%M', '' + addZeros(date.getMinutes(), 2));\n        fmt = fmt.replace('%S', '' + addZeros(date.getSeconds(), 2));\n        fmt = fmt.replace('%I', '' + ((date.getHours() + 11) % 12 + 1));\n        fmt = fmt.replace('%p', '' + (date.getHours() < 12 ? 'AM' : 'PM'));\n        fmt = fmt.replace('%B', '' + editor.translate(monthsLong[date.getMonth()]));\n        fmt = fmt.replace('%b', '' + editor.translate(monthsShort[date.getMonth()]));\n        fmt = fmt.replace('%A', '' + editor.translate(daysLong[date.getDay()]));\n        fmt = fmt.replace('%a', '' + editor.translate(daysShort[date.getDay()]));\n        fmt = fmt.replace('%%', '%');\n        return fmt;\n    };\n    const updateElement = (editor, timeElm, computerTime, userTime) => {\n        const newTimeElm = editor.dom.create('time', { datetime: computerTime }, userTime);\n        editor.dom.replace(newTimeElm, timeElm);\n        editor.selection.select(newTimeElm, true);\n        editor.selection.collapse(false);\n    };\n    const insertDateTime = (editor, format) => {\n        if (shouldInsertTimeElement(editor) && editor.selection.isEditable()) {\n            const userTime = getDateTime(editor, format);\n            let computerTime;\n            if (/%[HMSIp]/.test(format)) {\n                computerTime = getDateTime(editor, '%Y-%m-%dT%H:%M');\n            }\n            else {\n                computerTime = getDateTime(editor, '%Y-%m-%d');\n            }\n            const timeElm = editor.dom.getParent(editor.selection.getStart(), 'time');\n            if (timeElm) {\n                updateElement(editor, timeElm, computerTime, userTime);\n            }\n            else {\n                editor.insertContent('<time datetime=\"' + computerTime + '\">' + userTime + '</time>');\n            }\n        }\n        else {\n            editor.insertContent(getDateTime(editor, format));\n        }\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('mceInsertDate', (_ui, value) => {\n            insertDateTime(editor, value !== null && value !== void 0 ? value : getDateFormat(editor));\n        });\n        editor.addCommand('mceInsertTime', (_ui, value) => {\n            insertDateTime(editor, value !== null && value !== void 0 ? value : getTimeFormat(editor));\n        });\n    };\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        const formats = getFormats(editor);\n        const defaultFormat = Cell(getDefaultDateTime(editor));\n        const insertDateTime = (format) => editor.execCommand('mceInsertDate', false, format);\n        editor.ui.registry.addSplitButton('insertdatetime', {\n            icon: 'insert-time',\n            tooltip: 'Insert date/time',\n            select: (value) => value === defaultFormat.get(),\n            fetch: (done) => {\n                done(global.map(formats, (format) => ({ type: 'choiceitem', text: getDateTime(editor, format), value: format })));\n            },\n            onAction: (_api) => {\n                insertDateTime(defaultFormat.get());\n            },\n            onItemAction: (_api, value) => {\n                defaultFormat.set(value);\n                insertDateTime(value);\n            },\n            onSetup: onSetupEditable(editor)\n        });\n        const makeMenuItemHandler = (format) => () => {\n            defaultFormat.set(format);\n            insertDateTime(format);\n        };\n        editor.ui.registry.addNestedMenuItem('insertdatetime', {\n            icon: 'insert-time',\n            text: 'Date/time',\n            getSubmenuItems: () => global.map(formats, (format) => ({\n                type: 'menuitem',\n                text: getDateTime(editor, format),\n                onAction: makeMenuItemHandler(format)\n            })),\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    var Plugin = () => {\n        global$1.add('insertdatetime', (editor) => {\n            register$2(editor);\n            register$1(editor);\n            register(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,6BAA6B;YACxC,WAAW;YACX,SAAS,OAAO,SAAS,CAAC;QAC9B;QACA,eAAe,6BAA6B;YACxC,WAAW;YACX,SAAS,OAAO,SAAS,CAAC;QAC9B;QACA,eAAe,0BAA0B;YACrC,WAAW;YACX,SAAS;gBAAC;gBAAY;gBAAY;gBAAe;aAAK;QAC1D;QACA,eAAe,0BAA0B;YACrC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,gBAAgB,OAAO;IAC7B,MAAM,gBAAgB,OAAO;IAC7B,MAAM,aAAa,OAAO;IAC1B,MAAM,0BAA0B,OAAO;IACvC,MAAM,qBAAqB,CAAC;QACxB,MAAM,UAAU,WAAW;QAC3B,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG,cAAc;IAC3D;IAEA,MAAM,YAAY,kCAAkC,KAAK,CAAC;IAC1D,MAAM,WAAW,kEAAkE,KAAK,CAAC;IACzF,MAAM,cAAc,kDAAkD,KAAK,CAAC;IAC5E,MAAM,aAAa,wFAAwF,KAAK,CAAC;IACjH,MAAM,WAAW,CAAC,OAAO;QACrB,QAAQ,KAAK;QACb,IAAI,MAAM,MAAM,GAAG,KAAK;YACpB,IAAK,IAAI,IAAI,GAAG,IAAK,MAAM,MAAM,MAAM,EAAG,IAAK;gBAC3C,QAAQ,MAAM;YAClB;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,SAAC,QAAQ;YAAK,wEAAO,IAAI;QACzC,MAAM,IAAI,OAAO,CAAC,MAAM;QACxB,MAAM,IAAI,OAAO,CAAC,MAAM;QACxB,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,WAAW;QAC7C,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO;QACzC,MAAM,IAAI,OAAO,CAAC,MAAM,SAAS,KAAK,QAAQ,KAAK,GAAG;QACtD,MAAM,IAAI,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI;QACjD,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,KAAK,QAAQ,IAAI;QACvD,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,KAAK,UAAU,IAAI;QACzD,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,KAAK,UAAU,IAAI;QACzD,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,CAAC;QAC7D,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,KAAK,QAAQ,KAAK,KAAK,OAAO,IAAI;QAChE,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,GAAG;QACzE,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,GAAG;QAC1E,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;QACrE,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG;QACtE,MAAM,IAAI,OAAO,CAAC,MAAM;QACxB,OAAO;IACX;IACA,MAAM,gBAAgB,CAAC,QAAQ,SAAS,cAAc;QAClD,MAAM,aAAa,OAAO,GAAG,CAAC,MAAM,CAAC,QAAQ;YAAE,UAAU;QAAa,GAAG;QACzE,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY;QAC/B,OAAO,SAAS,CAAC,MAAM,CAAC,YAAY;QACpC,OAAO,SAAS,CAAC,QAAQ,CAAC;IAC9B;IACA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,IAAI,wBAAwB,WAAW,OAAO,SAAS,CAAC,UAAU,IAAI;YAClE,MAAM,WAAW,YAAY,QAAQ;YACrC,IAAI;YACJ,IAAI,WAAW,IAAI,CAAC,SAAS;gBACzB,eAAe,YAAY,QAAQ;YACvC,OACK;gBACD,eAAe,YAAY,QAAQ;YACvC;YACA,MAAM,UAAU,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,QAAQ,IAAI;YAClE,IAAI,SAAS;gBACT,cAAc,QAAQ,SAAS,cAAc;YACjD,OACK;gBACD,OAAO,aAAa,CAAC,qBAAqB,eAAe,OAAO,WAAW;YAC/E;QACJ,OACK;YACD,OAAO,aAAa,CAAC,YAAY,QAAQ;QAC7C;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK;YACrC,eAAe,QAAQ,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,cAAc;QACtF;QACA,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK;YACrC,eAAe,QAAQ,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,cAAc;QACtF;IACJ;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,UAAU,WAAW;QAC3B,MAAM,gBAAgB,KAAK,mBAAmB;QAC9C,MAAM,iBAAiB,CAAC,SAAW,OAAO,WAAW,CAAC,iBAAiB,OAAO;QAC9E,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB;YAChD,MAAM;YACN,SAAS;YACT,QAAQ,CAAC,QAAU,UAAU,cAAc,GAAG;YAC9C,OAAO,CAAC;gBACJ,KAAK,OAAO,GAAG,CAAC,SAAS,CAAC,SAAW,CAAC;wBAAE,MAAM;wBAAc,MAAM,YAAY,QAAQ;wBAAS,OAAO;oBAAO,CAAC;YAClH;YACA,UAAU,CAAC;gBACP,eAAe,cAAc,GAAG;YACpC;YACA,cAAc,CAAC,MAAM;gBACjB,cAAc,GAAG,CAAC;gBAClB,eAAe;YACnB;YACA,SAAS,gBAAgB;QAC7B;QACA,MAAM,sBAAsB,CAAC,SAAW;gBACpC,cAAc,GAAG,CAAC;gBAClB,eAAe;YACnB;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB;YACnD,MAAM;YACN,MAAM;YACN,iBAAiB,IAAM,OAAO,GAAG,CAAC,SAAS,CAAC,SAAW,CAAC;wBACpD,MAAM;wBACN,MAAM,YAAY,QAAQ;wBAC1B,UAAU,oBAAoB;oBAClC,CAAC;YACD,SAAS,gBAAgB;QAC7B;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,kBAAkB,CAAC;YAC5B,WAAW;YACX,WAAW;YACX,SAAS;QACb;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/insertdatetime/index.js"], "sourcesContent": ["// Exports the \"insertdatetime\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/insertdatetime')\n//   ES2015:\n//     import 'tinymce/plugins/insertdatetime'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,oEAAoE;AACpE,SAAS;AACT,cAAc;AACd,gDAAgD;AAChD,YAAY;AACZ,8CAA8C", "ignoreList": [0], "debugId": null}}]}