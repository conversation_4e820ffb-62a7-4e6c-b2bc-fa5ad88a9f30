
from src.prompts.revise_manually_prompt import get_prompt
from src.models.gemini_model import ask_ai
import re

def revise_manually(newsletter : str) -> str :

    prompt = get_prompt(newsletter)
    generated_html = ask_ai(prompt).strip()
    cleaned = re.sub(r"^```html\s*", "", generated_html)  # Enlève ```html au début
    cleaned = re.sub(r"\s*```$", "", cleaned)   
     
    
    return cleaned.strip() if cleaned else ""



