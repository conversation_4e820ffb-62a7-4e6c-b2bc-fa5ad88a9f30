{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/media/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativePush = Array.prototype.push;\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const get$1 = (obj, key) => {\n        return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    /** Does 'str' start with 'prefix'?\n     *  Note: all strings start with the empty string.\n     *        More formally, for all strings x, startsWith(x, \"\").\n     *        This is so that for all strings x and y, startsWith(y + x, y)\n     */\n    const startsWith = (str, prefix) => {\n        return checkRange(str, prefix, 0);\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('audio_template_callback', {\n            processor: 'function'\n        });\n        registerOption('video_template_callback', {\n            processor: 'function'\n        });\n        registerOption('iframe_template_callback', {\n            processor: 'function'\n        });\n        registerOption('media_live_embeds', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('media_filter_html', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('media_url_resolver', {\n            processor: 'function'\n        });\n        registerOption('media_alt_source', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('media_poster', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('media_dimensions', {\n            processor: 'boolean',\n            default: true\n        });\n    };\n    const getAudioTemplateCallback = option('audio_template_callback');\n    const getVideoTemplateCallback = option('video_template_callback');\n    const getIframeTemplateCallback = option('iframe_template_callback');\n    const hasLiveEmbeds = option('media_live_embeds');\n    const shouldFilterHtml = option('media_filter_html');\n    const getUrlResolver = option('media_url_resolver');\n    const hasAltSource = option('media_alt_source');\n    const hasPoster = option('media_poster');\n    const hasDimensions = option('media_dimensions');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.html.DomParser');\n\n    const DOM$1 = global$4.DOM;\n    const trimPx = (value) => value.replace(/px$/, '');\n    const getEphoxEmbedData = (node) => {\n        const style = node.attr('style');\n        const styles = style ? DOM$1.parseStyle(style) : {};\n        return {\n            type: 'ephox-embed-iri',\n            source: node.attr('data-ephox-embed-iri'),\n            altsource: '',\n            poster: '',\n            width: get$1(styles, 'max-width').map(trimPx).getOr(''),\n            height: get$1(styles, 'max-height').map(trimPx).getOr('')\n        };\n    };\n    const htmlToData = (html, schema) => {\n        let data = {};\n        const parser = global$3({ validate: false, forced_root_block: false }, schema);\n        const rootNode = parser.parse(html);\n        for (let node = rootNode; node; node = node.walk()) {\n            if (node.type === 1) {\n                const name = node.name;\n                if (node.attr('data-ephox-embed-iri')) {\n                    data = getEphoxEmbedData(node);\n                    // Don't continue to collect if we find an EME embed\n                    break;\n                }\n                else {\n                    if (!data.source && name === 'param') {\n                        data.source = node.attr('movie');\n                    }\n                    if (name === 'iframe' || name === 'object' || name === 'embed' || name === 'video' || name === 'audio') {\n                        if (!data.type) {\n                            data.type = name;\n                        }\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        data = global$5.extend(node.attributes.map, data);\n                    }\n                    if (name === 'source') {\n                        if (!data.source) {\n                            data.source = node.attr('src');\n                        }\n                        else if (!data.altsource) {\n                            data.altsource = node.attr('src');\n                        }\n                    }\n                    if (name === 'img' && !data.poster) {\n                        data.poster = node.attr('src');\n                    }\n                }\n            }\n        }\n        data.source = data.source || data.src || '';\n        data.altsource = data.altsource || '';\n        data.poster = data.poster || '';\n        return data;\n    };\n\n    const guess = (url) => {\n        var _a;\n        const mimes = {\n            mp3: 'audio/mpeg',\n            m4a: 'audio/x-m4a',\n            wav: 'audio/wav',\n            mp4: 'video/mp4',\n            webm: 'video/webm',\n            ogg: 'video/ogg',\n            swf: 'application/x-shockwave-flash'\n        };\n        const fileEnd = (_a = url.toLowerCase().split('.').pop()) !== null && _a !== void 0 ? _a : '';\n        return get$1(mimes, fileEnd).getOr('');\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.html.Serializer');\n\n    const Parser = (schema, settings = {}) => global$3({\n        forced_root_block: false,\n        validate: false,\n        allow_conditional_comments: true,\n        ...settings\n    }, schema);\n\n    const DOM = global$4.DOM;\n    const addPx = (value) => /^[0-9.]+$/.test(value) ? (value + 'px') : value;\n    const updateEphoxEmbed = (data, node) => {\n        const style = node.attr('style');\n        const styleMap = style ? DOM.parseStyle(style) : {};\n        if (isNonNullable(data.width)) {\n            styleMap['max-width'] = addPx(data.width);\n        }\n        if (isNonNullable(data.height)) {\n            styleMap['max-height'] = addPx(data.height);\n        }\n        node.attr('style', DOM.serializeStyle(styleMap));\n    };\n    const sources = ['source', 'altsource'];\n    const updateHtml = (html, data, updateAll, schema) => {\n        let numSources = 0;\n        let sourceCount = 0;\n        const parser = Parser(schema);\n        parser.addNodeFilter('source', (nodes) => numSources = nodes.length);\n        const rootNode = parser.parse(html);\n        for (let node = rootNode; node; node = node.walk()) {\n            if (node.type === 1) {\n                const name = node.name;\n                if (node.attr('data-ephox-embed-iri')) {\n                    updateEphoxEmbed(data, node);\n                    // Don't continue to update if we find an EME embed\n                    break;\n                }\n                else {\n                    switch (name) {\n                        case 'video':\n                        case 'object':\n                        case 'embed':\n                        case 'img':\n                        case 'iframe':\n                            if (data.height !== undefined && data.width !== undefined) {\n                                node.attr('width', data.width);\n                                node.attr('height', data.height);\n                            }\n                            break;\n                    }\n                    if (updateAll) {\n                        switch (name) {\n                            case 'video':\n                                node.attr('poster', data.poster);\n                                node.attr('src', null);\n                                // Add <source> child elements\n                                for (let index = numSources; index < 2; index++) {\n                                    if (data[sources[index]]) {\n                                        const source = new global$2('source', 1);\n                                        source.attr('src', data[sources[index]]);\n                                        source.attr('type', data[sources[index] + 'mime'] || null);\n                                        node.append(source);\n                                    }\n                                }\n                                break;\n                            case 'iframe':\n                                node.attr('src', data.source);\n                                break;\n                            case 'object':\n                                const hasImage = node.getAll('img').length > 0;\n                                if (data.poster && !hasImage) {\n                                    node.attr('src', data.poster);\n                                    const img = new global$2('img', 1);\n                                    img.attr('src', data.poster);\n                                    img.attr('width', data.width);\n                                    img.attr('height', data.height);\n                                    node.append(img);\n                                }\n                                break;\n                            case 'source':\n                                if (sourceCount < 2) {\n                                    node.attr('src', data[sources[sourceCount]]);\n                                    node.attr('type', data[sources[sourceCount] + 'mime'] || null);\n                                    if (!data[sources[sourceCount]]) {\n                                        node.remove();\n                                        continue;\n                                    }\n                                }\n                                sourceCount++;\n                                break;\n                            case 'img':\n                                if (!data.poster) {\n                                    node.remove();\n                                }\n                                break;\n                        }\n                    }\n                }\n            }\n        }\n        return global$1({}, schema).serialize(rootNode);\n    };\n\n    const urlPatterns = [\n        {\n            regex: /youtu\\.be\\/([\\w\\-_\\?&=.]+)/i,\n            type: 'iframe', w: 560, h: 314,\n            url: 'www.youtube.com/embed/$1',\n            allowFullscreen: true\n        },\n        {\n            regex: /youtube\\.com(.+)v=([^&]+)(&([a-z0-9&=\\-_]+))?/i,\n            type: 'iframe', w: 560, h: 314,\n            url: 'www.youtube.com/embed/$2?$4',\n            allowFullscreen: true\n        },\n        {\n            regex: /youtube.com\\/embed\\/([a-z0-9\\?&=\\-_]+)/i,\n            type: 'iframe', w: 560, h: 314,\n            url: 'www.youtube.com/embed/$1',\n            allowFullscreen: true\n        },\n        {\n            regex: /vimeo\\.com\\/([0-9]+)\\?h=(\\w+)/,\n            type: 'iframe', w: 425, h: 350,\n            url: 'player.vimeo.com/video/$1?h=$2&title=0&byline=0&portrait=0&color=8dc7dc',\n            allowFullscreen: true\n        },\n        {\n            regex: /vimeo\\.com\\/(.*)\\/([0-9]+)\\?h=(\\w+)/,\n            type: 'iframe', w: 425, h: 350,\n            url: 'player.vimeo.com/video/$2?h=$3&title=0&amp;byline=0',\n            allowFullscreen: true\n        },\n        {\n            regex: /vimeo\\.com\\/([0-9]+)/,\n            type: 'iframe', w: 425, h: 350,\n            url: 'player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc',\n            allowFullscreen: true\n        },\n        {\n            regex: /vimeo\\.com\\/(.*)\\/([0-9]+)/,\n            type: 'iframe', w: 425, h: 350,\n            url: 'player.vimeo.com/video/$2?title=0&amp;byline=0',\n            allowFullscreen: true\n        },\n        {\n            regex: /maps\\.google\\.([a-z]{2,3})\\/maps\\/(.+)msid=(.+)/,\n            type: 'iframe', w: 425, h: 350,\n            url: 'maps.google.com/maps/ms?msid=$2&output=embed\"',\n            allowFullscreen: false\n        },\n        {\n            regex: /dailymotion\\.com\\/video\\/([^_]+)/,\n            type: 'iframe', w: 480, h: 270,\n            url: 'www.dailymotion.com/embed/video/$1',\n            allowFullscreen: true\n        },\n        {\n            regex: /dai\\.ly\\/([^_]+)/,\n            type: 'iframe', w: 480, h: 270,\n            url: 'www.dailymotion.com/embed/video/$1',\n            allowFullscreen: true\n        }\n    ];\n    const getProtocol = (url) => {\n        const protocolMatches = url.match(/^(https?:\\/\\/|www\\.)(.+)$/i);\n        if (protocolMatches && protocolMatches.length > 1) {\n            return protocolMatches[1] === 'www.' ? 'https://' : protocolMatches[1];\n        }\n        else {\n            return 'https://';\n        }\n    };\n    const getUrl = (pattern, url) => {\n        const protocol = getProtocol(url);\n        const match = pattern.regex.exec(url);\n        let newUrl = protocol + pattern.url;\n        if (isNonNullable(match)) {\n            for (let i = 0; i < match.length; i++) {\n                newUrl = newUrl.replace('$' + i, () => match[i] ? match[i] : '');\n            }\n        }\n        return newUrl.replace(/\\?$/, '');\n    };\n    const matchPattern = (url) => {\n        const patterns = urlPatterns.filter((pattern) => pattern.regex.test(url));\n        if (patterns.length > 0) {\n            return global$5.extend({}, patterns[0], { url: getUrl(patterns[0], url) });\n        }\n        else {\n            return null;\n        }\n    };\n\n    const getIframeHtml = (data, iframeTemplateCallback) => {\n        if (iframeTemplateCallback) {\n            return iframeTemplateCallback(data);\n        }\n        else {\n            const allowFullscreen = data.allowfullscreen ? ' allowFullscreen=\"1\"' : '';\n            return '<iframe src=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\"' + allowFullscreen + '></iframe>';\n        }\n    };\n    const getFlashHtml = (data) => {\n        let html = '<object data=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" type=\"application/x-shockwave-flash\">';\n        if (data.poster) {\n            html += '<img src=\"' + data.poster + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" />';\n        }\n        html += '</object>';\n        return html;\n    };\n    const getAudioHtml = (data, audioTemplateCallback) => {\n        if (audioTemplateCallback) {\n            return audioTemplateCallback(data);\n        }\n        else {\n            return ('<audio controls=\"controls\" src=\"' + data.source + '\">' +\n                (data.altsource ?\n                    '\\n<source src=\"' + data.altsource + '\"' +\n                        (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') +\n                        ' />\\n' : '') +\n                '</audio>');\n        }\n    };\n    const getVideoHtml = (data, videoTemplateCallback) => {\n        if (videoTemplateCallback) {\n            return videoTemplateCallback(data);\n        }\n        else {\n            return ('<video width=\"' + data.width +\n                '\" height=\"' + data.height + '\"' +\n                (data.poster ? ' poster=\"' + data.poster + '\"' : '') + ' controls=\"controls\">\\n' +\n                '<source src=\"' + data.source + '\"' +\n                (data.sourcemime ? ' type=\"' + data.sourcemime + '\"' : '') + ' />\\n' +\n                (data.altsource ? '<source src=\"' + data.altsource + '\"' +\n                    (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') + ' />\\n' : '') +\n                '</video>');\n        }\n    };\n    const dataToHtml = (editor, dataIn) => {\n        var _a;\n        const data = global$5.extend({}, dataIn);\n        if (!data.source) {\n            global$5.extend(data, htmlToData((_a = data.embed) !== null && _a !== void 0 ? _a : '', editor.schema));\n            if (!data.source) {\n                return '';\n            }\n        }\n        if (!data.altsource) {\n            data.altsource = '';\n        }\n        if (!data.poster) {\n            data.poster = '';\n        }\n        data.source = editor.convertURL(data.source, 'source');\n        data.altsource = editor.convertURL(data.altsource, 'source');\n        data.sourcemime = guess(data.source);\n        data.altsourcemime = guess(data.altsource);\n        data.poster = editor.convertURL(data.poster, 'poster');\n        const pattern = matchPattern(data.source);\n        if (pattern) {\n            data.source = pattern.url;\n            data.type = pattern.type;\n            data.allowfullscreen = pattern.allowFullscreen;\n            data.width = data.width || String(pattern.w);\n            data.height = data.height || String(pattern.h);\n        }\n        if (data.embed) {\n            return updateHtml(data.embed, data, true, editor.schema);\n        }\n        else {\n            const audioTemplateCallback = getAudioTemplateCallback(editor);\n            const videoTemplateCallback = getVideoTemplateCallback(editor);\n            const iframeTemplateCallback = getIframeTemplateCallback(editor);\n            data.width = data.width || '300';\n            data.height = data.height || '150';\n            global$5.each(data, (value, key) => {\n                data[key] = editor.dom.encode('' + value);\n            });\n            if (data.type === 'iframe') {\n                return getIframeHtml(data, iframeTemplateCallback);\n            }\n            else if (data.sourcemime === 'application/x-shockwave-flash') {\n                return getFlashHtml(data);\n            }\n            else if (data.sourcemime.indexOf('audio') !== -1) {\n                return getAudioHtml(data, audioTemplateCallback);\n            }\n            else {\n                return getVideoHtml(data, videoTemplateCallback);\n            }\n        }\n    };\n\n    const isMediaElement = (element) => element.hasAttribute('data-mce-object') || element.hasAttribute('data-ephox-embed-iri');\n    const setup$2 = (editor) => {\n        // TINY-10774: On Safari all events bubble out even if you click on the video play button on other browsers the video element doesn't bubble the event\n        editor.on('mousedown', (e) => {\n            const previewObj = editor.dom.getParent(e.target, '.mce-preview-object');\n            if (previewObj && editor.dom.getAttrib(previewObj, 'data-mce-selected') === '2') {\n                e.stopImmediatePropagation();\n            }\n        });\n        editor.on('click keyup touchend', () => {\n            const selectedNode = editor.selection.getNode();\n            if (selectedNode && editor.dom.hasClass(selectedNode, 'mce-preview-object')) {\n                if (editor.dom.getAttrib(selectedNode, 'data-mce-selected')) {\n                    selectedNode.setAttribute('data-mce-selected', '2');\n                }\n            }\n        });\n        editor.on('ObjectResized', (e) => {\n            const target = e.target;\n            if (target.getAttribute('data-mce-object')) {\n                let html = target.getAttribute('data-mce-html');\n                if (html) {\n                    html = unescape(html);\n                    target.setAttribute('data-mce-html', escape(updateHtml(html, {\n                        width: String(e.width),\n                        height: String(e.height)\n                    }, false, editor.schema)));\n                }\n            }\n        });\n    };\n\n    const cache = {};\n    const embedPromise = (data, dataToHtml, handler) => {\n        return new Promise((res, rej) => {\n            const wrappedResolve = (response) => {\n                if (response.html) {\n                    cache[data.source] = response;\n                }\n                return res({\n                    url: data.source,\n                    html: response.html ? response.html : dataToHtml(data)\n                });\n            };\n            if (cache[data.source]) {\n                wrappedResolve(cache[data.source]);\n            }\n            else {\n                handler({ url: data.source }).then(wrappedResolve).catch(rej);\n            }\n        });\n    };\n    const defaultPromise = (data, dataToHtml) => Promise.resolve({ html: dataToHtml(data), url: data.source });\n    const loadedData = (editor) => (data) => dataToHtml(editor, data);\n    const getEmbedHtml = (editor, data) => {\n        const embedHandler = getUrlResolver(editor);\n        return embedHandler ? embedPromise(data, loadedData(editor), embedHandler) : defaultPromise(data, loadedData(editor));\n    };\n    const isCached = (url) => has(cache, url);\n\n    const extractMeta = (sourceInput, data) => get$1(data, sourceInput).bind((mainData) => get$1(mainData, 'meta'));\n    const getValue = (data, metaData, sourceInput) => (prop) => {\n        // Cases:\n        // 1. Get the nested value prop (component is the executed urlinput)\n        // 2. Get from metadata (a urlinput was executed but urlinput != this component)\n        // 3. Not a urlinput so just get string\n        // If prop === sourceInput do 1, 2 then 3, else do 2 then 1 or 3\n        // ASSUMPTION: we only want to get values for props that already exist in data\n        const getFromData = () => get$1(data, prop);\n        const getFromMetaData = () => get$1(metaData, prop);\n        const getNonEmptyValue = (c) => get$1(c, 'value').bind((v) => v.length > 0 ? Optional.some(v) : Optional.none());\n        const getFromValueFirst = () => getFromData().bind((child) => isObject(child)\n            ? getNonEmptyValue(child).orThunk(getFromMetaData)\n            : getFromMetaData().orThunk(() => Optional.from(child)));\n        const getFromMetaFirst = () => getFromMetaData().orThunk(() => getFromData().bind((child) => isObject(child)\n            ? getNonEmptyValue(child)\n            : Optional.from(child)));\n        return { [prop]: (prop === sourceInput ? getFromValueFirst() : getFromMetaFirst()).getOr('') };\n    };\n    const getDimensions = (data, metaData) => {\n        const dimensions = {};\n        get$1(data, 'dimensions').each((dims) => {\n            each$1(['width', 'height'], (prop) => {\n                get$1(metaData, prop).orThunk(() => get$1(dims, prop)).each((value) => dimensions[prop] = value);\n            });\n        });\n        return dimensions;\n    };\n    const unwrap = (data, sourceInput) => {\n        const metaData = sourceInput && sourceInput !== 'dimensions' ? extractMeta(sourceInput, data).getOr({}) : {};\n        const get = getValue(data, metaData, sourceInput);\n        return {\n            ...get('source'),\n            ...get('altsource'),\n            ...get('poster'),\n            ...get('embed'),\n            ...getDimensions(data, metaData)\n        };\n    };\n    const wrap = (data) => {\n        const wrapped = {\n            ...data,\n            source: { value: get$1(data, 'source').getOr('') },\n            altsource: { value: get$1(data, 'altsource').getOr('') },\n            poster: { value: get$1(data, 'poster').getOr('') }\n        };\n        // Add additional size values that may or may not have been in the html\n        each$1(['width', 'height'], (prop) => {\n            get$1(data, prop).each((value) => {\n                const dimensions = wrapped.dimensions || {};\n                dimensions[prop] = value;\n                wrapped.dimensions = dimensions;\n            });\n        });\n        return wrapped;\n    };\n    const handleError = (editor) => (error) => {\n        const errorMessage = error && error.msg ?\n            'Media embed handler error: ' + error.msg :\n            'Media embed handler threw unknown error.';\n        editor.notificationManager.open({ type: 'error', text: errorMessage });\n    };\n    const getEditorData = (editor) => {\n        const element = editor.selection.getNode();\n        const snippet = isMediaElement(element) ? editor.serializer.serialize(element, { selection: true }) : '';\n        const data = htmlToData(snippet, editor.schema);\n        const getDimensionsOfElement = () => {\n            if (isEmbedIframe(data.source, data.type)) {\n                const rect = editor.dom.getRect(element);\n                return {\n                    width: rect.w.toString().replace(/px$/, ''),\n                    height: rect.h.toString().replace(/px$/, ''),\n                };\n            }\n            else {\n                return {};\n            }\n        };\n        const dimensions = getDimensionsOfElement();\n        return {\n            embed: snippet,\n            ...data,\n            ...dimensions\n        };\n    };\n    const addEmbedHtml = (api, editor) => (response) => {\n        // Only set values if a URL has been defined\n        if (isString(response.url) && response.url.trim().length > 0) {\n            const html = response.html;\n            const snippetData = htmlToData(html, editor.schema);\n            const nuData = {\n                ...snippetData,\n                source: response.url,\n                embed: html\n            };\n            api.setData(wrap(nuData));\n        }\n    };\n    const selectPlaceholder = (editor, beforeObjects) => {\n        const afterObjects = editor.dom.select('*[data-mce-object]');\n        // Find new image placeholder so we can select it\n        for (let i = 0; i < beforeObjects.length; i++) {\n            for (let y = afterObjects.length - 1; y >= 0; y--) {\n                if (beforeObjects[i] === afterObjects[y]) {\n                    afterObjects.splice(y, 1);\n                }\n            }\n        }\n        editor.selection.select(afterObjects[0]);\n    };\n    const handleInsert = (editor, html) => {\n        const beforeObjects = editor.dom.select('*[data-mce-object]');\n        editor.insertContent(html);\n        selectPlaceholder(editor, beforeObjects);\n        editor.nodeChanged();\n    };\n    const isEmbedIframe = (url, mediaDataType) => isNonNullable(mediaDataType) && mediaDataType === 'ephox-embed-iri' && isNonNullable(matchPattern(url));\n    const shouldInsertAsNewIframe = (prevData, newData) => {\n        const hasDimensionsChanged = (prevData, newData) => prevData.width !== newData.width || prevData.height !== newData.height;\n        return hasDimensionsChanged(prevData, newData) && isEmbedIframe(newData.source, prevData.type);\n    };\n    const submitForm = (prevData, newData, editor) => {\n        var _a;\n        newData.embed =\n            shouldInsertAsNewIframe(prevData, newData) && hasDimensions(editor)\n                ? dataToHtml(editor, { ...newData, embed: '' })\n                : updateHtml((_a = newData.embed) !== null && _a !== void 0 ? _a : '', newData, false, editor.schema);\n        // Only fetch the embed HTML content if the URL has changed from what it previously was\n        if (newData.embed && (prevData.source === newData.source || isCached(newData.source))) {\n            handleInsert(editor, newData.embed);\n        }\n        else {\n            getEmbedHtml(editor, newData)\n                .then((response) => {\n                handleInsert(editor, response.html);\n            }).catch(handleError(editor));\n        }\n    };\n    const showDialog = (editor) => {\n        const editorData = getEditorData(editor);\n        const currentData = Cell(editorData);\n        const initialData = wrap(editorData);\n        const handleSource = (prevData, api) => {\n            const serviceData = unwrap(api.getData(), 'source');\n            // If a new URL is entered, then clear the embed html and fetch the new data\n            if (prevData.source !== serviceData.source) {\n                addEmbedHtml(win, editor)({ url: serviceData.source, html: '' });\n                getEmbedHtml(editor, serviceData)\n                    .then(addEmbedHtml(win, editor))\n                    .catch(handleError(editor));\n            }\n        };\n        const handleEmbed = (api) => {\n            var _a;\n            const data = unwrap(api.getData());\n            const dataFromEmbed = htmlToData((_a = data.embed) !== null && _a !== void 0 ? _a : '', editor.schema);\n            api.setData(wrap(dataFromEmbed));\n        };\n        const handleUpdate = (api, sourceInput, prevData) => {\n            const dialogData = unwrap(api.getData(), sourceInput);\n            const data = shouldInsertAsNewIframe(prevData, dialogData) && hasDimensions(editor)\n                ? { ...dialogData, embed: '' }\n                : dialogData;\n            const embed = dataToHtml(editor, data);\n            api.setData(wrap({\n                ...data,\n                embed\n            }));\n        };\n        const mediaInput = [{\n                name: 'source',\n                type: 'urlinput',\n                filetype: 'media',\n                label: 'Source',\n                picker_text: 'Browse files'\n            }];\n        const sizeInput = !hasDimensions(editor) ? [] : [{\n                type: 'sizeinput',\n                name: 'dimensions',\n                label: 'Constrain proportions',\n                constrain: true\n            }];\n        const generalTab = {\n            title: 'General',\n            name: 'general',\n            items: flatten([mediaInput, sizeInput])\n        };\n        const embedTextarea = {\n            type: 'textarea',\n            name: 'embed',\n            label: 'Paste your embed code below:'\n        };\n        const embedTab = {\n            title: 'Embed',\n            items: [\n                embedTextarea\n            ]\n        };\n        const advancedFormItems = [];\n        if (hasAltSource(editor)) {\n            advancedFormItems.push({\n                name: 'altsource',\n                type: 'urlinput',\n                filetype: 'media',\n                label: 'Alternative source URL'\n            });\n        }\n        if (hasPoster(editor)) {\n            advancedFormItems.push({\n                name: 'poster',\n                type: 'urlinput',\n                filetype: 'image',\n                label: 'Media poster (Image URL)'\n            });\n        }\n        const advancedTab = {\n            title: 'Advanced',\n            name: 'advanced',\n            items: advancedFormItems\n        };\n        const tabs = [\n            generalTab,\n            embedTab\n        ];\n        if (advancedFormItems.length > 0) {\n            tabs.push(advancedTab);\n        }\n        const body = {\n            type: 'tabpanel',\n            tabs\n        };\n        const win = editor.windowManager.open({\n            title: 'Insert/Edit Media',\n            size: 'normal',\n            body,\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            onSubmit: (api) => {\n                const serviceData = unwrap(api.getData());\n                submitForm(currentData.get(), serviceData, editor);\n                api.close();\n            },\n            onChange: (api, detail) => {\n                switch (detail.name) {\n                    case 'source':\n                        handleSource(currentData.get(), api);\n                        break;\n                    case 'embed':\n                        handleEmbed(api);\n                        break;\n                    case 'dimensions':\n                    case 'altsource':\n                    case 'poster':\n                        handleUpdate(api, detail.name, currentData.get());\n                        break;\n                }\n                currentData.set(unwrap(api.getData()));\n            },\n            initialData\n        });\n    };\n\n    const get = (editor) => {\n        const showDialog$1 = () => {\n            showDialog(editor);\n        };\n        return {\n            showDialog: showDialog$1\n        };\n    };\n\n    const register$1 = (editor) => {\n        const showDialog$1 = () => {\n            showDialog(editor);\n        };\n        editor.addCommand('mceMedia', showDialog$1);\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const isLiveEmbedNode = (node) => {\n        const name = node.name;\n        return name === 'iframe' || name === 'video' || name === 'audio';\n    };\n    const getDimension = (node, styles, dimension, defaultValue = null) => {\n        const value = node.attr(dimension);\n        if (isNonNullable(value)) {\n            return value;\n        }\n        else if (!has(styles, dimension)) {\n            return defaultValue;\n        }\n        else {\n            return null;\n        }\n    };\n    const setDimensions = (node, previewNode, styles) => {\n        // Apply dimensions for video elements to maintain legacy behaviour\n        const useDefaults = previewNode.name === 'img' || node.name === 'video';\n        // Determine the defaults\n        const defaultWidth = useDefaults ? '300' : null;\n        const fallbackHeight = node.name === 'audio' ? '30' : '150';\n        const defaultHeight = useDefaults ? fallbackHeight : null;\n        previewNode.attr({\n            width: getDimension(node, styles, 'width', defaultWidth),\n            height: getDimension(node, styles, 'height', defaultHeight)\n        });\n    };\n    const appendNodeContent = (editor, nodeName, previewNode, html) => {\n        const newNode = Parser(editor.schema).parse(html, { context: nodeName });\n        while (newNode.firstChild) {\n            previewNode.append(newNode.firstChild);\n        }\n    };\n    const createPlaceholderNode = (editor, node) => {\n        const name = node.name;\n        const placeHolder = new global$2('img', 1);\n        retainAttributesAndInnerHtml(editor, node, placeHolder);\n        setDimensions(node, placeHolder, {});\n        placeHolder.attr({\n            'style': node.attr('style'),\n            'src': global.transparentSrc,\n            'data-mce-object': name,\n            'class': 'mce-object mce-object-' + name\n        });\n        return placeHolder;\n    };\n    const createPreviewNode = (editor, node) => {\n        var _a;\n        const name = node.name;\n        const previewWrapper = new global$2('span', 1);\n        previewWrapper.attr({\n            'contentEditable': 'false',\n            'style': node.attr('style'),\n            'data-mce-object': name,\n            'class': 'mce-preview-object mce-object-' + name\n        });\n        retainAttributesAndInnerHtml(editor, node, previewWrapper);\n        const styles = editor.dom.parseStyle((_a = node.attr('style')) !== null && _a !== void 0 ? _a : '');\n        const previewNode = new global$2(name, 1);\n        setDimensions(node, previewNode, styles);\n        previewNode.attr({\n            src: node.attr('src'),\n            style: node.attr('style'),\n            class: node.attr('class')\n        });\n        if (name === 'iframe') {\n            previewNode.attr({\n                allowfullscreen: node.attr('allowfullscreen'),\n                frameborder: '0',\n                sandbox: node.attr('sandbox'),\n                referrerpolicy: node.attr('referrerpolicy')\n            });\n        }\n        else {\n            // Exclude autoplay as we don't want video/audio to play by default\n            const attrs = ['controls', 'crossorigin', 'currentTime', 'loop', 'muted', 'poster', 'preload'];\n            each$1(attrs, (attrName) => {\n                previewNode.attr(attrName, node.attr(attrName));\n            });\n            // Recreate the child nodes using the sanitized inner HTML\n            const sanitizedHtml = previewWrapper.attr('data-mce-html');\n            if (isNonNullable(sanitizedHtml)) {\n                appendNodeContent(editor, name, previewNode, unescape(sanitizedHtml));\n            }\n        }\n        const shimNode = new global$2('span', 1);\n        shimNode.attr('class', 'mce-shim');\n        previewWrapper.append(previewNode);\n        previewWrapper.append(shimNode);\n        return previewWrapper;\n    };\n    const retainAttributesAndInnerHtml = (editor, sourceNode, targetNode) => {\n        var _a;\n        // Prefix all attributes except internal (data-mce-*), width, height and style since we\n        // will add these to the placeholder\n        const attribs = (_a = sourceNode.attributes) !== null && _a !== void 0 ? _a : [];\n        let ai = attribs.length;\n        while (ai--) {\n            const attrName = attribs[ai].name;\n            let attrValue = attribs[ai].value;\n            if (attrName !== 'width' && attrName !== 'height' && attrName !== 'style' && !startsWith(attrName, 'data-mce-')) {\n                if (attrName === 'data' || attrName === 'src') {\n                    attrValue = editor.convertURL(attrValue, attrName);\n                }\n                targetNode.attr('data-mce-p-' + attrName, attrValue);\n            }\n        }\n        // Place the inner HTML contents inside an escaped attribute\n        // This enables us to copy/paste the fake object\n        const serializer = global$1({ inner: true }, editor.schema);\n        const tempNode = new global$2('div', 1);\n        each$1(sourceNode.children(), (child) => tempNode.append(child));\n        const innerHtml = serializer.serialize(tempNode);\n        if (innerHtml) {\n            targetNode.attr('data-mce-html', escape(innerHtml));\n            targetNode.empty();\n        }\n    };\n    const isPageEmbedWrapper = (node) => {\n        const nodeClass = node.attr('class');\n        return isString(nodeClass) && /\\btiny-pageembed\\b/.test(nodeClass);\n    };\n    const isWithinEmbedWrapper = (node) => {\n        let tempNode = node;\n        while ((tempNode = tempNode.parent)) {\n            if (tempNode.attr('data-ephox-embed-iri') || isPageEmbedWrapper(tempNode)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const placeHolderConverter = (editor) => (nodes) => {\n        let i = nodes.length;\n        let node;\n        while (i--) {\n            node = nodes[i];\n            if (!node.parent) {\n                continue;\n            }\n            if (node.parent.attr('data-mce-object')) {\n                continue;\n            }\n            if (isLiveEmbedNode(node) && hasLiveEmbeds(editor)) {\n                if (!isWithinEmbedWrapper(node)) {\n                    node.replace(createPreviewNode(editor, node));\n                }\n            }\n            else {\n                if (!isWithinEmbedWrapper(node)) {\n                    node.replace(createPlaceholderNode(editor, node));\n                }\n            }\n        }\n    };\n\n    const parseAndSanitize = (editor, context, html) => {\n        const getEditorOption = editor.options.get;\n        const sanitize = getEditorOption('xss_sanitization');\n        const validate = shouldFilterHtml(editor);\n        return Parser(editor.schema, { sanitize, validate }).parse(html, { context });\n    };\n\n    const setup$1 = (editor) => {\n        editor.on('PreInit', () => {\n            const { schema, serializer, parser } = editor;\n            // Set browser specific allowFullscreen attribs as boolean\n            const boolAttrs = schema.getBoolAttrs();\n            each$1('webkitallowfullscreen mozallowfullscreen'.split(' '), (name) => {\n                boolAttrs[name] = {};\n            });\n            // Add some non-standard attributes to the schema\n            each({\n                embed: ['wmode']\n            }, (attrs, name) => {\n                const rule = schema.getElementRule(name);\n                if (rule) {\n                    each$1(attrs, (attr) => {\n                        rule.attributes[attr] = {};\n                        rule.attributesOrder.push(attr);\n                    });\n                }\n            });\n            // Converts iframe, video etc into placeholder images\n            parser.addNodeFilter('iframe,video,audio,object,embed', placeHolderConverter(editor));\n            // Replaces placeholder images with real elements for video, object, iframe etc\n            serializer.addAttributeFilter('data-mce-object', (nodes, name) => {\n                var _a;\n                let i = nodes.length;\n                while (i--) {\n                    const node = nodes[i];\n                    if (!node.parent) {\n                        continue;\n                    }\n                    const realElmName = node.attr(name);\n                    const realElm = new global$2(realElmName, 1);\n                    // Add width/height to everything but audio\n                    if (realElmName !== 'audio') {\n                        const className = node.attr('class');\n                        if (className && className.indexOf('mce-preview-object') !== -1 && node.firstChild) {\n                            realElm.attr({\n                                width: node.firstChild.attr('width'),\n                                height: node.firstChild.attr('height')\n                            });\n                        }\n                        else {\n                            realElm.attr({\n                                width: node.attr('width'),\n                                height: node.attr('height')\n                            });\n                        }\n                    }\n                    realElm.attr({\n                        style: node.attr('style')\n                    });\n                    // Unprefix all placeholder attributes\n                    const attribs = (_a = node.attributes) !== null && _a !== void 0 ? _a : [];\n                    let ai = attribs.length;\n                    while (ai--) {\n                        const attrName = attribs[ai].name;\n                        if (attrName.indexOf('data-mce-p-') === 0) {\n                            realElm.attr(attrName.substr(11), attribs[ai].value);\n                        }\n                    }\n                    // Inject innerhtml\n                    const innerHtml = node.attr('data-mce-html');\n                    if (innerHtml) {\n                        const fragment = parseAndSanitize(editor, realElmName, unescape(innerHtml));\n                        each$1(fragment.children(), (child) => realElm.append(child));\n                    }\n                    node.replace(realElm);\n                }\n            });\n        });\n        editor.on('SetContent', () => {\n            // TODO: This shouldn't be needed there should be a way to mark bogus\n            // elements so they are never removed except external save\n            const dom = editor.dom;\n            each$1(dom.select('span.mce-preview-object'), (elm) => {\n                if (dom.select('span.mce-shim', elm).length === 0) {\n                    dom.add(elm, 'span', { class: 'mce-shim' });\n                }\n            });\n        });\n    };\n\n    const setup = (editor) => {\n        editor.on('ResolveName', (e) => {\n            let name;\n            if (e.target.nodeType === 1 && (name = e.target.getAttribute('data-mce-object'))) {\n                e.name = name;\n            }\n        });\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceMedia');\n        editor.ui.registry.addToggleButton('media', {\n            tooltip: 'Insert/edit media',\n            icon: 'embed',\n            onAction,\n            onSetup: (buttonApi) => {\n                const selection = editor.selection;\n                buttonApi.setActive(isMediaElement(selection.getNode()));\n                const unbindSelectorChanged = selection.selectorChangedWithUnbind('img[data-mce-object],span[data-mce-object],div[data-ephox-embed-iri]', buttonApi.setActive).unbind;\n                const unbindEditable = onSetupEditable(editor)(buttonApi);\n                return () => {\n                    unbindSelectorChanged();\n                    unbindEditable();\n                };\n            }\n        });\n        editor.ui.registry.addMenuItem('media', {\n            icon: 'embed',\n            text: 'Media...',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    var Plugin = () => {\n        global$6.add('media', (editor) => {\n            register$2(editor);\n            register$1(editor);\n            register(editor);\n            setup(editor);\n            setup$1(editor);\n            setup$2(editor);\n            return get(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,WAAW,OAAO;IACxB,MAAM,WAAW,OAAO;IACxB,MAAM,UAAU,OAAO;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAEhC;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAE9D,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,QAAQ,CAAC,KAAK;QAChB,OAAO,IAAI,KAAK,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,SAAS,IAAI;IAClE;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IAEnD,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC,KAAK,QAAQ,QAAU,WAAW,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,QAAQ,OAAO,MAAM,MAAM;IACxI;;;;KAIC,GACD,MAAM,aAAa,CAAC,KAAK;QACrB,OAAO,WAAW,KAAK,QAAQ;IACnC;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,2BAA2B;YACtC,WAAW;QACf;QACA,eAAe,2BAA2B;YACtC,WAAW;QACf;QACA,eAAe,4BAA4B;YACvC,WAAW;QACf;QACA,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;QACA,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;QACA,eAAe,sBAAsB;YACjC,WAAW;QACf;QACA,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS;QACb;QACA,eAAe,gBAAgB;YAC3B,WAAW;YACX,SAAS;QACb;QACA,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,2BAA2B,OAAO;IACxC,MAAM,2BAA2B,OAAO;IACxC,MAAM,4BAA4B,OAAO;IACzC,MAAM,gBAAgB,OAAO;IAC7B,MAAM,mBAAmB,OAAO;IAChC,MAAM,iBAAiB,OAAO;IAC9B,MAAM,eAAe,OAAO;IAC5B,MAAM,YAAY,OAAO;IACzB,MAAM,gBAAgB,OAAO;IAE7B,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,QAAQ,SAAS,GAAG;IAC1B,MAAM,SAAS,CAAC,QAAU,MAAM,OAAO,CAAC,OAAO;IAC/C,MAAM,oBAAoB,CAAC;QACvB,MAAM,QAAQ,KAAK,IAAI,CAAC;QACxB,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,SAAS,CAAC;QAClD,OAAO;YACH,MAAM;YACN,QAAQ,KAAK,IAAI,CAAC;YAClB,WAAW;YACX,QAAQ;YACR,OAAO,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ,KAAK,CAAC;YACpD,QAAQ,MAAM,QAAQ,cAAc,GAAG,CAAC,QAAQ,KAAK,CAAC;QAC1D;IACJ;IACA,MAAM,aAAa,CAAC,MAAM;QACtB,IAAI,OAAO,CAAC;QACZ,MAAM,SAAS,SAAS;YAAE,UAAU;YAAO,mBAAmB;QAAM,GAAG;QACvE,MAAM,WAAW,OAAO,KAAK,CAAC;QAC9B,IAAK,IAAI,OAAO,UAAU,MAAM,OAAO,KAAK,IAAI,GAAI;YAChD,IAAI,KAAK,IAAI,KAAK,GAAG;gBACjB,MAAM,OAAO,KAAK,IAAI;gBACtB,IAAI,KAAK,IAAI,CAAC,yBAAyB;oBACnC,OAAO,kBAAkB;oBAEzB;gBACJ,OACK;oBACD,IAAI,CAAC,KAAK,MAAM,IAAI,SAAS,SAAS;wBAClC,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC;oBAC5B;oBACA,IAAI,SAAS,YAAY,SAAS,YAAY,SAAS,WAAW,SAAS,WAAW,SAAS,SAAS;wBACpG,IAAI,CAAC,KAAK,IAAI,EAAE;4BACZ,KAAK,IAAI,GAAG;wBAChB;wBACA,oEAAoE;wBACpE,OAAO,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,GAAG,EAAE;oBAChD;oBACA,IAAI,SAAS,UAAU;wBACnB,IAAI,CAAC,KAAK,MAAM,EAAE;4BACd,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC;wBAC5B,OACK,IAAI,CAAC,KAAK,SAAS,EAAE;4BACtB,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;wBAC/B;oBACJ;oBACA,IAAI,SAAS,SAAS,CAAC,KAAK,MAAM,EAAE;wBAChC,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC;oBAC5B;gBACJ;YACJ;QACJ;QACA,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,GAAG,IAAI;QACzC,KAAK,SAAS,GAAG,KAAK,SAAS,IAAI;QACnC,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI;QAC7B,OAAO;IACX;IAEA,MAAM,QAAQ,CAAC;QACX,IAAI;QACJ,MAAM,QAAQ;YACV,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,KAAK;YACL,KAAK;QACT;QACA,MAAM,UAAU,CAAC,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC3F,OAAO,MAAM,OAAO,SAAS,KAAK,CAAC;IACvC;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,SAAS,SAAC;YAAQ,4EAAW,CAAC;eAAM,SAAS;YAC/C,mBAAmB;YACnB,UAAU;YACV,4BAA4B;YAC5B,GAAG,QAAQ;QACf,GAAG;;IAEH,MAAM,MAAM,SAAS,GAAG;IACxB,MAAM,QAAQ,CAAC,QAAU,YAAY,IAAI,CAAC,SAAU,QAAQ,OAAQ;IACpE,MAAM,mBAAmB,CAAC,MAAM;QAC5B,MAAM,QAAQ,KAAK,IAAI,CAAC;QACxB,MAAM,WAAW,QAAQ,IAAI,UAAU,CAAC,SAAS,CAAC;QAClD,IAAI,cAAc,KAAK,KAAK,GAAG;YAC3B,QAAQ,CAAC,YAAY,GAAG,MAAM,KAAK,KAAK;QAC5C;QACA,IAAI,cAAc,KAAK,MAAM,GAAG;YAC5B,QAAQ,CAAC,aAAa,GAAG,MAAM,KAAK,MAAM;QAC9C;QACA,KAAK,IAAI,CAAC,SAAS,IAAI,cAAc,CAAC;IAC1C;IACA,MAAM,UAAU;QAAC;QAAU;KAAY;IACvC,MAAM,aAAa,CAAC,MAAM,MAAM,WAAW;QACvC,IAAI,aAAa;QACjB,IAAI,cAAc;QAClB,MAAM,SAAS,OAAO;QACtB,OAAO,aAAa,CAAC,UAAU,CAAC,QAAU,aAAa,MAAM,MAAM;QACnE,MAAM,WAAW,OAAO,KAAK,CAAC;QAC9B,IAAK,IAAI,OAAO,UAAU,MAAM,OAAO,KAAK,IAAI,GAAI;YAChD,IAAI,KAAK,IAAI,KAAK,GAAG;gBACjB,MAAM,OAAO,KAAK,IAAI;gBACtB,IAAI,KAAK,IAAI,CAAC,yBAAyB;oBACnC,iBAAiB,MAAM;oBAEvB;gBACJ,OACK;oBACD,OAAQ;wBACJ,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACD,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,KAAK,KAAK,WAAW;gCACvD,KAAK,IAAI,CAAC,SAAS,KAAK,KAAK;gCAC7B,KAAK,IAAI,CAAC,UAAU,KAAK,MAAM;4BACnC;4BACA;oBACR;oBACA,IAAI,WAAW;wBACX,OAAQ;4BACJ,KAAK;gCACD,KAAK,IAAI,CAAC,UAAU,KAAK,MAAM;gCAC/B,KAAK,IAAI,CAAC,OAAO;gCACjB,8BAA8B;gCAC9B,IAAK,IAAI,QAAQ,YAAY,QAAQ,GAAG,QAAS;oCAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wCACtB,MAAM,SAAS,IAAI,SAAS,UAAU;wCACtC,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;wCACvC,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,IAAI;wCACrD,KAAK,MAAM,CAAC;oCAChB;gCACJ;gCACA;4BACJ,KAAK;gCACD,KAAK,IAAI,CAAC,OAAO,KAAK,MAAM;gCAC5B;4BACJ,KAAK;gCACD,MAAM,WAAW,KAAK,MAAM,CAAC,OAAO,MAAM,GAAG;gCAC7C,IAAI,KAAK,MAAM,IAAI,CAAC,UAAU;oCAC1B,KAAK,IAAI,CAAC,OAAO,KAAK,MAAM;oCAC5B,MAAM,MAAM,IAAI,SAAS,OAAO;oCAChC,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;oCAC3B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK;oCAC5B,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM;oCAC9B,KAAK,MAAM,CAAC;gCAChB;gCACA;4BACJ,KAAK;gCACD,IAAI,cAAc,GAAG;oCACjB,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;oCAC3C,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,OAAO,IAAI;oCACzD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;wCAC7B,KAAK,MAAM;wCACX;oCACJ;gCACJ;gCACA;gCACA;4BACJ,KAAK;gCACD,IAAI,CAAC,KAAK,MAAM,EAAE;oCACd,KAAK,MAAM;gCACf;gCACA;wBACR;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC;IAC1C;IAEA,MAAM,cAAc;QAChB;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;QACA;YACI,OAAO;YACP,MAAM;YAAU,GAAG;YAAK,GAAG;YAC3B,KAAK;YACL,iBAAiB;QACrB;KACH;IACD,MAAM,cAAc,CAAC;QACjB,MAAM,kBAAkB,IAAI,KAAK,CAAC;QAClC,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;YAC/C,OAAO,eAAe,CAAC,EAAE,KAAK,SAAS,aAAa,eAAe,CAAC,EAAE;QAC1E,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,MAAM,WAAW,YAAY;QAC7B,MAAM,QAAQ,QAAQ,KAAK,CAAC,IAAI,CAAC;QACjC,IAAI,SAAS,WAAW,QAAQ,GAAG;QACnC,IAAI,cAAc,QAAQ;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,SAAS,OAAO,OAAO,CAAC,MAAM,GAAG,IAAM,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;YACjE;QACJ;QACA,OAAO,OAAO,OAAO,CAAC,OAAO;IACjC;IACA,MAAM,eAAe,CAAC;QAClB,MAAM,WAAW,YAAY,MAAM,CAAC,CAAC,UAAY,QAAQ,KAAK,CAAC,IAAI,CAAC;QACpE,IAAI,SAAS,MAAM,GAAG,GAAG;YACrB,OAAO,SAAS,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,EAAE;gBAAE,KAAK,OAAO,QAAQ,CAAC,EAAE,EAAE;YAAK;QAC5E,OACK;YACD,OAAO;QACX;IACJ;IAEA,MAAM,gBAAgB,CAAC,MAAM;QACzB,IAAI,wBAAwB;YACxB,OAAO,uBAAuB;QAClC,OACK;YACD,MAAM,kBAAkB,KAAK,eAAe,GAAG,yBAAyB;YACxE,OAAO,kBAAkB,KAAK,MAAM,GAAG,cAAc,KAAK,KAAK,GAAG,eAAe,KAAK,MAAM,GAAG,MAAM,kBAAkB;QAC3H;IACJ;IACA,MAAM,eAAe,CAAC;QAClB,IAAI,OAAO,mBAAmB,KAAK,MAAM,GAAG,cAAc,KAAK,KAAK,GAAG,eAAe,KAAK,MAAM,GAAG;QACpG,IAAI,KAAK,MAAM,EAAE;YACb,QAAQ,eAAe,KAAK,MAAM,GAAG,cAAc,KAAK,KAAK,GAAG,eAAe,KAAK,MAAM,GAAG;QACjG;QACA,QAAQ;QACR,OAAO;IACX;IACA,MAAM,eAAe,CAAC,MAAM;QACxB,IAAI,uBAAuB;YACvB,OAAO,sBAAsB;QACjC,OACK;YACD,OAAQ,qCAAqC,KAAK,MAAM,GAAG,OACvD,CAAC,KAAK,SAAS,GACX,oBAAoB,KAAK,SAAS,GAAG,MACjC,CAAC,KAAK,aAAa,GAAG,YAAY,KAAK,aAAa,GAAG,MAAM,EAAE,IAC/D,UAAU,EAAE,IACpB;QACR;IACJ;IACA,MAAM,eAAe,CAAC,MAAM;QACxB,IAAI,uBAAuB;YACvB,OAAO,sBAAsB;QACjC,OACK;YACD,OAAQ,mBAAmB,KAAK,KAAK,GACjC,eAAe,KAAK,MAAM,GAAG,MAC7B,CAAC,KAAK,MAAM,GAAG,cAAc,KAAK,MAAM,GAAG,MAAM,EAAE,IAAI,4BACvD,kBAAkB,KAAK,MAAM,GAAG,MAChC,CAAC,KAAK,UAAU,GAAG,YAAY,KAAK,UAAU,GAAG,MAAM,EAAE,IAAI,UAC7D,CAAC,KAAK,SAAS,GAAG,kBAAkB,KAAK,SAAS,GAAG,MACjD,CAAC,KAAK,aAAa,GAAG,YAAY,KAAK,aAAa,GAAG,MAAM,EAAE,IAAI,UAAU,EAAE,IACnF;QACR;IACJ;IACA,MAAM,aAAa,CAAC,QAAQ;QACxB,IAAI;QACJ,MAAM,OAAO,SAAS,MAAM,CAAC,CAAC,GAAG;QACjC,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,SAAS,MAAM,CAAC,MAAM,WAAW,CAAC,KAAK,KAAK,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM;YACrG,IAAI,CAAC,KAAK,MAAM,EAAE;gBACd,OAAO;YACX;QACJ;QACA,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,KAAK,SAAS,GAAG;QACrB;QACA,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,KAAK,MAAM,GAAG;QAClB;QACA,KAAK,MAAM,GAAG,OAAO,UAAU,CAAC,KAAK,MAAM,EAAE;QAC7C,KAAK,SAAS,GAAG,OAAO,UAAU,CAAC,KAAK,SAAS,EAAE;QACnD,KAAK,UAAU,GAAG,MAAM,KAAK,MAAM;QACnC,KAAK,aAAa,GAAG,MAAM,KAAK,SAAS;QACzC,KAAK,MAAM,GAAG,OAAO,UAAU,CAAC,KAAK,MAAM,EAAE;QAC7C,MAAM,UAAU,aAAa,KAAK,MAAM;QACxC,IAAI,SAAS;YACT,KAAK,MAAM,GAAG,QAAQ,GAAG;YACzB,KAAK,IAAI,GAAG,QAAQ,IAAI;YACxB,KAAK,eAAe,GAAG,QAAQ,eAAe;YAC9C,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,OAAO,QAAQ,CAAC;YAC3C,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,OAAO,QAAQ,CAAC;QACjD;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,OAAO,WAAW,KAAK,KAAK,EAAE,MAAM,MAAM,OAAO,MAAM;QAC3D,OACK;YACD,MAAM,wBAAwB,yBAAyB;YACvD,MAAM,wBAAwB,yBAAyB;YACvD,MAAM,yBAAyB,0BAA0B;YACzD,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI;YAC3B,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI;YAC7B,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBACxB,IAAI,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK;YACvC;YACA,IAAI,KAAK,IAAI,KAAK,UAAU;gBACxB,OAAO,cAAc,MAAM;YAC/B,OACK,IAAI,KAAK,UAAU,KAAK,iCAAiC;gBAC1D,OAAO,aAAa;YACxB,OACK,IAAI,KAAK,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG;gBAC9C,OAAO,aAAa,MAAM;YAC9B,OACK;gBACD,OAAO,aAAa,MAAM;YAC9B;QACJ;IACJ;IAEA,MAAM,iBAAiB,CAAC,UAAY,QAAQ,YAAY,CAAC,sBAAsB,QAAQ,YAAY,CAAC;IACpG,MAAM,UAAU,CAAC;QACb,sJAAsJ;QACtJ,OAAO,EAAE,CAAC,aAAa,CAAC;YACpB,MAAM,aAAa,OAAO,GAAG,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE;YAClD,IAAI,cAAc,OAAO,GAAG,CAAC,SAAS,CAAC,YAAY,yBAAyB,KAAK;gBAC7E,EAAE,wBAAwB;YAC9B;QACJ;QACA,OAAO,EAAE,CAAC,wBAAwB;YAC9B,MAAM,eAAe,OAAO,SAAS,CAAC,OAAO;YAC7C,IAAI,gBAAgB,OAAO,GAAG,CAAC,QAAQ,CAAC,cAAc,uBAAuB;gBACzE,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,cAAc,sBAAsB;oBACzD,aAAa,YAAY,CAAC,qBAAqB;gBACnD;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,iBAAiB,CAAC;YACxB,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,YAAY,CAAC,oBAAoB;gBACxC,IAAI,OAAO,OAAO,YAAY,CAAC;gBAC/B,IAAI,MAAM;oBACN,OAAO,SAAS;oBAChB,OAAO,YAAY,CAAC,iBAAiB,OAAO,WAAW,MAAM;wBACzD,OAAO,OAAO,EAAE,KAAK;wBACrB,QAAQ,OAAO,EAAE,MAAM;oBAC3B,GAAG,OAAO,OAAO,MAAM;gBAC3B;YACJ;QACJ;IACJ;IAEA,MAAM,QAAQ,CAAC;IACf,MAAM,eAAe,CAAC,MAAM,YAAY;QACpC,OAAO,IAAI,QAAQ,CAAC,KAAK;YACrB,MAAM,iBAAiB,CAAC;gBACpB,IAAI,SAAS,IAAI,EAAE;oBACf,KAAK,CAAC,KAAK,MAAM,CAAC,GAAG;gBACzB;gBACA,OAAO,IAAI;oBACP,KAAK,KAAK,MAAM;oBAChB,MAAM,SAAS,IAAI,GAAG,SAAS,IAAI,GAAG,WAAW;gBACrD;YACJ;YACA,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,EAAE;gBACpB,eAAe,KAAK,CAAC,KAAK,MAAM,CAAC;YACrC,OACK;gBACD,QAAQ;oBAAE,KAAK,KAAK,MAAM;gBAAC,GAAG,IAAI,CAAC,gBAAgB,KAAK,CAAC;YAC7D;QACJ;IACJ;IACA,MAAM,iBAAiB,CAAC,MAAM,aAAe,QAAQ,OAAO,CAAC;YAAE,MAAM,WAAW;YAAO,KAAK,KAAK,MAAM;QAAC;IACxG,MAAM,aAAa,CAAC,SAAW,CAAC,OAAS,WAAW,QAAQ;IAC5D,MAAM,eAAe,CAAC,QAAQ;QAC1B,MAAM,eAAe,eAAe;QACpC,OAAO,eAAe,aAAa,MAAM,WAAW,SAAS,gBAAgB,eAAe,MAAM,WAAW;IACjH;IACA,MAAM,WAAW,CAAC,MAAQ,IAAI,OAAO;IAErC,MAAM,cAAc,CAAC,aAAa,OAAS,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC,WAAa,MAAM,UAAU;IACvG,MAAM,WAAW,CAAC,MAAM,UAAU,cAAgB,CAAC;YAC/C,SAAS;YACT,oEAAoE;YACpE,gFAAgF;YAChF,uCAAuC;YACvC,gEAAgE;YAChE,8EAA8E;YAC9E,MAAM,cAAc,IAAM,MAAM,MAAM;YACtC,MAAM,kBAAkB,IAAM,MAAM,UAAU;YAC9C,MAAM,mBAAmB,CAAC,IAAM,MAAM,GAAG,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,GAAG,IAAI,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI;YAC7G,MAAM,oBAAoB,IAAM,cAAc,IAAI,CAAC,CAAC,QAAU,SAAS,SACjE,iBAAiB,OAAO,OAAO,CAAC,mBAChC,kBAAkB,OAAO,CAAC,IAAM,SAAS,IAAI,CAAC;YACpD,MAAM,mBAAmB,IAAM,kBAAkB,OAAO,CAAC,IAAM,cAAc,IAAI,CAAC,CAAC,QAAU,SAAS,SAChG,iBAAiB,SACjB,SAAS,IAAI,CAAC;YACpB,OAAO;gBAAE,CAAC,KAAK,EAAE,CAAC,SAAS,cAAc,sBAAsB,kBAAkB,EAAE,KAAK,CAAC;YAAI;QACjG;IACA,MAAM,gBAAgB,CAAC,MAAM;QACzB,MAAM,aAAa,CAAC;QACpB,MAAM,MAAM,cAAc,IAAI,CAAC,CAAC;YAC5B,OAAO;gBAAC;gBAAS;aAAS,EAAE,CAAC;gBACzB,MAAM,UAAU,MAAM,OAAO,CAAC,IAAM,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC,QAAU,UAAU,CAAC,KAAK,GAAG;YAC9F;QACJ;QACA,OAAO;IACX;IACA,MAAM,SAAS,CAAC,MAAM;QAClB,MAAM,WAAW,eAAe,gBAAgB,eAAe,YAAY,aAAa,MAAM,KAAK,CAAC,CAAC,KAAK,CAAC;QAC3G,MAAM,MAAM,SAAS,MAAM,UAAU;QACrC,OAAO;YACH,GAAG,IAAI,SAAS;YAChB,GAAG,IAAI,YAAY;YACnB,GAAG,IAAI,SAAS;YAChB,GAAG,IAAI,QAAQ;YACf,GAAG,cAAc,MAAM,SAAS;QACpC;IACJ;IACA,MAAM,OAAO,CAAC;QACV,MAAM,UAAU;YACZ,GAAG,IAAI;YACP,QAAQ;gBAAE,OAAO,MAAM,MAAM,UAAU,KAAK,CAAC;YAAI;YACjD,WAAW;gBAAE,OAAO,MAAM,MAAM,aAAa,KAAK,CAAC;YAAI;YACvD,QAAQ;gBAAE,OAAO,MAAM,MAAM,UAAU,KAAK,CAAC;YAAI;QACrD;QACA,uEAAuE;QACvE,OAAO;YAAC;YAAS;SAAS,EAAE,CAAC;YACzB,MAAM,MAAM,MAAM,IAAI,CAAC,CAAC;gBACpB,MAAM,aAAa,QAAQ,UAAU,IAAI,CAAC;gBAC1C,UAAU,CAAC,KAAK,GAAG;gBACnB,QAAQ,UAAU,GAAG;YACzB;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAC,SAAW,CAAC;YAC7B,MAAM,eAAe,SAAS,MAAM,GAAG,GACnC,gCAAgC,MAAM,GAAG,GACzC;YACJ,OAAO,mBAAmB,CAAC,IAAI,CAAC;gBAAE,MAAM;gBAAS,MAAM;YAAa;QACxE;IACA,MAAM,gBAAgB,CAAC;QACnB,MAAM,UAAU,OAAO,SAAS,CAAC,OAAO;QACxC,MAAM,UAAU,eAAe,WAAW,OAAO,UAAU,CAAC,SAAS,CAAC,SAAS;YAAE,WAAW;QAAK,KAAK;QACtG,MAAM,OAAO,WAAW,SAAS,OAAO,MAAM;QAC9C,MAAM,yBAAyB;YAC3B,IAAI,cAAc,KAAK,MAAM,EAAE,KAAK,IAAI,GAAG;gBACvC,MAAM,OAAO,OAAO,GAAG,CAAC,OAAO,CAAC;gBAChC,OAAO;oBACH,OAAO,KAAK,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO;oBACxC,QAAQ,KAAK,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO;gBAC7C;YACJ,OACK;gBACD,OAAO,CAAC;YACZ;QACJ;QACA,MAAM,aAAa;QACnB,OAAO;YACH,OAAO;YACP,GAAG,IAAI;YACP,GAAG,UAAU;QACjB;IACJ;IACA,MAAM,eAAe,CAAC,KAAK,SAAW,CAAC;YACnC,4CAA4C;YAC5C,IAAI,SAAS,SAAS,GAAG,KAAK,SAAS,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC1D,MAAM,OAAO,SAAS,IAAI;gBAC1B,MAAM,cAAc,WAAW,MAAM,OAAO,MAAM;gBAClD,MAAM,SAAS;oBACX,GAAG,WAAW;oBACd,QAAQ,SAAS,GAAG;oBACpB,OAAO;gBACX;gBACA,IAAI,OAAO,CAAC,KAAK;YACrB;QACJ;IACA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,MAAM,eAAe,OAAO,GAAG,CAAC,MAAM,CAAC;QACvC,iDAAiD;QACjD,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC3C,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBAC/C,IAAI,aAAa,CAAC,EAAE,KAAK,YAAY,CAAC,EAAE,EAAE;oBACtC,aAAa,MAAM,CAAC,GAAG;gBAC3B;YACJ;QACJ;QACA,OAAO,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;IAC3C;IACA,MAAM,eAAe,CAAC,QAAQ;QAC1B,MAAM,gBAAgB,OAAO,GAAG,CAAC,MAAM,CAAC;QACxC,OAAO,aAAa,CAAC;QACrB,kBAAkB,QAAQ;QAC1B,OAAO,WAAW;IACtB;IACA,MAAM,gBAAgB,CAAC,KAAK,gBAAkB,cAAc,kBAAkB,kBAAkB,qBAAqB,cAAc,aAAa;IAChJ,MAAM,0BAA0B,CAAC,UAAU;QACvC,MAAM,uBAAuB,CAAC,UAAU,UAAY,SAAS,KAAK,KAAK,QAAQ,KAAK,IAAI,SAAS,MAAM,KAAK,QAAQ,MAAM;QAC1H,OAAO,qBAAqB,UAAU,YAAY,cAAc,QAAQ,MAAM,EAAE,SAAS,IAAI;IACjG;IACA,MAAM,aAAa,CAAC,UAAU,SAAS;QACnC,IAAI;QACJ,QAAQ,KAAK,GACT,wBAAwB,UAAU,YAAY,cAAc,UACtD,WAAW,QAAQ;YAAE,GAAG,OAAO;YAAE,OAAO;QAAG,KAC3C,WAAW,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,OAAO,MAAM;QAC5G,uFAAuF;QACvF,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,MAAM,KAAK,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM,CAAC,GAAG;YACnF,aAAa,QAAQ,QAAQ,KAAK;QACtC,OACK;YACD,aAAa,QAAQ,SAChB,IAAI,CAAC,CAAC;gBACP,aAAa,QAAQ,SAAS,IAAI;YACtC,GAAG,KAAK,CAAC,YAAY;QACzB;IACJ;IACA,MAAM,aAAa,CAAC;QAChB,MAAM,aAAa,cAAc;QACjC,MAAM,cAAc,KAAK;QACzB,MAAM,cAAc,KAAK;QACzB,MAAM,eAAe,CAAC,UAAU;YAC5B,MAAM,cAAc,OAAO,IAAI,OAAO,IAAI;YAC1C,4EAA4E;YAC5E,IAAI,SAAS,MAAM,KAAK,YAAY,MAAM,EAAE;gBACxC,aAAa,KAAK,QAAQ;oBAAE,KAAK,YAAY,MAAM;oBAAE,MAAM;gBAAG;gBAC9D,aAAa,QAAQ,aAChB,IAAI,CAAC,aAAa,KAAK,SACvB,KAAK,CAAC,YAAY;YAC3B;QACJ;QACA,MAAM,cAAc,CAAC;YACjB,IAAI;YACJ,MAAM,OAAO,OAAO,IAAI,OAAO;YAC/B,MAAM,gBAAgB,WAAW,CAAC,KAAK,KAAK,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM;YACrG,IAAI,OAAO,CAAC,KAAK;QACrB;QACA,MAAM,eAAe,CAAC,KAAK,aAAa;YACpC,MAAM,aAAa,OAAO,IAAI,OAAO,IAAI;YACzC,MAAM,OAAO,wBAAwB,UAAU,eAAe,cAAc,UACtE;gBAAE,GAAG,UAAU;gBAAE,OAAO;YAAG,IAC3B;YACN,MAAM,QAAQ,WAAW,QAAQ;YACjC,IAAI,OAAO,CAAC,KAAK;gBACb,GAAG,IAAI;gBACP;YACJ;QACJ;QACA,MAAM,aAAa;YAAC;gBACZ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACjB;SAAE;QACN,MAAM,YAAY,CAAC,cAAc,UAAU,EAAE,GAAG;YAAC;gBACzC,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;YACf;SAAE;QACN,MAAM,aAAa;YACf,OAAO;YACP,MAAM;YACN,OAAO,QAAQ;gBAAC;gBAAY;aAAU;QAC1C;QACA,MAAM,gBAAgB;YAClB,MAAM;YACN,MAAM;YACN,OAAO;QACX;QACA,MAAM,WAAW;YACb,OAAO;YACP,OAAO;gBACH;aACH;QACL;QACA,MAAM,oBAAoB,EAAE;QAC5B,IAAI,aAAa,SAAS;YACtB,kBAAkB,IAAI,CAAC;gBACnB,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;YACX;QACJ;QACA,IAAI,UAAU,SAAS;YACnB,kBAAkB,IAAI,CAAC;gBACnB,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;YACX;QACJ;QACA,MAAM,cAAc;YAChB,OAAO;YACP,MAAM;YACN,OAAO;QACX;QACA,MAAM,OAAO;YACT;YACA;SACH;QACD,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAC9B,KAAK,IAAI,CAAC;QACd;QACA,MAAM,OAAO;YACT,MAAM;YACN;QACJ;QACA,MAAM,MAAM,OAAO,aAAa,CAAC,IAAI,CAAC;YAClC,OAAO;YACP,MAAM;YACN;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,UAAU,CAAC;gBACP,MAAM,cAAc,OAAO,IAAI,OAAO;gBACtC,WAAW,YAAY,GAAG,IAAI,aAAa;gBAC3C,IAAI,KAAK;YACb;YACA,UAAU,CAAC,KAAK;gBACZ,OAAQ,OAAO,IAAI;oBACf,KAAK;wBACD,aAAa,YAAY,GAAG,IAAI;wBAChC;oBACJ,KAAK;wBACD,YAAY;wBACZ;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,aAAa,KAAK,OAAO,IAAI,EAAE,YAAY,GAAG;wBAC9C;gBACR;gBACA,YAAY,GAAG,CAAC,OAAO,IAAI,OAAO;YACtC;YACA;QACJ;IACJ;IAEA,MAAM,MAAM,CAAC;QACT,MAAM,eAAe;YACjB,WAAW;QACf;QACA,OAAO;YACH,YAAY;QAChB;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,MAAM,eAAe;YACjB,WAAW;QACf;QACA,OAAO,UAAU,CAAC,YAAY;IAClC;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,kBAAkB,CAAC;QACrB,MAAM,OAAO,KAAK,IAAI;QACtB,OAAO,SAAS,YAAY,SAAS,WAAW,SAAS;IAC7D;IACA,MAAM,eAAe,SAAC,MAAM,QAAQ;YAAW,gFAAe;QAC1D,MAAM,QAAQ,KAAK,IAAI,CAAC;QACxB,IAAI,cAAc,QAAQ;YACtB,OAAO;QACX,OACK,IAAI,CAAC,IAAI,QAAQ,YAAY;YAC9B,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,gBAAgB,CAAC,MAAM,aAAa;QACtC,mEAAmE;QACnE,MAAM,cAAc,YAAY,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK;QAChE,yBAAyB;QACzB,MAAM,eAAe,cAAc,QAAQ;QAC3C,MAAM,iBAAiB,KAAK,IAAI,KAAK,UAAU,OAAO;QACtD,MAAM,gBAAgB,cAAc,iBAAiB;QACrD,YAAY,IAAI,CAAC;YACb,OAAO,aAAa,MAAM,QAAQ,SAAS;YAC3C,QAAQ,aAAa,MAAM,QAAQ,UAAU;QACjD;IACJ;IACA,MAAM,oBAAoB,CAAC,QAAQ,UAAU,aAAa;QACtD,MAAM,UAAU,OAAO,OAAO,MAAM,EAAE,KAAK,CAAC,MAAM;YAAE,SAAS;QAAS;QACtE,MAAO,QAAQ,UAAU,CAAE;YACvB,YAAY,MAAM,CAAC,QAAQ,UAAU;QACzC;IACJ;IACA,MAAM,wBAAwB,CAAC,QAAQ;QACnC,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,cAAc,IAAI,SAAS,OAAO;QACxC,6BAA6B,QAAQ,MAAM;QAC3C,cAAc,MAAM,aAAa,CAAC;QAClC,YAAY,IAAI,CAAC;YACb,SAAS,KAAK,IAAI,CAAC;YACnB,OAAO,OAAO,cAAc;YAC5B,mBAAmB;YACnB,SAAS,2BAA2B;QACxC;QACA,OAAO;IACX;IACA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,IAAI;QACJ,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,iBAAiB,IAAI,SAAS,QAAQ;QAC5C,eAAe,IAAI,CAAC;YAChB,mBAAmB;YACnB,SAAS,KAAK,IAAI,CAAC;YACnB,mBAAmB;YACnB,SAAS,mCAAmC;QAChD;QACA,6BAA6B,QAAQ,MAAM;QAC3C,MAAM,SAAS,OAAO,GAAG,CAAC,UAAU,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAChG,MAAM,cAAc,IAAI,SAAS,MAAM;QACvC,cAAc,MAAM,aAAa;QACjC,YAAY,IAAI,CAAC;YACb,KAAK,KAAK,IAAI,CAAC;YACf,OAAO,KAAK,IAAI,CAAC;YACjB,OAAO,KAAK,IAAI,CAAC;QACrB;QACA,IAAI,SAAS,UAAU;YACnB,YAAY,IAAI,CAAC;gBACb,iBAAiB,KAAK,IAAI,CAAC;gBAC3B,aAAa;gBACb,SAAS,KAAK,IAAI,CAAC;gBACnB,gBAAgB,KAAK,IAAI,CAAC;YAC9B;QACJ,OACK;YACD,mEAAmE;YACnE,MAAM,QAAQ;gBAAC;gBAAY;gBAAe;gBAAe;gBAAQ;gBAAS;gBAAU;aAAU;YAC9F,OAAO,OAAO,CAAC;gBACX,YAAY,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;YACzC;YACA,0DAA0D;YAC1D,MAAM,gBAAgB,eAAe,IAAI,CAAC;YAC1C,IAAI,cAAc,gBAAgB;gBAC9B,kBAAkB,QAAQ,MAAM,aAAa,SAAS;YAC1D;QACJ;QACA,MAAM,WAAW,IAAI,SAAS,QAAQ;QACtC,SAAS,IAAI,CAAC,SAAS;QACvB,eAAe,MAAM,CAAC;QACtB,eAAe,MAAM,CAAC;QACtB,OAAO;IACX;IACA,MAAM,+BAA+B,CAAC,QAAQ,YAAY;QACtD,IAAI;QACJ,uFAAuF;QACvF,oCAAoC;QACpC,MAAM,UAAU,CAAC,KAAK,WAAW,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAChF,IAAI,KAAK,QAAQ,MAAM;QACvB,MAAO,KAAM;YACT,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,IAAI;YACjC,IAAI,YAAY,OAAO,CAAC,GAAG,CAAC,KAAK;YACjC,IAAI,aAAa,WAAW,aAAa,YAAY,aAAa,WAAW,CAAC,WAAW,UAAU,cAAc;gBAC7G,IAAI,aAAa,UAAU,aAAa,OAAO;oBAC3C,YAAY,OAAO,UAAU,CAAC,WAAW;gBAC7C;gBACA,WAAW,IAAI,CAAC,gBAAgB,UAAU;YAC9C;QACJ;QACA,4DAA4D;QAC5D,gDAAgD;QAChD,MAAM,aAAa,SAAS;YAAE,OAAO;QAAK,GAAG,OAAO,MAAM;QAC1D,MAAM,WAAW,IAAI,SAAS,OAAO;QACrC,OAAO,WAAW,QAAQ,IAAI,CAAC,QAAU,SAAS,MAAM,CAAC;QACzD,MAAM,YAAY,WAAW,SAAS,CAAC;QACvC,IAAI,WAAW;YACX,WAAW,IAAI,CAAC,iBAAiB,OAAO;YACxC,WAAW,KAAK;QACpB;IACJ;IACA,MAAM,qBAAqB,CAAC;QACxB,MAAM,YAAY,KAAK,IAAI,CAAC;QAC5B,OAAO,SAAS,cAAc,qBAAqB,IAAI,CAAC;IAC5D;IACA,MAAM,uBAAuB,CAAC;QAC1B,IAAI,WAAW;QACf,MAAQ,WAAW,SAAS,MAAM,CAAG;YACjC,IAAI,SAAS,IAAI,CAAC,2BAA2B,mBAAmB,WAAW;gBACvE,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,uBAAuB,CAAC,SAAW,CAAC;YACtC,IAAI,IAAI,MAAM,MAAM;YACpB,IAAI;YACJ,MAAO,IAAK;gBACR,OAAO,KAAK,CAAC,EAAE;gBACf,IAAI,CAAC,KAAK,MAAM,EAAE;oBACd;gBACJ;gBACA,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,oBAAoB;oBACrC;gBACJ;gBACA,IAAI,gBAAgB,SAAS,cAAc,SAAS;oBAChD,IAAI,CAAC,qBAAqB,OAAO;wBAC7B,KAAK,OAAO,CAAC,kBAAkB,QAAQ;oBAC3C;gBACJ,OACK;oBACD,IAAI,CAAC,qBAAqB,OAAO;wBAC7B,KAAK,OAAO,CAAC,sBAAsB,QAAQ;oBAC/C;gBACJ;YACJ;QACJ;IAEA,MAAM,mBAAmB,CAAC,QAAQ,SAAS;QACvC,MAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG;QAC1C,MAAM,WAAW,gBAAgB;QACjC,MAAM,WAAW,iBAAiB;QAClC,OAAO,OAAO,OAAO,MAAM,EAAE;YAAE;YAAU;QAAS,GAAG,KAAK,CAAC,MAAM;YAAE;QAAQ;IAC/E;IAEA,MAAM,UAAU,CAAC;QACb,OAAO,EAAE,CAAC,WAAW;YACjB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;YACvC,0DAA0D;YAC1D,MAAM,YAAY,OAAO,YAAY;YACrC,OAAO,2CAA2C,KAAK,CAAC,MAAM,CAAC;gBAC3D,SAAS,CAAC,KAAK,GAAG,CAAC;YACvB;YACA,iDAAiD;YACjD,KAAK;gBACD,OAAO;oBAAC;iBAAQ;YACpB,GAAG,CAAC,OAAO;gBACP,MAAM,OAAO,OAAO,cAAc,CAAC;gBACnC,IAAI,MAAM;oBACN,OAAO,OAAO,CAAC;wBACX,KAAK,UAAU,CAAC,KAAK,GAAG,CAAC;wBACzB,KAAK,eAAe,CAAC,IAAI,CAAC;oBAC9B;gBACJ;YACJ;YACA,qDAAqD;YACrD,OAAO,aAAa,CAAC,mCAAmC,qBAAqB;YAC7E,+EAA+E;YAC/E,WAAW,kBAAkB,CAAC,mBAAmB,CAAC,OAAO;gBACrD,IAAI;gBACJ,IAAI,IAAI,MAAM,MAAM;gBACpB,MAAO,IAAK;oBACR,MAAM,OAAO,KAAK,CAAC,EAAE;oBACrB,IAAI,CAAC,KAAK,MAAM,EAAE;wBACd;oBACJ;oBACA,MAAM,cAAc,KAAK,IAAI,CAAC;oBAC9B,MAAM,UAAU,IAAI,SAAS,aAAa;oBAC1C,2CAA2C;oBAC3C,IAAI,gBAAgB,SAAS;wBACzB,MAAM,YAAY,KAAK,IAAI,CAAC;wBAC5B,IAAI,aAAa,UAAU,OAAO,CAAC,0BAA0B,CAAC,KAAK,KAAK,UAAU,EAAE;4BAChF,QAAQ,IAAI,CAAC;gCACT,OAAO,KAAK,UAAU,CAAC,IAAI,CAAC;gCAC5B,QAAQ,KAAK,UAAU,CAAC,IAAI,CAAC;4BACjC;wBACJ,OACK;4BACD,QAAQ,IAAI,CAAC;gCACT,OAAO,KAAK,IAAI,CAAC;gCACjB,QAAQ,KAAK,IAAI,CAAC;4BACtB;wBACJ;oBACJ;oBACA,QAAQ,IAAI,CAAC;wBACT,OAAO,KAAK,IAAI,CAAC;oBACrB;oBACA,sCAAsC;oBACtC,MAAM,UAAU,CAAC,KAAK,KAAK,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;oBAC1E,IAAI,KAAK,QAAQ,MAAM;oBACvB,MAAO,KAAM;wBACT,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,IAAI;wBACjC,IAAI,SAAS,OAAO,CAAC,mBAAmB,GAAG;4BACvC,QAAQ,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,KAAK;wBACvD;oBACJ;oBACA,mBAAmB;oBACnB,MAAM,YAAY,KAAK,IAAI,CAAC;oBAC5B,IAAI,WAAW;wBACX,MAAM,WAAW,iBAAiB,QAAQ,aAAa,SAAS;wBAChE,OAAO,SAAS,QAAQ,IAAI,CAAC,QAAU,QAAQ,MAAM,CAAC;oBAC1D;oBACA,KAAK,OAAO,CAAC;gBACjB;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,cAAc;YACpB,qEAAqE;YACrE,0DAA0D;YAC1D,MAAM,MAAM,OAAO,GAAG;YACtB,OAAO,IAAI,MAAM,CAAC,4BAA4B,CAAC;gBAC3C,IAAI,IAAI,MAAM,CAAC,iBAAiB,KAAK,MAAM,KAAK,GAAG;oBAC/C,IAAI,GAAG,CAAC,KAAK,QAAQ;wBAAE,OAAO;oBAAW;gBAC7C;YACJ;QACJ;IACJ;IAEA,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,eAAe,CAAC;YACtB,IAAI;YACJ,IAAI,EAAE,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,YAAY,CAAC,kBAAkB,GAAG;gBAC9E,EAAE,IAAI,GAAG;YACb;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS;YACxC,SAAS;YACT,MAAM;YACN;YACA,SAAS,CAAC;gBACN,MAAM,YAAY,OAAO,SAAS;gBAClC,UAAU,SAAS,CAAC,eAAe,UAAU,OAAO;gBACpD,MAAM,wBAAwB,UAAU,yBAAyB,CAAC,wEAAwE,UAAU,SAAS,EAAE,MAAM;gBACrK,MAAM,iBAAiB,gBAAgB,QAAQ;gBAC/C,OAAO;oBACH;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS;YACpC,MAAM;YACN,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,SAAS,CAAC;YACnB,WAAW;YACX,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO,IAAI;QACf;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/media/index.js"], "sourcesContent": ["// Exports the \"media\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/media')\n//   ES2015:\n//     import 'tinymce/plugins/media'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,SAAS;AACT,cAAc;AACd,uCAAuC;AACvC,YAAY;AACZ,qCAAqC", "ignoreList": [0], "debugId": null}}]}