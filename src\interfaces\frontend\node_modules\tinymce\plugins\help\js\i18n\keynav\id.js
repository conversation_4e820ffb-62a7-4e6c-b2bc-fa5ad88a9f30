tinymce.Resource.add('tinymce.html-i18n.help-keynav.id',
'<h1><PERSON><PERSON><PERSON> navigas<PERSON> keyboard</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Fokus pada bilah Menu</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Fokus pada Bilah Alat</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Fokus pada footer</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Fokuskan pemberitahuan</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Fokus pada bilah alat kontekstual</dt>\n' +
  '  <dd>Windows, Linux, atau macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigasi akan dimulai dari item pertama UI, yang akan disorot atau digarisbawahi di\n' +
  '  alur elemen Footer.</p>\n' +
  '\n' +
  '<h1>Berpindah antar-bagian UI</h1>\n' +
  '\n' +
  '<p>Untuk berpindah dari satu bagian UI ke bagian berikutnya, tekan <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>Untuk berpindah dari satu bagian UI ke bagian sebelumnya, tekan <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Urutan <strong>Tab</strong> bagian-bagian UI ini adalah:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Bilah menu</li>\n' +
  '  <li>Tiap grup bilah alat</li>\n' +
  '  <li>Bilah sisi</li>\n' +
  '  <li>Alur elemen di footer</li>\n' +
  '  <li>Tombol aktifkan/nonaktifkan jumlah kata di footer</li>\n' +
  '  <li>Tautan merek di footer</li>\n' +
  '  <li>Pengatur pengubahan ukuran editor di footer</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Jika suatu bagian UI tidak ada, bagian tersebut dilewati.</p>\n' +
  '\n' +
  '<p>Jika fokus navigasi keyboard ada pada footer, tetapi tidak ada bilah sisi yang terlihat, menekan <strong>Shift+Tab</strong>\n' +
  '  akan memindahkan fokus ke grup bilah alat pertama, bukan yang terakhir.</p>\n' +
  '\n' +
  '<h1>Berpindah di dalam bagian-bagian UI</h1>\n' +
  '\n' +
  '<p>Untuk berpindah dari satu elemen UI ke elemen berikutnya, tekan tombol <strong>Panah</strong> yang sesuai.</p>\n' +
  '\n' +
  '<p>Tombol panah <strong>Kiri</strong> dan <strong>Kanan</strong> untuk</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>berpindah-pindah antar-menu di dalam bilah menu.</li>\n' +
  '  <li>membuka sub-menu di dalam menu.</li>\n' +
  '  <li>berpindah-pindah antar-tombol di dalam grup bilah alat.</li>\n' +
  '  <li>berpindah-pindah antar-item di dalam alur elemen footer.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Tombol panah <strong>Bawah</strong> dan <strong>Atas</strong> untuk</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>berpindah-pindah antar-item menu di dalam menu.</li>\n' +
  '  <li>berpindah-pindah antar-item di dalam menu pop-up bilah alat.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Tombol <strong>Panah</strong> hanya bergerak di dalam bagian UI yang difokuskan.</p>\n' +
  '\n' +
  '<p>Untuk menutup menu, sub-menu, atau menu pop-up yang terbuka, tekan tombol <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Jika fokus sedang berada di ‘atas’ bagian UI tertentu, menekan tombol <strong>Esc</strong> juga dapat mengeluarkan fokus\n' +
  '  dari seluruh navigasi keyboard.</p>\n' +
  '\n' +
  '<h1>Menjalankan item menu atau tombol bilah alat</h1>\n' +
  '\n' +
  '<p>Jika item menu atau tombol bilah alat yang diinginkan tersorot, tekan <strong>Return</strong>, <strong>Enter</strong>,\n' +
  '  atau <strong>Spasi</strong> untuk menjalankan item.</p>\n' +
  '\n' +
  '<h1>Berpindah dalam dialog tanpa tab</h1>\n' +
  '\n' +
  '<p>Dalam dialog tanpa tab, fokus diarahkan pada komponen interaktif pertama saat dialog terbuka.</p>\n' +
  '\n' +
  '<p>Berpindah di antara komponen dalam dialog interaktif dengan menekan <strong>Tab</strong> atau <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Berpindah dalam dialog dengan tab</h1>\n' +
  '\n' +
  '<p>Dalam dialog yang memiliki tab, fokus diarahkan pada tombol pertama di dalam menu saat dialog terbuka.</p>\n' +
  '\n' +
  '<p>Berpindah di antara komponen-komponen interaktif pada tab dialog ini dengan menekan <strong>Tab</strong> atau\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Beralih ke tab dialog lain dengan mengarahkan fokus pada menu tab lalu tekan tombol <strong>Panah</strong>\n' +
  '  yang sesuai untuk berpindah ke berbagai tab yang tersedia.</p>\n');