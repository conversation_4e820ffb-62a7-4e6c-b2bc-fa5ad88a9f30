"use client";

import { useState, ChangeEvent, useEffect } from "react";
import axios from "axios";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/client/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/client/components/ui/card";
import { <PERSON><PERSON> } from "@/client/components/ui/button";
import { Input } from "@/client/components/ui/input";
import { Textarea } from "@/client/components/ui/textarea";
import { Badge } from "@/client/components/ui/badge";
import { 
  PenTool, 
  Eye, 
  Send, 
  FileText, 
  Upload, 
  Mail,
  Sparkles,
  Clock,
  CheckCircle,
  Trash2,
  AlertCircle
} from "lucide-react";
import NewsletterDisplay from "@/client/components/NewsletterDisplay";
import FeedbackForm from "@/client/components/FeedbackForm";
import CopyButton from "@/client/components/CopyButton";
import EditableNewsletter from '@/client/components/EditableNewsletter';

export default function NewsletterApp() {
  // Generation tab state
  const [theme, setTheme] = useState("");
  const [description, setDescription] = useState("");
  const [newsletter, setNewsletter] = useState("");
  const [revised, setRevised] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const content = revised || newsletter;
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchNewsletters();
  }, []);
 
  // Visualization tab state - real API data
  const [newsletters, setNewsletters] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [selectedContent, setSelectedContent] = useState<string>("");

  // Sending tab state
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [sendingProgress, setSendingProgress] = useState(0);
  const [isSending, setIsSending] = useState(false);
  const [status, setStatus] = useState<string>("");

  const fetchNewsletters = async () => {
    try {
      const res = await axios.get("http://localhost:8000/list-newsletters");
      setNewsletters(res.data.files || []);
    } catch {
      setStatus("Erreur lors du chargement des newsletters.");
    }
  };



  const handleView = async (filename: string) => {
    try {
      const res = await axios.get(`http://localhost:8000/static/${filename}`, {
        responseType: "text",
      });
      setSelectedFile(filename);
      setSelectedContent(res.data);
    } catch {
      setStatus("Erreur lors de l'affichage du contenu.");
    }
  };

  const handleDelete = async (filename: string) => {
    if (!confirm(`Supprimer "${filename}" ?`)) return;

    try {
      await axios.delete("http://localhost:8000/delete-newsletter", {
        params: { filename },
      });
      setStatus(`"${filename}" supprimée.`);
      fetchNewsletters(); // recharger la liste
      if (selectedFile === filename) {
        setSelectedFile(null);
        setSelectedContent("");
      }
    } catch (e: any) {
      setStatus("Erreur de suppression : " + (e.response?.data?.detail || e.message));
    }
  };

  const generateFromWeb = async () => {
    if (!theme || !description) {
      alert("Thème et description requis.");
      return;
    }
    setLoading(true);
    try {
      const res = await axios.post("http://localhost:8000/generate-web", {
        theme,
        description,
      });
      setNewsletter(res.data.newsletter);
      setRevised("");
      // Refresh the newsletter list to include the new one
      fetchNewsletters();
    } catch {
      alert("Erreur lors de la génération depuis le web.");
    } finally {
      setLoading(false);
    }
  };

  const generateFromFile = async () => {
    if (!theme || !file || !description) {
      alert("Veuillez fournir un thème, description et sélectionner un fichier.");
      return;
    }

    const formData = new FormData();
    formData.append("theme", theme);
    formData.append("description", description);
    formData.append("file", file);

    setLoading(true);
    try {
      const res = await axios.post("http://localhost:8000/generate-file", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      setNewsletter(res.data.newsletter);
      setRevised("");
      // Refresh the newsletter list to include the new one
      fetchNewsletters();
    } catch {
      alert("Erreur lors de la génération depuis un fichier.");
    } finally {
      setLoading(false);
    }
  };

  const reviseNewsletter = async (feedback: string) => {
    try {
      const res = await axios.post("http://localhost:8000/revise", {
        feedback,
        theme,
        description,
      });
      setRevised(res.data.revised_newsletter);
      setNewsletter(""); // on efface l'ancienne version
      
      // Recharger la liste des newsletters pour l'onglet visualisation
      fetchNewsletters();
    } catch {
      alert("Erreur lors de la révision.");
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleCsvFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setCsvFile(e.target.files[0]);
    }
  };

  const sendNewsletter = async () => {
    if (!selectedFile || !csvFile) {
      alert("Veuillez sélectionner une newsletter et un fichier CSV.");
      return;
    }

    const formData = new FormData();
    formData.append("newsletter_file", selectedFile);
    formData.append("csv_file", csvFile);

    setIsSending(true);
    try {
      const res = await axios.post("http://localhost:8000/send-newsletter", formData);
      setStatus(res.data.detail);
      alert("Newsletter envoyée avec succès !");
    } catch (e: any) {
      setStatus("Erreur lors de l'envoi : " + (e.response?.data?.detail || e.message));
    } finally {
      setIsSending(false);
    }
  };

  const handleSaveNewsletter = (updatedContent: string) => {
    // Vérifier que le contenu est valide
    if (!updatedContent || typeof updatedContent !== 'string') {
      console.error('Contenu invalide reçu:', updatedContent);
      alert('Erreur: Contenu invalide reçu');
      return;
    }
    
    try {
      // Mettre à jour le contenu dans la génération
      if (revised) {
        setRevised(updatedContent);
      } else {
        setNewsletter(updatedContent);
      }
      // Recharger la liste des newsletters
      fetchNewsletters();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du contenu:', error);
      alert('Erreur lors de la mise à jour du contenu');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="relative mb-12">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-gray-900/40 to-blue-900/30 rounded-3xl"></div>
          
          {/* Header content */}
          <div className="relative flex flex-col lg:flex-row items-center justify-between p-8 lg:p-12">
            {/* Logo and title section */}
            <div className="flex items-center gap-6 mb-6 lg:mb-0">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-black via-gray-900 to-blue-900 blur-lg opacity-40"></div>
                <img
                  src="http://holokia.com/wp-content/uploads/2025/04/logo_holokia_noir.png"
                  alt="Holokia"
                  className="relative h-24 w-auto object-contain drop-shadow-2xl"
                />
              </div>
              <div>
                <h1 className="text-3xl lg:text-5xl font-bold text-white mb-2">
                  Newsletter
                  <span className="bg-gradient-to-r from-blue-300 via-gray-200 to-white bg-clip-text text-transparent"> IA</span>
                </h1>
                <p className="text-gray-300 text-sm lg:text-base">
                  Générateur intelligent par Holokia
                </p>
              </div>
            </div>
            
            {/* Decorative elements */}
            <div className="hidden lg:flex items-center gap-4">
              <div className="flex space-x-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                <div className="w-3 h-3 bg-gray-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-3 h-3 bg-black rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-blue-400 via-gray-400 to-transparent"></div>
              <div className="text-xs text-gray-400 font-medium">
                Powered by AI
              </div>
            </div>
          </div>
          
          {/* Bottom accent */}
          <div className="h-1 bg-gradient-to-r from-black via-blue-600 to-gray-500 rounded-full mx-8 lg:mx-12"></div>
        </div>

        {/* Main Tabs Interface */}
        <Tabs defaultValue="generation" className="w-full max-w-7xl mx-auto">
          <TabsList className="grid w-full grid-cols-3 mb-8 bg-black/50 backdrop-blur-md border-gray-600/50">
            <TabsTrigger value="generation" className="text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white">
              <PenTool className="w-5 h-5 mr-2" />
              Génération
            </TabsTrigger>
            <TabsTrigger value="visualization" className="text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white">
              <Eye className="w-5 h-5 mr-2" />
              Visualisation
            </TabsTrigger>
            <TabsTrigger value="sending" className="text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white">
              <Send className="w-5 h-5 mr-2" />
              Envoi
            </TabsTrigger>
          </TabsList>

          {/* Generation Tab */}
          <TabsContent value="generation" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Form Column - 1/3 */}
              <Card className="lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-blue-700" />
                    Paramètres de génération
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Thème de la newsletter</label>
                    <Input
                      placeholder="Ex: Technologie, Marketing, Santé..."
                      value={theme}
                      onChange={(e) => setTheme(e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Description détaillée</label>
                    <Textarea
                      placeholder="Décrivez le contenu souhaité pour votre newsletter..."
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={4}
                    />
                  </div>

                  <div className="space-y-3">
                    <Button
                      onClick={generateFromWeb}
                      disabled={loading}
                      className="w-full bg-gradient-to-r from-black via-blue-900 to-gray-800 hover:from-gray-900 hover:via-blue-800 hover:to-black"
                      size="lg"
                    >
                      {loading ? "Génération en cours..." : "Générer via web"}
                    </Button>

                    <Button
                      onClick={() => setShowFileUpload(!showFileUpload)}
                      variant="outline"
                      className="w-full"
                      size="lg"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Charger un fichier
                    </Button>

                    {showFileUpload && (
                      <Card className="border-dashed">
                        <CardContent className="pt-6">
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium mb-2">
                                Sélectionnez un fichier :
                              </label>
                              <Input
                                type="file"
                                accept=".txt,.pdf,.doc,.docx,.rtf,.md"
                                onChange={handleFileChange}
                              />
                              {file && (
                                <p className="mt-2 text-sm text-green-600 flex items-center gap-2">
                                  <FileText className="w-4 h-4" />
                                  Fichier sélectionné : <strong>{file.name}</strong>
                                </p>
                              )}
                            </div>
                            <Button
                              onClick={generateFromFile}
                              disabled={loading || !file}
                              className="w-full"
                            >
                              {loading ? "Génération en cours..." : "Générer via fichier"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </CardContent>
              </Card>

             {/* Preview Column - 2/3 */}
<Card className="lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
  <CardHeader className="flex justify-between items-center">
    <CardTitle>Aperçu de la newsletter</CardTitle>
    {content && (
      <Button
        variant="outline"
        onClick={() => setIsEditing(!isEditing)}
        className="text-sm"
      >
        {isEditing ? "Annuler" : "Modifier"}
      </Button>
    )}
  </CardHeader>
  <CardContent>
    {content ? (
      <div className="space-y-4">
        <EditableNewsletter
          initialContent={content}
          theme={theme}
          onSave={handleSaveNewsletter}
          editing={isEditing}
          onEditingChange={setIsEditing}
        />

        <div className="space-y-3">
          <FeedbackForm onRevise={reviseNewsletter} />
        </div>
      </div>
    ) : (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200">
          <FileText className="w-8 h-8 text-blue-400" />
        </div>
        <p className="text-blue-600">Aucune newsletter générée</p>
        <p className="text-sm text-blue-500">
          Remplissez le formulaire et cliquez sur générer
        </p>
      </div>
    )}
  </CardContent>
</Card>

            </div>
          </TabsContent>

          {/* Visualization Tab */}
          <TabsContent value="visualization">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Liste des newsletters - 1/3 */}
              <Card className="lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Newsletters disponibles
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {newsletters.length > 0 ? (
                      newsletters.map((file) => (
                        <div key={file} className="flex items-center justify-between p-3 bg-blue-50/80 rounded-lg border border-blue-100 hover:bg-blue-100/60 transition-colors">
                          <span className="truncate max-w-[60%] font-medium">{file}</span>
                          <div className="flex gap-2">
                            <Button
                              onClick={() => handleView(file)}
                              size="sm"
                              variant="outline"
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              Voir
                            </Button>
                            <Button
                              onClick={() => handleDelete(file)}
                              size="sm"
                              variant="destructive"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-blue-600">
                        <FileText className="w-12 h-12 mx-auto mb-3 text-blue-300" />
                        <p>Aucune newsletter disponible</p>
                        <p className="text-sm text-blue-500">Générez votre première newsletter dans l'onglet "Génération"</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Aperçu de la newsletter sélectionnée - 2/3 */}
              <Card className="lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    Aperçu
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedFile ? (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 p-2 bg-blue-100/80 rounded-lg border border-blue-200">
                        <FileText className="w-4 h-4 text-blue-700" />
                        <span className="font-medium text-blue-900">{selectedFile}</span>
                      </div>
                      <div className="max-h-96 overflow-auto border border-blue-200 rounded-lg p-4 bg-white/95 backdrop-blur-sm">
                        <div 
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{ __html: selectedContent }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200">
                        <Eye className="w-8 h-8 text-blue-400" />
                      </div>
                      <p className="text-blue-600">Aucune newsletter sélectionnée</p>
                      <p className="text-sm text-blue-500">Cliquez sur "Voir" pour afficher une newsletter</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Sending Tab */}
          <TabsContent value="sending">
            <Card className="bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
              <CardHeader>
                <CardTitle>Envoi de newsletter</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Sélectionner une newsletter
                      </label>
                      <select 
                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                        value={selectedFile || ""}
                        onChange={(e) => setSelectedFile(e.target.value)}
                      >
                        <option value="">Choisir une newsletter...</option>
                        {newsletters.map((file) => (
                          <option key={file} value={file}>
                            {file}
                          </option>
                        ))}
                      </select>
                      {selectedFile && (
                        <p className="mt-2 text-sm text-green-600 flex items-center gap-2">
                          <CheckCircle className="w-4 h-4" />
                          Newsletter sélectionnée : <strong>{selectedFile}</strong>
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Fichier CSV des emails
                      </label>
                      <Input
                        type="file"
                        accept=".csv"
                        onChange={handleCsvFileChange}
                      />
                      {csvFile && (
                        <p className="mt-2 text-sm text-green-600 flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Fichier CSV : <strong>{csvFile.name}</strong>
                        </p>
                      )}
                    </div>

                    <Button
                      onClick={sendNewsletter}
                      disabled={!selectedFile || !csvFile || isSending}
                      className="w-full"
                      size="lg"
                    >
                      <Send className="w-4 h-4 mr-2" />
                      {isSending ? "Envoi en cours..." : "Envoyer la newsletter"}
                    </Button>
                  </div>

                  <Card className="bg-gradient-to-br from-blue-50/90 to-gray-100/80 border-blue-200/50 backdrop-blur-md">
                    <CardHeader>
                      <CardTitle className="text-blue-800">Informations</CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm text-blue-700 space-y-2">
                      <p>• Le fichier CSV doit contenir une colonne "email"</p>
                      <p>• Format accepté : <EMAIL></p>
                      <p>• Maximum 1000 emails par envoi</p>
                      <p>• L'envoi peut prendre quelque minutes</p>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
       
        {/* Status Message */}
        {status && (
          <Card className="mt-6 border-l-4 border-l-blue-600 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-blue-700" />
                <p className="text-sm text-gray-900">{status}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
















