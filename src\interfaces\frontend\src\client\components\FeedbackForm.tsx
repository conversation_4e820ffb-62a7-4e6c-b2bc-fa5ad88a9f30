import { useState } from "react";
import { Button } from "@/client/components/ui/button";
import { Textarea } from "@/client/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/client/components/ui/card";

interface FeedbackFormProps {
  onRevise: (feedback: string) => Promise<void>;
}

export default function FeedbackForm({ onRevise }: FeedbackFormProps) {
  const [feedback, setFeedback] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!feedback.trim()) return;
    
    setLoading(true);
    try {
      await onRevise(feedback);
      setFeedback("");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Réviser la newsletter</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Textarea
            placeholder="Entrez vos commentaires pour réviser la newsletter..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            rows={3}
          />
          <Button 
            type="submit" 
            disabled={loading || !feedback.trim()}
            className="w-full"
          >
            {loading ? "Révision en cours..." : "Réviser"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}















// "use client";

// import { useState } from "react";

// export default function FeedbackForm({ onRevise }: { onRevise: (feedback: string) => Promise<void> }) {
//   const [feedback, setFeedback] = useState("");
//   const [isLoading, setIsLoading] = useState(false);

//   const handleRevise = async () => {
//     setIsLoading(true);
//     await onRevise(feedback);
//     setIsLoading(false);
//   };

//   return (
//     <div className="mt-4">
//       <textarea
//         placeholder="Entrer vos remarques ici..."
//         value={feedback}
//         onChange={(e) => setFeedback(e.target.value)}
//         rows={3}
//         className="w-full border p-3 rounded-md resize-none mb-2 text-gray-900 placeholder-gray-500"
//       />
//       <button
//         onClick={handleRevise}
//         disabled={isLoading}
//         className={`w-full py-2 rounded-md transition ${
//           isLoading
//             ? "bg-green-400 cursor-not-allowed"
//             : "bg-green-600 hover:bg-green-700 text-white"
//         }`}
//       >
//         {isLoading ? "amélioration en cours" : "Proposer des améliorations"}
//       </button>
//     </div>
//   );
// }
