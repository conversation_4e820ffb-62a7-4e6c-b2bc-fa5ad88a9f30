import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))


from src.tools.scraping_tool import scrape_content
from src.tools.vectorisation_tool import vectorize_scraped_data
from src.tools.newsletter_generator import generate_newsletter_with_context
from src.prompts.revise_prompt import get_prompt as get_revise_prompt
from src.tools.newsletter_generator import generate_newsletter_with_context as generate_from_prompt
from src.rag.file_to_newsletter import generate_newsletter_from_uploaded_file
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("NewsletterAgent")

@mcp.tool()
async def scrape_news(theme: str, description: str) -> list[str]:
    return scrape_content(theme, description)

@mcp.tool()
async def vectorize_news(chunks: list[str]) -> str:
    vectorize_scraped_data(chunks)
    return f"{len(chunks)} documents vectorises."

@mcp.tool()
async def generate_newsletter(chunks: list[str], theme: str) -> str:
    return generate_newsletter_with_context(chunks, theme)

@mcp.tool()
async def revise_newsletter(original_newsletter: str, feedback: str) -> str:
    prompt = get_revise_prompt(original_newsletter, feedback)
    return generate_from_prompt([prompt])

@mcp.tool()
async def generate_newsletter_from_file(text: str, theme: str, description: str) -> str:
    return generate_newsletter_from_uploaded_file(text, theme, description)

# 🚀 Démarrer le serveur
if __name__ == "__main__":
    print("🟡 Lancement du serveur MCP...")
    mcp.run(transport="stdio")
