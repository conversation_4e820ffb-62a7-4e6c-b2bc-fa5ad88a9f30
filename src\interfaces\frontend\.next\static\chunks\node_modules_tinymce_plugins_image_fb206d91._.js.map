{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/image/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const getPrototypeOf = Object.getPrototypeOf;\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq = (t) => (a) => t === a;\n    const is = (value, constructor) => isObject(value) && hasProto(value, constructor, (o, proto) => getPrototypeOf(o) === proto);\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isPlainObject = (value) => is(value, Object);\n    const isArray = isType('array');\n    const isNull = eq(null);\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n    const isArrayOf = (value, pred) => {\n        if (isArray(value)) {\n            for (let i = 0, len = value.length; i < len; ++i) {\n                if (!(pred(value[i]))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        return false;\n    };\n\n    const noop = () => { };\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativePush = Array.prototype.push;\n    const flatten = (xs) => {\n        // Note, this is possible because push supports multiple arguments:\n        // http://jsperf.com/concat-push/6\n        // Note that in the past, concat() would silently work (very slowly) for array-like objects.\n        // With this change it will throw an error.\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; ++i) {\n            // Ensure that each value is an array itself\n            if (!isArray(xs[i])) {\n                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n            }\n            nativePush.apply(r, xs[i]);\n        }\n        return r;\n    };\n    const get = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = (xs) => get(xs, 0);\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n    const findMap = (arr, f) => {\n        for (let i = 0; i < arr.length; i++) {\n            const r = f(arr[i], i);\n            if (r.isSome()) {\n                return r;\n            }\n        }\n        return Optional.none();\n    };\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const objAcc = (r) => (x, i) => {\n        r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n        each(obj, (x, i) => {\n            (pred(x, i) ? onTrue : onFalse)(x, i);\n        });\n    };\n    const filter = (obj, pred) => {\n        const t = {};\n        internalFilter(obj, pred, objAcc(t), noop);\n        return t;\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n\n    const deep = (old, nu) => {\n        const bothObjects = isPlainObject(old) && isPlainObject(nu);\n        return bothObjects ? deepMerge(old, nu) : nu;\n    };\n    const baseMerge = (merger) => {\n        return (...objects) => {\n            if (objects.length === 0) {\n                throw new Error(`Can't merge zero objects`);\n            }\n            const ret = {};\n            for (let j = 0; j < objects.length; j++) {\n                const curObject = objects[j];\n                for (const key in curObject) {\n                    if (has(curObject, key)) {\n                        ret[key] = merger(ret[key], curObject[key]);\n                    }\n                }\n            }\n            return ret;\n        };\n    };\n    const deepMerge = baseMerge(deep);\n\n    const isNotEmpty = (s) => s.length > 0;\n\n    const fromHtml = (html, scope) => {\n        const doc = scope || document;\n        const div = doc.createElement('div');\n        div.innerHTML = html;\n        if (!div.hasChildNodes() || div.childNodes.length > 1) {\n            const message = 'HTML does not have a single root node';\n            // eslint-disable-next-line no-console\n            console.error(message, html);\n            throw new Error(message);\n        }\n        return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n        const doc = scope || document;\n        const node = doc.createElement(tag);\n        return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n        const doc = scope || document;\n        const node = doc.createTextNode(text);\n        return fromDom(node);\n    };\n    const fromDom = (node) => {\n        // TODO: Consider removing this check, but left atm for safety\n        if (node === null || node === undefined) {\n            throw new Error('Node cannot be null or undefined');\n        }\n        return {\n            dom: node\n        };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    // tslint:disable-next-line:variable-name\n    const SugarElement = {\n        fromHtml,\n        fromTag,\n        fromText,\n        fromDom,\n        fromPoint\n    };\n\n    const rawSet = (dom, key, value) => {\n        /*\n         * JQuery coerced everything to a string, and silently did nothing on text node/null/undefined.\n         *\n         * We fail on those invalid cases, only allowing numbers and booleans.\n         */\n        if (isString(value) || isBoolean(value) || isNumber(value)) {\n            dom.setAttribute(key, value + '');\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n            throw new Error('Attribute value was not simple');\n        }\n    };\n    const set = (element, key, value) => {\n        rawSet(element.dom, key, value);\n    };\n    const remove = (element, key) => {\n        element.dom.removeAttribute(key);\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('image_dimensions', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('image_advtab', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('image_uploadtab', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('image_prepend_url', {\n            processor: 'string',\n            default: ''\n        });\n        registerOption('image_class_list', {\n            processor: 'object[]'\n        });\n        registerOption('image_description', {\n            processor: 'boolean',\n            default: true\n        });\n        registerOption('image_title', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('image_caption', {\n            processor: 'boolean',\n            default: false\n        });\n        registerOption('image_list', {\n            processor: (value) => {\n                const valid = value === false || isString(value) || isArrayOf(value, isObject) || isFunction(value);\n                return valid ? { value, valid } : { valid: false, message: 'Must be false, a string, an array or a function.' };\n            },\n            default: false\n        });\n    };\n    const hasDimensions = option('image_dimensions');\n    const hasAdvTab = option('image_advtab');\n    const hasUploadTab = option('image_uploadtab');\n    const getPrependUrl = option('image_prepend_url');\n    const getClassList = option('image_class_list');\n    const hasDescription = option('image_description');\n    const hasImageTitle = option('image_title');\n    const hasImageCaption = option('image_caption');\n    const getImageList = option('image_list');\n    const showAccessibilityOptions = option('a11y_advanced_options');\n    const isAutomaticUploadsEnabled = option('automatic_uploads');\n    const hasUploadUrl = (editor) => isNotEmpty(editor.options.get('images_upload_url'));\n    const hasUploadHandler = (editor) => isNonNullable(editor.options.get('images_upload_handler'));\n\n    // TODO: Figure out if these would ever be something other than numbers. This was added in: #TINY-1350\n    const parseIntAndGetMax = (val1, val2) => Math.max(parseInt(val1, 10), parseInt(val2, 10));\n    const getImageSize = (url) => new Promise((callback) => {\n        const img = document.createElement('img');\n        const done = (dimensions) => {\n            if (img.parentNode) {\n                img.parentNode.removeChild(img);\n            }\n            callback(dimensions);\n        };\n        img.addEventListener('load', () => {\n            const width = parseIntAndGetMax(img.width, img.clientWidth);\n            const height = parseIntAndGetMax(img.height, img.clientHeight);\n            const dimensions = { width, height };\n            done(Promise.resolve(dimensions));\n        });\n        img.addEventListener('error', () => {\n            done(Promise.reject(`Failed to get image dimensions for: ${url}`));\n        });\n        const style = img.style;\n        style.visibility = 'hidden';\n        style.position = 'fixed';\n        style.bottom = style.left = '0px';\n        style.width = style.height = 'auto';\n        document.body.appendChild(img);\n        img.src = url;\n    });\n    const removePixelSuffix = (value) => {\n        if (value) {\n            value = value.replace(/px$/, '');\n        }\n        return value;\n    };\n    const addPixelSuffix = (value) => {\n        if (value.length > 0 && /^[0-9]+$/.test(value)) {\n            value += 'px';\n        }\n        return value;\n    };\n    const mergeMargins = (css) => {\n        if (css.margin) {\n            const splitMargin = String(css.margin).split(' ');\n            switch (splitMargin.length) {\n                case 1: // margin: toprightbottomleft;\n                    css['margin-top'] = css['margin-top'] || splitMargin[0];\n                    css['margin-right'] = css['margin-right'] || splitMargin[0];\n                    css['margin-bottom'] = css['margin-bottom'] || splitMargin[0];\n                    css['margin-left'] = css['margin-left'] || splitMargin[0];\n                    break;\n                case 2: // margin: topbottom rightleft;\n                    css['margin-top'] = css['margin-top'] || splitMargin[0];\n                    css['margin-right'] = css['margin-right'] || splitMargin[1];\n                    css['margin-bottom'] = css['margin-bottom'] || splitMargin[0];\n                    css['margin-left'] = css['margin-left'] || splitMargin[1];\n                    break;\n                case 3: // margin: top rightleft bottom;\n                    css['margin-top'] = css['margin-top'] || splitMargin[0];\n                    css['margin-right'] = css['margin-right'] || splitMargin[1];\n                    css['margin-bottom'] = css['margin-bottom'] || splitMargin[2];\n                    css['margin-left'] = css['margin-left'] || splitMargin[1];\n                    break;\n                case 4: // margin: top right bottom left;\n                    css['margin-top'] = css['margin-top'] || splitMargin[0];\n                    css['margin-right'] = css['margin-right'] || splitMargin[1];\n                    css['margin-bottom'] = css['margin-bottom'] || splitMargin[2];\n                    css['margin-left'] = css['margin-left'] || splitMargin[3];\n            }\n            delete css.margin;\n        }\n        return css;\n    };\n    // TODO: Input on this callback should really be validated\n    const createImageList = (editor, callback) => {\n        const imageList = getImageList(editor);\n        if (isString(imageList)) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            fetch(imageList)\n                .then((res) => {\n                if (res.ok) {\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                    res.json().then(callback);\n                }\n            });\n        }\n        else if (isFunction(imageList)) {\n            imageList(callback);\n        }\n        else {\n            callback(imageList);\n        }\n    };\n    const waitLoadImage = (editor, data, imgElm) => {\n        const selectImage = () => {\n            imgElm.onload = imgElm.onerror = null;\n            if (editor.selection) {\n                editor.selection.select(imgElm);\n                editor.nodeChanged();\n            }\n        };\n        imgElm.onload = () => {\n            if (!data.width && !data.height && hasDimensions(editor)) {\n                editor.dom.setAttribs(imgElm, {\n                    width: String(imgElm.clientWidth),\n                    height: String(imgElm.clientHeight)\n                });\n            }\n            selectImage();\n        };\n        imgElm.onerror = selectImage;\n    };\n    const blobToDataUri = (blob) => new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            resolve(reader.result);\n        };\n        reader.onerror = () => {\n            var _a;\n            reject((_a = reader.error) === null || _a === void 0 ? void 0 : _a.message);\n        };\n        reader.readAsDataURL(blob);\n    });\n    const isPlaceholderImage = (imgElm) => imgElm.nodeName === 'IMG' && (imgElm.hasAttribute('data-mce-object') || imgElm.hasAttribute('data-mce-placeholder'));\n    const isSafeImageUrl = (editor, src) => {\n        const getOption = editor.options.get;\n        return global$2.isDomSafe(src, 'img', {\n            allow_html_data_urls: getOption('allow_html_data_urls'),\n            allow_script_urls: getOption('allow_script_urls'),\n            allow_svg_data_urls: getOption('allow_svg_data_urls')\n        });\n    };\n\n    const DOM = global$3.DOM;\n    const getHspace = (image) => {\n        if (image.style.marginLeft && image.style.marginRight && image.style.marginLeft === image.style.marginRight) {\n            return removePixelSuffix(image.style.marginLeft);\n        }\n        else {\n            return '';\n        }\n    };\n    const getVspace = (image) => {\n        if (image.style.marginTop && image.style.marginBottom && image.style.marginTop === image.style.marginBottom) {\n            return removePixelSuffix(image.style.marginTop);\n        }\n        else {\n            return '';\n        }\n    };\n    const getBorder = (image) => {\n        if (image.style.borderWidth) {\n            return removePixelSuffix(image.style.borderWidth);\n        }\n        else {\n            return '';\n        }\n    };\n    const getAttrib = (image, name) => {\n        var _a;\n        if (image.hasAttribute(name)) {\n            return (_a = image.getAttribute(name)) !== null && _a !== void 0 ? _a : '';\n        }\n        else {\n            return '';\n        }\n    };\n    const hasCaption = (image) => image.parentNode !== null && image.parentNode.nodeName === 'FIGURE';\n    const updateAttrib = (image, name, value) => {\n        if (value === '' || value === null) {\n            image.removeAttribute(name);\n        }\n        else {\n            image.setAttribute(name, value);\n        }\n    };\n    const wrapInFigure = (image) => {\n        const figureElm = DOM.create('figure', { class: 'image' });\n        DOM.insertAfter(figureElm, image);\n        figureElm.appendChild(image);\n        figureElm.appendChild(DOM.create('figcaption', { contentEditable: 'true' }, 'Caption'));\n        figureElm.contentEditable = 'false';\n    };\n    const removeFigure = (image) => {\n        const figureElm = image.parentNode;\n        if (isNonNullable(figureElm)) {\n            DOM.insertAfter(image, figureElm);\n            DOM.remove(figureElm);\n        }\n    };\n    const toggleCaption = (image) => {\n        if (hasCaption(image)) {\n            removeFigure(image);\n        }\n        else {\n            wrapInFigure(image);\n        }\n    };\n    const normalizeStyle = (image, normalizeCss) => {\n        const attrValue = image.getAttribute('style');\n        const value = normalizeCss(attrValue !== null ? attrValue : '');\n        if (value.length > 0) {\n            image.setAttribute('style', value);\n            image.setAttribute('data-mce-style', value);\n        }\n        else {\n            image.removeAttribute('style');\n        }\n    };\n    const setSize = (name, normalizeCss) => (image, name, value) => {\n        const styles = image.style;\n        if (styles[name]) {\n            styles[name] = addPixelSuffix(value);\n            normalizeStyle(image, normalizeCss);\n        }\n        else {\n            updateAttrib(image, name, value);\n        }\n    };\n    const getSize = (image, name) => {\n        if (image.style[name]) {\n            return removePixelSuffix(image.style[name]);\n        }\n        else {\n            return getAttrib(image, name);\n        }\n    };\n    const setHspace = (image, value) => {\n        const pxValue = addPixelSuffix(value);\n        image.style.marginLeft = pxValue;\n        image.style.marginRight = pxValue;\n    };\n    const setVspace = (image, value) => {\n        const pxValue = addPixelSuffix(value);\n        image.style.marginTop = pxValue;\n        image.style.marginBottom = pxValue;\n    };\n    const setBorder = (image, value) => {\n        const pxValue = addPixelSuffix(value);\n        image.style.borderWidth = pxValue;\n    };\n    const setBorderStyle = (image, value) => {\n        image.style.borderStyle = value;\n    };\n    const getBorderStyle = (image) => { var _a; return (_a = image.style.borderStyle) !== null && _a !== void 0 ? _a : ''; };\n    const isFigure = (elm) => isNonNullable(elm) && elm.nodeName === 'FIGURE';\n    const isImage = (elm) => elm.nodeName === 'IMG';\n    const getIsDecorative = (image) => DOM.getAttrib(image, 'alt').length === 0 && DOM.getAttrib(image, 'role') === 'presentation';\n    const getAlt = (image) => {\n        if (getIsDecorative(image)) {\n            return '';\n        }\n        else {\n            return getAttrib(image, 'alt');\n        }\n    };\n    const defaultData = () => ({\n        src: '',\n        alt: '',\n        title: '',\n        width: '',\n        height: '',\n        class: '',\n        style: '',\n        caption: false,\n        hspace: '',\n        vspace: '',\n        border: '',\n        borderStyle: '',\n        isDecorative: false\n    });\n    const getStyleValue = (normalizeCss, data) => {\n        var _a;\n        const image = document.createElement('img');\n        updateAttrib(image, 'style', data.style);\n        if (getHspace(image) || data.hspace !== '') {\n            setHspace(image, data.hspace);\n        }\n        if (getVspace(image) || data.vspace !== '') {\n            setVspace(image, data.vspace);\n        }\n        if (getBorder(image) || data.border !== '') {\n            setBorder(image, data.border);\n        }\n        if (getBorderStyle(image) || data.borderStyle !== '') {\n            setBorderStyle(image, data.borderStyle);\n        }\n        return normalizeCss((_a = image.getAttribute('style')) !== null && _a !== void 0 ? _a : '');\n    };\n    const create = (normalizeCss, data) => {\n        const image = document.createElement('img');\n        write(normalizeCss, { ...data, caption: false }, image);\n        // Always set alt even if data.alt is an empty string\n        setAlt(image, data.alt, data.isDecorative);\n        if (data.caption) {\n            const figure = DOM.create('figure', { class: 'image' });\n            figure.appendChild(image);\n            figure.appendChild(DOM.create('figcaption', { contentEditable: 'true' }, 'Caption'));\n            figure.contentEditable = 'false';\n            return figure;\n        }\n        else {\n            return image;\n        }\n    };\n    const read = (normalizeCss, image) => ({\n        src: getAttrib(image, 'src'),\n        alt: getAlt(image),\n        title: getAttrib(image, 'title'),\n        width: getSize(image, 'width'),\n        height: getSize(image, 'height'),\n        class: getAttrib(image, 'class'),\n        style: normalizeCss(getAttrib(image, 'style')),\n        caption: hasCaption(image),\n        hspace: getHspace(image),\n        vspace: getVspace(image),\n        border: getBorder(image),\n        borderStyle: getBorderStyle(image),\n        isDecorative: getIsDecorative(image)\n    });\n    const updateProp = (image, oldData, newData, name, set) => {\n        if (newData[name] !== oldData[name]) {\n            set(image, name, String(newData[name]));\n        }\n    };\n    const setAlt = (image, alt, isDecorative) => {\n        if (isDecorative) {\n            DOM.setAttrib(image, 'role', 'presentation');\n            // unfortunately can't set \"\" attr value with domutils\n            const sugarImage = SugarElement.fromDom(image);\n            set(sugarImage, 'alt', '');\n        }\n        else {\n            if (isNull(alt)) {\n                const sugarImage = SugarElement.fromDom(image);\n                remove(sugarImage, 'alt');\n            }\n            else {\n                // unfortunately can't set \"\" attr value with domutils\n                const sugarImage = SugarElement.fromDom(image);\n                set(sugarImage, 'alt', alt);\n            }\n            if (DOM.getAttrib(image, 'role') === 'presentation') {\n                DOM.setAttrib(image, 'role', '');\n            }\n        }\n    };\n    const updateAlt = (image, oldData, newData) => {\n        if (newData.alt !== oldData.alt || newData.isDecorative !== oldData.isDecorative) {\n            setAlt(image, newData.alt, newData.isDecorative);\n        }\n    };\n    const normalized = (set, normalizeCss) => (image, name, value) => {\n        set(image, value);\n        normalizeStyle(image, normalizeCss);\n    };\n    const write = (normalizeCss, newData, image) => {\n        const oldData = read(normalizeCss, image);\n        updateProp(image, oldData, newData, 'caption', (image, _name, _value) => toggleCaption(image));\n        updateProp(image, oldData, newData, 'src', updateAttrib);\n        updateProp(image, oldData, newData, 'title', updateAttrib);\n        updateProp(image, oldData, newData, 'width', setSize('width', normalizeCss));\n        updateProp(image, oldData, newData, 'height', setSize('height', normalizeCss));\n        updateProp(image, oldData, newData, 'class', updateAttrib);\n        updateProp(image, oldData, newData, 'style', normalized((image, value) => updateAttrib(image, 'style', value), normalizeCss));\n        updateProp(image, oldData, newData, 'hspace', normalized(setHspace, normalizeCss));\n        updateProp(image, oldData, newData, 'vspace', normalized(setVspace, normalizeCss));\n        updateProp(image, oldData, newData, 'border', normalized(setBorder, normalizeCss));\n        updateProp(image, oldData, newData, 'borderStyle', normalized(setBorderStyle, normalizeCss));\n        updateAlt(image, oldData, newData);\n    };\n\n    const normalizeCss$1 = (editor, cssText) => {\n        const css = editor.dom.styles.parse(cssText);\n        const mergedCss = mergeMargins(css);\n        const compressed = editor.dom.styles.parse(editor.dom.styles.serialize(mergedCss));\n        return editor.dom.styles.serialize(compressed);\n    };\n    const getSelectedImage = (editor) => {\n        const imgElm = editor.selection.getNode();\n        const figureElm = editor.dom.getParent(imgElm, 'figure.image');\n        if (figureElm) {\n            return editor.dom.select('img', figureElm)[0];\n        }\n        if (imgElm && (imgElm.nodeName !== 'IMG' || isPlaceholderImage(imgElm))) {\n            return null;\n        }\n        return imgElm;\n    };\n    const splitTextBlock = (editor, figure) => {\n        var _a;\n        const dom = editor.dom;\n        const textBlockElements = filter(editor.schema.getTextBlockElements(), (_, parentElm) => !editor.schema.isValidChild(parentElm, 'figure'));\n        const textBlock = dom.getParent(figure.parentNode, (node) => hasNonNullableKey(textBlockElements, node.nodeName), editor.getBody());\n        if (textBlock) {\n            return (_a = dom.split(textBlock, figure)) !== null && _a !== void 0 ? _a : figure;\n        }\n        else {\n            return figure;\n        }\n    };\n    const readImageDataFromSelection = (editor) => {\n        const image = getSelectedImage(editor);\n        return image ? read((css) => normalizeCss$1(editor, css), image) : defaultData();\n    };\n    const insertImageAtCaret = (editor, data) => {\n        const elm = create((css) => normalizeCss$1(editor, css), data);\n        editor.dom.setAttrib(elm, 'data-mce-id', '__mcenew');\n        editor.focus();\n        editor.selection.setContent(elm.outerHTML);\n        const insertedElm = editor.dom.select('*[data-mce-id=\"__mcenew\"]')[0];\n        editor.dom.setAttrib(insertedElm, 'data-mce-id', null);\n        if (isFigure(insertedElm)) {\n            const figure = splitTextBlock(editor, insertedElm);\n            editor.selection.select(figure);\n        }\n        else {\n            editor.selection.select(insertedElm);\n        }\n    };\n    const syncSrcAttr = (editor, image) => {\n        editor.dom.setAttrib(image, 'src', image.getAttribute('src'));\n    };\n    const deleteImage = (editor, image) => {\n        if (image) {\n            const elm = editor.dom.is(image.parentNode, 'figure.image') ? image.parentNode : image;\n            editor.dom.remove(elm);\n            editor.focus();\n            editor.nodeChanged();\n            if (editor.dom.isEmpty(editor.getBody())) {\n                editor.setContent('');\n                editor.selection.setCursorLocation();\n            }\n        }\n    };\n    const writeImageDataToSelection = (editor, data) => {\n        const image = getSelectedImage(editor);\n        if (image) {\n            write((css) => normalizeCss$1(editor, css), data, image);\n            syncSrcAttr(editor, image);\n            if (isFigure(image.parentNode)) {\n                editor.dom.setStyle(image, 'float', '');\n                const figure = image.parentNode;\n                splitTextBlock(editor, figure);\n                editor.selection.select(image.parentNode);\n            }\n            else {\n                editor.selection.select(image);\n                waitLoadImage(editor, data, image);\n            }\n        }\n    };\n    const sanitizeImageData = (editor, data) => {\n        // Sanitize the URL\n        const src = data.src;\n        return {\n            ...data,\n            src: isSafeImageUrl(editor, src) ? src : ''\n        };\n    };\n    const insertOrUpdateImage = (editor, partialData) => {\n        const image = getSelectedImage(editor);\n        if (image) {\n            const selectedImageData = read((css) => normalizeCss$1(editor, css), image);\n            const data = { ...selectedImageData, ...partialData };\n            const sanitizedData = sanitizeImageData(editor, data);\n            if (data.src) {\n                writeImageDataToSelection(editor, sanitizedData);\n            }\n            else {\n                deleteImage(editor, image);\n            }\n        }\n        else if (partialData.src) {\n            insertImageAtCaret(editor, { ...defaultData(), ...partialData });\n        }\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.ImageUploader');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getValue = (item) => isString(item.value) ? item.value : '';\n    const getText = (item) => {\n        if (isString(item.text)) {\n            return item.text;\n        }\n        else if (isString(item.title)) {\n            return item.title;\n        }\n        else {\n            return '';\n        }\n    };\n    const sanitizeList = (list, extractValue) => {\n        const out = [];\n        global.each(list, (item) => {\n            const text = getText(item);\n            if (item.menu !== undefined) {\n                const items = sanitizeList(item.menu, extractValue);\n                out.push({ text, items }); // list group\n            }\n            else {\n                const value = extractValue(item);\n                out.push({ text, value }); // list value\n            }\n        });\n        return out;\n    };\n    const sanitizer = (extractor = getValue) => (list) => {\n        if (list) {\n            return Optional.from(list).map((list) => sanitizeList(list, extractor));\n        }\n        else {\n            return Optional.none();\n        }\n    };\n    const sanitize = (list) => sanitizer(getValue)(list);\n    const isGroup = (item) => has(item, 'items');\n    const findEntryDelegate = (list, value) => findMap(list, (item) => {\n        if (isGroup(item)) {\n            return findEntryDelegate(item.items, value);\n        }\n        else if (item.value === value) {\n            return Optional.some(item);\n        }\n        else {\n            return Optional.none();\n        }\n    });\n    const findEntry = (optList, value) => optList.bind((list) => findEntryDelegate(list, value));\n    const ListUtils = {\n        sanitizer,\n        sanitize,\n        findEntry\n    };\n\n    const makeTab$2 = (_info) => ({\n        title: 'Advanced',\n        name: 'advanced',\n        items: [\n            {\n                type: 'grid',\n                columns: 2,\n                items: [\n                    {\n                        type: 'input',\n                        label: 'Vertical space',\n                        name: 'vspace',\n                        inputMode: 'numeric'\n                    },\n                    {\n                        type: 'input',\n                        label: 'Horizontal space',\n                        name: 'hspace',\n                        inputMode: 'numeric'\n                    },\n                    {\n                        type: 'input',\n                        label: 'Border width',\n                        name: 'border',\n                        inputMode: 'numeric'\n                    },\n                    {\n                        type: 'listbox',\n                        name: 'borderstyle',\n                        label: 'Border style',\n                        items: [\n                            { text: 'Select...', value: '' },\n                            { text: 'Solid', value: 'solid' },\n                            { text: 'Dotted', value: 'dotted' },\n                            { text: 'Dashed', value: 'dashed' },\n                            { text: 'Double', value: 'double' },\n                            { text: 'Groove', value: 'groove' },\n                            { text: 'Ridge', value: 'ridge' },\n                            { text: 'Inset', value: 'inset' },\n                            { text: 'Outset', value: 'outset' },\n                            { text: 'None', value: 'none' },\n                            { text: 'Hidden', value: 'hidden' }\n                        ]\n                    }\n                ]\n            }\n        ]\n    });\n    const AdvTab = {\n        makeTab: makeTab$2\n    };\n\n    const collect = (editor) => {\n        const urlListSanitizer = ListUtils.sanitizer((item) => editor.convertURL(item.value || item.url || '', 'src'));\n        const futureImageList = new Promise((completer) => {\n            createImageList(editor, (imageList) => {\n                completer(urlListSanitizer(imageList).map((items) => flatten([\n                    [{ text: 'None', value: '' }],\n                    items\n                ])));\n            });\n        });\n        const classList = ListUtils.sanitize(getClassList(editor));\n        const hasAdvTab$1 = hasAdvTab(editor);\n        const hasUploadTab$1 = hasUploadTab(editor);\n        const hasUploadUrl$1 = hasUploadUrl(editor);\n        const hasUploadHandler$1 = hasUploadHandler(editor);\n        const image = readImageDataFromSelection(editor);\n        const hasDescription$1 = hasDescription(editor);\n        const hasImageTitle$1 = hasImageTitle(editor);\n        const hasDimensions$1 = hasDimensions(editor);\n        const hasImageCaption$1 = hasImageCaption(editor);\n        const hasAccessibilityOptions = showAccessibilityOptions(editor);\n        const automaticUploads = isAutomaticUploadsEnabled(editor);\n        const prependURL = Optional.some(getPrependUrl(editor)).filter((preUrl) => isString(preUrl) && preUrl.length > 0);\n        return futureImageList.then((imageList) => ({\n            image,\n            imageList,\n            classList,\n            hasAdvTab: hasAdvTab$1,\n            hasUploadTab: hasUploadTab$1,\n            hasUploadUrl: hasUploadUrl$1,\n            hasUploadHandler: hasUploadHandler$1,\n            hasDescription: hasDescription$1,\n            hasImageTitle: hasImageTitle$1,\n            hasDimensions: hasDimensions$1,\n            hasImageCaption: hasImageCaption$1,\n            prependURL,\n            hasAccessibilityOptions,\n            automaticUploads\n        }));\n    };\n\n    const makeItems = (info) => {\n        const imageUrl = {\n            name: 'src',\n            type: 'urlinput',\n            filetype: 'image',\n            label: 'Source',\n            picker_text: 'Browse files'\n        };\n        const imageList = info.imageList.map((items) => ({\n            name: 'images',\n            type: 'listbox',\n            label: 'Image list',\n            items\n        }));\n        const imageDescription = {\n            name: 'alt',\n            type: 'input',\n            label: 'Alternative description',\n            enabled: !(info.hasAccessibilityOptions && info.image.isDecorative)\n        };\n        const imageTitle = {\n            name: 'title',\n            type: 'input',\n            label: 'Image title'\n        };\n        const imageDimensions = {\n            name: 'dimensions',\n            type: 'sizeinput'\n        };\n        const isDecorative = {\n            type: 'label',\n            label: 'Accessibility',\n            items: [{\n                    name: 'isDecorative',\n                    type: 'checkbox',\n                    label: 'Image is decorative'\n                }]\n        };\n        // TODO: the original listbox supported styled items but bridge does not seem to support this\n        const classList = info.classList.map((items) => ({\n            name: 'classes',\n            type: 'listbox',\n            label: 'Class',\n            items\n        }));\n        const caption = {\n            type: 'label',\n            label: 'Caption',\n            items: [\n                {\n                    type: 'checkbox',\n                    name: 'caption',\n                    label: 'Show caption'\n                }\n            ]\n        };\n        const getDialogContainerType = (useColumns) => useColumns ? { type: 'grid', columns: 2 } : { type: 'panel' };\n        return flatten([\n            [imageUrl],\n            imageList.toArray(),\n            info.hasAccessibilityOptions && info.hasDescription ? [isDecorative] : [],\n            info.hasDescription ? [imageDescription] : [],\n            info.hasImageTitle ? [imageTitle] : [],\n            info.hasDimensions ? [imageDimensions] : [],\n            [{\n                    ...getDialogContainerType(info.classList.isSome() && info.hasImageCaption),\n                    items: flatten([\n                        classList.toArray(),\n                        info.hasImageCaption ? [caption] : []\n                    ])\n                }]\n        ]);\n    };\n    const makeTab$1 = (info) => ({\n        title: 'General',\n        name: 'general',\n        items: makeItems(info)\n    });\n    const MainTab = {\n        makeTab: makeTab$1,\n        makeItems\n    };\n\n    const makeTab = (_info) => {\n        const items = [\n            {\n                type: 'dropzone',\n                name: 'fileinput'\n            }\n        ];\n        return {\n            title: 'Upload',\n            name: 'upload',\n            items\n        };\n    };\n    const UploadTab = {\n        makeTab\n    };\n\n    const createState = (info) => ({\n        prevImage: ListUtils.findEntry(info.imageList, info.image.src),\n        prevAlt: info.image.alt,\n        open: true\n    });\n    const fromImageData = (image) => ({\n        src: {\n            value: image.src,\n            meta: {}\n        },\n        images: image.src,\n        alt: image.alt,\n        title: image.title,\n        dimensions: {\n            width: image.width,\n            height: image.height\n        },\n        classes: image.class,\n        caption: image.caption,\n        style: image.style,\n        vspace: image.vspace,\n        border: image.border,\n        hspace: image.hspace,\n        borderstyle: image.borderStyle,\n        fileinput: [],\n        isDecorative: image.isDecorative\n    });\n    const toImageData = (data, removeEmptyAlt) => ({\n        src: data.src.value,\n        alt: (data.alt === null || data.alt.length === 0) && removeEmptyAlt ? null : data.alt,\n        title: data.title,\n        width: data.dimensions.width,\n        height: data.dimensions.height,\n        class: data.classes,\n        style: data.style,\n        caption: data.caption,\n        hspace: data.hspace,\n        vspace: data.vspace,\n        border: data.border,\n        borderStyle: data.borderstyle,\n        isDecorative: data.isDecorative\n    });\n    const addPrependUrl2 = (info, srcURL) => {\n        // Add the prependURL\n        if (!/^(?:[a-zA-Z]+:)?\\/\\//.test(srcURL)) {\n            return info.prependURL.bind((prependUrl) => {\n                if (srcURL.substring(0, prependUrl.length) !== prependUrl) {\n                    return Optional.some(prependUrl + srcURL);\n                }\n                return Optional.none();\n            });\n        }\n        return Optional.none();\n    };\n    const addPrependUrl = (info, api) => {\n        const data = api.getData();\n        addPrependUrl2(info, data.src.value).each((srcURL) => {\n            api.setData({ src: { value: srcURL, meta: data.src.meta } });\n        });\n    };\n    const formFillFromMeta2 = (info, data, meta) => {\n        if (info.hasDescription && isString(meta.alt)) {\n            data.alt = meta.alt;\n        }\n        if (info.hasAccessibilityOptions) {\n            data.isDecorative = meta.isDecorative || data.isDecorative || false;\n        }\n        if (info.hasImageTitle && isString(meta.title)) {\n            data.title = meta.title;\n        }\n        if (info.hasDimensions) {\n            if (isString(meta.width)) {\n                data.dimensions.width = meta.width;\n            }\n            if (isString(meta.height)) {\n                data.dimensions.height = meta.height;\n            }\n        }\n        if (isString(meta.class)) {\n            ListUtils.findEntry(info.classList, meta.class).each((entry) => {\n                data.classes = entry.value;\n            });\n        }\n        if (info.hasImageCaption) {\n            if (isBoolean(meta.caption)) {\n                data.caption = meta.caption;\n            }\n        }\n        if (info.hasAdvTab) {\n            if (isString(meta.style)) {\n                data.style = meta.style;\n            }\n            if (isString(meta.vspace)) {\n                data.vspace = meta.vspace;\n            }\n            if (isString(meta.border)) {\n                data.border = meta.border;\n            }\n            if (isString(meta.hspace)) {\n                data.hspace = meta.hspace;\n            }\n            if (isString(meta.borderstyle)) {\n                data.borderstyle = meta.borderstyle;\n            }\n        }\n    };\n    const formFillFromMeta = (info, api) => {\n        const data = api.getData();\n        const meta = data.src.meta;\n        if (meta !== undefined) {\n            const newData = deepMerge({}, data);\n            formFillFromMeta2(info, newData, meta);\n            api.setData(newData);\n        }\n    };\n    const calculateImageSize = (helpers, info, state, api) => {\n        const data = api.getData();\n        const url = data.src.value;\n        const meta = data.src.meta || {};\n        if (!meta.width && !meta.height && info.hasDimensions) {\n            if (isNotEmpty(url)) {\n                helpers.imageSize(url)\n                    .then((size) => {\n                    if (state.open) {\n                        api.setData({ dimensions: size });\n                    }\n                })\n                    // eslint-disable-next-line no-console\n                    .catch((e) => console.error(e));\n            }\n            else {\n                api.setData({ dimensions: { width: '', height: '' } });\n            }\n        }\n    };\n    const updateImagesDropdown = (info, state, api) => {\n        const data = api.getData();\n        const image = ListUtils.findEntry(info.imageList, data.src.value);\n        state.prevImage = image;\n        api.setData({ images: image.map((entry) => entry.value).getOr('') });\n    };\n    const changeSrc = (helpers, info, state, api) => {\n        addPrependUrl(info, api);\n        formFillFromMeta(info, api);\n        calculateImageSize(helpers, info, state, api);\n        updateImagesDropdown(info, state, api);\n    };\n    const changeImages = (helpers, info, state, api) => {\n        const data = api.getData();\n        const image = ListUtils.findEntry(info.imageList, data.images);\n        image.each((img) => {\n            const updateAlt = data.alt === '' || state.prevImage.map((image) => image.text === data.alt).getOr(false);\n            if (updateAlt) {\n                if (img.value === '') {\n                    api.setData({ src: img, alt: state.prevAlt });\n                }\n                else {\n                    api.setData({ src: img, alt: img.text });\n                }\n            }\n            else {\n                api.setData({ src: img });\n            }\n        });\n        state.prevImage = image;\n        changeSrc(helpers, info, state, api);\n    };\n    const changeFileInput = (helpers, info, state, api) => {\n        const data = api.getData();\n        api.block('Uploading image'); // What msg do we pass to the lock?\n        head(data.fileinput)\n            .fold(() => {\n            api.unblock();\n        }, (file) => {\n            const blobUri = URL.createObjectURL(file);\n            const finalize = () => {\n                api.unblock();\n                URL.revokeObjectURL(blobUri);\n            };\n            const updateSrcAndSwitchTab = (url) => {\n                api.setData({ src: { value: url, meta: {} } });\n                api.showTab('general');\n                changeSrc(helpers, info, state, api);\n                api.focus('src');\n            };\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            blobToDataUri(file).then((dataUrl) => {\n                const blobInfo = helpers.createBlobCache(file, blobUri, dataUrl);\n                if (info.automaticUploads) {\n                    helpers.uploadImage(blobInfo).then((result) => {\n                        updateSrcAndSwitchTab(result.url);\n                        finalize();\n                    }).catch((err) => {\n                        finalize();\n                        helpers.alertErr(err, () => {\n                            api.focus('fileinput');\n                        });\n                    });\n                }\n                else {\n                    helpers.addToBlobCache(blobInfo);\n                    updateSrcAndSwitchTab(blobInfo.blobUri());\n                    api.unblock();\n                }\n            });\n        });\n    };\n    const changeHandler = (helpers, info, state) => (api, evt) => {\n        if (evt.name === 'src') {\n            changeSrc(helpers, info, state, api);\n        }\n        else if (evt.name === 'images') {\n            changeImages(helpers, info, state, api);\n        }\n        else if (evt.name === 'alt') {\n            state.prevAlt = api.getData().alt;\n        }\n        else if (evt.name === 'fileinput') {\n            changeFileInput(helpers, info, state, api);\n        }\n        else if (evt.name === 'isDecorative') {\n            api.setEnabled('alt', !api.getData().isDecorative);\n        }\n    };\n    const closeHandler = (state) => () => {\n        state.open = false;\n    };\n    const makeDialogBody = (info) => {\n        if (info.hasAdvTab || info.hasUploadUrl || info.hasUploadHandler) {\n            const tabPanel = {\n                type: 'tabpanel',\n                tabs: flatten([\n                    [MainTab.makeTab(info)],\n                    info.hasAdvTab ? [AdvTab.makeTab(info)] : [],\n                    info.hasUploadTab && (info.hasUploadUrl || info.hasUploadHandler) ? [UploadTab.makeTab(info)] : []\n                ])\n            };\n            return tabPanel;\n        }\n        else {\n            const panel = {\n                type: 'panel',\n                items: MainTab.makeItems(info)\n            };\n            return panel;\n        }\n    };\n    const submitHandler = (editor, info, helpers) => (api) => {\n        const data = deepMerge(fromImageData(info.image), api.getData());\n        // The data architecture relies on passing everything through the style field for validation.\n        // Since the style field was removed that process must be simulated on submit.\n        const finalData = {\n            ...data,\n            style: getStyleValue(helpers.normalizeCss, toImageData(data, false))\n        };\n        editor.execCommand('mceUpdateImage', false, toImageData(finalData, info.hasAccessibilityOptions));\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        editor.editorUpload.uploadImagesAuto();\n        api.close();\n    };\n    const imageSize = (editor) => (url) => {\n        // If the URL isn't safe then don't attempt to load it to get the sizes\n        if (!isSafeImageUrl(editor, url)) {\n            return Promise.resolve({ width: '', height: '' });\n        }\n        else {\n            return getImageSize(editor.documentBaseURI.toAbsolute(url)).then((dimensions) => ({\n                width: String(dimensions.width),\n                height: String(dimensions.height)\n            }));\n        }\n    };\n    const createBlobCache = (editor) => (file, blobUri, dataUrl) => {\n        var _a;\n        return editor.editorUpload.blobCache.create({\n            blob: file,\n            blobUri,\n            name: (_a = file.name) === null || _a === void 0 ? void 0 : _a.replace(/\\.[^\\.]+$/, ''),\n            filename: file.name,\n            base64: dataUrl.split(',')[1]\n        });\n    };\n    const addToBlobCache = (editor) => (blobInfo) => {\n        editor.editorUpload.blobCache.add(blobInfo);\n    };\n    const alertErr = (editor) => (message, callback) => {\n        editor.windowManager.alert(message, callback);\n    };\n    const normalizeCss = (editor) => (cssText) => normalizeCss$1(editor, cssText);\n    const parseStyle = (editor) => (cssText) => editor.dom.parseStyle(cssText);\n    const serializeStyle = (editor) => (stylesArg, name) => editor.dom.serializeStyle(stylesArg, name);\n    const uploadImage = (editor) => (blobInfo) => global$1(editor).upload([blobInfo], false).then((results) => {\n        var _a;\n        if (results.length === 0) {\n            return Promise.reject('Failed to upload image');\n        }\n        else if (results[0].status === false) {\n            return Promise.reject((_a = results[0].error) === null || _a === void 0 ? void 0 : _a.message);\n        }\n        else {\n            return results[0];\n        }\n    });\n    const Dialog = (editor) => {\n        const helpers = {\n            imageSize: imageSize(editor),\n            addToBlobCache: addToBlobCache(editor),\n            createBlobCache: createBlobCache(editor),\n            alertErr: alertErr(editor),\n            normalizeCss: normalizeCss(editor),\n            parseStyle: parseStyle(editor),\n            serializeStyle: serializeStyle(editor),\n            uploadImage: uploadImage(editor)\n        };\n        const open = () => {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            collect(editor)\n                .then((info) => {\n                const state = createState(info);\n                return {\n                    title: 'Insert/Edit Image',\n                    size: 'normal',\n                    body: makeDialogBody(info),\n                    buttons: [\n                        {\n                            type: 'cancel',\n                            name: 'cancel',\n                            text: 'Cancel'\n                        },\n                        {\n                            type: 'submit',\n                            name: 'save',\n                            text: 'Save',\n                            primary: true\n                        }\n                    ],\n                    initialData: fromImageData(info.image),\n                    onSubmit: submitHandler(editor, info, helpers),\n                    onChange: changeHandler(helpers, info, state),\n                    onClose: closeHandler(state)\n                };\n            })\n                .then(editor.windowManager.open);\n        };\n        return {\n            open\n        };\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('mceImage', Dialog(editor).open);\n        // TODO: This command is likely to be short lived we only need it until we expose the rtc model though a new api so it shouldn't be documented\n        // it's just a command since that is a convenient method for the rtc plugin to override the default dom mutation behaviour\n        editor.addCommand('mceUpdateImage', (_ui, data) => {\n            editor.undoManager.transact(() => insertOrUpdateImage(editor, data));\n        });\n    };\n\n    const hasImageClass = (node) => {\n        const className = node.attr('class');\n        return isNonNullable(className) && /\\bimage\\b/.test(className);\n    };\n    const toggleContentEditableState = (state) => (nodes) => {\n        let i = nodes.length;\n        const toggleContentEditable = (node) => {\n            node.attr('contenteditable', state ? 'true' : null);\n        };\n        while (i--) {\n            const node = nodes[i];\n            if (hasImageClass(node)) {\n                node.attr('contenteditable', state ? 'false' : null);\n                global.each(node.getAll('figcaption'), toggleContentEditable);\n            }\n        }\n    };\n    const setup = (editor) => {\n        editor.on('PreInit', () => {\n            editor.parser.addNodeFilter('figure', toggleContentEditableState(true));\n            editor.serializer.addNodeFilter('figure', toggleContentEditableState(false));\n        });\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        editor.ui.registry.addToggleButton('image', {\n            icon: 'image',\n            tooltip: 'Insert/edit image',\n            onAction: Dialog(editor).open,\n            onSetup: (buttonApi) => {\n                // Set the initial state and then bind to selection changes to update the state when the selection changes\n                buttonApi.setActive(isNonNullable(getSelectedImage(editor)));\n                const unbindSelectorChanged = editor.selection.selectorChangedWithUnbind('img:not([data-mce-object]):not([data-mce-placeholder]),figure.image', buttonApi.setActive).unbind;\n                const unbindEditable = onSetupEditable(editor)(buttonApi);\n                return () => {\n                    unbindSelectorChanged();\n                    unbindEditable();\n                };\n            }\n        });\n        editor.ui.registry.addMenuItem('image', {\n            icon: 'image',\n            text: 'Image...',\n            onAction: Dialog(editor).open,\n            onSetup: onSetupEditable(editor)\n        });\n        editor.ui.registry.addContextMenu('image', {\n            update: (element) => editor.selection.isEditable() && (isFigure(element) || (isImage(element) && !isPlaceholderImage(element))) ? ['image'] : []\n        });\n    };\n\n    var Plugin = () => {\n        global$4.add('image', (editor) => {\n            register$2(editor);\n            setup(editor);\n            register(editor);\n            register$1(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,KAAK,CAAC,OAAO,cAAgB,SAAS,UAAU,SAAS,OAAO,aAAa,CAAC,GAAG,QAAU,eAAe,OAAO;IACvH,MAAM,WAAW,OAAO;IACxB,MAAM,WAAW,OAAO;IACxB,MAAM,gBAAgB,CAAC,QAAU,GAAG,OAAO;IAC3C,MAAM,UAAU,OAAO;IACvB,MAAM,SAAS,GAAG;IAClB,MAAM,YAAY,aAAa;IAC/B,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAChC,MAAM,WAAW,aAAa;IAC9B,MAAM,YAAY,CAAC,OAAO;QACtB,IAAI,QAAQ,QAAQ;YAChB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;gBAC9C,IAAI,CAAE,KAAK,KAAK,CAAC,EAAE,GAAI;oBACnB,OAAO;gBACX;YACJ;YACA,OAAO;QACX;QACA,OAAO;IACX;IAEA,MAAM,OAAO,KAAQ;IAErB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,aAAa,MAAM,SAAS,CAAC,IAAI;IACvC,MAAM,UAAU,CAAC;QACb,mEAAmE;QACnE,kCAAkC;QAClC,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC3C,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG;gBACjB,MAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;YAC7E;YACA,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,MAAM,MAAM,CAAC,IAAI,IAAM,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI;IACrF,MAAM,OAAO,CAAC,KAAO,IAAI,IAAI;IAC7B,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAC9D,MAAM,UAAU,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,MAAM,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,SAAS,CAAC,IAAM,CAAC,GAAG;YACtB,CAAC,CAAC,EAAE,GAAG;QACX;IACA,MAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ;QACvC,KAAK,KAAK,CAAC,GAAG;YACV,CAAC,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,GAAG;QACvC;IACJ;IACA,MAAM,SAAS,CAAC,KAAK;QACjB,MAAM,IAAI,CAAC;QACX,eAAe,KAAK,MAAM,OAAO,IAAI;QACrC,OAAO;IACX;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IACnD,MAAM,oBAAoB,CAAC,KAAK,MAAQ,IAAI,KAAK,QAAQ,GAAG,CAAC,IAAI,KAAK,aAAa,GAAG,CAAC,IAAI,KAAK;IAEhG,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,cAAc,cAAc,QAAQ,cAAc;QACxD,OAAO,cAAc,UAAU,KAAK,MAAM;IAC9C;IACA,MAAM,YAAY,CAAC;QACf,OAAO;6CAAI;gBAAA;;YACP,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACtB,MAAM,IAAI,MAAO;YACrB;YACA,MAAM,MAAM,CAAC;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACrC,MAAM,YAAY,OAAO,CAAC,EAAE;gBAC5B,IAAK,MAAM,OAAO,UAAW;oBACzB,IAAI,IAAI,WAAW,MAAM;wBACrB,GAAG,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI;oBAC9C;gBACJ;YACJ;YACA,OAAO;QACX;IACJ;IACA,MAAM,YAAY,UAAU;IAE5B,MAAM,aAAa,CAAC,IAAM,EAAE,MAAM,GAAG;IAErC,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,IAAI,aAAa,CAAC;QAC9B,IAAI,SAAS,GAAG;QAChB,IAAI,CAAC,IAAI,aAAa,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,UAAU;YAChB,sCAAsC;YACtC,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,QAAQ,IAAI,UAAU,CAAC,EAAE;IACpC;IACA,MAAM,UAAU,CAAC,KAAK;QAClB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,aAAa,CAAC;QAC/B,OAAO,QAAQ;IACnB;IACA,MAAM,WAAW,CAAC,MAAM;QACpB,MAAM,MAAM,SAAS;QACrB,MAAM,OAAO,IAAI,cAAc,CAAC;QAChC,OAAO,QAAQ;IACnB;IACA,MAAM,UAAU,CAAC;QACb,8DAA8D;QAC9D,IAAI,SAAS,QAAQ,SAAS,WAAW;YACrC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;YACH,KAAK;QACT;IACJ;IACA,MAAM,YAAY,CAAC,QAAQ,GAAG,IAAM,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,CAAC;IACzF,yCAAyC;IACzC,MAAM,eAAe;QACjB;QACA;QACA;QACA;QACA;IACJ;IAEA,MAAM,SAAS,CAAC,KAAK,KAAK;QACtB;;;;SAIC,GACD,IAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;YACxD,IAAI,YAAY,CAAC,KAAK,QAAQ;QAClC,OACK;YACD,sCAAsC;YACtC,QAAQ,KAAK,CAAC,uCAAuC,KAAK,aAAa,OAAO,eAAe;YAC7F,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,MAAM,CAAC,SAAS,KAAK;QACvB,OAAO,QAAQ,GAAG,EAAE,KAAK;IAC7B;IACA,MAAM,SAAS,CAAC,SAAS;QACrB,QAAQ,GAAG,CAAC,eAAe,CAAC;IAChC;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS;QACb;QACA,eAAe,gBAAgB;YAC3B,WAAW;YACX,SAAS;QACb;QACA,eAAe,mBAAmB;YAC9B,WAAW;YACX,SAAS;QACb;QACA,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;QACA,eAAe,oBAAoB;YAC/B,WAAW;QACf;QACA,eAAe,qBAAqB;YAChC,WAAW;YACX,SAAS;QACb;QACA,eAAe,eAAe;YAC1B,WAAW;YACX,SAAS;QACb;QACA,eAAe,iBAAiB;YAC5B,WAAW;YACX,SAAS;QACb;QACA,eAAe,cAAc;YACzB,WAAW,CAAC;gBACR,MAAM,QAAQ,UAAU,SAAS,SAAS,UAAU,UAAU,OAAO,aAAa,WAAW;gBAC7F,OAAO,QAAQ;oBAAE;oBAAO;gBAAM,IAAI;oBAAE,OAAO;oBAAO,SAAS;gBAAmD;YAClH;YACA,SAAS;QACb;IACJ;IACA,MAAM,gBAAgB,OAAO;IAC7B,MAAM,YAAY,OAAO;IACzB,MAAM,eAAe,OAAO;IAC5B,MAAM,gBAAgB,OAAO;IAC7B,MAAM,eAAe,OAAO;IAC5B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,gBAAgB,OAAO;IAC7B,MAAM,kBAAkB,OAAO;IAC/B,MAAM,eAAe,OAAO;IAC5B,MAAM,2BAA2B,OAAO;IACxC,MAAM,4BAA4B,OAAO;IACzC,MAAM,eAAe,CAAC,SAAW,WAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IAC/D,MAAM,mBAAmB,CAAC,SAAW,cAAc,OAAO,OAAO,CAAC,GAAG,CAAC;IAEtE,sGAAsG;IACtG,MAAM,oBAAoB,CAAC,MAAM,OAAS,KAAK,GAAG,CAAC,SAAS,MAAM,KAAK,SAAS,MAAM;IACtF,MAAM,eAAe,CAAC,MAAQ,IAAI,QAAQ,CAAC;YACvC,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,MAAM,OAAO,CAAC;gBACV,IAAI,IAAI,UAAU,EAAE;oBAChB,IAAI,UAAU,CAAC,WAAW,CAAC;gBAC/B;gBACA,SAAS;YACb;YACA,IAAI,gBAAgB,CAAC,QAAQ;gBACzB,MAAM,QAAQ,kBAAkB,IAAI,KAAK,EAAE,IAAI,WAAW;gBAC1D,MAAM,SAAS,kBAAkB,IAAI,MAAM,EAAE,IAAI,YAAY;gBAC7D,MAAM,aAAa;oBAAE;oBAAO;gBAAO;gBACnC,KAAK,QAAQ,OAAO,CAAC;YACzB;YACA,IAAI,gBAAgB,CAAC,SAAS;gBAC1B,KAAK,QAAQ,MAAM,CAAC,AAAC,uCAA0C,OAAJ;YAC/D;YACA,MAAM,QAAQ,IAAI,KAAK;YACvB,MAAM,UAAU,GAAG;YACnB,MAAM,QAAQ,GAAG;YACjB,MAAM,MAAM,GAAG,MAAM,IAAI,GAAG;YAC5B,MAAM,KAAK,GAAG,MAAM,MAAM,GAAG;YAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,GAAG,GAAG;QACd;IACA,MAAM,oBAAoB,CAAC;QACvB,IAAI,OAAO;YACP,QAAQ,MAAM,OAAO,CAAC,OAAO;QACjC;QACA,OAAO;IACX;IACA,MAAM,iBAAiB,CAAC;QACpB,IAAI,MAAM,MAAM,GAAG,KAAK,WAAW,IAAI,CAAC,QAAQ;YAC5C,SAAS;QACb;QACA,OAAO;IACX;IACA,MAAM,eAAe,CAAC;QAClB,IAAI,IAAI,MAAM,EAAE;YACZ,MAAM,cAAc,OAAO,IAAI,MAAM,EAAE,KAAK,CAAC;YAC7C,OAAQ,YAAY,MAAM;gBACtB,KAAK;oBACD,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,IAAI,WAAW,CAAC,EAAE;oBACvD,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,IAAI,WAAW,CAAC,EAAE;oBAC3D,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,IAAI,WAAW,CAAC,EAAE;oBAC7D,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,IAAI,WAAW,CAAC,EAAE;oBACzD;gBACJ,KAAK;oBACD,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,IAAI,WAAW,CAAC,EAAE;oBACvD,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,IAAI,WAAW,CAAC,EAAE;oBAC3D,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,IAAI,WAAW,CAAC,EAAE;oBAC7D,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,IAAI,WAAW,CAAC,EAAE;oBACzD;gBACJ,KAAK;oBACD,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,IAAI,WAAW,CAAC,EAAE;oBACvD,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,IAAI,WAAW,CAAC,EAAE;oBAC3D,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,IAAI,WAAW,CAAC,EAAE;oBAC7D,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,IAAI,WAAW,CAAC,EAAE;oBACzD;gBACJ,KAAK;oBACD,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,IAAI,WAAW,CAAC,EAAE;oBACvD,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,IAAI,WAAW,CAAC,EAAE;oBAC3D,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,IAAI,WAAW,CAAC,EAAE;oBAC7D,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,IAAI,WAAW,CAAC,EAAE;YACjE;YACA,OAAO,IAAI,MAAM;QACrB;QACA,OAAO;IACX;IACA,0DAA0D;IAC1D,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,MAAM,YAAY,aAAa;QAC/B,IAAI,SAAS,YAAY;YACrB,mEAAmE;YACnE,MAAM,WACD,IAAI,CAAC,CAAC;gBACP,IAAI,IAAI,EAAE,EAAE;oBACR,mEAAmE;oBACnE,IAAI,IAAI,GAAG,IAAI,CAAC;gBACpB;YACJ;QACJ,OACK,IAAI,WAAW,YAAY;YAC5B,UAAU;QACd,OACK;YACD,SAAS;QACb;IACJ;IACA,MAAM,gBAAgB,CAAC,QAAQ,MAAM;QACjC,MAAM,cAAc;YAChB,OAAO,MAAM,GAAG,OAAO,OAAO,GAAG;YACjC,IAAI,OAAO,SAAS,EAAE;gBAClB,OAAO,SAAS,CAAC,MAAM,CAAC;gBACxB,OAAO,WAAW;YACtB;QACJ;QACA,OAAO,MAAM,GAAG;YACZ,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,cAAc,SAAS;gBACtD,OAAO,GAAG,CAAC,UAAU,CAAC,QAAQ;oBAC1B,OAAO,OAAO,OAAO,WAAW;oBAChC,QAAQ,OAAO,OAAO,YAAY;gBACtC;YACJ;YACA;QACJ;QACA,OAAO,OAAO,GAAG;IACrB;IACA,MAAM,gBAAgB,CAAC,OAAS,IAAI,QAAQ,CAAC,SAAS;YAClD,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG;gBACZ,QAAQ,OAAO,MAAM;YACzB;YACA,OAAO,OAAO,GAAG;gBACb,IAAI;gBACJ,OAAO,CAAC,KAAK,OAAO,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;YAC9E;YACA,OAAO,aAAa,CAAC;QACzB;IACA,MAAM,qBAAqB,CAAC,SAAW,OAAO,QAAQ,KAAK,SAAS,CAAC,OAAO,YAAY,CAAC,sBAAsB,OAAO,YAAY,CAAC,uBAAuB;IAC1J,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,MAAM,YAAY,OAAO,OAAO,CAAC,GAAG;QACpC,OAAO,SAAS,SAAS,CAAC,KAAK,OAAO;YAClC,sBAAsB,UAAU;YAChC,mBAAmB,UAAU;YAC7B,qBAAqB,UAAU;QACnC;IACJ;IAEA,MAAM,MAAM,SAAS,GAAG;IACxB,MAAM,YAAY,CAAC;QACf,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,MAAM,KAAK,CAAC,WAAW,IAAI,MAAM,KAAK,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,WAAW,EAAE;YACzG,OAAO,kBAAkB,MAAM,KAAK,CAAC,UAAU;QACnD,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAC;QACf,IAAI,MAAM,KAAK,CAAC,SAAS,IAAI,MAAM,KAAK,CAAC,YAAY,IAAI,MAAM,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,CAAC,YAAY,EAAE;YACzG,OAAO,kBAAkB,MAAM,KAAK,CAAC,SAAS;QAClD,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAC;QACf,IAAI,MAAM,KAAK,CAAC,WAAW,EAAE;YACzB,OAAO,kBAAkB,MAAM,KAAK,CAAC,WAAW;QACpD,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAC,OAAO;QACtB,IAAI;QACJ,IAAI,MAAM,YAAY,CAAC,OAAO;YAC1B,OAAO,CAAC,KAAK,MAAM,YAAY,CAAC,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC5E,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,aAAa,CAAC,QAAU,MAAM,UAAU,KAAK,QAAQ,MAAM,UAAU,CAAC,QAAQ,KAAK;IACzF,MAAM,eAAe,CAAC,OAAO,MAAM;QAC/B,IAAI,UAAU,MAAM,UAAU,MAAM;YAChC,MAAM,eAAe,CAAC;QAC1B,OACK;YACD,MAAM,YAAY,CAAC,MAAM;QAC7B;IACJ;IACA,MAAM,eAAe,CAAC;QAClB,MAAM,YAAY,IAAI,MAAM,CAAC,UAAU;YAAE,OAAO;QAAQ;QACxD,IAAI,WAAW,CAAC,WAAW;QAC3B,UAAU,WAAW,CAAC;QACtB,UAAU,WAAW,CAAC,IAAI,MAAM,CAAC,cAAc;YAAE,iBAAiB;QAAO,GAAG;QAC5E,UAAU,eAAe,GAAG;IAChC;IACA,MAAM,eAAe,CAAC;QAClB,MAAM,YAAY,MAAM,UAAU;QAClC,IAAI,cAAc,YAAY;YAC1B,IAAI,WAAW,CAAC,OAAO;YACvB,IAAI,MAAM,CAAC;QACf;IACJ;IACA,MAAM,gBAAgB,CAAC;QACnB,IAAI,WAAW,QAAQ;YACnB,aAAa;QACjB,OACK;YACD,aAAa;QACjB;IACJ;IACA,MAAM,iBAAiB,CAAC,OAAO;QAC3B,MAAM,YAAY,MAAM,YAAY,CAAC;QACrC,MAAM,QAAQ,aAAa,cAAc,OAAO,YAAY;QAC5D,IAAI,MAAM,MAAM,GAAG,GAAG;YAClB,MAAM,YAAY,CAAC,SAAS;YAC5B,MAAM,YAAY,CAAC,kBAAkB;QACzC,OACK;YACD,MAAM,eAAe,CAAC;QAC1B;IACJ;IACA,MAAM,UAAU,CAAC,MAAM,eAAiB,CAAC,OAAO,MAAM;YAClD,MAAM,SAAS,MAAM,KAAK;YAC1B,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,MAAM,CAAC,KAAK,GAAG,eAAe;gBAC9B,eAAe,OAAO;YAC1B,OACK;gBACD,aAAa,OAAO,MAAM;YAC9B;QACJ;IACA,MAAM,UAAU,CAAC,OAAO;QACpB,IAAI,MAAM,KAAK,CAAC,KAAK,EAAE;YACnB,OAAO,kBAAkB,MAAM,KAAK,CAAC,KAAK;QAC9C,OACK;YACD,OAAO,UAAU,OAAO;QAC5B;IACJ;IACA,MAAM,YAAY,CAAC,OAAO;QACtB,MAAM,UAAU,eAAe;QAC/B,MAAM,KAAK,CAAC,UAAU,GAAG;QACzB,MAAM,KAAK,CAAC,WAAW,GAAG;IAC9B;IACA,MAAM,YAAY,CAAC,OAAO;QACtB,MAAM,UAAU,eAAe;QAC/B,MAAM,KAAK,CAAC,SAAS,GAAG;QACxB,MAAM,KAAK,CAAC,YAAY,GAAG;IAC/B;IACA,MAAM,YAAY,CAAC,OAAO;QACtB,MAAM,UAAU,eAAe;QAC/B,MAAM,KAAK,CAAC,WAAW,GAAG;IAC9B;IACA,MAAM,iBAAiB,CAAC,OAAO;QAC3B,MAAM,KAAK,CAAC,WAAW,GAAG;IAC9B;IACA,MAAM,iBAAiB,CAAC;QAAY,IAAI;QAAI,OAAO,CAAC,KAAK,MAAM,KAAK,CAAC,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAAI;IACvH,MAAM,WAAW,CAAC,MAAQ,cAAc,QAAQ,IAAI,QAAQ,KAAK;IACjE,MAAM,UAAU,CAAC,MAAQ,IAAI,QAAQ,KAAK;IAC1C,MAAM,kBAAkB,CAAC,QAAU,IAAI,SAAS,CAAC,OAAO,OAAO,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC,OAAO,YAAY;IAChH,MAAM,SAAS,CAAC;QACZ,IAAI,gBAAgB,QAAQ;YACxB,OAAO;QACX,OACK;YACD,OAAO,UAAU,OAAO;QAC5B;IACJ;IACA,MAAM,cAAc,IAAM,CAAC;YACvB,KAAK;YACL,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,OAAO;YACP,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,cAAc;QAClB,CAAC;IACD,MAAM,gBAAgB,CAAC,cAAc;QACjC,IAAI;QACJ,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,aAAa,OAAO,SAAS,KAAK,KAAK;QACvC,IAAI,UAAU,UAAU,KAAK,MAAM,KAAK,IAAI;YACxC,UAAU,OAAO,KAAK,MAAM;QAChC;QACA,IAAI,UAAU,UAAU,KAAK,MAAM,KAAK,IAAI;YACxC,UAAU,OAAO,KAAK,MAAM;QAChC;QACA,IAAI,UAAU,UAAU,KAAK,MAAM,KAAK,IAAI;YACxC,UAAU,OAAO,KAAK,MAAM;QAChC;QACA,IAAI,eAAe,UAAU,KAAK,WAAW,KAAK,IAAI;YAClD,eAAe,OAAO,KAAK,WAAW;QAC1C;QACA,OAAO,aAAa,CAAC,KAAK,MAAM,YAAY,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC5F;IACA,MAAM,SAAS,CAAC,cAAc;QAC1B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,cAAc;YAAE,GAAG,IAAI;YAAE,SAAS;QAAM,GAAG;QACjD,qDAAqD;QACrD,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK,YAAY;QACzC,IAAI,KAAK,OAAO,EAAE;YACd,MAAM,SAAS,IAAI,MAAM,CAAC,UAAU;gBAAE,OAAO;YAAQ;YACrD,OAAO,WAAW,CAAC;YACnB,OAAO,WAAW,CAAC,IAAI,MAAM,CAAC,cAAc;gBAAE,iBAAiB;YAAO,GAAG;YACzE,OAAO,eAAe,GAAG;YACzB,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,OAAO,CAAC,cAAc,QAAU,CAAC;YACnC,KAAK,UAAU,OAAO;YACtB,KAAK,OAAO;YACZ,OAAO,UAAU,OAAO;YACxB,OAAO,QAAQ,OAAO;YACtB,QAAQ,QAAQ,OAAO;YACvB,OAAO,UAAU,OAAO;YACxB,OAAO,aAAa,UAAU,OAAO;YACrC,SAAS,WAAW;YACpB,QAAQ,UAAU;YAClB,QAAQ,UAAU;YAClB,QAAQ,UAAU;YAClB,aAAa,eAAe;YAC5B,cAAc,gBAAgB;QAClC,CAAC;IACD,MAAM,aAAa,CAAC,OAAO,SAAS,SAAS,MAAM;QAC/C,IAAI,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;YACjC,IAAI,OAAO,MAAM,OAAO,OAAO,CAAC,KAAK;QACzC;IACJ;IACA,MAAM,SAAS,CAAC,OAAO,KAAK;QACxB,IAAI,cAAc;YACd,IAAI,SAAS,CAAC,OAAO,QAAQ;YAC7B,sDAAsD;YACtD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY,OAAO;QAC3B,OACK;YACD,IAAI,OAAO,MAAM;gBACb,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,OAAO,YAAY;YACvB,OACK;gBACD,sDAAsD;gBACtD,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY,OAAO;YAC3B;YACA,IAAI,IAAI,SAAS,CAAC,OAAO,YAAY,gBAAgB;gBACjD,IAAI,SAAS,CAAC,OAAO,QAAQ;YACjC;QACJ;IACJ;IACA,MAAM,YAAY,CAAC,OAAO,SAAS;QAC/B,IAAI,QAAQ,GAAG,KAAK,QAAQ,GAAG,IAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY,EAAE;YAC9E,OAAO,OAAO,QAAQ,GAAG,EAAE,QAAQ,YAAY;QACnD;IACJ;IACA,MAAM,aAAa,CAAC,KAAK,eAAiB,CAAC,OAAO,MAAM;YACpD,IAAI,OAAO;YACX,eAAe,OAAO;QAC1B;IACA,MAAM,QAAQ,CAAC,cAAc,SAAS;QAClC,MAAM,UAAU,KAAK,cAAc;QACnC,WAAW,OAAO,SAAS,SAAS,WAAW,CAAC,OAAO,OAAO,SAAW,cAAc;QACvF,WAAW,OAAO,SAAS,SAAS,OAAO;QAC3C,WAAW,OAAO,SAAS,SAAS,SAAS;QAC7C,WAAW,OAAO,SAAS,SAAS,SAAS,QAAQ,SAAS;QAC9D,WAAW,OAAO,SAAS,SAAS,UAAU,QAAQ,UAAU;QAChE,WAAW,OAAO,SAAS,SAAS,SAAS;QAC7C,WAAW,OAAO,SAAS,SAAS,SAAS,WAAW,CAAC,OAAO,QAAU,aAAa,OAAO,SAAS,QAAQ;QAC/G,WAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;QACpE,WAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;QACpE,WAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;QACpE,WAAW,OAAO,SAAS,SAAS,eAAe,WAAW,gBAAgB;QAC9E,UAAU,OAAO,SAAS;IAC9B;IAEA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,MAAM,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;QACpC,MAAM,YAAY,aAAa;QAC/B,MAAM,aAAa,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvE,OAAO,OAAO,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;IACvC;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,SAAS,OAAO,SAAS,CAAC,OAAO;QACvC,MAAM,YAAY,OAAO,GAAG,CAAC,SAAS,CAAC,QAAQ;QAC/C,IAAI,WAAW;YACX,OAAO,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,UAAU,CAAC,EAAE;QACjD;QACA,IAAI,UAAU,CAAC,OAAO,QAAQ,KAAK,SAAS,mBAAmB,OAAO,GAAG;YACrE,OAAO;QACX;QACA,OAAO;IACX;IACA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,IAAI;QACJ,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,oBAAoB,OAAO,OAAO,MAAM,CAAC,oBAAoB,IAAI,CAAC,GAAG,YAAc,CAAC,OAAO,MAAM,CAAC,YAAY,CAAC,WAAW;QAChI,MAAM,YAAY,IAAI,SAAS,CAAC,OAAO,UAAU,EAAE,CAAC,OAAS,kBAAkB,mBAAmB,KAAK,QAAQ,GAAG,OAAO,OAAO;QAChI,IAAI,WAAW;YACX,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAChF,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,6BAA6B,CAAC;QAChC,MAAM,QAAQ,iBAAiB;QAC/B,OAAO,QAAQ,KAAK,CAAC,MAAQ,eAAe,QAAQ,MAAM,SAAS;IACvE;IACA,MAAM,qBAAqB,CAAC,QAAQ;QAChC,MAAM,MAAM,OAAO,CAAC,MAAQ,eAAe,QAAQ,MAAM;QACzD,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,eAAe;QACzC,OAAO,KAAK;QACZ,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,SAAS;QACzC,MAAM,cAAc,OAAO,GAAG,CAAC,MAAM,CAAC,4BAA4B,CAAC,EAAE;QACrE,OAAO,GAAG,CAAC,SAAS,CAAC,aAAa,eAAe;QACjD,IAAI,SAAS,cAAc;YACvB,MAAM,SAAS,eAAe,QAAQ;YACtC,OAAO,SAAS,CAAC,MAAM,CAAC;QAC5B,OACK;YACD,OAAO,SAAS,CAAC,MAAM,CAAC;QAC5B;IACJ;IACA,MAAM,cAAc,CAAC,QAAQ;QACzB,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,OAAO,MAAM,YAAY,CAAC;IAC1D;IACA,MAAM,cAAc,CAAC,QAAQ;QACzB,IAAI,OAAO;YACP,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,MAAM,UAAU,EAAE,kBAAkB,MAAM,UAAU,GAAG;YACjF,OAAO,GAAG,CAAC,MAAM,CAAC;YAClB,OAAO,KAAK;YACZ,OAAO,WAAW;YAClB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,OAAO,KAAK;gBACtC,OAAO,UAAU,CAAC;gBAClB,OAAO,SAAS,CAAC,iBAAiB;YACtC;QACJ;IACJ;IACA,MAAM,4BAA4B,CAAC,QAAQ;QACvC,MAAM,QAAQ,iBAAiB;QAC/B,IAAI,OAAO;YACP,MAAM,CAAC,MAAQ,eAAe,QAAQ,MAAM,MAAM;YAClD,YAAY,QAAQ;YACpB,IAAI,SAAS,MAAM,UAAU,GAAG;gBAC5B,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO,SAAS;gBACpC,MAAM,SAAS,MAAM,UAAU;gBAC/B,eAAe,QAAQ;gBACvB,OAAO,SAAS,CAAC,MAAM,CAAC,MAAM,UAAU;YAC5C,OACK;gBACD,OAAO,SAAS,CAAC,MAAM,CAAC;gBACxB,cAAc,QAAQ,MAAM;YAChC;QACJ;IACJ;IACA,MAAM,oBAAoB,CAAC,QAAQ;QAC/B,mBAAmB;QACnB,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO;YACH,GAAG,IAAI;YACP,KAAK,eAAe,QAAQ,OAAO,MAAM;QAC7C;IACJ;IACA,MAAM,sBAAsB,CAAC,QAAQ;QACjC,MAAM,QAAQ,iBAAiB;QAC/B,IAAI,OAAO;YACP,MAAM,oBAAoB,KAAK,CAAC,MAAQ,eAAe,QAAQ,MAAM;YACrE,MAAM,OAAO;gBAAE,GAAG,iBAAiB;gBAAE,GAAG,WAAW;YAAC;YACpD,MAAM,gBAAgB,kBAAkB,QAAQ;YAChD,IAAI,KAAK,GAAG,EAAE;gBACV,0BAA0B,QAAQ;YACtC,OACK;gBACD,YAAY,QAAQ;YACxB;QACJ,OACK,IAAI,YAAY,GAAG,EAAE;YACtB,mBAAmB,QAAQ;gBAAE,GAAG,aAAa;gBAAE,GAAG,WAAW;YAAC;QAClE;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,WAAW,CAAC,OAAS,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;IAC/D,MAAM,UAAU,CAAC;QACb,IAAI,SAAS,KAAK,IAAI,GAAG;YACrB,OAAO,KAAK,IAAI;QACpB,OACK,IAAI,SAAS,KAAK,KAAK,GAAG;YAC3B,OAAO,KAAK,KAAK;QACrB,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC,MAAM;QACxB,MAAM,MAAM,EAAE;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;YACf,MAAM,OAAO,QAAQ;YACrB,IAAI,KAAK,IAAI,KAAK,WAAW;gBACzB,MAAM,QAAQ,aAAa,KAAK,IAAI,EAAE;gBACtC,IAAI,IAAI,CAAC;oBAAE;oBAAM;gBAAM,IAAI,aAAa;YAC5C,OACK;gBACD,MAAM,QAAQ,aAAa;gBAC3B,IAAI,IAAI,CAAC;oBAAE;oBAAM;gBAAM,IAAI,aAAa;YAC5C;QACJ;QACA,OAAO;IACX;IACA,MAAM,YAAY;YAAC,6EAAY;eAAa,CAAC;YACzC,IAAI,MAAM;gBACN,OAAO,SAAS,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,OAAS,aAAa,MAAM;YAChE,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;;IACA,MAAM,WAAW,CAAC,OAAS,UAAU,UAAU;IAC/C,MAAM,UAAU,CAAC,OAAS,IAAI,MAAM;IACpC,MAAM,oBAAoB,CAAC,MAAM,QAAU,QAAQ,MAAM,CAAC;YACtD,IAAI,QAAQ,OAAO;gBACf,OAAO,kBAAkB,KAAK,KAAK,EAAE;YACzC,OACK,IAAI,KAAK,KAAK,KAAK,OAAO;gBAC3B,OAAO,SAAS,IAAI,CAAC;YACzB,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;IACA,MAAM,YAAY,CAAC,SAAS,QAAU,QAAQ,IAAI,CAAC,CAAC,OAAS,kBAAkB,MAAM;IACrF,MAAM,YAAY;QACd;QACA;QACA;IACJ;IAEA,MAAM,YAAY,CAAC,QAAU,CAAC;YAC1B,OAAO;YACP,MAAM;YACN,OAAO;gBACH;oBACI,MAAM;oBACN,SAAS;oBACT,OAAO;wBACH;4BACI,MAAM;4BACN,OAAO;4BACP,MAAM;4BACN,WAAW;wBACf;wBACA;4BACI,MAAM;4BACN,OAAO;4BACP,MAAM;4BACN,WAAW;wBACf;wBACA;4BACI,MAAM;4BACN,OAAO;4BACP,MAAM;4BACN,WAAW;wBACf;wBACA;4BACI,MAAM;4BACN,MAAM;4BACN,OAAO;4BACP,OAAO;gCACH;oCAAE,MAAM;oCAAa,OAAO;gCAAG;gCAC/B;oCAAE,MAAM;oCAAS,OAAO;gCAAQ;gCAChC;oCAAE,MAAM;oCAAU,OAAO;gCAAS;gCAClC;oCAAE,MAAM;oCAAU,OAAO;gCAAS;gCAClC;oCAAE,MAAM;oCAAU,OAAO;gCAAS;gCAClC;oCAAE,MAAM;oCAAU,OAAO;gCAAS;gCAClC;oCAAE,MAAM;oCAAS,OAAO;gCAAQ;gCAChC;oCAAE,MAAM;oCAAS,OAAO;gCAAQ;gCAChC;oCAAE,MAAM;oCAAU,OAAO;gCAAS;gCAClC;oCAAE,MAAM;oCAAQ,OAAO;gCAAO;gCAC9B;oCAAE,MAAM;oCAAU,OAAO;gCAAS;6BACrC;wBACL;qBACH;gBACL;aACH;QACL,CAAC;IACD,MAAM,SAAS;QACX,SAAS;IACb;IAEA,MAAM,UAAU,CAAC;QACb,MAAM,mBAAmB,UAAU,SAAS,CAAC,CAAC,OAAS,OAAO,UAAU,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,IAAI;QACvG,MAAM,kBAAkB,IAAI,QAAQ,CAAC;YACjC,gBAAgB,QAAQ,CAAC;gBACrB,UAAU,iBAAiB,WAAW,GAAG,CAAC,CAAC,QAAU,QAAQ;wBACzD;4BAAC;gCAAE,MAAM;gCAAQ,OAAO;4BAAG;yBAAE;wBAC7B;qBACH;YACL;QACJ;QACA,MAAM,YAAY,UAAU,QAAQ,CAAC,aAAa;QAClD,MAAM,cAAc,UAAU;QAC9B,MAAM,iBAAiB,aAAa;QACpC,MAAM,iBAAiB,aAAa;QACpC,MAAM,qBAAqB,iBAAiB;QAC5C,MAAM,QAAQ,2BAA2B;QACzC,MAAM,mBAAmB,eAAe;QACxC,MAAM,kBAAkB,cAAc;QACtC,MAAM,kBAAkB,cAAc;QACtC,MAAM,oBAAoB,gBAAgB;QAC1C,MAAM,0BAA0B,yBAAyB;QACzD,MAAM,mBAAmB,0BAA0B;QACnD,MAAM,aAAa,SAAS,IAAI,CAAC,cAAc,SAAS,MAAM,CAAC,CAAC,SAAW,SAAS,WAAW,OAAO,MAAM,GAAG;QAC/G,OAAO,gBAAgB,IAAI,CAAC,CAAC,YAAc,CAAC;gBACxC;gBACA;gBACA;gBACA,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,kBAAkB;gBAClB,gBAAgB;gBAChB,eAAe;gBACf,eAAe;gBACf,iBAAiB;gBACjB;gBACA;gBACA;YACJ,CAAC;IACL;IAEA,MAAM,YAAY,CAAC;QACf,MAAM,WAAW;YACb,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;QACjB;QACA,MAAM,YAAY,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,QAAU,CAAC;gBAC7C,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP;YACJ,CAAC;QACD,MAAM,mBAAmB;YACrB,MAAM;YACN,MAAM;YACN,OAAO;YACP,SAAS,CAAC,CAAC,KAAK,uBAAuB,IAAI,KAAK,KAAK,CAAC,YAAY;QACtE;QACA,MAAM,aAAa;YACf,MAAM;YACN,MAAM;YACN,OAAO;QACX;QACA,MAAM,kBAAkB;YACpB,MAAM;YACN,MAAM;QACV;QACA,MAAM,eAAe;YACjB,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;oBACA,MAAM;oBACN,MAAM;oBACN,OAAO;gBACX;aAAE;QACV;QACA,6FAA6F;QAC7F,MAAM,YAAY,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,QAAU,CAAC;gBAC7C,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP;YACJ,CAAC;QACD,MAAM,UAAU;YACZ,MAAM;YACN,OAAO;YACP,OAAO;gBACH;oBACI,MAAM;oBACN,MAAM;oBACN,OAAO;gBACX;aACH;QACL;QACA,MAAM,yBAAyB,CAAC,aAAe,aAAa;gBAAE,MAAM;gBAAQ,SAAS;YAAE,IAAI;gBAAE,MAAM;YAAQ;QAC3G,OAAO,QAAQ;YACX;gBAAC;aAAS;YACV,UAAU,OAAO;YACjB,KAAK,uBAAuB,IAAI,KAAK,cAAc,GAAG;gBAAC;aAAa,GAAG,EAAE;YACzE,KAAK,cAAc,GAAG;gBAAC;aAAiB,GAAG,EAAE;YAC7C,KAAK,aAAa,GAAG;gBAAC;aAAW,GAAG,EAAE;YACtC,KAAK,aAAa,GAAG;gBAAC;aAAgB,GAAG,EAAE;YAC3C;gBAAC;oBACO,GAAG,uBAAuB,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,eAAe,CAAC;oBAC1E,OAAO,QAAQ;wBACX,UAAU,OAAO;wBACjB,KAAK,eAAe,GAAG;4BAAC;yBAAQ,GAAG,EAAE;qBACxC;gBACL;aAAE;SACT;IACL;IACA,MAAM,YAAY,CAAC,OAAS,CAAC;YACzB,OAAO;YACP,MAAM;YACN,OAAO,UAAU;QACrB,CAAC;IACD,MAAM,UAAU;QACZ,SAAS;QACT;IACJ;IAEA,MAAM,UAAU,CAAC;QACb,MAAM,QAAQ;YACV;gBACI,MAAM;gBACN,MAAM;YACV;SACH;QACD,OAAO;YACH,OAAO;YACP,MAAM;YACN;QACJ;IACJ;IACA,MAAM,YAAY;QACd;IACJ;IAEA,MAAM,cAAc,CAAC,OAAS,CAAC;YAC3B,WAAW,UAAU,SAAS,CAAC,KAAK,SAAS,EAAE,KAAK,KAAK,CAAC,GAAG;YAC7D,SAAS,KAAK,KAAK,CAAC,GAAG;YACvB,MAAM;QACV,CAAC;IACD,MAAM,gBAAgB,CAAC,QAAU,CAAC;YAC9B,KAAK;gBACD,OAAO,MAAM,GAAG;gBAChB,MAAM,CAAC;YACX;YACA,QAAQ,MAAM,GAAG;YACjB,KAAK,MAAM,GAAG;YACd,OAAO,MAAM,KAAK;YAClB,YAAY;gBACR,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM;YACxB;YACA,SAAS,MAAM,KAAK;YACpB,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,aAAa,MAAM,WAAW;YAC9B,WAAW,EAAE;YACb,cAAc,MAAM,YAAY;QACpC,CAAC;IACD,MAAM,cAAc,CAAC,MAAM,iBAAmB,CAAC;YAC3C,KAAK,KAAK,GAAG,CAAC,KAAK;YACnB,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,KAAK,iBAAiB,OAAO,KAAK,GAAG;YACrF,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,UAAU,CAAC,KAAK;YAC5B,QAAQ,KAAK,UAAU,CAAC,MAAM;YAC9B,OAAO,KAAK,OAAO;YACnB,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,OAAO;YACrB,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,aAAa,KAAK,WAAW;YAC7B,cAAc,KAAK,YAAY;QACnC,CAAC;IACD,MAAM,iBAAiB,CAAC,MAAM;QAC1B,qBAAqB;QACrB,IAAI,CAAC,uBAAuB,IAAI,CAAC,SAAS;YACtC,OAAO,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,OAAO,SAAS,CAAC,GAAG,WAAW,MAAM,MAAM,YAAY;oBACvD,OAAO,SAAS,IAAI,CAAC,aAAa;gBACtC;gBACA,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,gBAAgB,CAAC,MAAM;QACzB,MAAM,OAAO,IAAI,OAAO;QACxB,eAAe,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC;gBAAE,KAAK;oBAAE,OAAO;oBAAQ,MAAM,KAAK,GAAG,CAAC,IAAI;gBAAC;YAAE;QAC9D;IACJ;IACA,MAAM,oBAAoB,CAAC,MAAM,MAAM;QACnC,IAAI,KAAK,cAAc,IAAI,SAAS,KAAK,GAAG,GAAG;YAC3C,KAAK,GAAG,GAAG,KAAK,GAAG;QACvB;QACA,IAAI,KAAK,uBAAuB,EAAE;YAC9B,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAClE;QACA,IAAI,KAAK,aAAa,IAAI,SAAS,KAAK,KAAK,GAAG;YAC5C,KAAK,KAAK,GAAG,KAAK,KAAK;QAC3B;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,IAAI,SAAS,KAAK,KAAK,GAAG;gBACtB,KAAK,UAAU,CAAC,KAAK,GAAG,KAAK,KAAK;YACtC;YACA,IAAI,SAAS,KAAK,MAAM,GAAG;gBACvB,KAAK,UAAU,CAAC,MAAM,GAAG,KAAK,MAAM;YACxC;QACJ;QACA,IAAI,SAAS,KAAK,KAAK,GAAG;YACtB,UAAU,SAAS,CAAC,KAAK,SAAS,EAAE,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC;gBAClD,KAAK,OAAO,GAAG,MAAM,KAAK;YAC9B;QACJ;QACA,IAAI,KAAK,eAAe,EAAE;YACtB,IAAI,UAAU,KAAK,OAAO,GAAG;gBACzB,KAAK,OAAO,GAAG,KAAK,OAAO;YAC/B;QACJ;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,SAAS,KAAK,KAAK,GAAG;gBACtB,KAAK,KAAK,GAAG,KAAK,KAAK;YAC3B;YACA,IAAI,SAAS,KAAK,MAAM,GAAG;gBACvB,KAAK,MAAM,GAAG,KAAK,MAAM;YAC7B;YACA,IAAI,SAAS,KAAK,MAAM,GAAG;gBACvB,KAAK,MAAM,GAAG,KAAK,MAAM;YAC7B;YACA,IAAI,SAAS,KAAK,MAAM,GAAG;gBACvB,KAAK,MAAM,GAAG,KAAK,MAAM;YAC7B;YACA,IAAI,SAAS,KAAK,WAAW,GAAG;gBAC5B,KAAK,WAAW,GAAG,KAAK,WAAW;YACvC;QACJ;IACJ;IACA,MAAM,mBAAmB,CAAC,MAAM;QAC5B,MAAM,OAAO,IAAI,OAAO;QACxB,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI;QAC1B,IAAI,SAAS,WAAW;YACpB,MAAM,UAAU,UAAU,CAAC,GAAG;YAC9B,kBAAkB,MAAM,SAAS;YACjC,IAAI,OAAO,CAAC;QAChB;IACJ;IACA,MAAM,qBAAqB,CAAC,SAAS,MAAM,OAAO;QAC9C,MAAM,OAAO,IAAI,OAAO;QACxB,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK;QAC1B,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC;QAC/B,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,aAAa,EAAE;YACnD,IAAI,WAAW,MAAM;gBACjB,QAAQ,SAAS,CAAC,KACb,IAAI,CAAC,CAAC;oBACP,IAAI,MAAM,IAAI,EAAE;wBACZ,IAAI,OAAO,CAAC;4BAAE,YAAY;wBAAK;oBACnC;gBACJ,EACI,sCAAsC;iBACrC,KAAK,CAAC,CAAC,IAAM,QAAQ,KAAK,CAAC;YACpC,OACK;gBACD,IAAI,OAAO,CAAC;oBAAE,YAAY;wBAAE,OAAO;wBAAI,QAAQ;oBAAG;gBAAE;YACxD;QACJ;IACJ;IACA,MAAM,uBAAuB,CAAC,MAAM,OAAO;QACvC,MAAM,OAAO,IAAI,OAAO;QACxB,MAAM,QAAQ,UAAU,SAAS,CAAC,KAAK,SAAS,EAAE,KAAK,GAAG,CAAC,KAAK;QAChE,MAAM,SAAS,GAAG;QAClB,IAAI,OAAO,CAAC;YAAE,QAAQ,MAAM,GAAG,CAAC,CAAC,QAAU,MAAM,KAAK,EAAE,KAAK,CAAC;QAAI;IACtE;IACA,MAAM,YAAY,CAAC,SAAS,MAAM,OAAO;QACrC,cAAc,MAAM;QACpB,iBAAiB,MAAM;QACvB,mBAAmB,SAAS,MAAM,OAAO;QACzC,qBAAqB,MAAM,OAAO;IACtC;IACA,MAAM,eAAe,CAAC,SAAS,MAAM,OAAO;QACxC,MAAM,OAAO,IAAI,OAAO;QACxB,MAAM,QAAQ,UAAU,SAAS,CAAC,KAAK,SAAS,EAAE,KAAK,MAAM;QAC7D,MAAM,IAAI,CAAC,CAAC;YACR,MAAM,YAAY,KAAK,GAAG,KAAK,MAAM,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,KAAK,GAAG,EAAE,KAAK,CAAC;YACnG,IAAI,WAAW;gBACX,IAAI,IAAI,KAAK,KAAK,IAAI;oBAClB,IAAI,OAAO,CAAC;wBAAE,KAAK;wBAAK,KAAK,MAAM,OAAO;oBAAC;gBAC/C,OACK;oBACD,IAAI,OAAO,CAAC;wBAAE,KAAK;wBAAK,KAAK,IAAI,IAAI;oBAAC;gBAC1C;YACJ,OACK;gBACD,IAAI,OAAO,CAAC;oBAAE,KAAK;gBAAI;YAC3B;QACJ;QACA,MAAM,SAAS,GAAG;QAClB,UAAU,SAAS,MAAM,OAAO;IACpC;IACA,MAAM,kBAAkB,CAAC,SAAS,MAAM,OAAO;QAC3C,MAAM,OAAO,IAAI,OAAO;QACxB,IAAI,KAAK,CAAC,oBAAoB,mCAAmC;QACjE,KAAK,KAAK,SAAS,EACd,IAAI,CAAC;YACN,IAAI,OAAO;QACf,GAAG,CAAC;YACA,MAAM,UAAU,IAAI,eAAe,CAAC;YACpC,MAAM,WAAW;gBACb,IAAI,OAAO;gBACX,IAAI,eAAe,CAAC;YACxB;YACA,MAAM,wBAAwB,CAAC;gBAC3B,IAAI,OAAO,CAAC;oBAAE,KAAK;wBAAE,OAAO;wBAAK,MAAM,CAAC;oBAAE;gBAAE;gBAC5C,IAAI,OAAO,CAAC;gBACZ,UAAU,SAAS,MAAM,OAAO;gBAChC,IAAI,KAAK,CAAC;YACd;YACA,mEAAmE;YACnE,cAAc,MAAM,IAAI,CAAC,CAAC;gBACtB,MAAM,WAAW,QAAQ,eAAe,CAAC,MAAM,SAAS;gBACxD,IAAI,KAAK,gBAAgB,EAAE;oBACvB,QAAQ,WAAW,CAAC,UAAU,IAAI,CAAC,CAAC;wBAChC,sBAAsB,OAAO,GAAG;wBAChC;oBACJ,GAAG,KAAK,CAAC,CAAC;wBACN;wBACA,QAAQ,QAAQ,CAAC,KAAK;4BAClB,IAAI,KAAK,CAAC;wBACd;oBACJ;gBACJ,OACK;oBACD,QAAQ,cAAc,CAAC;oBACvB,sBAAsB,SAAS,OAAO;oBACtC,IAAI,OAAO;gBACf;YACJ;QACJ;IACJ;IACA,MAAM,gBAAgB,CAAC,SAAS,MAAM,QAAU,CAAC,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,OAAO;gBACpB,UAAU,SAAS,MAAM,OAAO;YACpC,OACK,IAAI,IAAI,IAAI,KAAK,UAAU;gBAC5B,aAAa,SAAS,MAAM,OAAO;YACvC,OACK,IAAI,IAAI,IAAI,KAAK,OAAO;gBACzB,MAAM,OAAO,GAAG,IAAI,OAAO,GAAG,GAAG;YACrC,OACK,IAAI,IAAI,IAAI,KAAK,aAAa;gBAC/B,gBAAgB,SAAS,MAAM,OAAO;YAC1C,OACK,IAAI,IAAI,IAAI,KAAK,gBAAgB;gBAClC,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,YAAY;YACrD;QACJ;IACA,MAAM,eAAe,CAAC,QAAU;YAC5B,MAAM,IAAI,GAAG;QACjB;IACA,MAAM,iBAAiB,CAAC;QACpB,IAAI,KAAK,SAAS,IAAI,KAAK,YAAY,IAAI,KAAK,gBAAgB,EAAE;YAC9D,MAAM,WAAW;gBACb,MAAM;gBACN,MAAM,QAAQ;oBACV;wBAAC,QAAQ,OAAO,CAAC;qBAAM;oBACvB,KAAK,SAAS,GAAG;wBAAC,OAAO,OAAO,CAAC;qBAAM,GAAG,EAAE;oBAC5C,KAAK,YAAY,IAAI,CAAC,KAAK,YAAY,IAAI,KAAK,gBAAgB,IAAI;wBAAC,UAAU,OAAO,CAAC;qBAAM,GAAG,EAAE;iBACrG;YACL;YACA,OAAO;QACX,OACK;YACD,MAAM,QAAQ;gBACV,MAAM;gBACN,OAAO,QAAQ,SAAS,CAAC;YAC7B;YACA,OAAO;QACX;IACJ;IACA,MAAM,gBAAgB,CAAC,QAAQ,MAAM,UAAY,CAAC;YAC9C,MAAM,OAAO,UAAU,cAAc,KAAK,KAAK,GAAG,IAAI,OAAO;YAC7D,6FAA6F;YAC7F,8EAA8E;YAC9E,MAAM,YAAY;gBACd,GAAG,IAAI;gBACP,OAAO,cAAc,QAAQ,YAAY,EAAE,YAAY,MAAM;YACjE;YACA,OAAO,WAAW,CAAC,kBAAkB,OAAO,YAAY,WAAW,KAAK,uBAAuB;YAC/F,mEAAmE;YACnE,OAAO,YAAY,CAAC,gBAAgB;YACpC,IAAI,KAAK;QACb;IACA,MAAM,YAAY,CAAC,SAAW,CAAC;YAC3B,uEAAuE;YACvE,IAAI,CAAC,eAAe,QAAQ,MAAM;gBAC9B,OAAO,QAAQ,OAAO,CAAC;oBAAE,OAAO;oBAAI,QAAQ;gBAAG;YACnD,OACK;gBACD,OAAO,aAAa,OAAO,eAAe,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,aAAe,CAAC;wBAC9E,OAAO,OAAO,WAAW,KAAK;wBAC9B,QAAQ,OAAO,WAAW,MAAM;oBACpC,CAAC;YACL;QACJ;IACA,MAAM,kBAAkB,CAAC,SAAW,CAAC,MAAM,SAAS;YAChD,IAAI;YACJ,OAAO,OAAO,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC;gBACxC,MAAM;gBACN;gBACA,MAAM,CAAC,KAAK,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,aAAa;gBACpF,UAAU,KAAK,IAAI;gBACnB,QAAQ,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;YACjC;QACJ;IACA,MAAM,iBAAiB,CAAC,SAAW,CAAC;YAChC,OAAO,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC;QACtC;IACA,MAAM,WAAW,CAAC,SAAW,CAAC,SAAS;YACnC,OAAO,aAAa,CAAC,KAAK,CAAC,SAAS;QACxC;IACA,MAAM,eAAe,CAAC,SAAW,CAAC,UAAY,eAAe,QAAQ;IACrE,MAAM,aAAa,CAAC,SAAW,CAAC,UAAY,OAAO,GAAG,CAAC,UAAU,CAAC;IAClE,MAAM,iBAAiB,CAAC,SAAW,CAAC,WAAW,OAAS,OAAO,GAAG,CAAC,cAAc,CAAC,WAAW;IAC7F,MAAM,cAAc,CAAC,SAAW,CAAC,WAAa,SAAS,QAAQ,MAAM,CAAC;gBAAC;aAAS,EAAE,OAAO,IAAI,CAAC,CAAC;gBAC3F,IAAI;gBACJ,IAAI,QAAQ,MAAM,KAAK,GAAG;oBACtB,OAAO,QAAQ,MAAM,CAAC;gBAC1B,OACK,IAAI,OAAO,CAAC,EAAE,CAAC,MAAM,KAAK,OAAO;oBAClC,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;gBACjG,OACK;oBACD,OAAO,OAAO,CAAC,EAAE;gBACrB;YACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,UAAU;YACZ,WAAW,UAAU;YACrB,gBAAgB,eAAe;YAC/B,iBAAiB,gBAAgB;YACjC,UAAU,SAAS;YACnB,cAAc,aAAa;YAC3B,YAAY,WAAW;YACvB,gBAAgB,eAAe;YAC/B,aAAa,YAAY;QAC7B;QACA,MAAM,OAAO;YACT,mEAAmE;YACnE,QAAQ,QACH,IAAI,CAAC,CAAC;gBACP,MAAM,QAAQ,YAAY;gBAC1B,OAAO;oBACH,OAAO;oBACP,MAAM;oBACN,MAAM,eAAe;oBACrB,SAAS;wBACL;4BACI,MAAM;4BACN,MAAM;4BACN,MAAM;wBACV;wBACA;4BACI,MAAM;4BACN,MAAM;4BACN,MAAM;4BACN,SAAS;wBACb;qBACH;oBACD,aAAa,cAAc,KAAK,KAAK;oBACrC,UAAU,cAAc,QAAQ,MAAM;oBACtC,UAAU,cAAc,SAAS,MAAM;oBACvC,SAAS,aAAa;gBAC1B;YACJ,GACK,IAAI,CAAC,OAAO,aAAa,CAAC,IAAI;QACvC;QACA,OAAO;YACH;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,YAAY,OAAO,QAAQ,IAAI;QACjD,8IAA8I;QAC9I,0HAA0H;QAC1H,OAAO,UAAU,CAAC,kBAAkB,CAAC,KAAK;YACtC,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAM,oBAAoB,QAAQ;QAClE;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,MAAM,YAAY,KAAK,IAAI,CAAC;QAC5B,OAAO,cAAc,cAAc,YAAY,IAAI,CAAC;IACxD;IACA,MAAM,6BAA6B,CAAC,QAAU,CAAC;YAC3C,IAAI,IAAI,MAAM,MAAM;YACpB,MAAM,wBAAwB,CAAC;gBAC3B,KAAK,IAAI,CAAC,mBAAmB,QAAQ,SAAS;YAClD;YACA,MAAO,IAAK;gBACR,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,cAAc,OAAO;oBACrB,KAAK,IAAI,CAAC,mBAAmB,QAAQ,UAAU;oBAC/C,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,eAAe;gBAC3C;YACJ;QACJ;IACA,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,WAAW;YACjB,OAAO,MAAM,CAAC,aAAa,CAAC,UAAU,2BAA2B;YACjE,OAAO,UAAU,CAAC,aAAa,CAAC,UAAU,2BAA2B;QACzE;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS;YACxC,MAAM;YACN,SAAS;YACT,UAAU,OAAO,QAAQ,IAAI;YAC7B,SAAS,CAAC;gBACN,0GAA0G;gBAC1G,UAAU,SAAS,CAAC,cAAc,iBAAiB;gBACnD,MAAM,wBAAwB,OAAO,SAAS,CAAC,yBAAyB,CAAC,uEAAuE,UAAU,SAAS,EAAE,MAAM;gBAC3K,MAAM,iBAAiB,gBAAgB,QAAQ;gBAC/C,OAAO;oBACH;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS;YACpC,MAAM;YACN,MAAM;YACN,UAAU,OAAO,QAAQ,IAAI;YAC7B,SAAS,gBAAgB;QAC7B;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS;YACvC,QAAQ,CAAC,UAAY,OAAO,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,YAAa,QAAQ,YAAY,CAAC,mBAAmB,QAAS,IAAI;oBAAC;iBAAQ,GAAG,EAAE;QACpJ;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,SAAS,CAAC;YACnB,WAAW;YACX,MAAM;YACN,SAAS;YACT,WAAW;QACf;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/image/index.js"], "sourcesContent": ["// Exports the \"image\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/image')\n//   ES2015:\n//     import 'tinymce/plugins/image'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,SAAS;AACT,cAAc;AACd,uCAAuC;AACvC,YAAY;AACZ,qCAAqC", "ignoreList": [0], "debugId": null}}]}