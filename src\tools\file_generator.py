from src.prompts.file_generator_prompt import get_prompt
from src.models.gemini_model import ask_ai
from typing import List
import re

# Fonction principale

def generate_newsletter_with_file(chunks: List[str], theme: str="actualités", description: str="actualités") -> str:
    context = "\n\n".join(chunks)
    prompt = get_prompt(context, theme, description)


    generated_html = ask_ai(prompt).strip()
    cleaned = re.sub(r"^```html\s*", "", generated_html)  # Enlève ```html au début
    cleaned = re.sub(r"\s*```$", "", cleaned)              # Enlève ``` à la fin

    return cleaned

    # gemini = get_ai_model()
    # response = gemini.generate_content(prompt)

    # cleaned = re.sub(r"^```html\s*", "", response)  # Enlève ```html au début
    # cleaned = re.sub(r"\s*```$", "", cleaned)              # Enlève ``` à la fin

    # return cleaned










# from src.prompts.file_generator_prompt import get_prompt
# from src.models.gemini_model import gemini__model
# from typing import List

# #  Fonction principale

# def generate_newsletter_with_file(chunks: List[str], theme: str , description : str) -> str:
#     context = "\n\n".join(chunks)
#     prompt = get_prompt(context, theme , description)

#     response = gemini__model.generate_content(prompt)
#     return response.text.strip()














