{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/wordcount/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const eq = (t) => (a) => t === a;\n    const isNull = eq(null);\n\n    const identity = (x) => {\n        return x;\n    };\n\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n\n    // Run a function fn after rate ms. If another invocation occurs\n    // during the time it is waiting, ignore it completely.\n    const first = (fn, rate) => {\n        let timer = null;\n        const cancel = () => {\n            if (!isNull(timer)) {\n                clearTimeout(timer);\n                timer = null;\n            }\n        };\n        const throttle = (...args) => {\n            if (isNull(timer)) {\n                timer = setTimeout(() => {\n                    timer = null;\n                    fn.apply(null, args);\n                }, rate);\n            }\n        };\n        return {\n            cancel,\n            throttle\n        };\n    };\n\n    const removeZwsp$1 = (s) => s.replace(/\\uFEFF/g, '');\n\n    /* eslint-disable max-len */\n    const punctuationStr = `[~№|!-*+-\\\\/:;?@\\\\[-\\`{}\\u00A1\\u00AB\\u00B7\\u00BB\\u00BF;\\u00B7\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1361-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u3008\\u3009\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30\\u2E31\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]`;\n    const regExps = {\n        aletter: '[A-Za-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F3\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097F\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C33\\u0C35-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u10A0-\\u10C5\\u10D0-\\u10FA\\u10FC\\u1100-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F0\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191C\\u1A00-\\u1A16\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BC0-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u24B6-\\u24E9\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2D00-\\u2D25\\u2D30-\\u2D65\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u303B\\u303C\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790\\uA791\\uA7A0-\\uA7A9\\uA7FA-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFFA0-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]',\n        midnumlet: `[-'\\\\.\\u2018\\u2019\\u2024\\uFE52\\uFF07\\uFF0E]`,\n        midletter: '[:\\u00B7\\u00B7\\u05F4\\u2027\\uFE13\\uFE55\\uFF1A]',\n        midnum: '[±+*/,;;\\u0589\\u060C\\u060D\\u066C\\u07F8\\u2044\\uFE10\\uFE14\\uFE50\\uFE54\\uFF0C\\uFF1B]',\n        numeric: '[0-9\\u0660-\\u0669\\u066B\\u06F0-\\u06F9\\u07C0-\\u07C9\\u0966-\\u096F\\u09E6-\\u09EF\\u0A66-\\u0A6F\\u0AE6-\\u0AEF\\u0B66-\\u0B6F\\u0BE6-\\u0BEF\\u0C66-\\u0C6F\\u0CE6-\\u0CEF\\u0D66-\\u0D6F\\u0E50-\\u0E59\\u0ED0-\\u0ED9\\u0F20-\\u0F29\\u1040-\\u1049\\u1090-\\u1099\\u17E0-\\u17E9\\u1810-\\u1819\\u1946-\\u194F\\u19D0-\\u19D9\\u1A80-\\u1A89\\u1A90-\\u1A99\\u1B50-\\u1B59\\u1BB0-\\u1BB9\\u1C40-\\u1C49\\u1C50-\\u1C59\\uA620-\\uA629\\uA8D0-\\uA8D9\\uA900-\\uA909\\uA9D0-\\uA9D9\\uAA50-\\uAA59\\uABF0-\\uABF9]',\n        cr: '\\\\r',\n        lf: '\\\\n',\n        newline: '[\\u000B\\u000C\\u0085\\u2028\\u2029]',\n        extend: '[\\u0300-\\u036F\\u0483-\\u0489\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u0610-\\u061A\\u064B-\\u065F\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED\\u0711\\u0730-\\u074A\\u07A6-\\u07B0\\u07EB-\\u07F3\\u0816-\\u0819\\u081B-\\u0823\\u0825-\\u0827\\u0829-\\u082D\\u0859-\\u085B\\u0900-\\u0903\\u093A-\\u093C\\u093E-\\u094F\\u0951-\\u0957\\u0962\\u0963\\u0981-\\u0983\\u09BC\\u09BE-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CD\\u09D7\\u09E2\\u09E3\\u0A01-\\u0A03\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A70\\u0A71\\u0A75\\u0A81-\\u0A83\\u0ABC\\u0ABE-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AE2\\u0AE3\\u0B01-\\u0B03\\u0B3C\\u0B3E-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B62\\u0B63\\u0B82\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD7\\u0C01-\\u0C03\\u0C3E-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C62\\u0C63\\u0C82\\u0C83\\u0CBC\\u0CBE-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CE2\\u0CE3\\u0D02\\u0D03\\u0D3E-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4D\\u0D57\\u0D62\\u0D63\\u0D82\\u0D83\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DF2\\u0DF3\\u0E31\\u0E34-\\u0E3A\\u0E47-\\u0E4E\\u0EB1\\u0EB4-\\u0EB9\\u0EBB\\u0EBC\\u0EC8-\\u0ECD\\u0F18\\u0F19\\u0F35\\u0F37\\u0F39\\u0F3E\\u0F3F\\u0F71-\\u0F84\\u0F86\\u0F87\\u0F8D-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u102B-\\u103E\\u1056-\\u1059\\u105E-\\u1060\\u1062-\\u1064\\u1067-\\u106D\\u1071-\\u1074\\u1082-\\u108D\\u108F\\u109A-\\u109D\\u135D-\\u135F\\u1712-\\u1714\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17B6-\\u17D3\\u17DD\\u180B-\\u180D\\u18A9\\u1920-\\u192B\\u1930-\\u193B\\u19B0-\\u19C0\\u19C8\\u19C9\\u1A17-\\u1A1B\\u1A55-\\u1A5E\\u1A60-\\u1A7C\\u1A7F\\u1B00-\\u1B04\\u1B34-\\u1B44\\u1B6B-\\u1B73\\u1B80-\\u1B82\\u1BA1-\\u1BAA\\u1BE6-\\u1BF3\\u1C24-\\u1C37\\u1CD0-\\u1CD2\\u1CD4-\\u1CE8\\u1CED\\u1CF2\\u1DC0-\\u1DE6\\u1DFC-\\u1DFF\\u200C\\u200D\\u20D0-\\u20F0\\u2CEF-\\u2CF1\\u2D7F\\u2DE0-\\u2DFF\\u302A-\\u302F\\u3099\\u309A\\uA66F-\\uA672\\uA67C\\uA67D\\uA6F0\\uA6F1\\uA802\\uA806\\uA80B\\uA823-\\uA827\\uA880\\uA881\\uA8B4-\\uA8C4\\uA8E0-\\uA8F1\\uA926-\\uA92D\\uA947-\\uA953\\uA980-\\uA983\\uA9B3-\\uA9C0\\uAA29-\\uAA36\\uAA43\\uAA4C\\uAA4D\\uAA7B\\uAAB0\\uAAB2-\\uAAB4\\uAAB7\\uAAB8\\uAABE\\uAABF\\uAAC1\\uABE3-\\uABEA\\uABEC\\uABED\\uFB1E\\uFE00-\\uFE0F\\uFE20-\\uFE26\\uFF9E\\uFF9F]',\n        format: '[\\u00AD\\u0600-\\u0603\\u06DD\\u070F\\u17B4\\u17B5\\u200E\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u206A-\\u206F\\uFEFF\\uFFF9-\\uFFFB]',\n        katakana: '[\\u3031-\\u3035\\u309B\\u309C\\u30A0-\\u30FA\\u30FC-\\u30FF\\u31F0-\\u31FF\\u32D0-\\u32FE\\u3300-\\u3357\\uFF66-\\uFF9D]',\n        extendnumlet: '[=_\\u203F\\u2040\\u2054\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFF3F\\u2200-\\u22FF\\u003c\\u003e]',\n        punctuation: punctuationStr\n    };\n    /* eslint-enable max-len */\n    const characterIndices = {\n        ALETTER: 0,\n        MIDNUMLET: 1,\n        MIDLETTER: 2,\n        MIDNUM: 3,\n        NUMERIC: 4,\n        CR: 5,\n        LF: 6,\n        NEWLINE: 7,\n        EXTEND: 8,\n        FORMAT: 9,\n        KATAKANA: 10,\n        EXTENDNUMLET: 11,\n        AT: 12,\n        OTHER: 13\n    };\n    // RegExp objects generated from code point data. Each regex matches a single\n    // character against a set of Unicode code points. The index of each item in\n    // this array must match its corresponding code point constant value defined\n    // above.\n    const SETS$1 = [\n        new RegExp(regExps.aletter),\n        new RegExp(regExps.midnumlet),\n        new RegExp(regExps.midletter),\n        new RegExp(regExps.midnum),\n        new RegExp(regExps.numeric),\n        new RegExp(regExps.cr),\n        new RegExp(regExps.lf),\n        new RegExp(regExps.newline),\n        new RegExp(regExps.extend),\n        new RegExp(regExps.format),\n        new RegExp(regExps.katakana),\n        new RegExp(regExps.extendnumlet),\n        new RegExp('@')\n    ];\n    const EMPTY_STRING$1 = '';\n    const PUNCTUATION$1 = new RegExp('^' + regExps.punctuation + '$');\n    const WHITESPACE$1 = /^\\s+$/;\n\n    const SETS = SETS$1;\n    const OTHER = characterIndices.OTHER;\n    const getType = (char) => {\n        let type = OTHER;\n        const setsLength = SETS.length;\n        for (let j = 0; j < setsLength; ++j) {\n            const set = SETS[j];\n            if (set && set.test(char)) {\n                type = j;\n                break;\n            }\n        }\n        return type;\n    };\n    const memoize = (func) => {\n        const cache = {};\n        return (char) => {\n            if (cache[char]) {\n                return cache[char];\n            }\n            else {\n                const result = func(char);\n                cache[char] = result;\n                return result;\n            }\n        };\n    };\n    const classify = (characters) => {\n        const memoized = memoize(getType);\n        return map(characters, memoized);\n    };\n\n    const isWordBoundary = (map, index) => {\n        const type = map[index];\n        const nextType = map[index + 1];\n        if (index < 0 || (index > map.length - 1 && index !== 0)) {\n            return false;\n        }\n        // WB5. Don't break between most letters.\n        if (type === characterIndices.ALETTER && nextType === characterIndices.ALETTER) {\n            return false;\n        }\n        const nextNextType = map[index + 2];\n        // WB6. Don't break letters across certain punctuation.\n        if (type === characterIndices.ALETTER &&\n            (nextType === characterIndices.MIDLETTER || nextType === characterIndices.MIDNUMLET || nextType === characterIndices.AT) &&\n            nextNextType === characterIndices.ALETTER) {\n            return false;\n        }\n        const prevType = map[index - 1];\n        // WB7. Don't break letters across certain punctuation.\n        if ((type === characterIndices.MIDLETTER || type === characterIndices.MIDNUMLET || nextType === characterIndices.AT) &&\n            nextType === characterIndices.ALETTER &&\n            prevType === characterIndices.ALETTER) {\n            return false;\n        }\n        // WB8/WB9/WB10. Don't break inside sequences of digits or digits\n        // adjacent to letters.\n        if ((type === characterIndices.NUMERIC || type === characterIndices.ALETTER) &&\n            (nextType === characterIndices.NUMERIC || nextType === characterIndices.ALETTER)) {\n            return false;\n        }\n        // WB11. Don't break inside numeric sequences like \"3.2\" or\n        // \"3,456.789\".\n        if ((type === characterIndices.MIDNUM || type === characterIndices.MIDNUMLET) &&\n            nextType === characterIndices.NUMERIC &&\n            prevType === characterIndices.NUMERIC) {\n            return false;\n        }\n        // WB12. Don't break inside numeric sequences like \"3.2\" or\n        // \"3,456.789\".\n        if (type === characterIndices.NUMERIC &&\n            (nextType === characterIndices.MIDNUM || nextType === characterIndices.MIDNUMLET) &&\n            nextNextType === characterIndices.NUMERIC) {\n            return false;\n        }\n        // WB4. Ignore format and extend characters.\n        if ((type === characterIndices.EXTEND || type === characterIndices.FORMAT) &&\n            (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC || nextType === characterIndices.KATAKANA ||\n                nextType === characterIndices.EXTEND || nextType === characterIndices.FORMAT)\n            ||\n                (nextType === characterIndices.EXTEND ||\n                    // TINY-9654: Only ignore format characters if they do not precede a word boundary. Since some format characters overlap with whitespace characters (ex: \\ufeff) and\n                    // our word extraction logic excludes whitespace characters, if a whitespace-overlapping format character that precedes a word boundary is not split on, whichever word\n                    // it is a part of will not be added to the list of extracted words, causing inaccuracies.\n                    nextType === characterIndices.FORMAT && (nextNextType === characterIndices.ALETTER || nextNextType === characterIndices.NUMERIC || nextNextType === characterIndices.KATAKANA || nextNextType === characterIndices.EXTEND || nextNextType === characterIndices.FORMAT))\n                    && (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA || type === characterIndices.EXTEND || type === characterIndices.FORMAT)) {\n            return false;\n        }\n        // WB3. Don't break inside CRLF.\n        if (type === characterIndices.CR && nextType === characterIndices.LF) {\n            return false;\n        }\n        // WB3a. Break before newlines (including CR and LF).\n        if (type === characterIndices.NEWLINE || type === characterIndices.CR || type === characterIndices.LF) {\n            return true;\n        }\n        // WB3b. Break after newlines (including CR and LF).\n        if (nextType === characterIndices.NEWLINE || nextType === characterIndices.CR || nextType === characterIndices.LF) {\n            return true;\n        }\n        // WB13. Don't break between Katakana characters.\n        if (type === characterIndices.KATAKANA && nextType === characterIndices.KATAKANA) {\n            return false;\n        }\n        // WB13a. Don't break from extenders.\n        if (nextType === characterIndices.EXTENDNUMLET &&\n            (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA ||\n                type === characterIndices.EXTENDNUMLET)) {\n            return false;\n        }\n        // WB13b. Don't break from extenders.\n        if (type === characterIndices.EXTENDNUMLET &&\n            (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC ||\n                nextType === characterIndices.KATAKANA)) {\n            return false;\n        }\n        if (type === characterIndices.AT) {\n            return false;\n        }\n        // Break after any character not covered by the rules above.\n        return true;\n    };\n\n    const EMPTY_STRING = EMPTY_STRING$1;\n    const WHITESPACE = WHITESPACE$1;\n    const PUNCTUATION = PUNCTUATION$1;\n    const isProtocol = (str) => str === 'http' || str === 'https';\n    const findWordEnd = (characters, startIndex) => {\n        let i;\n        for (i = startIndex; i < characters.length; i++) {\n            if (WHITESPACE.test(characters[i])) {\n                break;\n            }\n        }\n        return i;\n    };\n    const findUrlEnd = (characters, startIndex) => {\n        const endIndex = findWordEnd(characters, startIndex + 1);\n        const peakedWord = characters.slice(startIndex + 1, endIndex).join(EMPTY_STRING);\n        return peakedWord.substr(0, 3) === '://' ? endIndex : startIndex;\n    };\n    const findWordsWithIndices = (chars, sChars, characterMap, options) => {\n        const words = [];\n        const indices = [];\n        let word = [];\n        // Loop through each character in the classification map and determine whether\n        // it precedes a word boundary, building an array of distinct words as we go.\n        for (let i = 0; i < characterMap.length; ++i) {\n            // Append this character to the current word.\n            word.push(chars[i]);\n            // If there's a word boundary between the current character and the next character,\n            // append the current word to the words array and start building a new word.\n            if (isWordBoundary(characterMap, i)) {\n                const ch = sChars[i];\n                if ((options.includeWhitespace || !WHITESPACE.test(ch)) &&\n                    (options.includePunctuation || !PUNCTUATION.test(ch))) {\n                    const startOfWord = i - word.length + 1;\n                    const endOfWord = i + 1;\n                    const str = sChars.slice(startOfWord, endOfWord).join(EMPTY_STRING);\n                    if (isProtocol(str)) {\n                        const endOfUrl = findUrlEnd(sChars, i);\n                        const url = chars.slice(endOfWord, endOfUrl);\n                        Array.prototype.push.apply(word, url);\n                        i = endOfUrl;\n                    }\n                    // If the word is an abbreviation, include the next character if it's a period.\n                    if (sChars[i + 1] === '.' && /^([a-zA-Z]\\.)+$/.test(str + '.')) {\n                        word.push(chars[i + 1]);\n                        indices.push({\n                            start: startOfWord,\n                            end: endOfWord + 1\n                        });\n                    }\n                    else {\n                        indices.push({\n                            start: startOfWord,\n                            end: endOfWord\n                        });\n                    }\n                    words.push(word);\n                }\n                word = [];\n            }\n        }\n        return { words, indices };\n    };\n    const getDefaultOptions = () => ({\n        includeWhitespace: false,\n        includePunctuation: false\n    });\n    const getWordsWithIndices = (chars, extract, options) => {\n        options = {\n            ...getDefaultOptions(),\n            ...options\n        };\n        const extractedChars = map(chars, extract);\n        const characterMap = classify(extractedChars);\n        return findWordsWithIndices(chars, extractedChars, characterMap, options);\n    };\n    const getWords$1 = (chars, extract, options) => getWordsWithIndices(chars, extract, options).words;\n\n    const getWords = getWords$1;\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    const getText = (node, schema) => {\n        const blockElements = schema.getBlockElements();\n        const voidElements = schema.getVoidElements();\n        const isNewline = (node) => blockElements[node.nodeName] || voidElements[node.nodeName];\n        const textBlocks = [];\n        let txt = '';\n        const treeWalker = new global$1(node, node);\n        let tempNode;\n        while ((tempNode = treeWalker.next())) {\n            if (tempNode.nodeType === 3) {\n                txt += removeZwsp$1(tempNode.data);\n            }\n            else if (isNewline(tempNode) && txt.length) {\n                textBlocks.push(txt);\n                txt = '';\n            }\n        }\n        if (txt.length) {\n            textBlocks.push(txt);\n        }\n        return textBlocks;\n    };\n\n    const removeZwsp = (text) => text.replace(/\\u200B/g, '');\n    const strLen = (str) => str.replace(/[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g, '_').length;\n    const countWords = (node, schema) => {\n        // TODO - TINY-9708: See if TINY-7484 fix can be replaced by adding \\u200B to the \"format\" character class as per Unicode standard\n        // TINY-7484: The grapheme word boundary logic used by Polaris states a ZWSP (\\u200B) should be treated as\n        // a word boundary, however word counting normally does not consider it as anything so we strip it out.\n        const text = removeZwsp(getText(node, schema).join('\\n'));\n        return getWords(text.split(''), identity).length;\n    };\n    const countCharacters = (node, schema) => {\n        const text = getText(node, schema).join('');\n        return strLen(text);\n    };\n    const countCharactersWithoutSpaces = (node, schema) => {\n        const text = getText(node, schema).join('').replace(/\\s/g, '');\n        return strLen(text);\n    };\n\n    const createBodyCounter = (editor, count) => () => count(editor.getBody(), editor.schema);\n    const createSelectionCounter = (editor, count) => () => count(editor.selection.getRng().cloneContents(), editor.schema);\n    const createBodyWordCounter = (editor) => createBodyCounter(editor, countWords);\n    const get = (editor) => ({\n        body: {\n            getWordCount: createBodyWordCounter(editor),\n            getCharacterCount: createBodyCounter(editor, countCharacters),\n            getCharacterCountWithoutSpaces: createBodyCounter(editor, countCharactersWithoutSpaces)\n        },\n        selection: {\n            getWordCount: createSelectionCounter(editor, countWords),\n            getCharacterCount: createSelectionCounter(editor, countCharacters),\n            getCharacterCountWithoutSpaces: createSelectionCounter(editor, countCharactersWithoutSpaces)\n        },\n        getCount: createBodyWordCounter(editor)\n    });\n\n    const open = (editor, api) => {\n        editor.windowManager.open({\n            title: 'Word Count',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        type: 'table',\n                        header: ['Count', 'Document', 'Selection'],\n                        cells: [\n                            [\n                                'Words',\n                                String(api.body.getWordCount()),\n                                String(api.selection.getWordCount())\n                            ],\n                            [\n                                'Characters (no spaces)',\n                                String(api.body.getCharacterCountWithoutSpaces()),\n                                String(api.selection.getCharacterCountWithoutSpaces())\n                            ],\n                            [\n                                'Characters',\n                                String(api.body.getCharacterCount()),\n                                String(api.selection.getCharacterCount())\n                            ]\n                        ]\n                    }\n                ]\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'close',\n                    text: 'Close',\n                    primary: true\n                }\n            ]\n        });\n    };\n\n    const register$1 = (editor, api) => {\n        editor.addCommand('mceWordCount', () => open(editor, api));\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    const fireWordCountUpdate = (editor, api) => {\n        editor.dispatch('wordCountUpdate', {\n            wordCount: {\n                words: api.body.getWordCount(),\n                characters: api.body.getCharacterCount(),\n                charactersWithoutSpaces: api.body.getCharacterCountWithoutSpaces()\n            }\n        });\n    };\n\n    const updateCount = (editor, api) => {\n        fireWordCountUpdate(editor, api);\n    };\n    const setup = (editor, api, delay) => {\n        const debouncedUpdate = first(() => updateCount(editor, api), delay);\n        editor.on('init', () => {\n            updateCount(editor, api);\n            global.setEditorTimeout(editor, () => {\n                editor.on('SetContent BeforeAddUndo Undo Redo ViewUpdate keyup', debouncedUpdate.throttle);\n            }, 0);\n            editor.on('remove', debouncedUpdate.cancel);\n        });\n    };\n\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceWordCount');\n        editor.ui.registry.addButton('wordcount', {\n            tooltip: 'Word count',\n            icon: 'character-count',\n            onAction,\n            context: 'any'\n        });\n        editor.ui.registry.addMenuItem('wordcount', {\n            text: 'Word count',\n            icon: 'character-count',\n            onAction,\n            context: 'any'\n        });\n    };\n\n    var Plugin = (delay = 300) => {\n        global$2.add('wordcount', (editor) => {\n            const api = get(editor);\n            register$1(editor, api);\n            register(editor);\n            setup(editor, api, delay);\n            return api;\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,SAAS,GAAG;IAElB,MAAM,WAAW,CAAC;QACd,OAAO;IACX;IAEA,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IAEA,gEAAgE;IAChE,uDAAuD;IACvD,MAAM,QAAQ,CAAC,IAAI;QACf,IAAI,QAAQ;QACZ,MAAM,SAAS;YACX,IAAI,CAAC,OAAO,QAAQ;gBAChB,aAAa;gBACb,QAAQ;YACZ;QACJ;QACA,MAAM,WAAW;6CAAI;gBAAA;;YACjB,IAAI,OAAO,QAAQ;gBACf,QAAQ,WAAW;oBACf,QAAQ;oBACR,GAAG,KAAK,CAAC,MAAM;gBACnB,GAAG;YACP;QACJ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,eAAe,CAAC,IAAM,EAAE,OAAO,CAAC,WAAW;IAEjD,0BAA0B,GAC1B,MAAM,iBAAkB;IACxB,MAAM,UAAU;QACZ,SAAS;QACT,WAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,cAAc;QACd,aAAa;IACjB;IACA,yBAAyB,GACzB,MAAM,mBAAmB;QACrB,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,cAAc;QACd,IAAI;QACJ,OAAO;IACX;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,4EAA4E;IAC5E,SAAS;IACT,MAAM,SAAS;QACX,IAAI,OAAO,QAAQ,OAAO;QAC1B,IAAI,OAAO,QAAQ,SAAS;QAC5B,IAAI,OAAO,QAAQ,SAAS;QAC5B,IAAI,OAAO,QAAQ,MAAM;QACzB,IAAI,OAAO,QAAQ,OAAO;QAC1B,IAAI,OAAO,QAAQ,EAAE;QACrB,IAAI,OAAO,QAAQ,EAAE;QACrB,IAAI,OAAO,QAAQ,OAAO;QAC1B,IAAI,OAAO,QAAQ,MAAM;QACzB,IAAI,OAAO,QAAQ,MAAM;QACzB,IAAI,OAAO,QAAQ,QAAQ;QAC3B,IAAI,OAAO,QAAQ,YAAY;QAC/B,IAAI,OAAO;KACd;IACD,MAAM,iBAAiB;IACvB,MAAM,gBAAgB,IAAI,OAAO,MAAM,QAAQ,WAAW,GAAG;IAC7D,MAAM,eAAe;IAErB,MAAM,OAAO;IACb,MAAM,QAAQ,iBAAiB,KAAK;IACpC,MAAM,UAAU,CAAC;QACb,IAAI,OAAO;QACX,MAAM,aAAa,KAAK,MAAM;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,EAAE,EAAG;YACjC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO;gBACvB,OAAO;gBACP;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,UAAU,CAAC;QACb,MAAM,QAAQ,CAAC;QACf,OAAO,CAAC;YACJ,IAAI,KAAK,CAAC,KAAK,EAAE;gBACb,OAAO,KAAK,CAAC,KAAK;YACtB,OACK;gBACD,MAAM,SAAS,KAAK;gBACpB,KAAK,CAAC,KAAK,GAAG;gBACd,OAAO;YACX;QACJ;IACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,QAAQ;QACzB,OAAO,IAAI,YAAY;IAC3B;IAEA,MAAM,iBAAiB,CAAC,KAAK;QACzB,MAAM,OAAO,GAAG,CAAC,MAAM;QACvB,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE;QAC/B,IAAI,QAAQ,KAAM,QAAQ,IAAI,MAAM,GAAG,KAAK,UAAU,GAAI;YACtD,OAAO;QACX;QACA,yCAAyC;QACzC,IAAI,SAAS,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,OAAO,EAAE;YAC5E,OAAO;QACX;QACA,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE;QACnC,uDAAuD;QACvD,IAAI,SAAS,iBAAiB,OAAO,IACjC,CAAC,aAAa,iBAAiB,SAAS,IAAI,aAAa,iBAAiB,SAAS,IAAI,aAAa,iBAAiB,EAAE,KACvH,iBAAiB,iBAAiB,OAAO,EAAE;YAC3C,OAAO;QACX;QACA,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE;QAC/B,uDAAuD;QACvD,IAAI,CAAC,SAAS,iBAAiB,SAAS,IAAI,SAAS,iBAAiB,SAAS,IAAI,aAAa,iBAAiB,EAAE,KAC/G,aAAa,iBAAiB,OAAO,IACrC,aAAa,iBAAiB,OAAO,EAAE;YACvC,OAAO;QACX;QACA,iEAAiE;QACjE,uBAAuB;QACvB,IAAI,CAAC,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,OAAO,KACvE,CAAC,aAAa,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,OAAO,GAAG;YAClF,OAAO;QACX;QACA,2DAA2D;QAC3D,eAAe;QACf,IAAI,CAAC,SAAS,iBAAiB,MAAM,IAAI,SAAS,iBAAiB,SAAS,KACxE,aAAa,iBAAiB,OAAO,IACrC,aAAa,iBAAiB,OAAO,EAAE;YACvC,OAAO;QACX;QACA,2DAA2D;QAC3D,eAAe;QACf,IAAI,SAAS,iBAAiB,OAAO,IACjC,CAAC,aAAa,iBAAiB,MAAM,IAAI,aAAa,iBAAiB,SAAS,KAChF,iBAAiB,iBAAiB,OAAO,EAAE;YAC3C,OAAO;QACX;QACA,4CAA4C;QAC5C,IAAI,CAAC,SAAS,iBAAiB,MAAM,IAAI,SAAS,iBAAiB,MAAM,KACrE,CAAC,aAAa,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,QAAQ,IACrH,aAAa,iBAAiB,MAAM,IAAI,aAAa,iBAAiB,MAAM,KAE5E,CAAC,aAAa,iBAAiB,MAAM,IACjC,oKAAoK;QACpK,uKAAuK;QACvK,0FAA0F;QAC1F,aAAa,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,iBAAiB,OAAO,IAAI,iBAAiB,iBAAiB,OAAO,IAAI,iBAAiB,iBAAiB,QAAQ,IAAI,iBAAiB,iBAAiB,MAAM,IAAI,iBAAiB,iBAAiB,MAAM,CAAC,KACnQ,CAAC,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,QAAQ,IAAI,SAAS,iBAAiB,MAAM,IAAI,SAAS,iBAAiB,MAAM,GAAG;YACnM,OAAO;QACX;QACA,gCAAgC;QAChC,IAAI,SAAS,iBAAiB,EAAE,IAAI,aAAa,iBAAiB,EAAE,EAAE;YAClE,OAAO;QACX;QACA,qDAAqD;QACrD,IAAI,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,EAAE,IAAI,SAAS,iBAAiB,EAAE,EAAE;YACnG,OAAO;QACX;QACA,oDAAoD;QACpD,IAAI,aAAa,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,EAAE,IAAI,aAAa,iBAAiB,EAAE,EAAE;YAC/G,OAAO;QACX;QACA,iDAAiD;QACjD,IAAI,SAAS,iBAAiB,QAAQ,IAAI,aAAa,iBAAiB,QAAQ,EAAE;YAC9E,OAAO;QACX;QACA,qCAAqC;QACrC,IAAI,aAAa,iBAAiB,YAAY,IAC1C,CAAC,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,OAAO,IAAI,SAAS,iBAAiB,QAAQ,IACzG,SAAS,iBAAiB,YAAY,GAAG;YAC7C,OAAO;QACX;QACA,qCAAqC;QACrC,IAAI,SAAS,iBAAiB,YAAY,IACtC,CAAC,aAAa,iBAAiB,OAAO,IAAI,aAAa,iBAAiB,OAAO,IAC3E,aAAa,iBAAiB,QAAQ,GAAG;YAC7C,OAAO;QACX;QACA,IAAI,SAAS,iBAAiB,EAAE,EAAE;YAC9B,OAAO;QACX;QACA,4DAA4D;QAC5D,OAAO;IACX;IAEA,MAAM,eAAe;IACrB,MAAM,aAAa;IACnB,MAAM,cAAc;IACpB,MAAM,aAAa,CAAC,MAAQ,QAAQ,UAAU,QAAQ;IACtD,MAAM,cAAc,CAAC,YAAY;QAC7B,IAAI;QACJ,IAAK,IAAI,YAAY,IAAI,WAAW,MAAM,EAAE,IAAK;YAC7C,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;gBAChC;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,aAAa,CAAC,YAAY;QAC5B,MAAM,WAAW,YAAY,YAAY,aAAa;QACtD,MAAM,aAAa,WAAW,KAAK,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC;QACnE,OAAO,WAAW,MAAM,CAAC,GAAG,OAAO,QAAQ,WAAW;IAC1D;IACA,MAAM,uBAAuB,CAAC,OAAO,QAAQ,cAAc;QACvD,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,IAAI,OAAO,EAAE;QACb,8EAA8E;QAC9E,6EAA6E;QAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,EAAE,EAAG;YAC1C,6CAA6C;YAC7C,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE;YAClB,mFAAmF;YACnF,4EAA4E;YAC5E,IAAI,eAAe,cAAc,IAAI;gBACjC,MAAM,KAAK,MAAM,CAAC,EAAE;gBACpB,IAAI,CAAC,QAAQ,iBAAiB,IAAI,CAAC,WAAW,IAAI,CAAC,GAAG,KAClD,CAAC,QAAQ,kBAAkB,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,GAAG;oBACvD,MAAM,cAAc,IAAI,KAAK,MAAM,GAAG;oBACtC,MAAM,YAAY,IAAI;oBACtB,MAAM,MAAM,OAAO,KAAK,CAAC,aAAa,WAAW,IAAI,CAAC;oBACtD,IAAI,WAAW,MAAM;wBACjB,MAAM,WAAW,WAAW,QAAQ;wBACpC,MAAM,MAAM,MAAM,KAAK,CAAC,WAAW;wBACnC,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;wBACjC,IAAI;oBACR;oBACA,+EAA+E;oBAC/E,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO,kBAAkB,IAAI,CAAC,MAAM,MAAM;wBAC5D,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;wBACtB,QAAQ,IAAI,CAAC;4BACT,OAAO;4BACP,KAAK,YAAY;wBACrB;oBACJ,OACK;wBACD,QAAQ,IAAI,CAAC;4BACT,OAAO;4BACP,KAAK;wBACT;oBACJ;oBACA,MAAM,IAAI,CAAC;gBACf;gBACA,OAAO,EAAE;YACb;QACJ;QACA,OAAO;YAAE;YAAO;QAAQ;IAC5B;IACA,MAAM,oBAAoB,IAAM,CAAC;YAC7B,mBAAmB;YACnB,oBAAoB;QACxB,CAAC;IACD,MAAM,sBAAsB,CAAC,OAAO,SAAS;QACzC,UAAU;YACN,GAAG,mBAAmB;YACtB,GAAG,OAAO;QACd;QACA,MAAM,iBAAiB,IAAI,OAAO;QAClC,MAAM,eAAe,SAAS;QAC9B,OAAO,qBAAqB,OAAO,gBAAgB,cAAc;IACrE;IACA,MAAM,aAAa,CAAC,OAAO,SAAS,UAAY,oBAAoB,OAAO,SAAS,SAAS,KAAK;IAElG,MAAM,WAAW;IAEjB,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,UAAU,CAAC,MAAM;QACnB,MAAM,gBAAgB,OAAO,gBAAgB;QAC7C,MAAM,eAAe,OAAO,eAAe;QAC3C,MAAM,YAAY,CAAC,OAAS,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,YAAY,CAAC,KAAK,QAAQ,CAAC;QACvF,MAAM,aAAa,EAAE;QACrB,IAAI,MAAM;QACV,MAAM,aAAa,IAAI,SAAS,MAAM;QACtC,IAAI;QACJ,MAAQ,WAAW,WAAW,IAAI,GAAK;YACnC,IAAI,SAAS,QAAQ,KAAK,GAAG;gBACzB,OAAO,aAAa,SAAS,IAAI;YACrC,OACK,IAAI,UAAU,aAAa,IAAI,MAAM,EAAE;gBACxC,WAAW,IAAI,CAAC;gBAChB,MAAM;YACV;QACJ;QACA,IAAI,IAAI,MAAM,EAAE;YACZ,WAAW,IAAI,CAAC;QACpB;QACA,OAAO;IACX;IAEA,MAAM,aAAa,CAAC,OAAS,KAAK,OAAO,CAAC,WAAW;IACrD,MAAM,SAAS,CAAC,MAAQ,IAAI,OAAO,CAAC,mCAAmC,KAAK,MAAM;IAClF,MAAM,aAAa,CAAC,MAAM;QACtB,kIAAkI;QAClI,0GAA0G;QAC1G,uGAAuG;QACvG,MAAM,OAAO,WAAW,QAAQ,MAAM,QAAQ,IAAI,CAAC;QACnD,OAAO,SAAS,KAAK,KAAK,CAAC,KAAK,UAAU,MAAM;IACpD;IACA,MAAM,kBAAkB,CAAC,MAAM;QAC3B,MAAM,OAAO,QAAQ,MAAM,QAAQ,IAAI,CAAC;QACxC,OAAO,OAAO;IAClB;IACA,MAAM,+BAA+B,CAAC,MAAM;QACxC,MAAM,OAAO,QAAQ,MAAM,QAAQ,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO;QAC3D,OAAO,OAAO;IAClB;IAEA,MAAM,oBAAoB,CAAC,QAAQ,QAAU,IAAM,MAAM,OAAO,OAAO,IAAI,OAAO,MAAM;IACxF,MAAM,yBAAyB,CAAC,QAAQ,QAAU,IAAM,MAAM,OAAO,SAAS,CAAC,MAAM,GAAG,aAAa,IAAI,OAAO,MAAM;IACtH,MAAM,wBAAwB,CAAC,SAAW,kBAAkB,QAAQ;IACpE,MAAM,MAAM,CAAC,SAAW,CAAC;YACrB,MAAM;gBACF,cAAc,sBAAsB;gBACpC,mBAAmB,kBAAkB,QAAQ;gBAC7C,gCAAgC,kBAAkB,QAAQ;YAC9D;YACA,WAAW;gBACP,cAAc,uBAAuB,QAAQ;gBAC7C,mBAAmB,uBAAuB,QAAQ;gBAClD,gCAAgC,uBAAuB,QAAQ;YACnE;YACA,UAAU,sBAAsB;QACpC,CAAC;IAED,MAAM,OAAO,CAAC,QAAQ;QAClB,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,QAAQ;4BAAC;4BAAS;4BAAY;yBAAY;wBAC1C,OAAO;4BACH;gCACI;gCACA,OAAO,IAAI,IAAI,CAAC,YAAY;gCAC5B,OAAO,IAAI,SAAS,CAAC,YAAY;6BACpC;4BACD;gCACI;gCACA,OAAO,IAAI,IAAI,CAAC,8BAA8B;gCAC9C,OAAO,IAAI,SAAS,CAAC,8BAA8B;6BACtD;4BACD;gCACI;gCACA,OAAO,IAAI,IAAI,CAAC,iBAAiB;gCACjC,OAAO,IAAI,SAAS,CAAC,iBAAiB;6BACzC;yBACJ;oBACL;iBACH;YACL;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;QACL;IACJ;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,gBAAgB,IAAM,KAAK,QAAQ;IACzD;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,sBAAsB,CAAC,QAAQ;QACjC,OAAO,QAAQ,CAAC,mBAAmB;YAC/B,WAAW;gBACP,OAAO,IAAI,IAAI,CAAC,YAAY;gBAC5B,YAAY,IAAI,IAAI,CAAC,iBAAiB;gBACtC,yBAAyB,IAAI,IAAI,CAAC,8BAA8B;YACpE;QACJ;IACJ;IAEA,MAAM,cAAc,CAAC,QAAQ;QACzB,oBAAoB,QAAQ;IAChC;IACA,MAAM,QAAQ,CAAC,QAAQ,KAAK;QACxB,MAAM,kBAAkB,MAAM,IAAM,YAAY,QAAQ,MAAM;QAC9D,OAAO,EAAE,CAAC,QAAQ;YACd,YAAY,QAAQ;YACpB,OAAO,gBAAgB,CAAC,QAAQ;gBAC5B,OAAO,EAAE,CAAC,uDAAuD,gBAAgB,QAAQ;YAC7F,GAAG;YACH,OAAO,EAAE,CAAC,UAAU,gBAAgB,MAAM;QAC9C;IACJ;IAEA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa;YACtC,SAAS;YACT,MAAM;YACN;YACA,SAAS;QACb;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa;YACxC,MAAM;YACN,MAAM;YACN;YACA,SAAS;QACb;IACJ;IAEA,IAAI,SAAS;YAAC,yEAAQ;QAClB,SAAS,GAAG,CAAC,aAAa,CAAC;YACvB,MAAM,MAAM,IAAI;YAChB,WAAW,QAAQ;YACnB,SAAS;YACT,MAAM,QAAQ,KAAK;YACnB,OAAO;QACX;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/wordcount/index.js"], "sourcesContent": ["// Exports the \"wordcount\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/wordcount')\n//   ES2015:\n//     import 'tinymce/plugins/wordcount'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,SAAS;AACT,cAAc;AACd,2CAA2C;AAC3C,YAAY;AACZ,yCAAyC", "ignoreList": [0], "debugId": null}}]}