from src.tools.vectorisation_tool import vectorize_scraped_data
from src.tools.file_generator import generate_newsletter_with_file
from src.tools.preprocessing import clean_text

def generate_newsletter_from_uploaded_file(content: str, theme: str="actualités" , description : str="actualités") -> str:
    """
    Prend un texte brut issu d’un fichier utilisateur et génère une newsletter via RAG.
    """
    cleaned = clean_text(content)
    chunks = cleaned.split("\n")

    # Vectorisation RAG-style (même si pas utilisée pour retrieval ici)
    vectorize_scraped_data(chunks)

    # Génération directe depuis tout le contexte
    newsletter = generate_newsletter_with_file(chunks, theme , description)
    return newsletter
