(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/tinymce/tinymce.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_tinymce_6c002cd9.js",
  "static/chunks/node_modules_tinymce_tinymce_1afa4d1b.js",
  "static/chunks/node_modules_tinymce_tinymce_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/tinymce.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/themes/silver/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_themes_silver_theme_deab9b21.js",
  "static/chunks/node_modules_tinymce_themes_silver_index_8a678f8d.js",
  "static/chunks/node_modules_tinymce_themes_silver_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/themes/silver/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/advlist/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_advlist_a8b06718._.js",
  "static/chunks/node_modules_tinymce_plugins_advlist_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/advlist/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/autolink/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_autolink_de66c286._.js",
  "static/chunks/node_modules_tinymce_plugins_autolink_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/autolink/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/lists/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_lists_84238695._.js",
  "static/chunks/node_modules_tinymce_plugins_lists_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/lists/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/link/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_link_eac06471._.js",
  "static/chunks/node_modules_tinymce_plugins_link_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/link/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/image/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_image_fb206d91._.js",
  "static/chunks/node_modules_tinymce_plugins_image_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/image/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/charmap/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_charmap_95fe799c._.js",
  "static/chunks/node_modules_tinymce_plugins_charmap_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/charmap/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/preview/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_preview_2dd953e1._.js",
  "static/chunks/node_modules_tinymce_plugins_preview_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/preview/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/anchor/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_anchor_be0537aa._.js",
  "static/chunks/node_modules_tinymce_plugins_anchor_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/anchor/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/searchreplace/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_searchreplace_0b18f691._.js",
  "static/chunks/node_modules_tinymce_plugins_searchreplace_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/searchreplace/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/visualblocks/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_visualblocks_4a1bd38f._.js",
  "static/chunks/node_modules_tinymce_plugins_visualblocks_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/visualblocks/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/code/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_code_2efbb394._.js",
  "static/chunks/node_modules_tinymce_plugins_code_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/code/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/fullscreen/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_fullscreen_9b4d9bee._.js",
  "static/chunks/node_modules_tinymce_plugins_fullscreen_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/fullscreen/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/insertdatetime/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_insertdatetime_38db2cdf._.js",
  "static/chunks/node_modules_tinymce_plugins_insertdatetime_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/insertdatetime/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/media/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_media_87abbd61._.js",
  "static/chunks/node_modules_tinymce_plugins_media_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/media/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/table/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_table_e2ae030c._.js",
  "static/chunks/node_modules_tinymce_plugins_table_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/table/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/help/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_help_c0add73e._.js",
  "static/chunks/node_modules_tinymce_plugins_help_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/help/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/wordcount/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_wordcount_fbb0ced3._.js",
  "static/chunks/node_modules_tinymce_plugins_wordcount_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/wordcount/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/emoticons/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_emoticons_420aeb98._.js",
  "static/chunks/node_modules_tinymce_plugins_emoticons_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/emoticons/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/tinymce/plugins/codesample/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_tinymce_plugins_codesample_d3b70424._.js",
  "static/chunks/node_modules_tinymce_plugins_codesample_index_0e69c7af.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/tinymce/plugins/codesample/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_85fd1a95._.js",
  "static/chunks/node_modules_@tinymce_tinymce-react_lib_es2015_main_ts_index_714a8417.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);