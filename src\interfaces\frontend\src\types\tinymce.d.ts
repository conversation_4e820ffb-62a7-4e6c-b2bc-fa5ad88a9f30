// Déclarations TypeScript pour TinyMCE
declare module 'tinymce/tinymce';
declare module 'tinymce/themes/silver';
declare module 'tinymce/plugins/advlist';
declare module 'tinymce/plugins/autolink';
declare module 'tinymce/plugins/lists';
declare module 'tinymce/plugins/link';
declare module 'tinymce/plugins/image';
declare module 'tinymce/plugins/charmap';
declare module 'tinymce/plugins/preview';
declare module 'tinymce/plugins/anchor';
declare module 'tinymce/plugins/searchreplace';
declare module 'tinymce/plugins/visualblocks';
declare module 'tinymce/plugins/code';
declare module 'tinymce/plugins/fullscreen';
declare module 'tinymce/plugins/insertdatetime';
declare module 'tinymce/plugins/media';
declare module 'tinymce/plugins/table';
declare module 'tinymce/plugins/help';
declare module 'tinymce/plugins/wordcount';
declare module 'tinymce/plugins/emoticons';
declare module 'tinymce/plugins/template';
declare module 'tinymce/plugins/codesample';
declare module 'tinymce/plugins/textcolor';
declare module 'tinymce/plugins/colorpicker';
