
# from typing import List
# from src.tools.scraping import scrape_content


# def scrape_news(theme: str, description: str) -> List[str]:
#     """Scrape des actualités liées à un thème et une description en français."""
#     return scrape_content(theme, description)
from typing import List, Dict
from src.tools.scraping import scrape_content

def scrape_news(theme: str, description: str) -> List[Dict[str, str]]:
    """Scrape des actualités liées à un thème et une description (format MCP)."""
    return scrape_content(theme, description)
