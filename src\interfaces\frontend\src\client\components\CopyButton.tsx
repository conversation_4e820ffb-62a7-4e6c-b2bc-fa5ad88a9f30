import { useState } from "react";
import { But<PERSON> } from "@/client/components/ui/button";
import { Copy, Check } from "lucide-react";

interface CopyButtonProps {
  content: string;
}

export default function CopyButton({ content }: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <Button
      onClick={handleCopy}
      variant="outline"
      className="w-full"
    >
      {copied ? (
        <>
          <Check className="w-4 h-4 mr-2" />
          Copié !
        </>
      ) : (
        <>
          <Copy className="w-4 h-4 mr-2" />
          Copier la newsletter
        </>
      )}
    </Button>
  );
}












// "use client";

// import { useState } from "react";

// export default function CopyButton({ content }: { content: string }) {
//   const [copied, setCopied] = useState(false);

//   const copy = () => {
//     navigator.clipboard.writeText(content);
//     setCopied(true);
//   };

//   return (
//     <button
//       onClick={copy}
//       className="mt-4 w-full bg-gray-800 text-white py-2 rounded-md hover:bg-gray-900 transition"
//     >
//       {copied ? "✅ Copié !" : "📋 Copier la Newsletter"}
//     </button>
//   );
// }
