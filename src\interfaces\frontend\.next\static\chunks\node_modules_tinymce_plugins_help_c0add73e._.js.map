{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/help/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq = (t) => (a) => t === a;\n    const isString = isType('string');\n    const isUndefined = eq(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const never = constant(false);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    /* eslint-enable */\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const map = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    const filter = (xs, pred) => {\n        const r = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                r.push(x);\n            }\n        }\n        return r;\n    };\n    const findUntil = (xs, pred, until) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return Optional.some(x);\n            }\n            else if (until(x, i)) {\n                break;\n            }\n        }\n        return Optional.none();\n    };\n    const find = (xs, pred) => {\n        return findUntil(xs, pred, never);\n    };\n    const sort = (xs, comparator) => {\n        const copy = nativeSlice.call(xs, 0);\n        copy.sort(comparator);\n        return copy;\n    };\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const get$1 = (obj, key) => {\n        return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    /**\n     * Adds two numbers, and wrap to a range.\n     * If the result overflows to the right, snap to the left.\n     * If the result overflows to the left, snap to the right.\n     */\n    // the division is meant to get a number between 0 and 1 for more information check this discussion: https://stackoverflow.com/questions/58285941/how-to-replace-math-random-with-crypto-getrandomvalues-and-keep-same-result\n    const random = () => window.crypto.getRandomValues(new Uint32Array(1))[0] / 4294967295;\n\n    /**\n     * Generate a unique identifier.\n     *\n     * The unique portion of the identifier only contains an underscore\n     * and digits, so that it may safely be used within HTML attributes.\n     *\n     * The chance of generating a non-unique identifier has been minimized\n     * by combining the current time, a random number and a one-up counter.\n     *\n     * generate :: String -> String\n     */\n    let unique = 0;\n    const generate = (prefix) => {\n        const date = new Date();\n        const time = date.getTime();\n        const random$1 = Math.floor(random() * 1000000000);\n        unique++;\n        return prefix + '_' + random$1 + unique + String(time);\n    };\n\n    const cat = (arr) => {\n        const r = [];\n        const push = (x) => {\n            r.push(x);\n        };\n        for (let i = 0; i < arr.length; i++) {\n            arr[i].each(push);\n        }\n        return r;\n    };\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const get = (customTabs) => {\n        const addTab = (spec) => {\n            var _a;\n            const name = (_a = spec.name) !== null && _a !== void 0 ? _a : generate('tab-name');\n            const currentCustomTabs = customTabs.get();\n            currentCustomTabs[name] = spec;\n            customTabs.set(currentCustomTabs);\n        };\n        return {\n            addTab\n        };\n    };\n\n    const register$2 = (editor, dialogOpener) => {\n        editor.addCommand('mceHelp', dialogOpener);\n    };\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$1 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('help_tabs', {\n            processor: 'array'\n        });\n    };\n    const getHelpTabs = option('help_tabs');\n    const getForcedPlugins = option('forced_plugins');\n\n    const register = (editor, dialogOpener) => {\n        editor.ui.registry.addButton('help', {\n            icon: 'help',\n            tooltip: 'Help',\n            onAction: dialogOpener,\n            context: 'any'\n        });\n        editor.ui.registry.addMenuItem('help', {\n            text: 'Help',\n            icon: 'help',\n            shortcut: 'Alt+0',\n            onAction: dialogOpener,\n            context: 'any'\n        });\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.Resource');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.I18n');\n\n    const pLoadHtmlByLangCode = (baseUrl, langCode) => global$3.load(`tinymce.html-i18n.help-keynav.${langCode}`, `${baseUrl}/js/i18n/keynav/${langCode}.js`);\n    const pLoadI18nHtml = (baseUrl) => \n    // TINY-9928: Load language file for the current language, or English if the file is not available\n    pLoadHtmlByLangCode(baseUrl, global$2.getCode()).catch(() => pLoadHtmlByLangCode(baseUrl, 'en'));\n    const initI18nLoad = (editor, baseUrl) => {\n        editor.on('init', () => {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            pLoadI18nHtml(baseUrl);\n        });\n    };\n\n    const pTab = async (pluginUrl) => {\n        const body = {\n            type: 'htmlpanel',\n            presets: 'document',\n            html: await pLoadI18nHtml(pluginUrl)\n        };\n        return {\n            name: 'keyboardnav',\n            title: 'Keyboard Navigation',\n            items: [body]\n        };\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    // Converts shortcut format to Mac/PC variants\n    const convertText = (source) => {\n        const isMac = global$1.os.isMacOS() || global$1.os.isiOS();\n        const mac = {\n            alt: '&#x2325;',\n            ctrl: '&#x2303;',\n            shift: '&#x21E7;',\n            meta: '&#x2318;',\n            access: '&#x2303;&#x2325;'\n        };\n        const other = {\n            meta: 'Ctrl ',\n            access: 'Shift + Alt '\n        };\n        const replace = isMac ? mac : other;\n        const shortcut = source.split('+');\n        const updated = map(shortcut, (segment) => {\n            // search lowercase, but if not found use the original\n            const search = segment.toLowerCase().trim();\n            return has(replace, search) ? replace[search] : segment;\n        });\n        return isMac ? (updated.join('')).replace(/\\s/, '') : updated.join('+');\n    };\n\n    const shortcuts = [\n        { shortcuts: ['Meta + B'], action: 'Bold' },\n        { shortcuts: ['Meta + I'], action: 'Italic' },\n        { shortcuts: ['Meta + U'], action: 'Underline' },\n        { shortcuts: ['Meta + A'], action: 'Select all' },\n        { shortcuts: ['Meta + Y', 'Meta + Shift + Z'], action: 'Redo' },\n        { shortcuts: ['Meta + Z'], action: 'Undo' },\n        { shortcuts: ['Access + 1'], action: 'Heading 1' },\n        { shortcuts: ['Access + 2'], action: 'Heading 2' },\n        { shortcuts: ['Access + 3'], action: 'Heading 3' },\n        { shortcuts: ['Access + 4'], action: 'Heading 4' },\n        { shortcuts: ['Access + 5'], action: 'Heading 5' },\n        { shortcuts: ['Access + 6'], action: 'Heading 6' },\n        { shortcuts: ['Access + 7'], action: 'Paragraph' },\n        { shortcuts: ['Access + 8'], action: 'Div' },\n        { shortcuts: ['Access + 9'], action: 'Address' },\n        { shortcuts: ['Alt + 0'], action: 'Open help dialog' },\n        { shortcuts: ['Alt + F9'], action: 'Focus to menubar' },\n        { shortcuts: ['Alt + F10'], action: 'Focus to toolbar' },\n        { shortcuts: ['Alt + F11'], action: 'Focus to element path' },\n        { shortcuts: ['Alt + F12'], action: 'Focus to notification' },\n        { shortcuts: ['Ctrl + F9'], action: 'Focus to contextual toolbar' },\n        { shortcuts: ['Shift + Enter'], action: 'Open popup menu for split buttons' },\n        { shortcuts: ['Meta + K'], action: 'Insert link (if link plugin activated)' },\n        { shortcuts: ['Meta + S'], action: 'Save (if save plugin activated)' },\n        { shortcuts: ['Meta + F'], action: 'Find (if searchreplace plugin activated)' },\n        { shortcuts: ['Meta + Shift + F'], action: 'Switch to or from fullscreen mode' }\n    ];\n\n    const tab$2 = () => {\n        const shortcutList = map(shortcuts, (shortcut) => {\n            const shortcutText = map(shortcut.shortcuts, convertText).join(' or ');\n            return [shortcut.action, shortcutText];\n        });\n        const tablePanel = {\n            type: 'table',\n            // TODO: Fix table styles #TINY-2909\n            header: ['Action', 'Shortcut'],\n            cells: shortcutList\n        };\n        return {\n            name: 'shortcuts',\n            title: 'Handy Shortcuts',\n            items: [\n                tablePanel\n            ]\n        };\n    };\n\n    // These lists are automatically sorted when generating the dialog.\n    const urls = map([\n        { key: 'accordion', name: 'Accordion' },\n        { key: 'anchor', name: 'Anchor' },\n        { key: 'autolink', name: 'Autolink' },\n        { key: 'autoresize', name: 'Autoresize' },\n        { key: 'autosave', name: 'Autosave' },\n        { key: 'charmap', name: 'Character Map' },\n        { key: 'code', name: 'Code' },\n        { key: 'codesample', name: 'Code Sample' },\n        { key: 'colorpicker', name: 'Color Picker' },\n        { key: 'directionality', name: 'Directionality' },\n        { key: 'emoticons', name: 'Emoticons' },\n        { key: 'fullscreen', name: 'Full Screen' },\n        { key: 'help', name: 'Help' },\n        { key: 'image', name: 'Image' },\n        { key: 'importcss', name: 'Import CSS' },\n        { key: 'insertdatetime', name: 'Insert Date/Time' },\n        { key: 'link', name: 'Link' },\n        { key: 'lists', name: 'Lists' },\n        { key: 'advlist', name: 'List Styles' },\n        { key: 'media', name: 'Media' },\n        { key: 'nonbreaking', name: 'Nonbreaking' },\n        { key: 'pagebreak', name: 'Page Break' },\n        { key: 'preview', name: 'Preview' },\n        { key: 'quickbars', name: 'Quick Toolbars' },\n        { key: 'save', name: 'Save' },\n        { key: 'searchreplace', name: 'Search and Replace' },\n        { key: 'table', name: 'Table' },\n        { key: 'textcolor', name: 'Text Color' },\n        { key: 'visualblocks', name: 'Visual Blocks' },\n        { key: 'visualchars', name: 'Visual Characters' },\n        { key: 'wordcount', name: 'Word Count' },\n        // TODO: Add other premium plugins when they are included in the website\n        { key: 'a11ychecker', name: 'Accessibility Checker', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'typography', name: 'Advanced Typography', type: \"premium\" /* PluginType.Premium */, slug: 'advanced-typography' },\n        { key: 'ai', name: 'AI Assistant', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'casechange', name: 'Case Change', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'checklist', name: 'Checklist', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'advcode', name: 'Enhanced Code Editor', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'mediaembed', name: 'Enhanced Media Embed', type: \"premium\" /* PluginType.Premium */, slug: 'introduction-to-mediaembed' },\n        { key: 'advtable', name: 'Enhanced Tables', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'exportpdf', name: 'Export to PDF', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'exportword', name: 'Export to Word', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'footnotes', name: 'Footnotes', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'formatpainter', name: 'Format Painter', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'editimage', name: 'Image Editing', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'uploadcare', name: 'Image Optimizer Powered by Uploadcare', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'importword', name: 'Import from Word', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'inlinecss', name: 'Inline CSS', type: \"premium\" /* PluginType.Premium */, slug: 'inline-css' },\n        { key: 'linkchecker', name: 'Link Checker', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'math', name: 'Math', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'markdown', name: 'Markdown', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'mentions', name: 'Mentions', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'mergetags', name: 'Merge Tags', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'pageembed', name: 'Page Embed', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'permanentpen', name: 'Permanent Pen', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'powerpaste', name: 'PowerPaste', type: \"premium\" /* PluginType.Premium */, slug: 'introduction-to-powerpaste' },\n        { key: 'revisionhistory', name: 'Revision History', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'tinymcespellchecker', name: 'Spell Checker', type: \"premium\" /* PluginType.Premium */, slug: 'introduction-to-tiny-spellchecker' },\n        { key: 'autocorrect', name: 'Spelling Autocorrect', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'tableofcontents', name: 'Table of Contents', type: \"premium\" /* PluginType.Premium */ },\n        { key: 'advtemplate', name: 'Templates', type: \"premium\" /* PluginType.Premium */, slug: 'advanced-templates' },\n        { key: 'tinycomments', name: 'Tiny Comments', type: \"premium\" /* PluginType.Premium */, slug: 'introduction-to-tiny-comments' },\n        { key: 'tinydrive', name: 'Tiny Drive', type: \"premium\" /* PluginType.Premium */, slug: 'tinydrive-introduction' },\n    ], (item) => ({\n        ...item,\n        // Set the defaults/fallbacks for the plugin urls\n        type: item.type || \"opensource\" /* PluginType.OpenSource */,\n        slug: item.slug || item.key\n    }));\n\n    const tab$1 = (editor) => {\n        const availablePlugins = () => {\n            const premiumPlugins = filter(urls, ({ type }) => {\n                return type === \"premium\" /* PluginUrls.PluginType.Premium */;\n            });\n            const sortedPremiumPlugins = sort(map(premiumPlugins, (p) => p.name), (s1, s2) => s1.localeCompare(s2));\n            const premiumPluginList = map(sortedPremiumPlugins, (pluginName) => `<li>${pluginName}</li>`).join('');\n            return '<div>' +\n                '<p><b>' + global$2.translate('Premium plugins:') + '</b></p>' +\n                '<ul>' +\n                premiumPluginList +\n                '<li class=\"tox-help__more-link\" \">' +\n                '<a href=\"https://www.tiny.cloud/pricing/?utm_campaign=help_dialog_plugin_tab&utm_source=tiny&utm_medium=referral&utm_term=read_more&utm_content=premium_plugin_heading\" rel=\"noopener\" target=\"_blank\"' +\n                ' data-alloy-tabstop=\"true\" tabindex=\"-1\">' + global$2.translate('Learn more...') + '</a></li>' +\n                '</ul>' +\n                '</div>';\n        };\n        const makeLink = (p) => `<a data-alloy-tabstop=\"true\" tabindex=\"-1\" href=\"${p.url}\" target=\"_blank\" rel=\"noopener\">${p.name}</a>`;\n        const identifyUnknownPlugin = (editor, key) => {\n            const getMetadata = editor.plugins[key].getMetadata;\n            if (isFunction(getMetadata)) {\n                const metadata = getMetadata();\n                return { name: metadata.name, html: makeLink(metadata) };\n            }\n            else {\n                return { name: key, html: key };\n            }\n        };\n        const getPluginData = (editor, key) => find(urls, (x) => {\n            return x.key === key;\n        }).fold(() => {\n            return identifyUnknownPlugin(editor, key);\n        }, (x) => {\n            // We know this plugin, so use our stored details.\n            const name = x.type === \"premium\" /* PluginUrls.PluginType.Premium */ ? `${x.name}*` : x.name;\n            const html = makeLink({ name, url: `https://www.tiny.cloud/docs/tinymce/7/${x.slug}/` });\n            return { name, html };\n        });\n        const getPluginKeys = (editor) => {\n            const keys$1 = keys(editor.plugins);\n            const forcedPlugins = getForcedPlugins(editor);\n            const hiddenPlugins = isUndefined(forcedPlugins) ? ['onboarding'] : forcedPlugins.concat(['onboarding']);\n            return filter(keys$1, (k) => !contains(hiddenPlugins, k));\n        };\n        const pluginLister = (editor) => {\n            const pluginKeys = getPluginKeys(editor);\n            const sortedPluginData = sort(map(pluginKeys, (k) => getPluginData(editor, k)), (pd1, pd2) => pd1.name.localeCompare(pd2.name));\n            const pluginLis = map(sortedPluginData, (key) => {\n                return '<li>' + key.html + '</li>';\n            });\n            const count = pluginLis.length;\n            const pluginsString = pluginLis.join('');\n            const html = '<p><b>' + global$2.translate(['Plugins installed ({0}):', count]) + '</b></p>' +\n                '<ul>' + pluginsString + '</ul>';\n            return html;\n        };\n        const installedPlugins = (editor) => {\n            if (editor == null) {\n                return '';\n            }\n            return '<div>' +\n                pluginLister(editor) +\n                '</div>';\n        };\n        const htmlPanel = {\n            type: 'htmlpanel',\n            presets: 'document',\n            html: [\n                installedPlugins(editor),\n                availablePlugins()\n            ].join('')\n        };\n        return {\n            name: 'plugins',\n            title: 'Plugins',\n            items: [\n                htmlPanel\n            ]\n        };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    const tab = () => {\n        const getVersion = (major, minor) => major.indexOf('@') === 0 ? 'X.X.X' : major + '.' + minor;\n        const version = getVersion(global.majorVersion, global.minorVersion);\n        const changeLogLink = '<a data-alloy-tabstop=\"true\" tabindex=\"-1\" href=\"https://www.tiny.cloud/docs/tinymce/7/changelog/?utm_campaign=help_dialog_version_tab&utm_source=tiny&utm_medium=referral\" rel=\"noopener\" target=\"_blank\">TinyMCE ' + version + '</a>';\n        const htmlPanel = {\n            type: 'htmlpanel',\n            html: '<p>' + global$2.translate(['You are using {0}', changeLogLink]) + '</p>',\n            presets: 'document'\n        };\n        return {\n            name: 'versions',\n            title: 'Version',\n            items: [\n                htmlPanel\n            ]\n        };\n    };\n\n    const parseHelpTabsSetting = (tabsFromSettings, tabs) => {\n        const newTabs = {};\n        const names = map(tabsFromSettings, (t) => {\n            var _a;\n            if (isString(t)) {\n                // Code below shouldn't care if a tab name doesn't have a spec.\n                // If we find it does, we'll need to make this smarter.\n                // CustomTabsTest has a case for this.\n                if (has(tabs, t)) {\n                    newTabs[t] = tabs[t];\n                }\n                return t;\n            }\n            else {\n                const name = (_a = t.name) !== null && _a !== void 0 ? _a : generate('tab-name');\n                newTabs[name] = t;\n                return name;\n            }\n        });\n        return { tabs: newTabs, names };\n    };\n    const getNamesFromTabs = (tabs) => {\n        const names = keys(tabs);\n        // Move the versions tab to the end if it exists\n        const idx = names.indexOf('versions');\n        if (idx !== -1) {\n            names.splice(idx, 1);\n            names.push('versions');\n        }\n        return { tabs, names };\n    };\n    const pParseCustomTabs = async (editor, customTabs, pluginUrl) => {\n        const shortcuts = tab$2();\n        const nav = await pTab(pluginUrl);\n        const plugins = tab$1(editor);\n        const versions = tab();\n        const tabs = {\n            [shortcuts.name]: shortcuts,\n            [nav.name]: nav,\n            [plugins.name]: plugins,\n            [versions.name]: versions,\n            ...customTabs.get()\n        };\n        return Optional.from(getHelpTabs(editor)).fold(() => getNamesFromTabs(tabs), (tabsFromSettings) => parseHelpTabsSetting(tabsFromSettings, tabs));\n    };\n    const init = (editor, customTabs, pluginUrl) => () => {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        pParseCustomTabs(editor, customTabs, pluginUrl).then(({ tabs, names }) => {\n            const foundTabs = map(names, (name) => get$1(tabs, name));\n            const dialogTabs = cat(foundTabs);\n            const body = {\n                type: 'tabpanel',\n                tabs: dialogTabs\n            };\n            editor.windowManager.open({\n                title: 'Help',\n                size: 'medium',\n                body,\n                buttons: [\n                    {\n                        type: 'cancel',\n                        name: 'close',\n                        text: 'Close',\n                        primary: true\n                    }\n                ],\n                initialData: {}\n            });\n        });\n    };\n\n    var Plugin = () => {\n        global$4.add('help', (editor, pluginUrl) => {\n            const customTabs = Cell({});\n            const api = get(customTabs);\n            register$1(editor);\n            const dialogOpener = init(editor, customTabs, pluginUrl);\n            register(editor, dialogOpener);\n            register$2(editor, dialogOpener);\n            editor.shortcuts.add('Alt+0', 'Open help dialog', 'mceHelp');\n            initI18nLoad(editor, pluginUrl);\n            return api;\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,WAAW,OAAO;IACxB,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAEhC,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,SAAS;IAEvB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,gBAAgB,MAAM,SAAS,CAAC,OAAO;IAC7C,iBAAiB,GACjB,MAAM,aAAa,CAAC,IAAI,IAAM,cAAc,IAAI,CAAC,IAAI;IACrD,MAAM,WAAW,CAAC,IAAI,IAAM,WAAW,IAAI,KAAK,CAAC;IACjD,MAAM,MAAM,CAAC,IAAI;QACb,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,MAAM,SAAS,CAAC,IAAI;QAChB,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,EAAE,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,YAAY,CAAC,IAAI,MAAM;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO,SAAS,IAAI,CAAC;YACzB,OACK,IAAI,MAAM,GAAG,IAAI;gBAClB;YACJ;QACJ;QACA,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,OAAO,CAAC,IAAI;QACd,OAAO,UAAU,IAAI,MAAM;IAC/B;IACA,MAAM,OAAO,CAAC,IAAI;QACd,MAAM,OAAO,YAAY,IAAI,CAAC,IAAI;QAClC,KAAK,IAAI,CAAC;QACV,OAAO;IACX;IACA,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAE9D,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,QAAQ,CAAC,KAAK;QAChB,OAAO,IAAI,KAAK,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,SAAS,IAAI;IAClE;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IAEnD,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA;;;;KAIC,GACD,6NAA6N;IAC7N,MAAM,SAAS,IAAM,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE,GAAG;IAE5E;;;;;;;;;;KAUC,GACD,IAAI,SAAS;IACb,MAAM,WAAW,CAAC;QACd,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,OAAO;QACzB,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW;QACvC;QACA,OAAO,SAAS,MAAM,WAAW,SAAS,OAAO;IACrD;IAEA,MAAM,MAAM,CAAC;QACT,MAAM,IAAI,EAAE;QACZ,MAAM,OAAO,CAAC;YACV,EAAE,IAAI,CAAC;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,MAAM,CAAC;QACT,MAAM,SAAS,CAAC;YACZ,IAAI;YACJ,MAAM,OAAO,CAAC,KAAK,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,SAAS;YACxE,MAAM,oBAAoB,WAAW,GAAG;YACxC,iBAAiB,CAAC,KAAK,GAAG;YAC1B,WAAW,GAAG,CAAC;QACnB;QACA,OAAO;YACH;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,WAAW;IACjC;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,aAAa;YACxB,WAAW;QACf;IACJ;IACA,MAAM,cAAc,OAAO;IAC3B,MAAM,mBAAmB,OAAO;IAEhC,MAAM,WAAW,CAAC,QAAQ;QACtB,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ;YACjC,MAAM;YACN,SAAS;YACT,UAAU;YACV,SAAS;QACb;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ;YACnC,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,SAAS;QACb;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,MAAM,sBAAsB,CAAC,SAAS,WAAa,SAAS,IAAI,CAAC,AAAC,iCAAyC,OAAT,WAAY,AAAC,GAA4B,OAA1B,SAAQ,oBAA2B,OAAT,UAAS;IACpJ,MAAM,gBAAgB,CAAC,UACvB,kGAAkG;QAClG,oBAAoB,SAAS,SAAS,OAAO,IAAI,KAAK,CAAC,IAAM,oBAAoB,SAAS;IAC1F,MAAM,eAAe,CAAC,QAAQ;QAC1B,OAAO,EAAE,CAAC,QAAQ;YACd,mEAAmE;YACnE,cAAc;QAClB;IACJ;IAEA,MAAM,OAAO,OAAO;QAChB,MAAM,OAAO;YACT,MAAM;YACN,SAAS;YACT,MAAM,MAAM,cAAc;QAC9B;QACA,OAAO;YACH,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAK;QACjB;IACJ;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,8CAA8C;IAC9C,MAAM,cAAc,CAAC;QACjB,MAAM,QAAQ,SAAS,EAAE,CAAC,OAAO,MAAM,SAAS,EAAE,CAAC,KAAK;QACxD,MAAM,MAAM;YACR,KAAK;YACL,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;QACZ;QACA,MAAM,QAAQ;YACV,MAAM;YACN,QAAQ;QACZ;QACA,MAAM,UAAU,QAAQ,MAAM;QAC9B,MAAM,WAAW,OAAO,KAAK,CAAC;QAC9B,MAAM,UAAU,IAAI,UAAU,CAAC;YAC3B,sDAAsD;YACtD,MAAM,SAAS,QAAQ,WAAW,GAAG,IAAI;YACzC,OAAO,IAAI,SAAS,UAAU,OAAO,CAAC,OAAO,GAAG;QACpD;QACA,OAAO,QAAQ,AAAC,QAAQ,IAAI,CAAC,IAAK,OAAO,CAAC,MAAM,MAAM,QAAQ,IAAI,CAAC;IACvE;IAEA,MAAM,YAAY;QACd;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAO;QAC1C;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAS;QAC5C;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAY;QAC/C;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAa;QAChD;YAAE,WAAW;gBAAC;gBAAY;aAAmB;YAAE,QAAQ;QAAO;QAC9D;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAO;QAC1C;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAY;QACjD;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAM;QAC3C;YAAE,WAAW;gBAAC;aAAa;YAAE,QAAQ;QAAU;QAC/C;YAAE,WAAW;gBAAC;aAAU;YAAE,QAAQ;QAAmB;QACrD;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAmB;QACtD;YAAE,WAAW;gBAAC;aAAY;YAAE,QAAQ;QAAmB;QACvD;YAAE,WAAW;gBAAC;aAAY;YAAE,QAAQ;QAAwB;QAC5D;YAAE,WAAW;gBAAC;aAAY;YAAE,QAAQ;QAAwB;QAC5D;YAAE,WAAW;gBAAC;aAAY;YAAE,QAAQ;QAA8B;QAClE;YAAE,WAAW;gBAAC;aAAgB;YAAE,QAAQ;QAAoC;QAC5E;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAyC;QAC5E;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAAkC;QACrE;YAAE,WAAW;gBAAC;aAAW;YAAE,QAAQ;QAA2C;QAC9E;YAAE,WAAW;gBAAC;aAAmB;YAAE,QAAQ;QAAoC;KAClF;IAED,MAAM,QAAQ;QACV,MAAM,eAAe,IAAI,WAAW,CAAC;YACjC,MAAM,eAAe,IAAI,SAAS,SAAS,EAAE,aAAa,IAAI,CAAC;YAC/D,OAAO;gBAAC,SAAS,MAAM;gBAAE;aAAa;QAC1C;QACA,MAAM,aAAa;YACf,MAAM;YACN,oCAAoC;YACpC,QAAQ;gBAAC;gBAAU;aAAW;YAC9B,OAAO;QACX;QACA,OAAO;YACH,MAAM;YACN,OAAO;YACP,OAAO;gBACH;aACH;QACL;IACJ;IAEA,mEAAmE;IACnE,MAAM,OAAO,IAAI;QACb;YAAE,KAAK;YAAa,MAAM;QAAY;QACtC;YAAE,KAAK;YAAU,MAAM;QAAS;QAChC;YAAE,KAAK;YAAY,MAAM;QAAW;QACpC;YAAE,KAAK;YAAc,MAAM;QAAa;QACxC;YAAE,KAAK;YAAY,MAAM;QAAW;QACpC;YAAE,KAAK;YAAW,MAAM;QAAgB;QACxC;YAAE,KAAK;YAAQ,MAAM;QAAO;QAC5B;YAAE,KAAK;YAAc,MAAM;QAAc;QACzC;YAAE,KAAK;YAAe,MAAM;QAAe;QAC3C;YAAE,KAAK;YAAkB,MAAM;QAAiB;QAChD;YAAE,KAAK;YAAa,MAAM;QAAY;QACtC;YAAE,KAAK;YAAc,MAAM;QAAc;QACzC;YAAE,KAAK;YAAQ,MAAM;QAAO;QAC5B;YAAE,KAAK;YAAS,MAAM;QAAQ;QAC9B;YAAE,KAAK;YAAa,MAAM;QAAa;QACvC;YAAE,KAAK;YAAkB,MAAM;QAAmB;QAClD;YAAE,KAAK;YAAQ,MAAM;QAAO;QAC5B;YAAE,KAAK;YAAS,MAAM;QAAQ;QAC9B;YAAE,KAAK;YAAW,MAAM;QAAc;QACtC;YAAE,KAAK;YAAS,MAAM;QAAQ;QAC9B;YAAE,KAAK;YAAe,MAAM;QAAc;QAC1C;YAAE,KAAK;YAAa,MAAM;QAAa;QACvC;YAAE,KAAK;YAAW,MAAM;QAAU;QAClC;YAAE,KAAK;YAAa,MAAM;QAAiB;QAC3C;YAAE,KAAK;YAAQ,MAAM;QAAO;QAC5B;YAAE,KAAK;YAAiB,MAAM;QAAqB;QACnD;YAAE,KAAK;YAAS,MAAM;QAAQ;QAC9B;YAAE,KAAK;YAAa,MAAM;QAAa;QACvC;YAAE,KAAK;YAAgB,MAAM;QAAgB;QAC7C;YAAE,KAAK;YAAe,MAAM;QAAoB;QAChD;YAAE,KAAK;YAAa,MAAM;QAAa;QACvC,wEAAwE;QACxE;YAAE,KAAK;YAAe,MAAM;YAAyB,MAAM,UAAU,sBAAsB;QAAG;QAC9F;YAAE,KAAK;YAAc,MAAM;YAAuB,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAsB;QACxH;YAAE,KAAK;YAAM,MAAM;YAAgB,MAAM,UAAU,sBAAsB;QAAG;QAC5E;YAAE,KAAK;YAAc,MAAM;YAAe,MAAM,UAAU,sBAAsB;QAAG;QACnF;YAAE,KAAK;YAAa,MAAM;YAAa,MAAM,UAAU,sBAAsB;QAAG;QAChF;YAAE,KAAK;YAAW,MAAM;YAAwB,MAAM,UAAU,sBAAsB;QAAG;QACzF;YAAE,KAAK;YAAc,MAAM;YAAwB,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAA6B;QAChI;YAAE,KAAK;YAAY,MAAM;YAAmB,MAAM,UAAU,sBAAsB;QAAG;QACrF;YAAE,KAAK;YAAa,MAAM;YAAiB,MAAM,UAAU,sBAAsB;QAAG;QACpF;YAAE,KAAK;YAAc,MAAM;YAAkB,MAAM,UAAU,sBAAsB;QAAG;QACtF;YAAE,KAAK;YAAa,MAAM;YAAa,MAAM,UAAU,sBAAsB;QAAG;QAChF;YAAE,KAAK;YAAiB,MAAM;YAAkB,MAAM,UAAU,sBAAsB;QAAG;QACzF;YAAE,KAAK;YAAa,MAAM;YAAiB,MAAM,UAAU,sBAAsB;QAAG;QACpF;YAAE,KAAK;YAAc,MAAM;YAAyC,MAAM,UAAU,sBAAsB;QAAG;QAC7G;YAAE,KAAK;YAAc,MAAM;YAAoB,MAAM,UAAU,sBAAsB;QAAG;QACxF;YAAE,KAAK;YAAa,MAAM;YAAc,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAa;QACrG;YAAE,KAAK;YAAe,MAAM;YAAgB,MAAM,UAAU,sBAAsB;QAAG;QACrF;YAAE,KAAK;YAAQ,MAAM;YAAQ,MAAM,UAAU,sBAAsB;QAAG;QACtE;YAAE,KAAK;YAAY,MAAM;YAAY,MAAM,UAAU,sBAAsB;QAAG;QAC9E;YAAE,KAAK;YAAY,MAAM;YAAY,MAAM,UAAU,sBAAsB;QAAG;QAC9E;YAAE,KAAK;YAAa,MAAM;YAAc,MAAM,UAAU,sBAAsB;QAAG;QACjF;YAAE,KAAK;YAAa,MAAM;YAAc,MAAM,UAAU,sBAAsB;QAAG;QACjF;YAAE,KAAK;YAAgB,MAAM;YAAiB,MAAM,UAAU,sBAAsB;QAAG;QACvF;YAAE,KAAK;YAAc,MAAM;YAAc,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAA6B;QACtH;YAAE,KAAK;YAAmB,MAAM;YAAoB,MAAM,UAAU,sBAAsB;QAAG;QAC7F;YAAE,KAAK;YAAuB,MAAM;YAAiB,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAoC;QACzI;YAAE,KAAK;YAAe,MAAM;YAAwB,MAAM,UAAU,sBAAsB;QAAG;QAC7F;YAAE,KAAK;YAAmB,MAAM;YAAqB,MAAM,UAAU,sBAAsB;QAAG;QAC9F;YAAE,KAAK;YAAe,MAAM;YAAa,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAqB;QAC9G;YAAE,KAAK;YAAgB,MAAM;YAAiB,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAgC;QAC9H;YAAE,KAAK;YAAa,MAAM;YAAc,MAAM,UAAU,sBAAsB;YAAI,MAAM;QAAyB;KACpH,EAAE,CAAC,OAAS,CAAC;YACV,GAAG,IAAI;YACP,iDAAiD;YACjD,MAAM,KAAK,IAAI,IAAI,aAAa,yBAAyB;YACzD,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG;QAC/B,CAAC;IAED,MAAM,QAAQ,CAAC;QACX,MAAM,mBAAmB;YACrB,MAAM,iBAAiB,OAAO,MAAM;oBAAC,EAAE,IAAI,EAAE;gBACzC,OAAO,SAAS,UAAU,iCAAiC;YAC/D;YACA,MAAM,uBAAuB,KAAK,IAAI,gBAAgB,CAAC,IAAM,EAAE,IAAI,GAAG,CAAC,IAAI,KAAO,GAAG,aAAa,CAAC;YACnG,MAAM,oBAAoB,IAAI,sBAAsB,CAAC,aAAe,AAAC,OAAiB,OAAX,YAAW,UAAQ,IAAI,CAAC;YACnG,OAAO,UACH,WAAW,SAAS,SAAS,CAAC,sBAAsB,aACpD,SACA,oBACA,uCACA,2MACA,8CAA8C,SAAS,SAAS,CAAC,mBAAmB,cACpF,UACA;QACR;QACA,MAAM,WAAW,CAAC,IAAM,AAAC,oDAA4F,OAAzC,EAAE,GAAG,EAAC,qCAA0C,OAAP,EAAE,IAAI,EAAC;QAC5H,MAAM,wBAAwB,CAAC,QAAQ;YACnC,MAAM,cAAc,OAAO,OAAO,CAAC,IAAI,CAAC,WAAW;YACnD,IAAI,WAAW,cAAc;gBACzB,MAAM,WAAW;gBACjB,OAAO;oBAAE,MAAM,SAAS,IAAI;oBAAE,MAAM,SAAS;gBAAU;YAC3D,OACK;gBACD,OAAO;oBAAE,MAAM;oBAAK,MAAM;gBAAI;YAClC;QACJ;QACA,MAAM,gBAAgB,CAAC,QAAQ,MAAQ,KAAK,MAAM,CAAC;gBAC/C,OAAO,EAAE,GAAG,KAAK;YACrB,GAAG,IAAI,CAAC;gBACJ,OAAO,sBAAsB,QAAQ;YACzC,GAAG,CAAC;gBACA,kDAAkD;gBAClD,MAAM,OAAO,EAAE,IAAI,KAAK,UAAU,iCAAiC,MAAK,AAAC,GAAS,OAAP,EAAE,IAAI,EAAC,OAAK,EAAE,IAAI;gBAC7F,MAAM,OAAO,SAAS;oBAAE;oBAAM,KAAK,AAAC,yCAA+C,OAAP,EAAE,IAAI,EAAC;gBAAG;gBACtF,OAAO;oBAAE;oBAAM;gBAAK;YACxB;QACA,MAAM,gBAAgB,CAAC;YACnB,MAAM,SAAS,KAAK,OAAO,OAAO;YAClC,MAAM,gBAAgB,iBAAiB;YACvC,MAAM,gBAAgB,YAAY,iBAAiB;gBAAC;aAAa,GAAG,cAAc,MAAM,CAAC;gBAAC;aAAa;YACvG,OAAO,OAAO,QAAQ,CAAC,IAAM,CAAC,SAAS,eAAe;QAC1D;QACA,MAAM,eAAe,CAAC;YAClB,MAAM,aAAa,cAAc;YACjC,MAAM,mBAAmB,KAAK,IAAI,YAAY,CAAC,IAAM,cAAc,QAAQ,KAAK,CAAC,KAAK,MAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI;YAC7H,MAAM,YAAY,IAAI,kBAAkB,CAAC;gBACrC,OAAO,SAAS,IAAI,IAAI,GAAG;YAC/B;YACA,MAAM,QAAQ,UAAU,MAAM;YAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC;YACrC,MAAM,OAAO,WAAW,SAAS,SAAS,CAAC;gBAAC;gBAA4B;aAAM,IAAI,aAC9E,SAAS,gBAAgB;YAC7B,OAAO;QACX;QACA,MAAM,mBAAmB,CAAC;YACtB,IAAI,UAAU,MAAM;gBAChB,OAAO;YACX;YACA,OAAO,UACH,aAAa,UACb;QACR;QACA,MAAM,YAAY;YACd,MAAM;YACN,SAAS;YACT,MAAM;gBACF,iBAAiB;gBACjB;aACH,CAAC,IAAI,CAAC;QACX;QACA,OAAO;YACH,MAAM;YACN,OAAO;YACP,OAAO;gBACH;aACH;QACL;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,MAAM;QACR,MAAM,aAAa,CAAC,OAAO,QAAU,MAAM,OAAO,CAAC,SAAS,IAAI,UAAU,QAAQ,MAAM;QACxF,MAAM,UAAU,WAAW,OAAO,YAAY,EAAE,OAAO,YAAY;QACnE,MAAM,gBAAgB,wNAAwN,UAAU;QACxP,MAAM,YAAY;YACd,MAAM;YACN,MAAM,QAAQ,SAAS,SAAS,CAAC;gBAAC;gBAAqB;aAAc,IAAI;YACzE,SAAS;QACb;QACA,OAAO;YACH,MAAM;YACN,OAAO;YACP,OAAO;gBACH;aACH;QACL;IACJ;IAEA,MAAM,uBAAuB,CAAC,kBAAkB;QAC5C,MAAM,UAAU,CAAC;QACjB,MAAM,QAAQ,IAAI,kBAAkB,CAAC;YACjC,IAAI;YACJ,IAAI,SAAS,IAAI;gBACb,+DAA+D;gBAC/D,uDAAuD;gBACvD,sCAAsC;gBACtC,IAAI,IAAI,MAAM,IAAI;oBACd,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;gBACxB;gBACA,OAAO;YACX,OACK;gBACD,MAAM,OAAO,CAAC,KAAK,EAAE,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,SAAS;gBACrE,OAAO,CAAC,KAAK,GAAG;gBAChB,OAAO;YACX;QACJ;QACA,OAAO;YAAE,MAAM;YAAS;QAAM;IAClC;IACA,MAAM,mBAAmB,CAAC;QACtB,MAAM,QAAQ,KAAK;QACnB,gDAAgD;QAChD,MAAM,MAAM,MAAM,OAAO,CAAC;QAC1B,IAAI,QAAQ,CAAC,GAAG;YACZ,MAAM,MAAM,CAAC,KAAK;YAClB,MAAM,IAAI,CAAC;QACf;QACA,OAAO;YAAE;YAAM;QAAM;IACzB;IACA,MAAM,mBAAmB,OAAO,QAAQ,YAAY;QAChD,MAAM,YAAY;QAClB,MAAM,MAAM,MAAM,KAAK;QACvB,MAAM,UAAU,MAAM;QACtB,MAAM,WAAW;QACjB,MAAM,OAAO;YACT,CAAC,UAAU,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,EAAE;YACZ,CAAC,QAAQ,IAAI,CAAC,EAAE;YAChB,CAAC,SAAS,IAAI,CAAC,EAAE;YACjB,GAAG,WAAW,GAAG,EAAE;QACvB;QACA,OAAO,SAAS,IAAI,CAAC,YAAY,SAAS,IAAI,CAAC,IAAM,iBAAiB,OAAO,CAAC,mBAAqB,qBAAqB,kBAAkB;IAC9I;IACA,MAAM,OAAO,CAAC,QAAQ,YAAY,YAAc;YAC5C,mEAAmE;YACnE,iBAAiB,QAAQ,YAAY,WAAW,IAAI,CAAC;oBAAC,EAAE,IAAI,EAAE,KAAK,EAAE;gBACjE,MAAM,YAAY,IAAI,OAAO,CAAC,OAAS,MAAM,MAAM;gBACnD,MAAM,aAAa,IAAI;gBACvB,MAAM,OAAO;oBACT,MAAM;oBACN,MAAM;gBACV;gBACA,OAAO,aAAa,CAAC,IAAI,CAAC;oBACtB,OAAO;oBACP,MAAM;oBACN;oBACA,SAAS;wBACL;4BACI,MAAM;4BACN,MAAM;4BACN,MAAM;4BACN,SAAS;wBACb;qBACH;oBACD,aAAa,CAAC;gBAClB;YACJ;QACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,QAAQ,CAAC,QAAQ;YAC1B,MAAM,aAAa,KAAK,CAAC;YACzB,MAAM,MAAM,IAAI;YAChB,WAAW;YACX,MAAM,eAAe,KAAK,QAAQ,YAAY;YAC9C,SAAS,QAAQ;YACjB,WAAW,QAAQ;YACnB,OAAO,SAAS,CAAC,GAAG,CAAC,SAAS,oBAAoB;YAClD,aAAa,QAAQ;YACrB,OAAO;QACX;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/help/index.js"], "sourcesContent": ["// Exports the \"help\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/help')\n//   ES2015:\n//     import 'tinymce/plugins/help'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,SAAS;AACT,cAAc;AACd,sCAAsC;AACtC,YAAY;AACZ,oCAAoC", "ignoreList": [0], "debugId": null}}]}