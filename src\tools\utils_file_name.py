


import re
import unicodedata
from datetime import datetime

def slugify(text: str) -> str:
    """
    Nettoie une chaîne de texte pour générer un nom de fichier lisible et sûr.
    """
    text = unicodedata.normalize("NFKD", text).encode("ascii", "ignore").decode("utf-8")
    text = re.sub(r"[^\w\s-]", "", text.lower())
    return re.sub(r"[\s_]+", "-", text.strip())

def generate_newsletter_filename(theme: str) -> str:
    """
    Génère un nom de fichier type newsletter-theme-YYYYMMDD-HHMM.txt
    """
    base = slugify(theme)
    timestamp = datetime.now().strftime("%Y-%m-%d-%H%M")
    return f"newsletter-{base}-{timestamp}"
