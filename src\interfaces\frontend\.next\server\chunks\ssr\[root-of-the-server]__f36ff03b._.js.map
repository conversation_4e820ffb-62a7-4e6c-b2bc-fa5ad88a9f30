{"version": 3, "sources": [], "sections": [{"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,6HAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/FeedbackForm.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/client/components/ui/button\";\r\nimport { Textarea } from \"@/client/components/ui/textarea\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/client/components/ui/card\";\r\n\r\ninterface FeedbackFormProps {\r\n  onRevise: (feedback: string) => Promise<void>;\r\n}\r\n\r\nexport default function FeedbackForm({ onRevise }: FeedbackFormProps) {\r\n  const [feedback, setFeedback] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!feedback.trim()) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      await onRevise(feedback);\r\n      setFeedback(\"\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"text-lg\">Réviser la newsletter</CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n          <Textarea\r\n            placeholder=\"Entrez vos commentaires pour réviser la newsletter...\"\r\n            value={feedback}\r\n            onChange={(e) => setFeedback(e.target.value)}\r\n            rows={3}\r\n          />\r\n          <Button \r\n            type=\"submit\" \r\n            disabled={loading || !feedback.trim()}\r\n            className=\"w-full\"\r\n          >\r\n            {loading ? \"Révision en cours...\" : \"Réviser\"}\r\n          </Button>\r\n        </form>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// \"use client\";\r\n\r\n// import { useState } from \"react\";\r\n\r\n// export default function FeedbackForm({ onRevise }: { onRevise: (feedback: string) => Promise<void> }) {\r\n//   const [feedback, setFeedback] = useState(\"\");\r\n//   const [isLoading, setIsLoading] = useState(false);\r\n\r\n//   const handleRevise = async () => {\r\n//     setIsLoading(true);\r\n//     await onRevise(feedback);\r\n//     setIsLoading(false);\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"mt-4\">\r\n//       <textarea\r\n//         placeholder=\"Entrer vos remarques ici...\"\r\n//         value={feedback}\r\n//         onChange={(e) => setFeedback(e.target.value)}\r\n//         rows={3}\r\n//         className=\"w-full border p-3 rounded-md resize-none mb-2 text-gray-900 placeholder-gray-500\"\r\n//       />\r\n//       <button\r\n//         onClick={handleRevise}\r\n//         disabled={isLoading}\r\n//         className={`w-full py-2 rounded-md transition ${\r\n//           isLoading\r\n//             ? \"bg-green-400 cursor-not-allowed\"\r\n//             : \"bg-green-600 hover:bg-green-700 text-white\"\r\n//         }`}\r\n//       >\r\n//         {isLoading ? \"amélioration en cours\" : \"Proposer des améliorations\"}\r\n//       </button>\r\n//     </div>\r\n//   );\r\n// }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAMe,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,WAAW;QACX,IAAI;YACF,MAAM,SAAS;YACf,YAAY;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,OAAI;;0BACH,8OAAC,0IAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;oBAAC,WAAU;8BAAU;;;;;;;;;;;0BAEjC,8OAAC,0IAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC,8IAAA,CAAA,WAAQ;4BACP,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,MAAM;;;;;;sCAER,8OAAC,4IAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU,WAAW,CAAC,SAAS,IAAI;4BACnC,WAAU;sCAET,UAAU,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhD,EAgBA,gBAAgB;CAEhB,oCAAoC;CAEpC,0GAA0G;CAC1G,kDAAkD;CAClD,uDAAuD;CAEvD,uCAAuC;CACvC,0BAA0B;CAC1B,gCAAgC;CAChC,2BAA2B;CAC3B,OAAO;CAEP,aAAa;CACb,6BAA6B;CAC7B,kBAAkB;CAClB,oDAAoD;CACpD,2BAA2B;CAC3B,wDAAwD;CACxD,mBAAmB;CACnB,uGAAuG;CACvG,WAAW;CACX,gBAAgB;CAChB,iCAAiC;CACjC,+BAA+B;CAC/B,2DAA2D;CAC3D,sBAAsB;CACtB,kDAAkD;CAClD,6DAA6D;CAC7D,cAAc;CACd,UAAU;CACV,+EAA+E;CAC/E,kBAAkB;CAClB,aAAa;CACb,OAAO;CACP,IAAI", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/TinyMCEEditor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRef } from 'react';\r\nimport { Editor } from '@tinymce/tinymce-react';\r\n\r\ninterface TinyMCEEditorProps {\r\n  content: string;\r\n  onChange: (content: string) => void;\r\n  height?: number;\r\n}\r\n\r\nexport default function TinyMCEEditor({ content, onChange, height = 500 }: TinyMCEEditorProps) {\r\n  const editorRef = useRef<any>(null);\r\n\r\n  // Fonction pour optimiser le HTML pour les emails\r\n  const optimizeForEmail = (html: string): string => {\r\n    let optimized = html;\r\n\r\n    // Convertir les classes en styles inline\r\n    const styleConversions = [\r\n      [/class=\"cta-button\"/g, 'style=\"display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0;\"'],\r\n      [/<h1>/g, '<h1 style=\"color: #1a73e8; font-size: 28px; font-weight: bold; margin: 20px 0 10px 0;\">'],\r\n      [/<h2>/g, '<h2 style=\"color: #2563eb; font-size: 22px; font-weight: 600; margin: 15px 0 8px 0;\">'],\r\n      [/<h3>/g, '<h3 style=\"color: #333; font-size: 18px; font-weight: 600; margin: 12px 0 6px 0;\">'],\r\n      [/<p>/g, '<p style=\"color: #333; font-size: 16px; line-height: 1.6; margin: 10px 0;\">'],\r\n      [/<img([^>]*)>/g, '<img$1 style=\"max-width: 100%; height: auto; border-radius: 8px;\">'],\r\n    ];\r\n\r\n    styleConversions.forEach(([regex, replacement]) => {\r\n      optimized = optimized.replace(regex as RegExp, replacement as string);\r\n    });\r\n\r\n    return optimized;\r\n  };\r\n  return (\r\n    <div className=\"tinymce-container\">\r\n      <Editor\r\n        onInit={(evt, editor) => editorRef.current = editor}\r\n        initialValue={content}\r\n        init={{\r\n          height: height,\r\n          menubar: true,\r\n          plugins: [\r\n            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\r\n            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\r\n            'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',\r\n            'emoticons', 'template', 'codesample', 'textcolor', 'colorpicker'\r\n          ],\r\n          toolbar: [\r\n            'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table',\r\n            'align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat | code fullscreen preview'\r\n          ],\r\n          font_family_formats:\r\n            'Arial=Arial,Helvetica,sans-serif; ' +\r\n            'Georgia=Georgia,serif; ' +\r\n            'Helvetica=Helvetica,Arial,sans-serif; ' +\r\n            'Times New Roman=Times,Times New Roman,serif; ' +\r\n            'Verdana=Verdana,Geneva,sans-serif',\r\n          font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 36pt 48pt 72pt',\r\n\r\n          // Configuration spécifique pour les emails\r\n          content_style: `\r\n            body {\r\n              font-family: 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;\r\n              font-size: 16px;\r\n              line-height: 1.6;\r\n              color: #333;\r\n              max-width: 700px;\r\n              margin: 0 auto;\r\n              padding: 20px;\r\n            }\r\n            h1 { color: #1a73e8; font-size: 28px; font-weight: bold; }\r\n            h2 { color: #2563eb; font-size: 22px; font-weight: 600; }\r\n            h3 { color: #333; font-size: 18px; font-weight: 600; }\r\n            a { color: #1a73e8; }\r\n            img { max-width: 100%; height: auto; }\r\n            .cta-button {\r\n              display: inline-block;\r\n              padding: 12px 24px;\r\n              background: #1a73e8;\r\n              color: #fff !important;\r\n              text-decoration: none;\r\n              border-radius: 6px;\r\n              font-weight: 600;\r\n              margin: 10px 0;\r\n            }\r\n          `,\r\n\r\n          // Templates prédéfinis pour newsletters\r\n          templates: [\r\n            {\r\n              title: 'Titre + Texte + CTA',\r\n              description: 'Section avec titre, paragraphe et bouton',\r\n              content: `\r\n                <h2>Votre titre ici</h2>\r\n                <p>Votre contenu principal ici. Décrivez les points importants de votre newsletter.</p>\r\n                <div style=\"text-align: center; margin: 20px 0;\">\r\n                  <a href=\"#\" class=\"cta-button\">Votre bouton d'action</a>\r\n                </div>\r\n              `\r\n            },\r\n            {\r\n              title: 'Article avec image',\r\n              description: 'Section article avec image à gauche',\r\n              content: `\r\n                <table style=\"width: 100%; border-collapse: collapse;\">\r\n                  <tr>\r\n                    <td style=\"width: 150px; vertical-align: top; padding-right: 20px;\">\r\n                      <img src=\"https://via.placeholder.com/150x100\" alt=\"Image\" style=\"width: 100%; height: auto; border-radius: 8px;\">\r\n                    </td>\r\n                    <td style=\"vertical-align: top;\">\r\n                      <h3>Titre de l'article</h3>\r\n                      <p>Description de votre article ou actualité. Ajoutez ici les détails importants.</p>\r\n                      <a href=\"#\" style=\"color: #1a73e8;\">Lire la suite →</a>\r\n                    </td>\r\n                  </tr>\r\n                </table>\r\n              `\r\n            },\r\n            {\r\n              title: 'Liste de liens',\r\n              description: 'Liste de liens utiles',\r\n              content: `\r\n                <h3>📌 Liens utiles</h3>\r\n                <ul style=\"list-style: none; padding: 0;\">\r\n                  <li style=\"margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;\">\r\n                    <a href=\"#\" style=\"font-weight: 600;\">Titre du lien 1</a><br>\r\n                    <span style=\"color: #666; font-size: 14px;\">Description courte du lien</span>\r\n                  </li>\r\n                  <li style=\"margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;\">\r\n                    <a href=\"#\" style=\"font-weight: 600;\">Titre du lien 2</a><br>\r\n                    <span style=\"color: #666; font-size: 14px;\">Description courte du lien</span>\r\n                  </li>\r\n                </ul>\r\n              `\r\n            }\r\n          ],\r\n\r\n          // Configuration pour les emails (styles inline)\r\n          cleanup: true,\r\n          verify_html: false,\r\n          convert_urls: false,\r\n          remove_script_host: false,\r\n          relative_urls: false,\r\n\r\n          // Événements\r\n          setup: function(editor: any) {\r\n            // Bouton personnalisé pour CTA\r\n            editor.ui.registry.addButton('cta', {\r\n              text: 'CTA',\r\n              tooltip: 'Insérer un bouton Call-to-Action',\r\n              onAction: function() {\r\n                const text = prompt('Texte du bouton :') || 'Cliquez ici';\r\n                const url = prompt('URL du lien :') || '#';\r\n                editor.insertContent(`\r\n                  <div style=\"text-align: center; margin: 20px 0;\">\r\n                    <a href=\"${url}\" style=\"display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600;\">${text}</a>\r\n                  </div>\r\n                `);\r\n              }\r\n            });\r\n\r\n            // Bouton pour convertir en styles inline\r\n            editor.ui.registry.addButton('inlinestyles', {\r\n              text: 'Email',\r\n              tooltip: 'Optimiser pour email (styles inline)',\r\n              onAction: function() {\r\n                const content = editor.getContent();\r\n                const optimized = optimizeForEmail(content);\r\n                editor.setContent(optimized);\r\n              }\r\n            });\r\n          },\r\n\r\n          // Ajouter nos boutons personnalisés à la toolbar\r\n          toolbar1: 'undo redo | blocks fontfamily fontsize | bold italic underline | forecolor backcolor | link image cta inlinestyles',\r\n          toolbar2: 'align lineheight | checklist numlist bullist indent outdent | template | removeformat | code fullscreen preview'\r\n        }}\r\n        onEditorChange={(newContent) => onChange(newContent)}\r\n      />\r\n\r\n\r\n      <div className=\"mt-4 text-sm text-gray-600\">\r\n        <p><strong>💡 Conseils d'utilisation :</strong></p>\r\n        <ul className=\"list-disc list-inside space-y-1\">\r\n          <li>Utilisez les <strong>Templates</strong> pour des mises en page prêtes</li>\r\n          <li>Le bouton <strong>CTA</strong> insère des boutons d'action optimisés</li>\r\n          <li>Le bouton <strong>Email</strong> convertit automatiquement en styles inline</li>\r\n          <li>Prévisualisez avec <strong>Preview</strong> avant d'envoyer</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAFA;;;;AAWe,SAAS,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,GAAG,EAAsB;IAC3F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAE9B,kDAAkD;IAClD,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,yCAAyC;QACzC,MAAM,mBAAmB;YACvB;gBAAC;gBAAuB;aAA+K;YACvM;gBAAC;gBAAS;aAA0F;YACpG;gBAAC;gBAAS;aAAwF;YAClG;gBAAC;gBAAS;aAAqF;YAC/F;gBAAC;gBAAQ;aAA8E;YACvF;gBAAC;gBAAiB;aAAqE;SACxF;QAED,iBAAiB,OAAO,CAAC,CAAC,CAAC,OAAO,YAAY;YAC5C,YAAY,UAAU,OAAO,CAAC,OAAiB;QACjD;QAEA,OAAO;IACT;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,QAAQ,CAAC,KAAK,SAAW,UAAU,OAAO,GAAG;gBAC7C,cAAc;gBACd,MAAM;oBACJ,QAAQ;oBACR,SAAS;oBACT,SAAS;wBACP;wBAAW;wBAAY;wBAAS;wBAAQ;wBAAS;wBAAW;wBAC5D;wBAAU;wBAAiB;wBAAgB;wBAAQ;wBACnD;wBAAkB;wBAAS;wBAAS;wBAAQ;wBAAQ;wBACpD;wBAAa;wBAAY;wBAAc;wBAAa;qBACrD;oBACD,SAAS;wBACP;wBACA;qBACD;oBACD,qBACE,uCACA,4BACA,2CACA,kDACA;oBACF,mBAAmB;oBAEnB,2CAA2C;oBAC3C,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;UAyBhB,CAAC;oBAED,wCAAwC;oBACxC,WAAW;wBACT;4BACE,OAAO;4BACP,aAAa;4BACb,SAAS,CAAC;;;;;;cAMV,CAAC;wBACH;wBACA;4BACE,OAAO;4BACP,aAAa;4BACb,SAAS,CAAC;;;;;;;;;;;;;cAaV,CAAC;wBACH;wBACA;4BACE,OAAO;4BACP,aAAa;4BACb,SAAS,CAAC;;;;;;;;;;;;cAYV,CAAC;wBACH;qBACD;oBAED,gDAAgD;oBAChD,SAAS;oBACT,aAAa;oBACb,cAAc;oBACd,oBAAoB;oBACpB,eAAe;oBAEf,aAAa;oBACb,OAAO,SAAS,MAAW;wBACzB,+BAA+B;wBAC/B,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO;4BAClC,MAAM;4BACN,SAAS;4BACT,UAAU;gCACR,MAAM,OAAO,OAAO,wBAAwB;gCAC5C,MAAM,MAAM,OAAO,oBAAoB;gCACvC,OAAO,aAAa,CAAC,CAAC;;6BAET,EAAE,IAAI,+JAA+J,EAAE,KAAK;;gBAEzL,CAAC;4BACH;wBACF;wBAEA,yCAAyC;wBACzC,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,gBAAgB;4BAC3C,MAAM;4BACN,SAAS;4BACT,UAAU;gCACR,MAAM,UAAU,OAAO,UAAU;gCACjC,MAAM,YAAY,iBAAiB;gCACnC,OAAO,UAAU,CAAC;4BACpB;wBACF;oBACF;oBAEA,iDAAiD;oBACjD,UAAU;oBACV,UAAU;gBACZ;gBACA,gBAAgB,CAAC,aAAe,SAAS;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAE,cAAA,8OAAC;sCAAO;;;;;;;;;;;kCACX,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;;oCAAG;kDAAa,8OAAC;kDAAO;;;;;;oCAAkB;;;;;;;0CAC3C,8OAAC;;oCAAG;kDAAU,8OAAC;kDAAO;;;;;;oCAAY;;;;;;;0CAClC,8OAAC;;oCAAG;kDAAU,8OAAC;kDAAO;;;;;;oCAAc;;;;;;;0CACpC,8OAAC;;oCAAG;kDAAmB,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAKzD", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/TinyMCEGuide.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Card } from '@/client/components/ui/card';\r\nimport { Button } from '@/client/components/ui/button';\r\nimport { ChevronDown, ChevronRight, HelpCircle } from 'lucide-react';\r\n\r\nexport default function TinyMCEGuide() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const features = [\r\n    {\r\n      title: \"📝 Templates prédéfinis\",\r\n      description: \"Utilisez le menu 'Templates' pour insérer des mises en page toutes prêtes (Titre+CTA, Article avec image, etc.)\"\r\n    },\r\n    {\r\n      title: \"🎨 Formatage avancé\",\r\n      description: \"Police, taille, couleurs, alignement - tout comme dans Word ! Les couleurs sont automatiquement optimisées pour l'email.\"\r\n    },\r\n    {\r\n      title: \"🔗 Bouton CTA magique\",\r\n      description: \"Le bouton 'CTA' dans la barre d'outils crée automatiquement des boutons d'action optimisés pour les newsletters.\"\r\n    },\r\n    {\r\n      title: \"📧 Mode Email\",\r\n      description: \"Le bouton 'Email' convertit automatiquement votre contenu avec des styles inline compatibles avec tous les clients email.\"\r\n    },\r\n    {\r\n      title: \"🖼️ Images et médias\",\r\n      description: \"Insérez facilement des images, tableaux et liens. Les images sont automatiquement redimensionnées.\"\r\n    },\r\n    {\r\n      title: \"👀 Prévisualisation\",\r\n      description: \"Utilisez 'Preview' pour voir exactement à quoi ressemblera votre newsletter avant de l'envoyer.\"\r\n    }\r\n  ];\r\n\r\n  const tips = [\r\n    \"Commencez par choisir un template pour gagner du temps\",\r\n    \"Utilisez les titres H1/H2 pour structurer votre contenu\",\r\n    \"Ajoutez des boutons CTA colorés pour encourager l'action\",\r\n    \"Prévisualisez toujours avant de sauvegarder\",\r\n    \"Le bouton 'Email' optimise automatiquement pour l'envoi\"\r\n  ];\r\n\r\n  return (\r\n    <Card className=\"mb-4\">\r\n      <Button\r\n        variant=\"ghost\"\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"w-full justify-between p-4 h-auto\"\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <HelpCircle className=\"w-5 h-5 text-blue-600\" />\r\n          <span className=\"font-medium\">Guide d'utilisation TinyMCE</span>\r\n        </div>\r\n        {isOpen ? <ChevronDown className=\"w-4 h-4\" /> : <ChevronRight className=\"w-4 h-4\" />}\r\n      </Button>\r\n      \r\n      {isOpen && (\r\n        <div className=\"px-4 pb-4 space-y-4\">\r\n          <div>\r\n            <h4 className=\"font-semibold text-green-700 mb-2\">✨ Fonctionnalités principales</h4>\r\n            <div className=\"grid gap-3\">\r\n              {features.map((feature, index) => (\r\n                <div key={index} className=\"p-3 bg-green-50 rounded-lg\">\r\n                  <div className=\"font-medium text-sm text-green-800\">{feature.title}</div>\r\n                  <div className=\"text-xs text-green-700 mt-1\">{feature.description}</div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h4 className=\"font-semibold text-blue-700 mb-2\">💡 Conseils pratiques</h4>\r\n            <ul className=\"space-y-2\">\r\n              {tips.map((tip, index) => (\r\n                <li key={index} className=\"flex items-start gap-2 text-sm text-blue-700\">\r\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\r\n                  <span>{tip}</span>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n          \r\n          <div className=\"p-3 bg-yellow-50 rounded-lg border border-yellow-200\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-yellow-600\">⚠️</span>\r\n              <div className=\"text-sm\">\r\n                <div className=\"font-medium text-yellow-800\">Important pour les emails</div>\r\n                <div className=\"text-yellow-700\">Cliquez sur le bouton 'Email' avant de sauvegarder pour garantir la compatibilité avec tous les clients email (Gmail, Outlook, etc.)</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,OAAO;QACX;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC,0IAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,4IAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;oBAE/B,uBAAS,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAAe,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;YAGzE,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAsC,QAAQ,KAAK;;;;;;0DAClE,8OAAC;gDAAI,WAAU;0DAA+B,QAAQ,WAAW;;;;;;;uCAFzD;;;;;;;;;;;;;;;;kCAQhB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAG,WAAU;0CACX,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;kCAQf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/EditableNewsletter.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { Button } from '@/client/components/ui/button';\r\nimport TinyMCEEditor from './TinyMCEEditor';\r\nimport TinyMCEGuide from './TinyMCEGuide';\r\n\r\n\r\n\r\n// ===== Wrapper visuel pour l’aperçu (facultatif) =====\r\nconst WRAP_START =\r\n  `<div class=\"newsletter-container\" style=\"font-family:'Segoe UI', Roboto, sans-serif;max-width:700px;margin:auto;padding:30px;background-color:#ffffff;color:#333;\">`;\r\nconst WRAP_END = `</div>`;\r\n\r\nif (typeof window !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.innerHTML = `\r\n    .newsletter-container img { display:block; margin:auto; }\r\n    .newsletter-container h1,.newsletter-container h2,.newsletter-container h3 { color:#1a73e8; }\r\n    .newsletter-container a { color:#1a73e8; text-decoration:underline; }\r\n  `;\r\n  document.head.appendChild(style);\r\n}\r\n\r\ninterface Props {\r\n  initialContent: string;\r\n  theme: string;\r\n  onSave: (content: string) => void;\r\n  editing: boolean;\r\n  onEditingChange: (editing: boolean) => void;\r\n}\r\n\r\nexport default function EditableNewsletter({\r\n  initialContent,\r\n  theme,\r\n  onSave,\r\n  editing,\r\n  onEditingChange,\r\n}: Props) {\r\n  const [content, setContent] = useState(initialContent);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setContent(initialContent);\r\n  }, [initialContent]);\r\n\r\n  const handleSave = async () => {\r\n    setIsSaving(true);\r\n    try {\r\n      const res = await fetch('http://localhost:8000/save-newsletter', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ theme, content }),\r\n      });\r\n      \r\n      if (!res.ok) {\r\n        const errorData = await res.json().catch(() => ({}));\r\n        throw new Error(errorData.detail || `Erreur ${res.status}: ${res.statusText}`);\r\n      }\r\n      \r\n      const data = await res.json();\r\n      \r\n      // Vérifier que le contenu révisé est valide\r\n      if (data.content && typeof data.content === 'string' && data.content.trim()) {\r\n        onSave(data.content);\r\n      } else {\r\n        // Si pas de contenu révisé, utiliser le contenu original\r\n        console.warn('Aucun contenu révisé reçu, utilisation du contenu original');\r\n        onSave(content);\r\n      }\r\n      \r\n      onEditingChange(false);\r\n    } catch (e: any) {\r\n      console.error('Erreur lors de la sauvegarde:', e);\r\n      alert(`Erreur lors de l'enregistrement: ${e.message || e}`);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setContent(initialContent);\r\n    onEditingChange(false);\r\n  };\r\n\r\n  // Fonction pour nettoyer le HTML et éviter les erreurs d'affichage\r\n  const sanitizeContent = (html: string): string => {\r\n    if (!html || typeof html !== 'string') return '';\r\n    \r\n    // Supprimer les scripts potentiellement dangereux\r\n    let cleaned = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '');\r\n    \r\n    // Supprimer les balises on* (événements JavaScript)\r\n    cleaned = cleaned.replace(/\\s*on\\w+\\s*=\\s*[\"'][^\"']*[\"']/gi, '');\r\n    \r\n    // Supprimer les balises style avec du JavaScript\r\n    cleaned = cleaned.replace(/<style[^>]*>[\\s\\S]*?<\\/style>/gi, '');\r\n    \r\n    return cleaned;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {!editing ? (\r\n        <div \r\n          dangerouslySetInnerHTML={{ \r\n            __html: WRAP_START + sanitizeContent(content) + WRAP_END \r\n          }} \r\n        />\r\n      ) : (\r\n        <div>\r\n          {/* Guide d'utilisation pour TinyMCE */}\r\n          <TinyMCEGuide />\r\n          \r\n          {/* Éditeur TinyMCE */}\r\n          <TinyMCEEditor content={content} onChange={setContent} />\r\n          \r\n          <div className=\"flex gap-4 mt-4\">\r\n            <Button \r\n              onClick={handleSave} \r\n              disabled={isSaving}\r\n              className={`relative overflow-hidden transition-all duration-300 ${\r\n                isSaving \r\n                  ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse' \r\n                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'\r\n              } text-white shadow-lg hover:shadow-xl transform hover:scale-105`}\r\n            >\r\n              {isSaving ? (\r\n                <>\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse\"></div>\r\n                  <div className=\"relative flex items-center gap-2\">\r\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                    <span>Enregistrement...</span>\r\n                  </div>\r\n                </>\r\n              ) : (\r\n                <span>Enregistrer</span>\r\n              )}\r\n            </Button>\r\n            <Button onClick={handleCancel} variant=\"outline\" disabled={isSaving}>Annuler</Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AASA,wDAAwD;AACxD,MAAM,aACJ,CAAC,mKAAmK,CAAC;AACvK,MAAM,WAAW,CAAC,MAAM,CAAC;AAEzB;;AAkBe,SAAS,mBAAmB,EACzC,cAAc,EACd,KAAK,EACL,MAAM,EACN,OAAO,EACP,eAAe,EACT;IACN,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG;QAAC;KAAe;IAEnB,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,yCAAyC;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAQ;YACxC;YAEA,IAAI,CAAC,IAAI,EAAE,EAAE;gBACX,MAAM,YAAY,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBAClD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;YAC/E;YAEA,MAAM,OAAO,MAAM,IAAI,IAAI;YAE3B,4CAA4C;YAC5C,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,YAAY,KAAK,OAAO,CAAC,IAAI,IAAI;gBAC3E,OAAO,KAAK,OAAO;YACrB,OAAO;gBACL,yDAAyD;gBACzD,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,gBAAgB;QAClB,EAAE,OAAO,GAAQ;YACf,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,CAAC,iCAAiC,EAAE,EAAE,OAAO,IAAI,GAAG;QAC5D,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,gBAAgB;IAClB;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,kDAAkD;QAClD,IAAI,UAAU,KAAK,OAAO,CAAC,uDAAuD;QAElF,oDAAoD;QACpD,UAAU,QAAQ,OAAO,CAAC,mCAAmC;QAE7D,iDAAiD;QACjD,UAAU,QAAQ,OAAO,CAAC,mCAAmC;QAE7D,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,CAAC,wBACA,8OAAC;YACC,yBAAyB;gBACvB,QAAQ,aAAa,gBAAgB,WAAW;YAClD;;;;;iCAGF,8OAAC;;8BAEC,8OAAC,4IAAA,CAAA,UAAY;;;;;8BAGb,8OAAC,6IAAA,CAAA,UAAa;oBAAC,SAAS;oBAAS,UAAU;;;;;;8BAE3C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4IAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,qDAAqD,EAC/D,WACI,kGACA,mFACL,+DAA+D,CAAC;sCAEhE,yBACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;6DAIV,8OAAC;0CAAK;;;;;;;;;;;sCAGV,8OAAC,4IAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,SAAQ;4BAAU,UAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAMjF", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, ChangeEvent, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/client/components/ui/tabs\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/client/components/ui/card\";\r\nimport { <PERSON><PERSON> } from \"@/client/components/ui/button\";\r\nimport { Input } from \"@/client/components/ui/input\";\r\nimport { Textarea } from \"@/client/components/ui/textarea\";\r\nimport { Badge } from \"@/client/components/ui/badge\";\r\nimport { \r\n  PenTool, \r\n  Eye, \r\n  Send, \r\n  FileText, \r\n  Upload, \r\n  Mail,\r\n  Sparkles,\r\n  Clock,\r\n  CheckCircle,\r\n  Trash2,\r\n  AlertCircle\r\n} from \"lucide-react\";\r\nimport NewsletterDisplay from \"@/client/components/NewsletterDisplay\";\r\nimport FeedbackForm from \"@/client/components/FeedbackForm\";\r\nimport CopyButton from \"@/client/components/CopyButton\";\r\nimport EditableNewsletter from '@/client/components/EditableNewsletter';\r\n\r\nexport default function NewsletterApp() {\r\n  // Generation tab state\r\n  const [theme, setTheme] = useState(\"\");\r\n  const [description, setDescription] = useState(\"\");\r\n  const [newsletter, setNewsletter] = useState(\"\");\r\n  const [revised, setRevised] = useState(\"\");\r\n  const [file, setFile] = useState<File | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showFileUpload, setShowFileUpload] = useState(false);\r\n  const content = revised || newsletter;\r\n  const [isEditing, setIsEditing] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchNewsletters();\r\n  }, []);\r\n \r\n  // Visualization tab state - real API data\r\n  const [newsletters, setNewsletters] = useState<string[]>([]);\r\n  const [selectedFile, setSelectedFile] = useState<string | null>(null);\r\n  const [selectedContent, setSelectedContent] = useState<string>(\"\");\r\n\r\n  // Sending tab state\r\n  const [csvFile, setCsvFile] = useState<File | null>(null);\r\n  const [sendingProgress, setSendingProgress] = useState(0);\r\n  const [isSending, setIsSending] = useState(false);\r\n  const [status, setStatus] = useState<string>(\"\");\r\n\r\n  const fetchNewsletters = async () => {\r\n    try {\r\n      const res = await axios.get(\"http://localhost:8000/list-newsletters\");\r\n      setNewsletters(res.data.files || []);\r\n    } catch {\r\n      setStatus(\"Erreur lors du chargement des newsletters.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (filename: string) => {\r\n    try {\r\n      const res = await axios.get(`http://localhost:8000/static/${filename}`, {\r\n        responseType: \"text\",\r\n      });\r\n      setSelectedFile(filename);\r\n      setSelectedContent(res.data);\r\n    } catch {\r\n      setStatus(\"Erreur lors de l'affichage du contenu.\");\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (filename: string) => {\r\n    if (!confirm(`Supprimer \"${filename}\" ?`)) return;\r\n\r\n    try {\r\n      await axios.delete(\"http://localhost:8000/delete-newsletter\", {\r\n        params: { filename },\r\n      });\r\n      setStatus(`\"${filename}\" supprimée.`);\r\n      fetchNewsletters(); // recharger la liste\r\n      if (selectedFile === filename) {\r\n        setSelectedFile(null);\r\n        setSelectedContent(\"\");\r\n      }\r\n    } catch (e: any) {\r\n      setStatus(\"Erreur de suppression : \" + (e.response?.data?.detail || e.message));\r\n    }\r\n  };\r\n\r\n  const generateFromWeb = async () => {\r\n    if (!theme || !description) {\r\n      alert(\"Thème et description requis.\");\r\n      return;\r\n    }\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/generate-web\", {\r\n        theme,\r\n        description,\r\n      });\r\n      setNewsletter(res.data.newsletter);\r\n      setRevised(\"\");\r\n      // Refresh the newsletter list to include the new one\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la génération depuis le web.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const generateFromFile = async () => {\r\n    if (!theme || !file || !description) {\r\n      alert(\"Veuillez fournir un thème, description et sélectionner un fichier.\");\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"theme\", theme);\r\n    formData.append(\"description\", description);\r\n    formData.append(\"file\", file);\r\n\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/generate-file\", formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      setNewsletter(res.data.newsletter);\r\n      setRevised(\"\");\r\n      // Refresh the newsletter list to include the new one\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la génération depuis un fichier.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const reviseNewsletter = async (feedback: string) => {\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/revise\", {\r\n        feedback,\r\n        theme,\r\n        description,\r\n      });\r\n      setRevised(res.data.revised_newsletter);\r\n      setNewsletter(\"\"); // on efface l'ancienne version\r\n      \r\n      // Recharger la liste des newsletters pour l'onglet visualisation\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la révision.\");\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      setFile(e.target.files[0]);\r\n    }\r\n  };\r\n\r\n  const handleCsvFileChange = (e: ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      setCsvFile(e.target.files[0]);\r\n    }\r\n  };\r\n\r\n  const sendNewsletter = async () => {\r\n    if (!selectedFile || !csvFile) {\r\n      alert(\"Veuillez sélectionner une newsletter et un fichier CSV.\");\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"newsletter_file\", selectedFile);\r\n    formData.append(\"csv_file\", csvFile);\r\n\r\n    setIsSending(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/send-newsletter\", formData);\r\n      setStatus(res.data.detail);\r\n      alert(\"Newsletter envoyée avec succès !\");\r\n    } catch (e: any) {\r\n      setStatus(\"Erreur lors de l'envoi : \" + (e.response?.data?.detail || e.message));\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  const handleSaveNewsletter = (updatedContent: string) => {\r\n    // Vérifier que le contenu est valide\r\n    if (!updatedContent || typeof updatedContent !== 'string') {\r\n      console.error('Contenu invalide reçu:', updatedContent);\r\n      alert('Erreur: Contenu invalide reçu');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      // Mettre à jour le contenu dans la génération\r\n      if (revised) {\r\n        setRevised(updatedContent);\r\n      } else {\r\n        setNewsletter(updatedContent);\r\n      }\r\n      // Recharger la liste des newsletters\r\n      fetchNewsletters();\r\n    } catch (error) {\r\n      console.error('Erreur lors de la mise à jour du contenu:', error);\r\n      alert('Erreur lors de la mise à jour du contenu');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        {/* Header */}\r\n        <div className=\"relative mb-12\">\r\n          {/* Background gradient */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-black/60 via-gray-900/40 to-blue-900/30 rounded-3xl\"></div>\r\n          \r\n          {/* Header content */}\r\n          <div className=\"relative flex flex-col lg:flex-row items-center justify-between p-8 lg:p-12\">\r\n            {/* Logo and title section */}\r\n            <div className=\"flex items-center gap-6 mb-6 lg:mb-0\">\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-black via-gray-900 to-blue-900 blur-lg opacity-40\"></div>\r\n                <img\r\n                  src=\"http://holokia.com/wp-content/uploads/2025/04/logo_holokia_noir.png\"\r\n                  alt=\"Holokia\"\r\n                  className=\"relative h-24 w-auto object-contain drop-shadow-2xl\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-3xl lg:text-5xl font-bold text-white mb-2\">\r\n                  Newsletter\r\n                  <span className=\"bg-gradient-to-r from-blue-300 via-gray-200 to-white bg-clip-text text-transparent\"> IA</span>\r\n                </h1>\r\n                <p className=\"text-gray-300 text-sm lg:text-base\">\r\n                  Générateur intelligent par Holokia\r\n                </p>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Decorative elements */}\r\n            <div className=\"hidden lg:flex items-center gap-4\">\r\n              <div className=\"flex space-x-2\">\r\n                <div className=\"w-3 h-3 bg-blue-400 rounded-full animate-pulse\"></div>\r\n                <div className=\"w-3 h-3 bg-gray-500 rounded-full animate-pulse\" style={{animationDelay: '0.2s'}}></div>\r\n                <div className=\"w-3 h-3 bg-black rounded-full animate-pulse\" style={{animationDelay: '0.4s'}}></div>\r\n              </div>\r\n              <div className=\"h-8 w-px bg-gradient-to-b from-blue-400 via-gray-400 to-transparent\"></div>\r\n              <div className=\"text-xs text-gray-400 font-medium\">\r\n                Powered by AI\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Bottom accent */}\r\n          <div className=\"h-1 bg-gradient-to-r from-black via-blue-600 to-gray-500 rounded-full mx-8 lg:mx-12\"></div>\r\n        </div>\r\n\r\n        {/* Main Tabs Interface */}\r\n        <Tabs defaultValue=\"generation\" className=\"w-full max-w-7xl mx-auto\">\r\n          <TabsList className=\"grid w-full grid-cols-3 mb-8 bg-black/50 backdrop-blur-md border-gray-600/50\">\r\n            <TabsTrigger value=\"generation\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <PenTool className=\"w-5 h-5 mr-2\" />\r\n              Génération\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"visualization\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <Eye className=\"w-5 h-5 mr-2\" />\r\n              Visualisation\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"sending\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <Send className=\"w-5 h-5 mr-2\" />\r\n              Envoi\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Generation Tab */}\r\n          <TabsContent value=\"generation\" className=\"space-y-6\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n              {/* Form Column - 1/3 */}\r\n              <Card className=\"lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"w-5 h-5 text-blue-700\" />\r\n                    Paramètres de génération\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium mb-2\">Thème de la newsletter</label>\r\n                    <Input\r\n                      placeholder=\"Ex: Technologie, Marketing, Santé...\"\r\n                      value={theme}\r\n                      onChange={(e) => setTheme(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  \r\n                  <div>\r\n                    <label className=\"block text-sm font-medium mb-2\">Description détaillée</label>\r\n                    <Textarea\r\n                      placeholder=\"Décrivez le contenu souhaité pour votre newsletter...\"\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      rows={4}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <Button\r\n                      onClick={generateFromWeb}\r\n                      disabled={loading}\r\n                      className=\"w-full bg-gradient-to-r from-black via-blue-900 to-gray-800 hover:from-gray-900 hover:via-blue-800 hover:to-black\"\r\n                      size=\"lg\"\r\n                    >\r\n                      {loading ? \"Génération en cours...\" : \"Générer via web\"}\r\n                    </Button>\r\n\r\n                    <Button\r\n                      onClick={() => setShowFileUpload(!showFileUpload)}\r\n                      variant=\"outline\"\r\n                      className=\"w-full\"\r\n                      size=\"lg\"\r\n                    >\r\n                      <Upload className=\"w-4 h-4 mr-2\" />\r\n                      Charger un fichier\r\n                    </Button>\r\n\r\n                    {showFileUpload && (\r\n                      <Card className=\"border-dashed\">\r\n                        <CardContent className=\"pt-6\">\r\n                          <div className=\"space-y-4\">\r\n                            <div>\r\n                              <label className=\"block text-sm font-medium mb-2\">\r\n                                Sélectionnez un fichier :\r\n                              </label>\r\n                              <Input\r\n                                type=\"file\"\r\n                                accept=\".txt,.pdf,.doc,.docx,.rtf,.md\"\r\n                                onChange={handleFileChange}\r\n                              />\r\n                              {file && (\r\n                                <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                                  <FileText className=\"w-4 h-4\" />\r\n                                  Fichier sélectionné : <strong>{file.name}</strong>\r\n                                </p>\r\n                              )}\r\n                            </div>\r\n                            <Button\r\n                              onClick={generateFromFile}\r\n                              disabled={loading || !file}\r\n                              className=\"w-full\"\r\n                            >\r\n                              {loading ? \"Génération en cours...\" : \"Générer via fichier\"}\r\n                            </Button>\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n             {/* Preview Column - 2/3 */}\r\n<Card className=\"lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n  <CardHeader className=\"flex justify-between items-center\">\r\n    <CardTitle>Aperçu de la newsletter</CardTitle>\r\n    {content && (\r\n      <Button\r\n        variant=\"outline\"\r\n        onClick={() => setIsEditing(!isEditing)}\r\n        className=\"text-sm\"\r\n      >\r\n        {isEditing ? \"Annuler\" : \"Modifier\"}\r\n      </Button>\r\n    )}\r\n  </CardHeader>\r\n  <CardContent>\r\n    {content ? (\r\n      <div className=\"space-y-4\">\r\n        <EditableNewsletter\r\n          initialContent={content}\r\n          theme={theme}\r\n          onSave={handleSaveNewsletter}\r\n          editing={isEditing}\r\n          onEditingChange={setIsEditing}\r\n        />\r\n\r\n        <div className=\"space-y-3\">\r\n          <FeedbackForm onRevise={reviseNewsletter} />\r\n        </div>\r\n      </div>\r\n    ) : (\r\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n        <div className=\"w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200\">\r\n          <FileText className=\"w-8 h-8 text-blue-400\" />\r\n        </div>\r\n        <p className=\"text-blue-600\">Aucune newsletter générée</p>\r\n        <p className=\"text-sm text-blue-500\">\r\n          Remplissez le formulaire et cliquez sur générer\r\n        </p>\r\n      </div>\r\n    )}\r\n  </CardContent>\r\n</Card>\r\n\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Visualization Tab */}\r\n          <TabsContent value=\"visualization\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n              {/* Liste des newsletters - 1/3 */}\r\n              <Card className=\"lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <FileText className=\"w-5 h-5\" />\r\n                    Newsletters disponibles\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-2\">\r\n                    {newsletters.length > 0 ? (\r\n                      newsletters.map((file) => (\r\n                        <div key={file} className=\"flex items-center justify-between p-3 bg-blue-50/80 rounded-lg border border-blue-100 hover:bg-blue-100/60 transition-colors\">\r\n                          <span className=\"truncate max-w-[60%] font-medium\">{file}</span>\r\n                          <div className=\"flex gap-2\">\r\n                            <Button\r\n                              onClick={() => handleView(file)}\r\n                              size=\"sm\"\r\n                              variant=\"outline\"\r\n                            >\r\n                              <Eye className=\"w-4 h-4 mr-1\" />\r\n                              Voir\r\n                            </Button>\r\n                            <Button\r\n                              onClick={() => handleDelete(file)}\r\n                              size=\"sm\"\r\n                              variant=\"destructive\"\r\n                            >\r\n                              <Trash2 className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </div>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div className=\"text-center py-8 text-blue-600\">\r\n                        <FileText className=\"w-12 h-12 mx-auto mb-3 text-blue-300\" />\r\n                        <p>Aucune newsletter disponible</p>\r\n                        <p className=\"text-sm text-blue-500\">Générez votre première newsletter dans l'onglet \"Génération\"</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Aperçu de la newsletter sélectionnée - 2/3 */}\r\n              <Card className=\"lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-5 h-5\" />\r\n                    Aperçu\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {selectedFile ? (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"flex items-center gap-2 p-2 bg-blue-100/80 rounded-lg border border-blue-200\">\r\n                        <FileText className=\"w-4 h-4 text-blue-700\" />\r\n                        <span className=\"font-medium text-blue-900\">{selectedFile}</span>\r\n                      </div>\r\n                      <div className=\"max-h-96 overflow-auto border border-blue-200 rounded-lg p-4 bg-white/95 backdrop-blur-sm\">\r\n                        <div \r\n                          className=\"prose prose-sm max-w-none\"\r\n                          dangerouslySetInnerHTML={{ __html: selectedContent }}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n                      <div className=\"w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200\">\r\n                        <Eye className=\"w-8 h-8 text-blue-400\" />\r\n                      </div>\r\n                      <p className=\"text-blue-600\">Aucune newsletter sélectionnée</p>\r\n                      <p className=\"text-sm text-blue-500\">Cliquez sur \"Voir\" pour afficher une newsletter</p>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Sending Tab */}\r\n          <TabsContent value=\"sending\">\r\n            <Card className=\"bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n              <CardHeader>\r\n                <CardTitle>Envoi de newsletter</CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium mb-2\">\r\n                        Sélectionner une newsletter\r\n                      </label>\r\n                      <select \r\n                        className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\r\n                        value={selectedFile || \"\"}\r\n                        onChange={(e) => setSelectedFile(e.target.value)}\r\n                      >\r\n                        <option value=\"\">Choisir une newsletter...</option>\r\n                        {newsletters.map((file) => (\r\n                          <option key={file} value={file}>\r\n                            {file}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                      {selectedFile && (\r\n                        <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                          <CheckCircle className=\"w-4 h-4\" />\r\n                          Newsletter sélectionnée : <strong>{selectedFile}</strong>\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium mb-2\">\r\n                        Fichier CSV des emails\r\n                      </label>\r\n                      <Input\r\n                        type=\"file\"\r\n                        accept=\".csv\"\r\n                        onChange={handleCsvFileChange}\r\n                      />\r\n                      {csvFile && (\r\n                        <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                          <FileText className=\"w-4 h-4\" />\r\n                          Fichier CSV : <strong>{csvFile.name}</strong>\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <Button\r\n                      onClick={sendNewsletter}\r\n                      disabled={!selectedFile || !csvFile || isSending}\r\n                      className=\"w-full\"\r\n                      size=\"lg\"\r\n                    >\r\n                      <Send className=\"w-4 h-4 mr-2\" />\r\n                      {isSending ? \"Envoi en cours...\" : \"Envoyer la newsletter\"}\r\n                    </Button>\r\n                  </div>\r\n\r\n                  <Card className=\"bg-gradient-to-br from-blue-50/90 to-gray-100/80 border-blue-200/50 backdrop-blur-md\">\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-blue-800\">Informations</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"text-sm text-blue-700 space-y-2\">\r\n                      <p>• Le fichier CSV doit contenir une colonne \"email\"</p>\r\n                      <p>• Format accepté : <EMAIL></p>\r\n                      <p>• Maximum 1000 emails par envoi</p>\r\n                      <p>• L'envoi peut prendre quelque minutes</p>\r\n                    </CardContent>\r\n                  </Card>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n       \r\n        {/* Status Message */}\r\n        {status && (\r\n          <Card className=\"mt-6 border-l-4 border-l-blue-600 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n            <CardContent className=\"pt-6\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <AlertCircle className=\"w-5 h-5 text-blue-700\" />\r\n                <p className=\"text-sm text-gray-900\">{status}</p>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAEA;AA1BA;;;;;;;;;;;;AA4Be,SAAS;IACtB,uBAAuB;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,UAAU,WAAW;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,oBAAoB;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;YAC5B,eAAe,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;QACrC,EAAE,OAAM;YACN,UAAU;QACZ;IACF;IAIA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,UAAU,EAAE;gBACtE,cAAc;YAChB;YACA,gBAAgB;YAChB,mBAAmB,IAAI,IAAI;QAC7B,EAAE,OAAM;YACN,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC,GAAG;QAE3C,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,2CAA2C;gBAC5D,QAAQ;oBAAE;gBAAS;YACrB;YACA,UAAU,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC;YACpC,oBAAoB,qBAAqB;YACzC,IAAI,iBAAiB,UAAU;gBAC7B,gBAAgB;gBAChB,mBAAmB;YACrB;QACF,EAAE,OAAO,GAAQ;YACf,UAAU,6BAA6B,CAAC,EAAE,QAAQ,EAAE,MAAM,UAAU,EAAE,OAAO;QAC/E;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,MAAM;YACN;QACF;QACA,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,sCAAsC;gBACjE;gBACA;YACF;YACA,cAAc,IAAI,IAAI,CAAC,UAAU;YACjC,WAAW;YACX,qDAAqD;YACrD;QACF,EAAE,OAAM;YACN,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa;YACnC,MAAM;YACN;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QACzB,SAAS,MAAM,CAAC,eAAe;QAC/B,SAAS,MAAM,CAAC,QAAQ;QAExB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,uCAAuC,UAAU;gBAC5E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,cAAc,IAAI,IAAI,CAAC,UAAU;YACjC,WAAW;YACX,qDAAqD;YACrD;QACF,EAAE,OAAM;YACN,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,gCAAgC;gBAC3D;gBACA;gBACA;YACF;YACA,WAAW,IAAI,IAAI,CAAC,kBAAkB;YACtC,cAAc,KAAK,+BAA+B;YAElD,iEAAiE;YACjE;QACF,EAAE,OAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAC7B,MAAM;YACN;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,mBAAmB;QACnC,SAAS,MAAM,CAAC,YAAY;QAE5B,aAAa;QACb,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAAyC;YACtE,UAAU,IAAI,IAAI,CAAC,MAAM;YACzB,MAAM;QACR,EAAE,OAAO,GAAQ;YACf,UAAU,8BAA8B,CAAC,EAAE,QAAQ,EAAE,MAAM,UAAU,EAAE,OAAO;QAChF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,qCAAqC;QACrC,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;YACzD,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;YACN;QACF;QAEA,IAAI;YACF,8CAA8C;YAC9C,IAAI,SAAS;gBACX,WAAW;YACb,OAAO;gBACL,cAAc;YAChB;YACA,qCAAqC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAAiD;sEAE7D,8OAAC;4DAAK,WAAU;sEAAqF;;;;;;;;;;;;8DAEvG,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;8CAOtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;oDAAiD,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;8DAC9F,8OAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;;;;;;;sDAE7F,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC,0IAAA,CAAA,OAAI;oBAAC,cAAa;oBAAa,WAAU;;sCACxC,8OAAC,0IAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,0IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAa,WAAU;;sDACxC,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGtC,8OAAC,0IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAgB,WAAU;;sDAC3C,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,8OAAC,0IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMrC,8OAAC,0IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,0IAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAA0B;;;;;;;;;;;;0DAIlD,8OAAC,0IAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiC;;;;;;0EAClD,8OAAC,2IAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAI5C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiC;;;;;;0EAClD,8OAAC,8IAAA,CAAA,WAAQ;gEACP,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gEAC9C,MAAM;;;;;;;;;;;;kEAIV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4IAAA,CAAA,SAAM;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;gEACV,MAAK;0EAEJ,UAAU,2BAA2B;;;;;;0EAGxC,8OAAC,4IAAA,CAAA,SAAM;gEACL,SAAS,IAAM,kBAAkB,CAAC;gEAClC,SAAQ;gEACR,WAAU;gEACV,MAAK;;kFAEL,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAIpC,gCACC,8OAAC,0IAAA,CAAA,OAAI;gEAAC,WAAU;0EACd,cAAA,8OAAC,0IAAA,CAAA,cAAW;oEAAC,WAAU;8EACrB,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;kGACC,8OAAC;wFAAM,WAAU;kGAAiC;;;;;;kGAGlD,8OAAC,2IAAA,CAAA,QAAK;wFACJ,MAAK;wFACL,QAAO;wFACP,UAAU;;;;;;oFAEX,sBACC,8OAAC;wFAAE,WAAU;;0GACX,8OAAC,8MAAA,CAAA,WAAQ;gGAAC,WAAU;;;;;;4FAAY;0GACV,8OAAC;0GAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;0FAI9C,8OAAC,4IAAA,CAAA,SAAM;gFACL,SAAS;gFACT,UAAU,WAAW,CAAC;gFACtB,WAAU;0FAET,UAAU,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpE,8OAAC,0IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,0IAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,8OAAC,0IAAA,CAAA,YAAS;kEAAC;;;;;;oDACV,yBACC,8OAAC,4IAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa,CAAC;wDAC7B,WAAU;kEAET,YAAY,YAAY;;;;;;;;;;;;0DAI/B,8OAAC,0IAAA,CAAA,cAAW;0DACT,wBACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kJAAA,CAAA,UAAkB;4DACjB,gBAAgB;4DAChB,OAAO;4DACP,QAAQ;4DACR,SAAS;4DACT,iBAAiB;;;;;;sEAGnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,4IAAA,CAAA,UAAY;gEAAC,UAAU;;;;;;;;;;;;;;;;yEAI5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnC,8OAAC,0IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,0IAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,0IAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,qBACf,8OAAC;4DAAe,WAAU;;8EACxB,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,4IAAA,CAAA,SAAM;4EACL,SAAS,IAAM,WAAW;4EAC1B,MAAK;4EACL,SAAQ;;8FAER,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,8OAAC,4IAAA,CAAA,SAAM;4EACL,SAAS,IAAM,aAAa;4EAC5B,MAAK;4EACL,SAAQ;sFAER,cAAA,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;2DAhBd;;;;kFAsBZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,8OAAC,0IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,0IAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAI/B,8OAAC,0IAAA,CAAA,cAAW;0DACT,6BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAE/C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,yBAAyB;oEAAE,QAAQ;gEAAgB;;;;;;;;;;;;;;;;yEAKzD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,8OAAC,0IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC,0IAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,0IAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,0IAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,8OAAC;oEACC,WAAU;oEACV,OAAO,gBAAgB;oEACvB,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sFAE/C,8OAAC;4EAAO,OAAM;sFAAG;;;;;;wEAChB,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;gFAAkB,OAAO;0FACvB;+EADU;;;;;;;;;;;gEAKhB,8BACC,8OAAC;oEAAE,WAAU;;sFACX,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAY;sFACT,8OAAC;sFAAQ;;;;;;;;;;;;;;;;;;sEAKzC,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,8OAAC,2IAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,QAAO;oEACP,UAAU;;;;;;gEAEX,yBACC,8OAAC;oEAAE,WAAU;;sFACX,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;sFAClB,8OAAC;sFAAQ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sEAKzC,8OAAC,4IAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU,CAAC,gBAAgB,CAAC,WAAW;4DACvC,WAAU;4DACV,MAAK;;8EAEL,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,YAAY,sBAAsB;;;;;;;;;;;;;8DAIvC,8OAAC,0IAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC,0IAAA,CAAA,aAAU;sEACT,cAAA,8OAAC,0IAAA,CAAA,YAAS;gEAAC,WAAU;0EAAgB;;;;;;;;;;;sEAEvC,8OAAC,0IAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;8EAAE;;;;;;8EACH,8OAAC;8EAAE;;;;;;8EACH,8OAAC;8EAAE;;;;;;8EACH,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUhB,wBACC,8OAAC,0IAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,0IAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}]}