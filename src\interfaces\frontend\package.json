{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tinymce/tinymce-react": "^5.1.1", "@uiw/react-md-editor": "^4.0.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "file-saver": "^2.0.5", "lucide-react": "^0.536.0", "marked": "^16.1.2", "next": "^15.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tinymce": "^7.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "postcss-nesting": "^13.0.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3"}}