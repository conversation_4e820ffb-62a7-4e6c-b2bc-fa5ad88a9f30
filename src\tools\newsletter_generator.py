from typing import List
import re
from src.prompts.generator_prompt import get_prompt
from src.models.gemini_model import ask_ai

def generate_newsletter_with_context(chunks: List[str], theme: str="actualité", description: str="actualité") -> str:
    """
    Génère une newsletter en HTML directement via le modèle LLM (Gemini),
    à partir d'une liste de contenus extraits (scrapés), d'un thème et d'une description.
    """
    # 1. Fusionner les contenus
    context = "\n\n".join(chunks)

    # 2. Construire le prompt HTML-ready
    prompt = get_prompt(context, theme, description)

    # 3. A<PERSON>er le LLM
    generated_html = ask_ai(prompt).strip()
    cleaned = re.sub(r"^```html\s*", "", generated_html)  # Enlève ```html au début
    cleaned = re.sub(r"\s*```$", "", cleaned)              # Enlève ``` à la fin

    return cleaned
    # 4. Retourner directement le HTML prêt à afficher dans le navigateur
    





















# from src.prompts.generator_prompt import get_prompt
# from src.models.gemini_model import ask_ai
# from src.tools.render_newsletter import render_newsletter_html
# from typing import List

# def generate_newsletter_with_context(chunks: List[str], theme: str, description: str) -> str:
#     context = "\n\n".join(chunks)
#     prompt = get_prompt(context, theme, description)
#     generated_text = ask_ai(prompt).strip()

#     bullet_points = [
#         line.strip("-• ").strip()
#         for line in generated_text.split("\n")
#         if line.strip().startswith(("-", "•"))
#     ]

#     title = f"Newsletter – {theme}"
#     introduction = f"Voici les points clés extraits du thème : {description}"
#     section_title = "🧠 Synthèse du contenu analysé"

#     html = render_newsletter_html(
#         title=title,
#         introduction=introduction,
#         section_title=section_title,
#         bullet_points=bullet_points,
#         chunks=chunks,
#         full_text=generated_text
#     )

#     return html















# from src.prompts.generator_prompt import get_prompt
# from src.models.gemini_model import ask_ai
# from typing import List
# from src.tools.render_newsletter import render_newsletter_html 

# # def generate_newsletter_with_context(chunks: List[list], theme: str="actualité" , description: str="actualité"  ) -> str:
# #     context = "\n\n".join(chunks)
# #     prompt = get_prompt(context, theme , description )
# #     return ask_ai(prompt).strip()

# def generate_newsletter_with_context(chunks: List[str], theme: str, description: str) -> str:
#     context = "\n\n".join(chunks)
#     prompt = get_prompt(context, theme, description)
#     generated_text = ask_ai(prompt).strip()

#     bullet_points = [line.strip("-• ") for line in generated_text.split("\n") if line.startswith(("-", "•"))]

#     html = render_newsletter_html(
#         title=f"Newsletter – {theme}",
#         introduction=f"Voici les points clés extraits du thème : {description}",
#         section_title=" Synthèse du contenu analysé",
#         bullet_points=bullet_points,
#         chunks=chunks
#     )
#     return html
