{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/anchor/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('allow_html_in_named_anchor', {\n            processor: 'boolean',\n            default: false\n        });\n    };\n    const allowHtmlInNamedAnchor = option('allow_html_in_named_anchor');\n\n    const namedAnchorSelector = 'a:not([href])';\n    const isEmptyString = (str) => !str;\n    const getIdFromAnchor = (elm) => {\n        const id = elm.getAttribute('id') || elm.getAttribute('name');\n        return id || '';\n    };\n    const isAnchor = (elm) => elm.nodeName.toLowerCase() === 'a';\n    const isNamedAnchor = (elm) => isAnchor(elm) && !elm.getAttribute('href') && getIdFromAnchor(elm) !== '';\n    const isEmptyNamedAnchor = (elm) => isNamedAnchor(elm) && !elm.firstChild;\n\n    const removeEmptyNamedAnchorsInSelection = (editor) => {\n        const dom = editor.dom;\n        global$1(dom).walk(editor.selection.getRng(), (nodes) => {\n            global.each(nodes, (node) => {\n                if (isEmptyNamedAnchor(node)) {\n                    dom.remove(node, false);\n                }\n            });\n        });\n    };\n    const isValidId = (id) => \n    // Follows HTML4 rules: https://www.w3.org/TR/html401/types.html#type-id\n    /^[A-Za-z][A-Za-z0-9\\-:._]*$/.test(id);\n    const getNamedAnchor = (editor) => editor.dom.getParent(editor.selection.getStart(), namedAnchorSelector);\n    const getId = (editor) => {\n        const anchor = getNamedAnchor(editor);\n        if (anchor) {\n            return getIdFromAnchor(anchor);\n        }\n        else {\n            return '';\n        }\n    };\n    const createAnchor = (editor, id) => {\n        editor.undoManager.transact(() => {\n            if (!allowHtmlInNamedAnchor(editor)) {\n                editor.selection.collapse(true);\n            }\n            if (editor.selection.isCollapsed()) {\n                editor.insertContent(editor.dom.createHTML('a', { id }));\n            }\n            else {\n                // Remove any empty named anchors in the selection as they cannot be removed by the formatter since they are cef\n                removeEmptyNamedAnchorsInSelection(editor);\n                // Format is set up to truncate any partially selected named anchors so that they are not completely removed\n                editor.formatter.remove('namedAnchor', undefined, undefined, true);\n                // Insert new anchor using the formatter - will wrap selected content in anchor\n                editor.formatter.apply('namedAnchor', { value: id });\n                // Need to add visual classes to anchors if required\n                editor.addVisual();\n            }\n        });\n    };\n    const updateAnchor = (editor, id, anchorElement) => {\n        anchorElement.removeAttribute('name');\n        anchorElement.id = id;\n        editor.addVisual(); // Need to add visual classes to anchors if required\n        editor.undoManager.add();\n    };\n    const insert = (editor, id) => {\n        const anchor = getNamedAnchor(editor);\n        if (anchor) {\n            updateAnchor(editor, id, anchor);\n        }\n        else {\n            createAnchor(editor, id);\n        }\n        editor.focus();\n    };\n\n    const insertAnchor = (editor, newId) => {\n        if (!isValidId(newId)) {\n            editor.windowManager.alert('ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.');\n            return false;\n        }\n        else {\n            insert(editor, newId);\n            return true;\n        }\n    };\n    const open = (editor) => {\n        const currentId = getId(editor);\n        editor.windowManager.open({\n            title: 'Anchor',\n            size: 'normal',\n            body: {\n                type: 'panel',\n                items: [\n                    {\n                        name: 'id',\n                        type: 'input',\n                        label: 'ID',\n                        placeholder: 'example'\n                    }\n                ]\n            },\n            buttons: [\n                {\n                    type: 'cancel',\n                    name: 'cancel',\n                    text: 'Cancel'\n                },\n                {\n                    type: 'submit',\n                    name: 'save',\n                    text: 'Save',\n                    primary: true\n                }\n            ],\n            initialData: {\n                id: currentId\n            },\n            onSubmit: (api) => {\n                if (insertAnchor(editor, api.getData().id)) { // TODO we need a better way to do validation\n                    api.close();\n                }\n            }\n        });\n    };\n\n    const register$1 = (editor) => {\n        editor.addCommand('mceAnchor', () => {\n            open(editor);\n        });\n    };\n\n    // Note: node.firstChild check is for the 'allow_html_in_named_anchor' setting\n    // Only want to add contenteditable attributes if there is no text within the anchor\n    const isNamedAnchorNode = (node) => isEmptyString(node.attr('href')) && !isEmptyString(node.attr('id') || node.attr('name'));\n    const isEmptyNamedAnchorNode = (node) => isNamedAnchorNode(node) && !node.firstChild;\n    const setContentEditable = (state) => (nodes) => {\n        for (let i = 0; i < nodes.length; i++) {\n            const node = nodes[i];\n            if (isEmptyNamedAnchorNode(node)) {\n                node.attr('contenteditable', state);\n            }\n        }\n    };\n    const setup = (editor) => {\n        editor.on('PreInit', () => {\n            editor.parser.addNodeFilter('a', setContentEditable('false'));\n            editor.serializer.addNodeFilter('a', setContentEditable(null));\n        });\n    };\n\n    const registerFormats = (editor) => {\n        editor.formatter.register('namedAnchor', {\n            inline: 'a',\n            selector: namedAnchorSelector,\n            remove: 'all',\n            split: true,\n            deep: true,\n            attributes: {\n                id: '%value'\n            },\n            onmatch: (node, _fmt, _itemName) => {\n                return isNamedAnchor(node);\n            }\n        });\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceAnchor');\n        editor.ui.registry.addToggleButton('anchor', {\n            icon: 'bookmark',\n            tooltip: 'Anchor',\n            onAction,\n            onSetup: (buttonApi) => {\n                const unbindSelectorChanged = editor.selection.selectorChangedWithUnbind('a:not([href])', buttonApi.setActive).unbind;\n                const unbindEditableChanged = onSetupEditable(editor)(buttonApi);\n                return () => {\n                    unbindSelectorChanged();\n                    unbindEditableChanged();\n                };\n            }\n        });\n        editor.ui.registry.addMenuItem('anchor', {\n            icon: 'bookmark',\n            text: 'Anchor...',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    var Plugin = () => {\n        global$2.add('anchor', (editor) => {\n            register$2(editor);\n            setup(editor);\n            register$1(editor);\n            register(editor);\n            editor.on('PreInit', () => {\n                registerFormats(editor);\n            });\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,8BAA8B;YACzC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,yBAAyB,OAAO;IAEtC,MAAM,sBAAsB;IAC5B,MAAM,gBAAgB,CAAC,MAAQ,CAAC;IAChC,MAAM,kBAAkB,CAAC;QACrB,MAAM,KAAK,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC;QACtD,OAAO,MAAM;IACjB;IACA,MAAM,WAAW,CAAC,MAAQ,IAAI,QAAQ,CAAC,WAAW,OAAO;IACzD,MAAM,gBAAgB,CAAC,MAAQ,SAAS,QAAQ,CAAC,IAAI,YAAY,CAAC,WAAW,gBAAgB,SAAS;IACtG,MAAM,qBAAqB,CAAC,MAAQ,cAAc,QAAQ,CAAC,IAAI,UAAU;IAEzE,MAAM,qCAAqC,CAAC;QACxC,MAAM,MAAM,OAAO,GAAG;QACtB,SAAS,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC;YAC3C,OAAO,IAAI,CAAC,OAAO,CAAC;gBAChB,IAAI,mBAAmB,OAAO;oBAC1B,IAAI,MAAM,CAAC,MAAM;gBACrB;YACJ;QACJ;IACJ;IACA,MAAM,YAAY,CAAC,KACnB,wEAAwE;QACxE,8BAA8B,IAAI,CAAC;IACnC,MAAM,iBAAiB,CAAC,SAAW,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,QAAQ,IAAI;IACrF,MAAM,QAAQ,CAAC;QACX,MAAM,SAAS,eAAe;QAC9B,IAAI,QAAQ;YACR,OAAO,gBAAgB;QAC3B,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,eAAe,CAAC,QAAQ;QAC1B,OAAO,WAAW,CAAC,QAAQ,CAAC;YACxB,IAAI,CAAC,uBAAuB,SAAS;gBACjC,OAAO,SAAS,CAAC,QAAQ,CAAC;YAC9B;YACA,IAAI,OAAO,SAAS,CAAC,WAAW,IAAI;gBAChC,OAAO,aAAa,CAAC,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK;oBAAE;gBAAG;YACzD,OACK;gBACD,gHAAgH;gBAChH,mCAAmC;gBACnC,4GAA4G;gBAC5G,OAAO,SAAS,CAAC,MAAM,CAAC,eAAe,WAAW,WAAW;gBAC7D,+EAA+E;gBAC/E,OAAO,SAAS,CAAC,KAAK,CAAC,eAAe;oBAAE,OAAO;gBAAG;gBAClD,oDAAoD;gBACpD,OAAO,SAAS;YACpB;QACJ;IACJ;IACA,MAAM,eAAe,CAAC,QAAQ,IAAI;QAC9B,cAAc,eAAe,CAAC;QAC9B,cAAc,EAAE,GAAG;QACnB,OAAO,SAAS,IAAI,oDAAoD;QACxE,OAAO,WAAW,CAAC,GAAG;IAC1B;IACA,MAAM,SAAS,CAAC,QAAQ;QACpB,MAAM,SAAS,eAAe;QAC9B,IAAI,QAAQ;YACR,aAAa,QAAQ,IAAI;QAC7B,OACK;YACD,aAAa,QAAQ;QACzB;QACA,OAAO,KAAK;IAChB;IAEA,MAAM,eAAe,CAAC,QAAQ;QAC1B,IAAI,CAAC,UAAU,QAAQ;YACnB,OAAO,aAAa,CAAC,KAAK,CAAC;YAC3B,OAAO;QACX,OACK;YACD,OAAO,QAAQ;YACf,OAAO;QACX;IACJ;IACA,MAAM,OAAO,CAAC;QACV,MAAM,YAAY,MAAM;QACxB,OAAO,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,MAAM;YACN,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH;wBACI,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,aAAa;oBACjB;iBACH;YACL;YACA,SAAS;gBACL;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;gBACV;gBACA;oBACI,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;aACH;YACD,aAAa;gBACT,IAAI;YACR;YACA,UAAU,CAAC;gBACP,IAAI,aAAa,QAAQ,IAAI,OAAO,GAAG,EAAE,GAAG;oBACxC,IAAI,KAAK;gBACb;YACJ;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,OAAO,UAAU,CAAC,aAAa;YAC3B,KAAK;QACT;IACJ;IAEA,8EAA8E;IAC9E,oFAAoF;IACpF,MAAM,oBAAoB,CAAC,OAAS,cAAc,KAAK,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC;IACpH,MAAM,yBAAyB,CAAC,OAAS,kBAAkB,SAAS,CAAC,KAAK,UAAU;IACpF,MAAM,qBAAqB,CAAC,QAAU,CAAC;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,uBAAuB,OAAO;oBAC9B,KAAK,IAAI,CAAC,mBAAmB;gBACjC;YACJ;QACJ;IACA,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,WAAW;YACjB,OAAO,MAAM,CAAC,aAAa,CAAC,KAAK,mBAAmB;YACpD,OAAO,UAAU,CAAC,aAAa,CAAC,KAAK,mBAAmB;QAC5D;IACJ;IAEA,MAAM,kBAAkB,CAAC;QACrB,OAAO,SAAS,CAAC,QAAQ,CAAC,eAAe;YACrC,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,OAAO;YACP,MAAM;YACN,YAAY;gBACR,IAAI;YACR;YACA,SAAS,CAAC,MAAM,MAAM;gBAClB,OAAO,cAAc;YACzB;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU;YACzC,MAAM;YACN,SAAS;YACT;YACA,SAAS,CAAC;gBACN,MAAM,wBAAwB,OAAO,SAAS,CAAC,yBAAyB,CAAC,iBAAiB,UAAU,SAAS,EAAE,MAAM;gBACrH,MAAM,wBAAwB,gBAAgB,QAAQ;gBACtD,OAAO;oBACH;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU;YACrC,MAAM;YACN,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,UAAU,CAAC;YACpB,WAAW;YACX,MAAM;YACN,WAAW;YACX,SAAS;YACT,OAAO,EAAE,CAAC,WAAW;gBACjB,gBAAgB;YACpB;QACJ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/anchor/index.js"], "sourcesContent": ["// Exports the \"anchor\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/anchor')\n//   ES2015:\n//     import 'tinymce/plugins/anchor'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,SAAS;AACT,cAAc;AACd,wCAAwC;AACxC,YAAY;AACZ,sCAAsC", "ignoreList": [0], "debugId": null}}]}