
```mermaid
graph TD

    %% Nœuds
    Start(["🎬 Start\nEntrée: theme, description, text, use_file, feedback"])
    Scrape["🌐 Scraping Node\nEntrée: theme, description\nSortie: scraped"]
    Vectorize["🧠 Vectorize Node\nEntrée: scraped\nSortie: vectorstore"]
    GenerateWeb["📰 Generate from Vectorized Node\nEntrée: scraped, theme\nSortie: newsletter"]
    GenerateFromFile["📁 Generate from File Node\nEntrée: text, theme, description\nSortie: newsletter"]
    Revise["✍️ Revise Node\nEntrée: newsletter, feedback\nSortie: revised"]
    End(["✅ END\nSortie: newsletter ou revised"])

    %% Liens
    Start -->|use_file = false| Scrape
    Scrape --> Vectorize --> GenerateWeb
    Start -->|use_file = true| GenerateFromFile

    GenerateWeb -->|feedback fourni| Revise --> End
    GenerateWeb -->|pas de feedback| End
    GenerateFromFile -->|feedback fourni| Revise
    GenerateFromFile -->|pas de feedback| End

    %% Style — couleurs visibles sur fond sombre
    style Start fill:#3f51b5,color:#ffffff,stroke:#1a237e,stroke-width:2px
    style Scrape fill:#2196f3,color:#ffffff,stroke:#0d47a1
    style Vectorize fill:#00acc1,color:#ffffff,stroke:#006064
    style GenerateWeb fill:#4caf50,color:#ffffff,stroke:#1b5e20
    style GenerateFromFile fill:#81c784,color:#000000,stroke:#2e7d32
    style Revise fill:#ffca28,color:#000000,stroke:#ff8f00
    style End fill:#009688,color:#ffffff,stroke:#004d40,stroke-width:2px

```