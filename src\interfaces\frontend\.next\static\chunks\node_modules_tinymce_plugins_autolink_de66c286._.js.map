{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/autolink/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const hasProto = (v, constructor, predicate) => {\n        var _a;\n        if (predicate(v, constructor.prototype)) {\n            return true;\n        }\n        else {\n            // String-based fallback time\n            return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n        }\n    };\n    const typeOf = (x) => {\n        const t = typeof x;\n        if (x === null) {\n            return 'null';\n        }\n        else if (t === 'object' && Array.isArray(x)) {\n            return 'array';\n        }\n        else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n            return 'string';\n        }\n        else {\n            return t;\n        }\n    };\n    const isType = (type) => (value) => typeOf(value) === type;\n    const eq = (t) => (a) => t === a;\n    const isString = isType('string');\n    const isUndefined = eq(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n\n    const not = (f) => (t) => !f(t);\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const contains = (str, substr, start = 0, end) => {\n        const idx = str.indexOf(substr, start);\n        if (idx !== -1) {\n            return isUndefined(end) ? true : idx + substr.length <= end;\n        }\n        else {\n            return false;\n        }\n    };\n    /** Does 'str' start with 'prefix'?\n     *  Note: all strings start with the empty string.\n     *        More formally, for all strings x, startsWith(x, \"\").\n     *        This is so that for all strings x and y, startsWith(y + x, y)\n     */\n    const startsWith = (str, prefix) => {\n        return checkRange(str, prefix, 0);\n    };\n\n    const zeroWidth = '\\uFEFF';\n    const isZwsp = (char) => char === zeroWidth;\n    const removeZwsp = (s) => s.replace(/\\uFEFF/g, '');\n\n    /*\n      The RegEx parses the following components (https://www.rfc-editor.org/rfc/rfc3986.txt):\n\n        scheme:[//[user:password@]host[:port]][/]path[?query][#fragment]\n\n              foo://example.com:8042/over/there?name=ferret#nose\n              \\_/   \\______________/\\_________/ \\_________/ \\__/\n              |           |            |            |        |\n            scheme     authority       path        query   fragment\n\n      Originally from:\n        http://blog.mattheworiordan.com/post/13174566389/url-regular-expression-for-links-with-or-without-the\n\n      Modified to:\n      - include port numbers\n      - allow full stops in email addresses\n      - allow [-.~*+=!&;:'%@?^${}(),\\/\\w] after the #\n      - allow [-.~*+=!&;:'%@?^${}(),\\/\\w] after the ?\n      - move allow -_.~*+=!&;:'%@?^${}() in email usernames to the first @ match (TBIO-4809)\n      - enforce domains to be [A-Za-z0-9-]+(?:\\.[A-Za-z0-9-]+)* so they can't end in a period (TBIO-4809)\n      - removed a bunch of escaping, made every group non-capturing (during TBIO-4809)\n      - colons are only valid when followed directly by // or some text and then @ (TBIO-4867)\n      - only include the fragment '#' if it has 1 or more trailing matches\n      - only include the query '?' if it has 1 or more trailing matches\n      - allow commas in URL path\n      - exclude trailing comma and period in URL path\n      - allow up to 15 character schemes including all valid characters from the spec https://url.spec.whatwg.org/#url-scheme-string (TINY-5074)\n      - changed instances of 0-9 to be \\d (TINY-5074)\n      - reduced duplication (TINY-5074)\n      - allow [*!;:'@$] in the path segment as they are valid characters per the spec: https://url.spec.whatwg.org/#url-path-segment-string (TINY-8069)\n\n    (?:\n      (?:\n        [A-Za-z][A-Za-z\\d.+-]{0,14}:\\/\\/(?:[-.~*+=!&;:'%@?^${}(),\\w]+@)?\n        | www\\.\n        | [-;:&=+$,.\\w]+@\n      )\n      [A-Za-z\\d-]+\n      (?:\\.[A-Za-z\\d-]+)*\n    )\n    (?::\\d+)?\n    (?:\n      \\/\n      (?:\n         [-.~*+=!;:'%@$(),\\/\\w]*[-~*+=%@$()\\/\\w]\n       )?\n    )?\n    (?:\n      \\?\n      (?:\n        [-.~*+=!&;:'%@?^${}(),\\/\\w]+\n      )\n    )?\n    (?:\n      #\n      (?:\n        [-.~*+=!&;:'%@?^${}(),\\/\\w]+\n      )\n    )?\n    */\n    const link = () => \n    // eslint-disable-next-line max-len\n    /(?:[A-Za-z][A-Za-z\\d.+-]{0,14}:\\/\\/(?:[-.~*+=!&;:'%@?^${}(),\\w]+@)?|www\\.|[-;:&=+$,.\\w]+@)[A-Za-z\\d-]+(?:\\.[A-Za-z\\d-]+)*(?::\\d+)?(?:\\/(?:[-.~*+=!;:'%@$(),\\/\\w]*[-~*+=%@$()\\/\\w])?)?(?:\\?(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?/g;\n\n    const option = (name) => (editor) => editor.options.get(name);\n    const register = (editor) => {\n        const registerOption = editor.options.register;\n        registerOption('autolink_pattern', {\n            processor: 'regexp',\n            // Use the Polaris link detection, however for autolink we need to make it be an exact match\n            default: new RegExp('^' + link().source + '$', 'i')\n        });\n        registerOption('link_default_target', {\n            processor: 'string'\n        });\n        registerOption('link_default_protocol', {\n            processor: 'string',\n            default: 'https'\n        });\n    };\n    const getAutoLinkPattern = option('autolink_pattern');\n    const getDefaultLinkTarget = option('link_default_target');\n    const getDefaultLinkProtocol = option('link_default_protocol');\n    const allowUnsafeLinkTarget = option('allow_unsafe_link_target');\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.TextSeeker');\n\n    const isTextNode = (node) => node.nodeType === 3;\n    const isElement = (node) => node.nodeType === 1;\n    const isBracketOrSpace = (char) => /^[(\\[{ \\u00a0]$/.test(char);\n    // Note: This is similar to the Polaris protocol detection, except it also handles `mailto` and any length scheme\n    const hasProtocol = (url) => /^([A-Za-z][A-Za-z\\d.+-]*:\\/\\/)|mailto:/.test(url);\n    // A limited list of punctuation characters that might be used after a link\n    const isPunctuation = (char) => /[?!,.;:]/.test(char);\n    const findChar = (text, index, predicate) => {\n        for (let i = index - 1; i >= 0; i--) {\n            const char = text.charAt(i);\n            if (!isZwsp(char) && predicate(char)) {\n                return i;\n            }\n        }\n        return -1;\n    };\n    const freefallRtl = (container, offset) => {\n        let tempNode = container;\n        let tempOffset = offset;\n        while (isElement(tempNode) && tempNode.childNodes[tempOffset]) {\n            tempNode = tempNode.childNodes[tempOffset];\n            tempOffset = isTextNode(tempNode) ? tempNode.data.length : tempNode.childNodes.length;\n        }\n        return { container: tempNode, offset: tempOffset };\n    };\n\n    const parseCurrentLine = (editor, offset) => {\n        var _a;\n        const voidElements = editor.schema.getVoidElements();\n        const autoLinkPattern = getAutoLinkPattern(editor);\n        const { dom, selection } = editor;\n        // Never create a link when we are inside a link\n        if (dom.getParent(selection.getNode(), 'a[href]') !== null || editor.mode.isReadOnly()) {\n            return null;\n        }\n        const rng = selection.getRng();\n        const textSeeker = global(dom, (node) => {\n            return dom.isBlock(node) || has(voidElements, node.nodeName.toLowerCase()) || dom.getContentEditable(node) === 'false' || dom.getParent(node, 'a[href]') !== null;\n        });\n        // Descend down the end container to find the text node\n        const { container: endContainer, offset: endOffset } = freefallRtl(rng.endContainer, rng.endOffset);\n        // Find the root container to use when walking\n        const root = (_a = dom.getParent(endContainer, dom.isBlock)) !== null && _a !== void 0 ? _a : dom.getRoot();\n        // Move the selection backwards to the start of the potential URL to account for the pressed character\n        // while also excluding the last full stop from a word like \"www.site.com.\"\n        const endSpot = textSeeker.backwards(endContainer, endOffset + offset, (node, offset) => {\n            const text = node.data;\n            const idx = findChar(text, offset, not(isBracketOrSpace));\n            // Move forward one so the offset is after the found character unless the found char is a punctuation char\n            return idx === -1 || isPunctuation(text[idx]) ? idx : idx + 1;\n        }, root);\n        if (!endSpot) {\n            return null;\n        }\n        // Walk backwards until we find a boundary or a bracket/space\n        let lastTextNode = endSpot.container;\n        const startSpot = textSeeker.backwards(endSpot.container, endSpot.offset, (node, offset) => {\n            lastTextNode = node;\n            const idx = findChar(node.data, offset, isBracketOrSpace);\n            // Move forward one so that the offset is after the bracket/space\n            return idx === -1 ? idx : idx + 1;\n        }, root);\n        const newRng = dom.createRng();\n        if (!startSpot) {\n            newRng.setStart(lastTextNode, 0);\n        }\n        else {\n            newRng.setStart(startSpot.container, startSpot.offset);\n        }\n        newRng.setEnd(endSpot.container, endSpot.offset);\n        const rngText = removeZwsp(newRng.toString());\n        const matches = rngText.match(autoLinkPattern);\n        if (matches) {\n            let url = matches[0];\n            if (startsWith(url, 'www.')) {\n                const protocol = getDefaultLinkProtocol(editor);\n                url = protocol + '://' + url;\n            }\n            else if (contains(url, '@') && !hasProtocol(url)) {\n                url = 'mailto:' + url;\n            }\n            return { rng: newRng, url };\n        }\n        else {\n            return null;\n        }\n    };\n    const convertToLink = (editor, result) => {\n        const { dom, selection } = editor;\n        const { rng, url } = result;\n        const bookmark = selection.getBookmark();\n        selection.setRng(rng);\n        // Needs to be a native createlink command since this is executed in a keypress event handler\n        // so the pending character that is to be inserted needs to be inserted after the link. That will not\n        // happen if we use the formatter create link version. Since we're using the native command\n        // then we also need to ensure the exec command events are fired for backwards compatibility.\n        const command = 'createlink';\n        const args = { command, ui: false, value: url };\n        const beforeExecEvent = editor.dispatch('BeforeExecCommand', args);\n        if (!beforeExecEvent.isDefaultPrevented()) {\n            editor.getDoc().execCommand(command, false, url);\n            editor.dispatch('ExecCommand', args);\n            const defaultLinkTarget = getDefaultLinkTarget(editor);\n            if (isString(defaultLinkTarget)) {\n                const anchor = selection.getNode();\n                dom.setAttrib(anchor, 'target', defaultLinkTarget);\n                // Ensure noopener is added for blank targets to prevent window opener attacks\n                if (defaultLinkTarget === '_blank' && !allowUnsafeLinkTarget(editor)) {\n                    dom.setAttrib(anchor, 'rel', 'noopener');\n                }\n            }\n        }\n        selection.moveToBookmark(bookmark);\n        editor.nodeChanged();\n    };\n    const handleSpacebar = (editor) => {\n        const result = parseCurrentLine(editor, -1);\n        if (isNonNullable(result)) {\n            convertToLink(editor, result);\n        }\n    };\n    const handleBracket = handleSpacebar;\n    const handleEnter = (editor) => {\n        const result = parseCurrentLine(editor, 0);\n        if (isNonNullable(result)) {\n            convertToLink(editor, result);\n        }\n    };\n    const setup = (editor) => {\n        editor.on('keydown', (e) => {\n            if (e.keyCode === 13 && !e.isDefaultPrevented()) {\n                handleEnter(editor);\n            }\n        });\n        editor.on('keyup', (e) => {\n            if (e.keyCode === 32) {\n                handleSpacebar(editor);\n                // One of the closing bracket keys: ), ] or }\n            }\n            else if (e.keyCode === 48 && e.shiftKey || e.keyCode === 221) {\n                handleBracket(editor);\n            }\n        });\n    };\n\n    var Plugin = () => {\n        global$1.add('autolink', (editor) => {\n            register(editor);\n            setup(editor);\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,WAAW,CAAC,GAAG,aAAa;QAC9B,IAAI;QACJ,IAAI,UAAU,GAAG,YAAY,SAAS,GAAG;YACrC,OAAO;QACX,OACK;YACD,6BAA6B;YAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,YAAY,IAAI;QACnG;IACJ;IACA,MAAM,SAAS,CAAC;QACZ,MAAM,IAAI,OAAO;QACjB,IAAI,MAAM,MAAM;YACZ,OAAO;QACX,OACK,IAAI,MAAM,YAAY,MAAM,OAAO,CAAC,IAAI;YACzC,OAAO;QACX,OACK,IAAI,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAU,MAAM,aAAa,CAAC,KAAK;YAClF,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,SAAS,CAAC,OAAS,CAAC,QAAU,OAAO,WAAW;IACtD,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,WAAW,OAAO;IACxB,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IAEzC,MAAM,MAAM,CAAC,IAAM,CAAC,IAAM,CAAC,EAAE;IAE7B,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IAEnD,MAAM,aAAa,CAAC,KAAK,QAAQ,QAAU,WAAW,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,QAAQ,OAAO,MAAM,MAAM;IACxI,MAAM,WAAW,SAAC,KAAK;YAAQ,yEAAQ,GAAG;QACtC,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ;QAChC,IAAI,QAAQ,CAAC,GAAG;YACZ,OAAO,YAAY,OAAO,OAAO,MAAM,OAAO,MAAM,IAAI;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IACA;;;;KAIC,GACD,MAAM,aAAa,CAAC,KAAK;QACrB,OAAO,WAAW,KAAK,QAAQ;IACnC;IAEA,MAAM,YAAY;IAClB,MAAM,SAAS,CAAC,OAAS,SAAS;IAClC,MAAM,aAAa,CAAC,IAAM,EAAE,OAAO,CAAC,WAAW;IAE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2DA,GACA,MAAM,OAAO,IACb,mCAAmC;QACnC;IAEA,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,WAAW,CAAC;QACd,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,oBAAoB;YAC/B,WAAW;YACX,4FAA4F;YAC5F,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,GAAG,KAAK;QACnD;QACA,eAAe,uBAAuB;YAClC,WAAW;QACf;QACA,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,qBAAqB,OAAO;IAClC,MAAM,uBAAuB,OAAO;IACpC,MAAM,yBAAyB,OAAO;IACtC,MAAM,wBAAwB,OAAO;IAErC,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,aAAa,CAAC,OAAS,KAAK,QAAQ,KAAK;IAC/C,MAAM,YAAY,CAAC,OAAS,KAAK,QAAQ,KAAK;IAC9C,MAAM,mBAAmB,CAAC,OAAS,kBAAkB,IAAI,CAAC;IAC1D,iHAAiH;IACjH,MAAM,cAAc,CAAC,MAAQ,yCAAyC,IAAI,CAAC;IAC3E,2EAA2E;IAC3E,MAAM,gBAAgB,CAAC,OAAS,WAAW,IAAI,CAAC;IAChD,MAAM,WAAW,CAAC,MAAM,OAAO;QAC3B,IAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAK;YACjC,MAAM,OAAO,KAAK,MAAM,CAAC;YACzB,IAAI,CAAC,OAAO,SAAS,UAAU,OAAO;gBAClC,OAAO;YACX;QACJ;QACA,OAAO,CAAC;IACZ;IACA,MAAM,cAAc,CAAC,WAAW;QAC5B,IAAI,WAAW;QACf,IAAI,aAAa;QACjB,MAAO,UAAU,aAAa,SAAS,UAAU,CAAC,WAAW,CAAE;YAC3D,WAAW,SAAS,UAAU,CAAC,WAAW;YAC1C,aAAa,WAAW,YAAY,SAAS,IAAI,CAAC,MAAM,GAAG,SAAS,UAAU,CAAC,MAAM;QACzF;QACA,OAAO;YAAE,WAAW;YAAU,QAAQ;QAAW;IACrD;IAEA,MAAM,mBAAmB,CAAC,QAAQ;QAC9B,IAAI;QACJ,MAAM,eAAe,OAAO,MAAM,CAAC,eAAe;QAClD,MAAM,kBAAkB,mBAAmB;QAC3C,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;QAC3B,gDAAgD;QAChD,IAAI,IAAI,SAAS,CAAC,UAAU,OAAO,IAAI,eAAe,QAAQ,OAAO,IAAI,CAAC,UAAU,IAAI;YACpF,OAAO;QACX;QACA,MAAM,MAAM,UAAU,MAAM;QAC5B,MAAM,aAAa,OAAO,KAAK,CAAC;YAC5B,OAAO,IAAI,OAAO,CAAC,SAAS,IAAI,cAAc,KAAK,QAAQ,CAAC,WAAW,OAAO,IAAI,kBAAkB,CAAC,UAAU,WAAW,IAAI,SAAS,CAAC,MAAM,eAAe;QACjK;QACA,uDAAuD;QACvD,MAAM,EAAE,WAAW,YAAY,EAAE,QAAQ,SAAS,EAAE,GAAG,YAAY,IAAI,YAAY,EAAE,IAAI,SAAS;QAClG,8CAA8C;QAC9C,MAAM,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO;QACzG,sGAAsG;QACtG,2EAA2E;QAC3E,MAAM,UAAU,WAAW,SAAS,CAAC,cAAc,YAAY,QAAQ,CAAC,MAAM;YAC1E,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,MAAM,SAAS,MAAM,QAAQ,IAAI;YACvC,0GAA0G;YAC1G,OAAO,QAAQ,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,MAAM,MAAM;QAChE,GAAG;QACH,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QACA,6DAA6D;QAC7D,IAAI,eAAe,QAAQ,SAAS;QACpC,MAAM,YAAY,WAAW,SAAS,CAAC,QAAQ,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC,MAAM;YAC7E,eAAe;YACf,MAAM,MAAM,SAAS,KAAK,IAAI,EAAE,QAAQ;YACxC,iEAAiE;YACjE,OAAO,QAAQ,CAAC,IAAI,MAAM,MAAM;QACpC,GAAG;QACH,MAAM,SAAS,IAAI,SAAS;QAC5B,IAAI,CAAC,WAAW;YACZ,OAAO,QAAQ,CAAC,cAAc;QAClC,OACK;YACD,OAAO,QAAQ,CAAC,UAAU,SAAS,EAAE,UAAU,MAAM;QACzD;QACA,OAAO,MAAM,CAAC,QAAQ,SAAS,EAAE,QAAQ,MAAM;QAC/C,MAAM,UAAU,WAAW,OAAO,QAAQ;QAC1C,MAAM,UAAU,QAAQ,KAAK,CAAC;QAC9B,IAAI,SAAS;YACT,IAAI,MAAM,OAAO,CAAC,EAAE;YACpB,IAAI,WAAW,KAAK,SAAS;gBACzB,MAAM,WAAW,uBAAuB;gBACxC,MAAM,WAAW,QAAQ;YAC7B,OACK,IAAI,SAAS,KAAK,QAAQ,CAAC,YAAY,MAAM;gBAC9C,MAAM,YAAY;YACtB;YACA,OAAO;gBAAE,KAAK;gBAAQ;YAAI;QAC9B,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,gBAAgB,CAAC,QAAQ;QAC3B,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;QAC3B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrB,MAAM,WAAW,UAAU,WAAW;QACtC,UAAU,MAAM,CAAC;QACjB,6FAA6F;QAC7F,qGAAqG;QACrG,2FAA2F;QAC3F,6FAA6F;QAC7F,MAAM,UAAU;QAChB,MAAM,OAAO;YAAE;YAAS,IAAI;YAAO,OAAO;QAAI;QAC9C,MAAM,kBAAkB,OAAO,QAAQ,CAAC,qBAAqB;QAC7D,IAAI,CAAC,gBAAgB,kBAAkB,IAAI;YACvC,OAAO,MAAM,GAAG,WAAW,CAAC,SAAS,OAAO;YAC5C,OAAO,QAAQ,CAAC,eAAe;YAC/B,MAAM,oBAAoB,qBAAqB;YAC/C,IAAI,SAAS,oBAAoB;gBAC7B,MAAM,SAAS,UAAU,OAAO;gBAChC,IAAI,SAAS,CAAC,QAAQ,UAAU;gBAChC,8EAA8E;gBAC9E,IAAI,sBAAsB,YAAY,CAAC,sBAAsB,SAAS;oBAClE,IAAI,SAAS,CAAC,QAAQ,OAAO;gBACjC;YACJ;QACJ;QACA,UAAU,cAAc,CAAC;QACzB,OAAO,WAAW;IACtB;IACA,MAAM,iBAAiB,CAAC;QACpB,MAAM,SAAS,iBAAiB,QAAQ,CAAC;QACzC,IAAI,cAAc,SAAS;YACvB,cAAc,QAAQ;QAC1B;IACJ;IACA,MAAM,gBAAgB;IACtB,MAAM,cAAc,CAAC;QACjB,MAAM,SAAS,iBAAiB,QAAQ;QACxC,IAAI,cAAc,SAAS;YACvB,cAAc,QAAQ;QAC1B;IACJ;IACA,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,WAAW,CAAC;YAClB,IAAI,EAAE,OAAO,KAAK,MAAM,CAAC,EAAE,kBAAkB,IAAI;gBAC7C,YAAY;YAChB;QACJ;QACA,OAAO,EAAE,CAAC,SAAS,CAAC;YAChB,IAAI,EAAE,OAAO,KAAK,IAAI;gBAClB,eAAe;YACf,6CAA6C;YACjD,OACK,IAAI,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,IAAI,EAAE,OAAO,KAAK,KAAK;gBAC1D,cAAc;YAClB;QACJ;IACJ;IAEA,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,YAAY,CAAC;YACtB,SAAS;YACT,MAAM;QACV;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/autolink/index.js"], "sourcesContent": ["// Exports the \"autolink\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autolink')\n//   ES2015:\n//     import 'tinymce/plugins/autolink'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,8DAA8D;AAC9D,SAAS;AACT,cAAc;AACd,0CAA0C;AAC1C,YAAY;AACZ,wCAAwC", "ignoreList": [0], "debugId": null}}]}