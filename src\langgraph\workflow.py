from langgraph.graph import StateGraph, END
from typing import TypedDict, List, Optional
from langchain_community.vectorstores import FAISS
from src.mcp.newsletter_client_instance import newsletter_client  # ✅ Corriger le chemin si besoin

# ✅ Instancier le client MCP refondu


# 🔁 Définir l’état partagé dans le graphe
class GraphState(TypedDict):
    theme: str
    description: str
    scraped: Optional[List[str]]
    newsletter: Optional[str]
    feedback: Optional[str]
    revised: Optional[str]
    text: Optional[str]
    use_file: bool
    vectorstore: Optional[FAISS]

# 🌐 Web nodes

async def scraping_node(state: GraphState) -> GraphState:
    scraped = await newsletter_client.call_tool("scrape_news", {
        "theme": state["theme"],
        "description": state["description"]
    }, timeout=300)  # 5 minutes au lieu de 3
    return {**state, "scraped": scraped}


# async def scraping_node(state: GraphState) -> GraphState:
#     scraped = await newsletter_client.call_tool("scrape_news", {
#         "theme": state["theme"],
#         "description": state["description"]
#     })
#     return {**state, "scraped": scraped}

async def vectorize_node(state: GraphState) -> GraphState:
    await newsletter_client.call_tool("vectorize_news", {
        "chunks": state["scraped"]
    })
    return state

async def generate_from_vectorized_node(state: GraphState) -> GraphState:
    newsletter = await newsletter_client.call_tool("generate_newsletter", {
        "chunks": state["scraped"],
        "theme": state["theme"]
    })
    return {**state, "newsletter": newsletter}

# 📁 File node

async def generate_from_file_node(state: GraphState) -> GraphState:
    newsletter = await newsletter_client.call_tool("generate_newsletter_from_file", {
        "text": state["text"],
        "theme": state["theme"],
        "description": state["description"]
    })
    return {**state, "newsletter": newsletter}

# ✍️ Révision

async def revise_node(state: GraphState) -> GraphState:
    revised = await newsletter_client.call_tool("revise_newsletter", {
        "original_newsletter": state["newsletter"],
        "feedback": state["feedback"]
    })
    return {**state, "revised": revised}

# 🚀 Construction du graphe LangGraph

def build_newsletter_graph(with_revision: bool = True, skip_vectorization: bool = False):
    builder = StateGraph(GraphState)

    async def route_start(state: GraphState) -> GraphState:
        return state

    builder.add_node("route_start", route_start)
    builder.set_entry_point("route_start")

    # 🎯 Rediriger selon source : fichier ou web
    def choose_entry(state: GraphState) -> str:
        return "generate_from_file" if state.get("use_file") else "scrape"

    builder.add_conditional_edges("route_start", choose_entry)

    # 🔧 Web path
    builder.add_node("scrape", scraping_node)
    
    if not skip_vectorization:
        builder.add_node("vectorize", vectorize_node)
        builder.add_node("generate_web", generate_from_vectorized_node)
        builder.add_edge("scrape", "vectorize")
        builder.add_edge("vectorize", "generate_web")
    else:
        builder.add_node("generate_web", generate_from_vectorized_node)
        builder.add_edge("scrape", "generate_web")

    # 📂 File path
    builder.add_node("generate_from_file", generate_from_file_node)

    if with_revision:
        builder.add_node("revise", revise_node)

        def next_after_generation(state: GraphState) -> str:
            return "revise" if state.get("feedback") else END

        builder.add_conditional_edges("generate_web", next_after_generation)
        builder.add_conditional_edges("generate_from_file", next_after_generation)
        builder.set_finish_point("revise")
    else:
        builder.add_edge("generate_web", END)
        builder.add_edge("generate_from_file", END)

    return builder.compile()
