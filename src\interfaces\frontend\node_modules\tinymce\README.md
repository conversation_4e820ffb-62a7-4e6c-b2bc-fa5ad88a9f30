# TinyMCE

The world's #1 open source rich text editor.

**Using an old version of TinyMCE?** We recommend you to upgrade to TinyMCE 7 to continue receiving security updates, or consider [TinyMCE 5 LTS](https://www.tiny.cloud/long-term-support/) if you need more time to upgrade.

Used and trusted by millions of developers, TinyMCE is the world’s most customizable, scalable, and flexible rich text editor. We’ve helped launch the likes of Atlassian, Medium, Evernote (and lots more that we can’t tell you), by empowering them to create exceptional content and experiences for their users.

With more than 350M+ downloads every year, we’re also one of the most trusted enterprise-grade open source HTML editors on the internet. There’s currently more than 100M+ products worldwide, powered by Tiny. As a high powered WYSIWYG editor, TinyMCE is built to scale, designed to innovate, and thrives on delivering results to difficult edge-cases.

You can access a [full featured demo of TinyMCE](https://www.tiny.cloud/docs/tinymce/7/premium-full-featured/) in the docs on the TinyMCE website.

<p align="center">
  <img alt="Screenshot of the TinyMCE Editor" src="https://www.tiny.cloud/storage/github-readme-images/tinymce-editor-6x.png"\>
</p>

## Get started with TinyMCE

Getting started with the TinyMCE rich text editor is easy, and for simple configurations can be done in less than 5 minutes.

[TinyMCE Cloud Deployment Quick Start Guide](https://www.tiny.cloud/docs/tinymce/7/cloud-quick-start/)

[TinyMCE Self-hosted Deployment Guide](https://www.tiny.cloud/docs/tinymce/7/npm-projects/)

TinyMCE provides a range of configuration options that allow you to integrate it into your application. Start customizing with a [basic setup](https://www.tiny.cloud/docs/tinymce/7/basic-setup/).

Configure it for one of three modes of editing:

- [TinyMCE classic editing mode](https://www.tiny.cloud/docs/tinymce/7/use-tinymce-classic/).
- [TinyMCE inline editing mode](https://www.tiny.cloud/docs/tinymce/7/use-tinymce-inline/).
- [TinyMCE distraction-free editing mode](https://www.tiny.cloud/docs/tinymce/7/use-tinymce-distraction-free/).

## Features

### Integration

TinyMCE is easily integrated into your projects with the help of components such as:

- [tinymce-react](https://github.com/tinymce/tinymce-react)
- [tinymce-vue](https://github.com/tinymce/tinymce-vue)
- [tinymce-angular](https://github.com/tinymce/tinymce-angular)

With over 29 integrations, and 400+ APIs, see the TinyMCE docs for a full list of editor [integrations](https://www.tiny.cloud/docs/tinymce/7/integrations/).

### Customization

It is easy to [configure the UI](https://www.tiny.cloud/docs/tinymce/7/customize-ui/) of your rich text editor to match the design of your site, product or application. Due to its flexibility, you can [configure the editor](https://www.tiny.cloud/docs/tinymce/7/basic-setup/) with as much or as little functionality as you like, depending on your requirements.

With [50+ powerful plugins available](https://www.tiny.cloud/tinymce/features/), and content editable as the basis of TinyMCE, adding additional functionality is as simple as including a single line of code.

Realizing the full power of most plugins requires only a few lines more.

### Extensibility

Sometimes your editor requirements can be quite unique, and you need the freedom and flexibility to innovate. Thanks to TinyMCE being open source, you can view the source code and develop your own extensions for custom functionality to meet your own requirements.

The TinyMCE [API](https://www.tiny.cloud/docs/tinymce/7/apis/tinymce.root/) is exposed to make it easier for you to write custom functionality that fits within the existing framework of TinyMCE [UI components](https://www.tiny.cloud/docs/tinymce/7/custom-ui-components/).

### Extended Features and Support

For the professional software teams that require more in-depth efficiency, compliance or collaborative features built to enterprise-grade standards, please [get in touch with our team](https://www.tiny.cloud/contact/).

Tiny also offers dedicated SLAs and support for professional development teams.

## Compiling and contributing

In 2019 the decision was made to transition our codebase to a monorepo. For information on compiling and contributing, see: [contribution guidelines](https://github.com/tinymce/tinymce/blob/master/CONTRIBUTING.md).

As an open source product, we encourage and support the active development of our software.

## Want more information?

Visit the [TinyMCE website](https://tiny.cloud/) and check out the [TinyMCE documentation](https://www.tiny.cloud/docs/).

## License

Licensed under the terms of GNU General Public License Version 2 or later. For full details about the license, please check the LICENSE.md file.
