(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/tinymce/plugins/code/plugin.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
/**
 * TinyMCE version 7.9.1 (2025-05-29)
 */ (function() {
    'use strict';
    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');
    const setContent = (editor, html)=>{
        // We get a lovely "Wrong document" error in IE 11 if we
        // don't move the focus to the editor before creating an undo
        // transaction since it tries to make a bookmark for the current selection
        editor.focus();
        editor.undoManager.transact(()=>{
            editor.setContent(html);
        });
        editor.selection.setCursorLocation();
        editor.nodeChanged();
    };
    const getContent = (editor)=>{
        return editor.getContent({
            source_view: true
        });
    };
    const open = (editor)=>{
        const editorContent = getContent(editor);
        editor.windowManager.open({
            title: 'Source Code',
            size: 'large',
            body: {
                type: 'panel',
                items: [
                    {
                        type: 'textarea',
                        name: 'code'
                    }
                ]
            },
            buttons: [
                {
                    type: 'cancel',
                    name: 'cancel',
                    text: 'Cancel'
                },
                {
                    type: 'submit',
                    name: 'save',
                    text: 'Save',
                    primary: true
                }
            ],
            initialData: {
                code: editorContent
            },
            onSubmit: (api)=>{
                setContent(editor, api.getData().code);
                api.close();
            }
        });
    };
    const register$1 = (editor)=>{
        editor.addCommand('mceCodeEditor', ()=>{
            open(editor);
        });
    };
    const register = (editor)=>{
        const onAction = ()=>editor.execCommand('mceCodeEditor');
        editor.ui.registry.addButton('code', {
            icon: 'sourcecode',
            tooltip: 'Source code',
            onAction
        });
        editor.ui.registry.addMenuItem('code', {
            icon: 'sourcecode',
            text: 'Source code',
            onAction
        });
    };
    var Plugin = ()=>{
        global.add('code', (editor)=>{
            register$1(editor);
            register(editor);
            return {};
        });
    };
    Plugin();
/** *****
     * DO NOT EXPORT ANYTHING
     *
     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE
     *******/ })();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/tinymce/plugins/code/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
// Exports the "code" plugin for usage with module loaders
// Usage:
//   CommonJS:
//     require('tinymce/plugins/code')
//   ES2015:
//     import 'tinymce/plugins/code'
__turbopack_context__.r("[project]/node_modules/tinymce/plugins/code/plugin.js [app-client] (ecmascript)");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/tinymce/plugins/code/index.js [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/node_modules/tinymce/plugins/code/index.js [app-client] (ecmascript)"));
}),
}]);

//# sourceMappingURL=node_modules_tinymce_plugins_code_2efbb394._.js.map