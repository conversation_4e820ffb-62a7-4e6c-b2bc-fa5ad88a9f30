{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAMG;AAJJ;AAIA,wCAA2C;IACzC,CAAC;QACH;QAEA,mFAAmF;QACnF,6DAA6D;QAC7D,IAAI,YAAY,OAAO,WAAW,cAAc,OAAO,GAAG;QAC1D,IAAI,qBAAqB,YAAY,OAAO,GAAG,CAAC,mBAAmB;QACnE,IAAI,oBAAoB,YAAY,OAAO,GAAG,CAAC,kBAAkB;QACjE,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,qBAAqB,YAAY,OAAO,GAAG,CAAC,mBAAmB,QAAQ,8EAA8E;QACzJ,qEAAqE;QAErE,IAAI,wBAAwB,YAAY,OAAO,GAAG,CAAC,sBAAsB;QACzE,IAAI,6BAA6B,YAAY,OAAO,GAAG,CAAC,2BAA2B;QACnF,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,2BAA2B,YAAY,OAAO,GAAG,CAAC,yBAAyB;QAC/E,IAAI,kBAAkB,YAAY,OAAO,GAAG,CAAC,gBAAgB;QAC7D,IAAI,kBAAkB,YAAY,OAAO,GAAG,CAAC,gBAAgB;QAC7D,IAAI,mBAAmB,YAAY,OAAO,GAAG,CAAC,iBAAiB;QAC/D,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,uBAAuB,YAAY,OAAO,GAAG,CAAC,qBAAqB;QACvE,IAAI,mBAAmB,YAAY,OAAO,GAAG,CAAC,iBAAiB;QAE/D,SAAS,mBAAmB,IAAI;YAC9B,OAAO,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,gFAAgF;YACjJ,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,QAAQ,CAAC,KAAK,QAAQ,KAAK,mBAAmB,KAAK,QAAQ,KAAK,mBAAmB,KAAK,QAAQ,KAAK,uBAAuB,KAAK,QAAQ,KAAK,sBAAsB,KAAK,QAAQ,KAAK,0BAA0B,KAAK,QAAQ,KAAK,0BAA0B,KAAK,QAAQ,KAAK,wBAAwB,KAAK,QAAQ,KAAK,oBAAoB,KAAK,QAAQ,KAAK,gBAAgB;QACpmB;QAEA,SAAS,OAAO,MAAM;YACpB,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;gBACjD,IAAI,WAAW,OAAO,QAAQ;gBAE9B,OAAQ;oBACN,KAAK;wBACH,IAAI,OAAO,OAAO,IAAI;wBAEtB,OAAQ;4BACN,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,OAAO;4BAET;gCACE,IAAI,eAAe,QAAQ,KAAK,QAAQ;gCAExC,OAAQ;oCACN,KAAK;oCACL,KAAK;oCACL,KAAK;oCACL,KAAK;oCACL,KAAK;wCACH,OAAO;oCAET;wCACE,OAAO;gCACX;wBAEJ;oBAEF,KAAK;wBACH,OAAO;gBACX;YACF;YAEA,OAAO;QACT,EAAE,iDAAiD;QAEnD,IAAI,YAAY;QAChB,IAAI,iBAAiB;QACrB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,UAAU;QACd,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,SAAS;QACb,IAAI,WAAW;QACf,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI,sCAAsC,OAAO,iCAAiC;QAElF,SAAS,YAAY,MAAM;YACzB;gBACE,IAAI,CAAC,qCAAqC;oBACxC,sCAAsC,MAAM,kDAAkD;oBAE9F,OAAO,CAAC,OAAO,CAAC,0DAA0D,+DAA+D;gBAC3I;YACF;YAEA,OAAO,iBAAiB,WAAW,OAAO,YAAY;QACxD;QACA,SAAS,iBAAiB,MAAM;YAC9B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,kBAAkB,MAAM;YAC/B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,kBAAkB,MAAM;YAC/B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,UAAU,MAAM;YACvB,OAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,QAAQ,KAAK;QAC9E;QACA,SAAS,aAAa,MAAM;YAC1B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,OAAO,MAAM;YACpB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,OAAO,MAAM;YACpB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,SAAS,MAAM;YACtB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,aAAa,MAAM;YAC1B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QAEA,QAAQ,SAAS,GAAG;QACpB,QAAQ,cAAc,GAAG;QACzB,QAAQ,eAAe,GAAG;QAC1B,QAAQ,eAAe,GAAG;QAC1B,QAAQ,OAAO,GAAG;QAClB,QAAQ,UAAU,GAAG;QACrB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,IAAI,GAAG;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,UAAU,GAAG;QACrB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,WAAW,GAAG;QACtB,QAAQ,gBAAgB,GAAG;QAC3B,QAAQ,iBAAiB,GAAG;QAC5B,QAAQ,iBAAiB,GAAG;QAC5B,QAAQ,SAAS,GAAG;QACpB,QAAQ,YAAY,GAAG;QACvB,QAAQ,UAAU,GAAG;QACrB,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;QACjB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,UAAU,GAAG;QACrB,QAAQ,YAAY,GAAG;QACvB,QAAQ,UAAU,GAAG;QACrB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,MAAM,GAAG;IACf,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/node_modules/react-is/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/next/src/build/polyfills/object-assign.ts"], "sourcesContent": ["var assign = Object.assign.bind(Object)\nmodule.exports = assign\nmodule.exports.default = module.exports\n"], "names": ["assign", "Object", "bind", "module", "exports", "default"], "mappings": "AAAA,IAAIA,SAASC,OAAOD,MAAM,CAACE,IAAI,CAACD;AAChCE,OAAOC,OAAO,GAAGJ;AACjBG,OAAOC,OAAO,CAACC,OAAO,GAAGF,OAAOC,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/lib/ReactPropTypesSecret.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAID,IAAI,uBAAuB;AAE3B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/lib/has.js"], "sourcesContent": ["module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/checkPropTypes.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GA4FK;AA1FN;AAEA,IAAI,eAAe,YAAY;AAE/B,wCAA2C;IACzC,IAAI;IACJ,IAAI,qBAAqB,CAAC;IAC1B,IAAI;IAEJ,eAAe,SAAS,IAAI;QAC1B,IAAI,UAAU,cAAc;QAC5B,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAO;IACrB;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,eAAe,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IAC1E,wCAA2C;QACzC,IAAK,IAAI,gBAAgB,UAAW;YAClC,IAAI,IAAI,WAAW,eAAe;gBAChC,IAAI;gBACJ,oEAAoE;gBACpE,mEAAmE;gBACnE,0DAA0D;gBAC1D,IAAI;oBACF,qEAAqE;oBACrE,mEAAmE;oBACnE,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,YAAY;wBACjD,IAAI,MAAM,MACR,CAAC,iBAAiB,aAAa,IAAI,OAAO,WAAW,YAAY,eAAe,mBAChF,iFAAiF,OAAO,SAAS,CAAC,aAAa,GAAG,OAClH;wBAEF,IAAI,IAAI,GAAG;wBACX,MAAM;oBACR;oBACA,QAAQ,SAAS,CAAC,aAAa,CAAC,QAAQ,cAAc,eAAe,UAAU,MAAM;gBACvF,EAAE,OAAO,IAAI;oBACX,QAAQ;gBACV;gBACA,IAAI,SAAS,CAAC,CAAC,iBAAiB,KAAK,GAAG;oBACtC,aACE,CAAC,iBAAiB,aAAa,IAAI,6BACnC,WAAW,OAAO,eAAe,oCACjC,8DAA8D,OAAO,QAAQ,OAC7E,oEACA,mEACA;gBAEJ;gBACA,IAAI,iBAAiB,SAAS,CAAC,CAAC,MAAM,OAAO,IAAI,kBAAkB,GAAG;oBACpE,wEAAwE;oBACxE,cAAc;oBACd,kBAAkB,CAAC,MAAM,OAAO,CAAC,GAAG;oBAEpC,IAAI,QAAQ,WAAW,aAAa;oBAEpC,aACE,YAAY,WAAW,YAAY,MAAM,OAAO,GAAG,CAAC,SAAS,OAAO,QAAQ,EAAE;gBAElF;YACF;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,eAAe,iBAAiB,GAAG;IACjC,IAAI,oDAAyB,cAAc;QACzC,qBAAqB,CAAC;IACxB;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/factoryWithTypeCheckers.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAsKO;AApKR;AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,eAAe,YAAY;AAE/B,wCAA2C;IACzC,eAAe,SAAS,IAAI;QAC1B,IAAI,UAAU,cAAc;QAC5B,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAC;IACf;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA,OAAO,OAAO,GAAG,SAAS,cAAc,EAAE,mBAAmB;IAC3D,iBAAiB,GACjB,IAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO,QAAQ;IACrE,IAAI,uBAAuB,cAAc,sBAAsB;IAE/D;;;;;;;;;;;;;GAaC,GACD,SAAS,cAAc,aAAa;QAClC,IAAI,aAAa,iBAAiB,CAAC,mBAAmB,aAAa,CAAC,gBAAgB,IAAI,aAAa,CAAC,qBAAqB;QAC3H,IAAI,OAAO,eAAe,YAAY;YACpC,OAAO;QACT;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CC,GAED,IAAI,YAAY;IAEhB,aAAa;IACb,qFAAqF;IACrF,IAAI,iBAAiB;QACnB,OAAO,2BAA2B;QAClC,QAAQ,2BAA2B;QACnC,MAAM,2BAA2B;QACjC,MAAM,2BAA2B;QACjC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QAEnC,KAAK;QACL,SAAS;QACT,SAAS;QACT,aAAa;QACb,YAAY;QACZ,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,OAAO;QACP,OAAO;IACT;IAEA;;;GAGC,GACD,gCAAgC,GAChC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,sBAAsB;QACtB,IAAI,MAAM,GAAG;YACX,kBAAkB;YAClB,0BAA0B;YAC1B,OAAO,MAAM,KAAK,IAAI,MAAM,IAAI;QAClC,OAAO;YACL,uBAAuB;YACvB,OAAO,MAAM,KAAK,MAAM;QAC1B;IACF;IACA,+BAA+B,GAE/B;;;;;;GAMC,GACD,SAAS,cAAc,OAAO,EAAE,IAAI;QAClC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG;IACf;IACA,0DAA0D;IAC1D,cAAc,SAAS,GAAG,MAAM,SAAS;IAEzC,SAAS,2BAA2B,QAAQ;QAC1C,IAAI,oDAAyB,cAAc;YACzC,IAAI,0BAA0B,CAAC;YAC/B,IAAI,6BAA6B;QACnC;QACA,SAAS,UAAU,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;YAC3F,gBAAgB,iBAAiB;YACjC,eAAe,gBAAgB;YAE/B,IAAI,WAAW,sBAAsB;gBACnC,IAAI,qBAAqB;oBACvB,sDAAsD;oBACtD,IAAI,MAAM,IAAI,MACZ,yFACA,oDACA;oBAEF,IAAI,IAAI,GAAG;oBACX,MAAM;gBACR,OAAO,IAAI,oDAAyB,gBAAgB,OAAO,YAAY,aAAa;oBAClF,gDAAgD;oBAChD,IAAI,WAAW,gBAAgB,MAAM;oBACrC,IACE,CAAC,uBAAuB,CAAC,SAAS,IAClC,0FAA0F;oBAC1F,6BAA6B,GAC7B;wBACA,aACE,2DACA,uBAAuB,eAAe,gBAAgB,gBAAgB,2BACtE,4DACA,mEACA,kEAAkE;wBAEpE,uBAAuB,CAAC,SAAS,GAAG;wBACpC;oBACF;gBACF;YACF;YACA,IAAI,KAAK,CAAC,SAAS,IAAI,MAAM;gBAC3B,IAAI,YAAY;oBACd,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM;wBAC5B,OAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,6BAA6B,CAAC,SAAS,gBAAgB,6BAA6B;oBACzJ;oBACA,OAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,gCAAgC,CAAC,MAAM,gBAAgB,kCAAkC;gBAC9J;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,SAAS,OAAO,UAAU,eAAe,UAAU;YAC5D;QACF;QAEA,IAAI,mBAAmB,UAAU,IAAI,CAAC,MAAM;QAC5C,iBAAiB,UAAU,GAAG,UAAU,IAAI,CAAC,MAAM;QAEnD,OAAO;IACT;IAEA,SAAS,2BAA2B,YAAY;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;YAC9E,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,cAAc;gBAC7B,qEAAqE;gBACrE,wEAAwE;gBACxE,sBAAsB;gBACtB,IAAI,cAAc,eAAe;gBAEjC,OAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,cAAc,oBAAoB,gBAAgB,cAAc,IAAI,CAAC,MAAM,eAAe,IAAI,GAClK;oBAAC,cAAc;gBAAY;YAE/B;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,OAAO,2BAA2B;IACpC;IAEA,SAAS,yBAAyB,WAAW;QAC3C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,OAAO,gBAAgB,YAAY;gBACrC,OAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;YAC9F;YACA,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;gBAC7B,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,uBAAuB;YACrK;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK;gBAC7F,IAAI,iBAAiB,OAAO;oBAC1B,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,eAAe,YAAY;gBAC9B,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,oCAAoC;YAClL;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,QAAQ,kBAAkB,CAAC,YAAY;gBAC1C,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,yCAAyC;YACvL;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,0BAA0B,aAAa;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,YAAY,aAAa,GAAG;gBAC/C,IAAI,oBAAoB,cAAc,IAAI,IAAI;gBAC9C,IAAI,kBAAkB,aAAa,KAAK,CAAC,SAAS;gBAClD,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,kBAAkB,oBAAoB,gBAAgB,cAAc,IAAI,CAAC,kBAAkB,oBAAoB,IAAI;YAClN;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,sBAAsB,cAAc;QAC3C,IAAI,CAAC,MAAM,OAAO,CAAC,iBAAiB;YAClC,wCAA2C;gBACzC,IAAI,UAAU,MAAM,GAAG,GAAG;oBACxB,aACE,iEAAiE,UAAU,MAAM,GAAG,iBACpF;gBAEJ,OAAO;oBACL,aAAa;gBACf;YACF;YACA,OAAO;QACT;QAEA,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,GAAG,WAAW,cAAc,CAAC,EAAE,GAAG;oBACpC,OAAO;gBACT;YACF;YAEA,IAAI,eAAe,KAAK,SAAS,CAAC,gBAAgB,SAAS,SAAS,GAAG,EAAE,KAAK;gBAC5E,IAAI,OAAO,eAAe;gBAC1B,IAAI,SAAS,UAAU;oBACrB,OAAO,OAAO;gBAChB;gBACA,OAAO;YACT;YACA,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,aAAa,OAAO,CAAC,kBAAkB,gBAAgB,wBAAwB,eAAe,GAAG;QAClM;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,0BAA0B,WAAW;QAC5C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,OAAO,gBAAgB,YAAY;gBACrC,OAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;YAC9F;YACA,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;YACtK;YACA,IAAK,IAAI,OAAO,UAAW;gBACzB,IAAI,IAAI,WAAW,MAAM;oBACvB,IAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;oBAC3F,IAAI,iBAAiB,OAAO;wBAC1B,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,uBAAuB,mBAAmB;QACjD,IAAI,CAAC,MAAM,OAAO,CAAC,sBAAsB;YACvC,uCAAwC,aAAa,4EAA4E;YACjI,OAAO;QACT;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;YACnD,IAAI,UAAU,mBAAmB,CAAC,EAAE;YACpC,IAAI,OAAO,YAAY,YAAY;gBACjC,aACE,uFACA,cAAc,yBAAyB,WAAW,eAAe,IAAI;gBAEvE,OAAO;YACT;QACF;QAEA,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,gBAAgB,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;gBACnD,IAAI,UAAU,mBAAmB,CAAC,EAAE;gBACpC,IAAI,gBAAgB,QAAQ,OAAO,UAAU,eAAe,UAAU,cAAc;gBACpF,IAAI,iBAAiB,MAAM;oBACzB,OAAO;gBACT;gBACA,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,IAAI,EAAE,iBAAiB;oBACjE,cAAc,IAAI,CAAC,cAAc,IAAI,CAAC,YAAY;gBACpD;YACF;YACA,IAAI,uBAAuB,AAAC,cAAc,MAAM,GAAG,IAAK,6BAA6B,cAAc,IAAI,CAAC,QAAQ,MAAK;YACrH,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,mBAAmB,CAAC,MAAM,gBAAgB,MAAM,uBAAuB,GAAG;QACnJ;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,CAAC,OAAO,KAAK,CAAC,SAAS,GAAG;gBAC5B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,mBAAmB,CAAC,MAAM,gBAAgB,0BAA0B;YAC7I;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,sBAAsB,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI;QAC7E,OAAO,IAAI,cACT,CAAC,iBAAiB,aAAa,IAAI,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,mBAC5F,iFAAiF,OAAO;IAE5F;IAEA,SAAS,uBAAuB,UAAU;QACxC,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,OAAO,CAAC,kBAAkB,gBAAgB,uBAAuB;YACrK;YACA,IAAK,IAAI,OAAO,WAAY;gBAC1B,IAAI,UAAU,UAAU,CAAC,IAAI;gBAC7B,IAAI,OAAO,YAAY,YAAY;oBACjC,OAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe;gBAC1F;gBACA,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;gBACvF,IAAI,OAAO;oBACT,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,6BAA6B,UAAU;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,OAAO,CAAC,kBAAkB,gBAAgB,uBAAuB;YACrK;YACA,8EAA8E;YAC9E,IAAI,UAAU,OAAO,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE;YAC1C,IAAK,IAAI,OAAO,QAAS;gBACvB,IAAI,UAAU,UAAU,CAAC,IAAI;gBAC7B,IAAI,IAAI,YAAY,QAAQ,OAAO,YAAY,YAAY;oBACzD,OAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe;gBAC1F;gBACA,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,OACpG,mBAAmB,KAAK,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,QACzD,mBAAmB,KAAK,SAAS,CAAC,OAAO,IAAI,CAAC,aAAa,MAAM;gBAErE;gBACA,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;gBACvF,IAAI,OAAO;oBACT,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAEA,OAAO,2BAA2B;IACpC;IAEA,SAAS,OAAO,SAAS;QACvB,OAAQ,OAAO;YACb,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC;YACV,KAAK;gBACH,IAAI,MAAM,OAAO,CAAC,YAAY;oBAC5B,OAAO,UAAU,KAAK,CAAC;gBACzB;gBACA,IAAI,cAAc,QAAQ,eAAe,YAAY;oBACnD,OAAO;gBACT;gBAEA,IAAI,aAAa,cAAc;gBAC/B,IAAI,YAAY;oBACd,IAAI,WAAW,WAAW,IAAI,CAAC;oBAC/B,IAAI;oBACJ,IAAI,eAAe,UAAU,OAAO,EAAE;wBACpC,MAAO,CAAC,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,IAAI,CAAE;4BACrC,IAAI,CAAC,OAAO,KAAK,KAAK,GAAG;gCACvB,OAAO;4BACT;wBACF;oBACF,OAAO;wBACL,+DAA+D;wBAC/D,MAAO,CAAC,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,IAAI,CAAE;4BACrC,IAAI,QAAQ,KAAK,KAAK;4BACtB,IAAI,OAAO;gCACT,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,GAAG;oCACrB,OAAO;gCACT;4BACF;wBACF;oBACF;gBACF,OAAO;oBACL,OAAO;gBACT;gBAEA,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS,SAAS,QAAQ,EAAE,SAAS;QACnC,iBAAiB;QACjB,IAAI,aAAa,UAAU;YACzB,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,wDAAwD;QACxD,IAAI,SAAS,CAAC,gBAAgB,KAAK,UAAU;YAC3C,OAAO;QACT;QAEA,gEAAgE;QAChE,IAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;YAC/D,OAAO;QACT;QAEA,OAAO;IACT;IAEA,yEAAyE;IACzE,SAAS,YAAY,SAAS;QAC5B,IAAI,WAAW,OAAO;QACtB,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,OAAO;QACT;QACA,IAAI,qBAAqB,QAAQ;YAC/B,yEAAyE;YACzE,wEAAwE;YACxE,2BAA2B;YAC3B,OAAO;QACT;QACA,IAAI,SAAS,UAAU,YAAY;YACjC,OAAO;QACT;QACA,OAAO;IACT;IAEA,4EAA4E;IAC5E,oCAAoC;IACpC,SAAS,eAAe,SAAS;QAC/B,IAAI,OAAO,cAAc,eAAe,cAAc,MAAM;YAC1D,OAAO,KAAK;QACd;QACA,IAAI,WAAW,YAAY;QAC3B,IAAI,aAAa,UAAU;YACzB,IAAI,qBAAqB,MAAM;gBAC7B,OAAO;YACT,OAAO,IAAI,qBAAqB,QAAQ;gBACtC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,yEAAyE;IACzE,8CAA8C;IAC9C,SAAS,yBAAyB,KAAK;QACrC,IAAI,OAAO,eAAe;QAC1B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,OAAO;YAChB;gBACE,OAAO;QACX;IACF;IAEA,4CAA4C;IAC5C,SAAS,aAAa,SAAS;QAC7B,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,UAAU,WAAW,CAAC,IAAI,EAAE;YACzD,OAAO;QACT;QACA,OAAO,UAAU,WAAW,CAAC,IAAI;IACnC;IAEA,eAAe,cAAc,GAAG;IAChC,eAAe,iBAAiB,GAAG,eAAe,iBAAiB;IACnE,eAAe,SAAS,GAAG;IAE3B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/prop-types/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAEG;AAAJ,wCAA2C;IACzC,IAAI;IAEJ,iFAAiF;IACjF,kCAAkC;IAClC,IAAI,sBAAsB;IAC1B,OAAO,OAAO,GAAG,kHAAqC,QAAQ,SAAS,EAAE;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/components/EditorPropTypes.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport * as PropTypes from 'prop-types';\nexport var eventPropTypes = {\n    onActivate: PropTypes.func,\n    onAddUndo: PropTypes.func,\n    onBeforeAddUndo: PropTypes.func,\n    onBeforeExecCommand: PropTypes.func,\n    onBeforeGetContent: PropTypes.func,\n    onBeforeRenderUI: PropTypes.func,\n    onBeforeSetContent: PropTypes.func,\n    onBeforePaste: PropTypes.func,\n    onBlur: PropTypes.func,\n    onChange: PropTypes.func,\n    onClearUndos: PropTypes.func,\n    onClick: PropTypes.func,\n    onContextMenu: PropTypes.func,\n    onCommentChange: PropTypes.func,\n    onCompositionEnd: PropTypes.func,\n    onCompositionStart: PropTypes.func,\n    onCompositionUpdate: PropTypes.func,\n    onCopy: PropTypes.func,\n    onCut: PropTypes.func,\n    onDblclick: PropTypes.func,\n    onDeactivate: PropTypes.func,\n    onDirty: PropTypes.func,\n    onDrag: PropTypes.func,\n    onDragDrop: PropTypes.func,\n    onDragEnd: PropTypes.func,\n    onDragGesture: PropTypes.func,\n    onDragOver: PropTypes.func,\n    onDrop: PropTypes.func,\n    onExecCommand: PropTypes.func,\n    onFocus: PropTypes.func,\n    onFocusIn: PropTypes.func,\n    onFocusOut: PropTypes.func,\n    onGetContent: PropTypes.func,\n    onHide: PropTypes.func,\n    onInit: PropTypes.func,\n    onInput: PropTypes.func,\n    onKeyDown: PropTypes.func,\n    onKeyPress: PropTypes.func,\n    onKeyUp: PropTypes.func,\n    onLoadContent: PropTypes.func,\n    onMouseDown: PropTypes.func,\n    onMouseEnter: PropTypes.func,\n    onMouseLeave: PropTypes.func,\n    onMouseMove: PropTypes.func,\n    onMouseOut: PropTypes.func,\n    onMouseOver: PropTypes.func,\n    onMouseUp: PropTypes.func,\n    onNodeChange: PropTypes.func,\n    onObjectResizeStart: PropTypes.func,\n    onObjectResized: PropTypes.func,\n    onObjectSelected: PropTypes.func,\n    onPaste: PropTypes.func,\n    onPostProcess: PropTypes.func,\n    onPostRender: PropTypes.func,\n    onPreProcess: PropTypes.func,\n    onProgressState: PropTypes.func,\n    onRedo: PropTypes.func,\n    onRemove: PropTypes.func,\n    onReset: PropTypes.func,\n    onSaveContent: PropTypes.func,\n    onSelectionChange: PropTypes.func,\n    onSetAttrib: PropTypes.func,\n    onSetContent: PropTypes.func,\n    onShow: PropTypes.func,\n    onSubmit: PropTypes.func,\n    onUndo: PropTypes.func,\n    onVisualAid: PropTypes.func,\n    onSkinLoadError: PropTypes.func,\n    onThemeLoadError: PropTypes.func,\n    onModelLoadError: PropTypes.func,\n    onPluginLoadError: PropTypes.func,\n    onIconsLoadError: PropTypes.func,\n    onLanguageLoadError: PropTypes.func,\n    onScriptsLoad: PropTypes.func,\n    onScriptsLoadError: PropTypes.func,\n};\nexport var EditorPropTypes = __assign({ apiKey: PropTypes.string, licenseKey: PropTypes.string, id: PropTypes.string, inline: PropTypes.bool, init: PropTypes.object, initialValue: PropTypes.string, onEditorChange: PropTypes.func, value: PropTypes.string, tagName: PropTypes.string, tabIndex: PropTypes.number, cloudChannel: PropTypes.string, plugins: PropTypes.oneOfType([PropTypes.string, PropTypes.array]), toolbar: PropTypes.oneOfType([PropTypes.string, PropTypes.array]), disabled: PropTypes.bool, textareaName: PropTypes.string, tinymceScriptSrc: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.arrayOf(PropTypes.string),\n        PropTypes.arrayOf(PropTypes.shape({\n            src: PropTypes.string,\n            async: PropTypes.bool,\n            defer: PropTypes.bool\n        }))\n    ]), rollback: PropTypes.oneOfType([PropTypes.number, PropTypes.oneOf([false])]), scriptLoading: PropTypes.shape({\n        async: PropTypes.bool,\n        defer: PropTypes.bool,\n        delay: PropTypes.number\n    }) }, eventPropTypes);\n"], "names": [], "mappings": ";;;;AAWA;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;;AAEO,IAAI,iBAAiB;IACxB,YAAY,yIAAA,CAAA,OAAc;IAC1B,WAAW,yIAAA,CAAA,OAAc;IACzB,iBAAiB,yIAAA,CAAA,OAAc;IAC/B,qBAAqB,yIAAA,CAAA,OAAc;IACnC,oBAAoB,yIAAA,CAAA,OAAc;IAClC,kBAAkB,yIAAA,CAAA,OAAc;IAChC,oBAAoB,yIAAA,CAAA,OAAc;IAClC,eAAe,yIAAA,CAAA,OAAc;IAC7B,QAAQ,yIAAA,CAAA,OAAc;IACtB,UAAU,yIAAA,CAAA,OAAc;IACxB,cAAc,yIAAA,CAAA,OAAc;IAC5B,SAAS,yIAAA,CAAA,OAAc;IACvB,eAAe,yIAAA,CAAA,OAAc;IAC7B,iBAAiB,yIAAA,CAAA,OAAc;IAC/B,kBAAkB,yIAAA,CAAA,OAAc;IAChC,oBAAoB,yIAAA,CAAA,OAAc;IAClC,qBAAqB,yIAAA,CAAA,OAAc;IACnC,QAAQ,yIAAA,CAAA,OAAc;IACtB,OAAO,yIAAA,CAAA,OAAc;IACrB,YAAY,yIAAA,CAAA,OAAc;IAC1B,cAAc,yIAAA,CAAA,OAAc;IAC5B,SAAS,yIAAA,CAAA,OAAc;IACvB,QAAQ,yIAAA,CAAA,OAAc;IACtB,YAAY,yIAAA,CAAA,OAAc;IAC1B,WAAW,yIAAA,CAAA,OAAc;IACzB,eAAe,yIAAA,CAAA,OAAc;IAC7B,YAAY,yIAAA,CAAA,OAAc;IAC1B,QAAQ,yIAAA,CAAA,OAAc;IACtB,eAAe,yIAAA,CAAA,OAAc;IAC7B,SAAS,yIAAA,CAAA,OAAc;IACvB,WAAW,yIAAA,CAAA,OAAc;IACzB,YAAY,yIAAA,CAAA,OAAc;IAC1B,cAAc,yIAAA,CAAA,OAAc;IAC5B,QAAQ,yIAAA,CAAA,OAAc;IACtB,QAAQ,yIAAA,CAAA,OAAc;IACtB,SAAS,yIAAA,CAAA,OAAc;IACvB,WAAW,yIAAA,CAAA,OAAc;IACzB,YAAY,yIAAA,CAAA,OAAc;IAC1B,SAAS,yIAAA,CAAA,OAAc;IACvB,eAAe,yIAAA,CAAA,OAAc;IAC7B,aAAa,yIAAA,CAAA,OAAc;IAC3B,cAAc,yIAAA,CAAA,OAAc;IAC5B,cAAc,yIAAA,CAAA,OAAc;IAC5B,aAAa,yIAAA,CAAA,OAAc;IAC3B,YAAY,yIAAA,CAAA,OAAc;IAC1B,aAAa,yIAAA,CAAA,OAAc;IAC3B,WAAW,yIAAA,CAAA,OAAc;IACzB,cAAc,yIAAA,CAAA,OAAc;IAC5B,qBAAqB,yIAAA,CAAA,OAAc;IACnC,iBAAiB,yIAAA,CAAA,OAAc;IAC/B,kBAAkB,yIAAA,CAAA,OAAc;IAChC,SAAS,yIAAA,CAAA,OAAc;IACvB,eAAe,yIAAA,CAAA,OAAc;IAC7B,cAAc,yIAAA,CAAA,OAAc;IAC5B,cAAc,yIAAA,CAAA,OAAc;IAC5B,iBAAiB,yIAAA,CAAA,OAAc;IAC/B,QAAQ,yIAAA,CAAA,OAAc;IACtB,UAAU,yIAAA,CAAA,OAAc;IACxB,SAAS,yIAAA,CAAA,OAAc;IACvB,eAAe,yIAAA,CAAA,OAAc;IAC7B,mBAAmB,yIAAA,CAAA,OAAc;IACjC,aAAa,yIAAA,CAAA,OAAc;IAC3B,cAAc,yIAAA,CAAA,OAAc;IAC5B,QAAQ,yIAAA,CAAA,OAAc;IACtB,UAAU,yIAAA,CAAA,OAAc;IACxB,QAAQ,yIAAA,CAAA,OAAc;IACtB,aAAa,yIAAA,CAAA,OAAc;IAC3B,iBAAiB,yIAAA,CAAA,OAAc;IAC/B,kBAAkB,yIAAA,CAAA,OAAc;IAChC,kBAAkB,yIAAA,CAAA,OAAc;IAChC,mBAAmB,yIAAA,CAAA,OAAc;IACjC,kBAAkB,yIAAA,CAAA,OAAc;IAChC,qBAAqB,yIAAA,CAAA,OAAc;IACnC,eAAe,yIAAA,CAAA,OAAc;IAC7B,oBAAoB,yIAAA,CAAA,OAAc;AACtC;AACO,IAAI,kBAAkB,SAAS;IAAE,QAAQ,yIAAA,CAAA,SAAgB;IAAE,YAAY,yIAAA,CAAA,SAAgB;IAAE,IAAI,yIAAA,CAAA,SAAgB;IAAE,QAAQ,yIAAA,CAAA,OAAc;IAAE,MAAM,yIAAA,CAAA,SAAgB;IAAE,cAAc,yIAAA,CAAA,SAAgB;IAAE,gBAAgB,yIAAA,CAAA,OAAc;IAAE,OAAO,yIAAA,CAAA,SAAgB;IAAE,SAAS,yIAAA,CAAA,SAAgB;IAAE,UAAU,yIAAA,CAAA,SAAgB;IAAE,cAAc,yIAAA,CAAA,SAAgB;IAAE,SAAS,yIAAA,CAAA,YAAmB,CAAC;QAAC,yIAAA,CAAA,SAAgB;QAAE,yIAAA,CAAA,QAAe;KAAC;IAAG,SAAS,yIAAA,CAAA,YAAmB,CAAC;QAAC,yIAAA,CAAA,SAAgB;QAAE,yIAAA,CAAA,QAAe;KAAC;IAAG,UAAU,yIAAA,CAAA,OAAc;IAAE,cAAc,yIAAA,CAAA,SAAgB;IAAE,kBAAkB,yIAAA,CAAA,YAAmB,CAAC;QACpjB,yIAAA,CAAA,SAAgB;QAChB,yIAAA,CAAA,UAAiB,CAAC,yIAAA,CAAA,SAAgB;QAClC,yIAAA,CAAA,UAAiB,CAAC,yIAAA,CAAA,QAAe,CAAC;YAC9B,KAAK,yIAAA,CAAA,SAAgB;YACrB,OAAO,yIAAA,CAAA,OAAc;YACrB,OAAO,yIAAA,CAAA,OAAc;QACzB;KACH;IAAG,UAAU,yIAAA,CAAA,YAAmB,CAAC;QAAC,yIAAA,CAAA,SAAgB;QAAE,yIAAA,CAAA,QAAe,CAAC;YAAC;SAAM;KAAE;IAAG,eAAe,yIAAA,CAAA,QAAe,CAAC;QAC5G,OAAO,yIAAA,CAAA,OAAc;QACrB,OAAO,yIAAA,CAAA,OAAc;QACrB,OAAO,yIAAA,CAAA,SAAgB;IAC3B;AAAG,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/Utils.js"], "sourcesContent": ["import { eventPropTypes } from './components/EditorPropTypes';\nexport var isFunction = function (x) { return typeof x === 'function'; };\nvar isEventProp = function (name) { return name in eventPropTypes; };\nvar eventAttrToEventName = function (attrName) { return attrName.substr(2); };\nexport var configHandlers2 = function (handlerLookup, on, off, adapter, prevProps, props, boundHandlers) {\n    var prevEventKeys = Object.keys(prevProps).filter(isEventProp);\n    var currEventKeys = Object.keys(props).filter(isEventProp);\n    var removedKeys = prevEventKeys.filter(function (key) { return props[key] === undefined; });\n    var addedKeys = currEventKeys.filter(function (key) { return prevProps[key] === undefined; });\n    removedKeys.forEach(function (key) {\n        // remove event handler\n        var eventName = eventAttrToEventName(key);\n        var wrappedHandler = boundHandlers[eventName];\n        off(eventName, wrappedHandler);\n        delete boundHandlers[eventName];\n    });\n    addedKeys.forEach(function (key) {\n        var wrappedHandler = adapter(handlerLookup, key);\n        var eventName = eventAttrToEventName(key);\n        boundHandlers[eventName] = wrappedHandler;\n        on(eventName, wrappedHandler);\n    });\n};\nexport var configHandlers = function (editor, prevProps, props, boundHandlers, lookup) {\n    return configHandlers2(lookup, editor.on.bind(editor), editor.off.bind(editor), \n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    function (handlerLookup, key) { return function (e) { var _a; return (_a = handlerLookup(key)) === null || _a === void 0 ? void 0 : _a(e, editor); }; }, prevProps, props, boundHandlers);\n};\nvar unique = 0;\nexport var uuid = function (prefix) {\n    var time = Date.now();\n    var random = Math.floor(Math.random() * 1000000000);\n    unique++;\n    return prefix + '_' + random + unique + String(time);\n};\nexport var isTextareaOrInput = function (element) {\n    return element !== null && (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input');\n};\nvar normalizePluginArray = function (plugins) {\n    if (typeof plugins === 'undefined' || plugins === '') {\n        return [];\n    }\n    return Array.isArray(plugins) ? plugins : plugins.split(' ');\n};\n// eslint-disable-next-line max-len\nexport var mergePlugins = function (initPlugins, inputPlugins) { return normalizePluginArray(initPlugins).concat(normalizePluginArray(inputPlugins)); };\nexport var isBeforeInputEventAvailable = function () { return window.InputEvent && typeof InputEvent.prototype.getTargetRanges === 'function'; };\nexport var isInDoc = function (elem) {\n    if (!('isConnected' in Node.prototype)) {\n        // Fallback for IE and old Edge\n        var current = elem;\n        var parent_1 = elem.parentNode;\n        while (parent_1 != null) {\n            current = parent_1;\n            parent_1 = current.parentNode;\n        }\n        return current === elem.ownerDocument;\n    }\n    return elem.isConnected;\n};\nexport var setMode = function (editor, mode) {\n    if (editor !== undefined) {\n        if (editor.mode != null && typeof editor.mode === 'object' && typeof editor.mode.set === 'function') {\n            editor.mode.set(mode);\n        }\n        else { // support TinyMCE 4\n            editor.setMode(mode);\n        }\n    }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AACO,IAAI,aAAa,SAAU,CAAC;IAAI,OAAO,OAAO,MAAM;AAAY;AACvE,IAAI,cAAc,SAAU,IAAI;IAAI,OAAO,QAAQ,gNAAA,CAAA,iBAAc;AAAE;AACnE,IAAI,uBAAuB,SAAU,QAAQ;IAAI,OAAO,SAAS,MAAM,CAAC;AAAI;AACrE,IAAI,kBAAkB,SAAU,aAAa,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa;IACnG,IAAI,gBAAgB,OAAO,IAAI,CAAC,WAAW,MAAM,CAAC;IAClD,IAAI,gBAAgB,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC;IAC9C,IAAI,cAAc,cAAc,MAAM,CAAC,SAAU,GAAG;QAAI,OAAO,KAAK,CAAC,IAAI,KAAK;IAAW;IACzF,IAAI,YAAY,cAAc,MAAM,CAAC,SAAU,GAAG;QAAI,OAAO,SAAS,CAAC,IAAI,KAAK;IAAW;IAC3F,YAAY,OAAO,CAAC,SAAU,GAAG;QAC7B,uBAAuB;QACvB,IAAI,YAAY,qBAAqB;QACrC,IAAI,iBAAiB,aAAa,CAAC,UAAU;QAC7C,IAAI,WAAW;QACf,OAAO,aAAa,CAAC,UAAU;IACnC;IACA,UAAU,OAAO,CAAC,SAAU,GAAG;QAC3B,IAAI,iBAAiB,QAAQ,eAAe;QAC5C,IAAI,YAAY,qBAAqB;QACrC,aAAa,CAAC,UAAU,GAAG;QAC3B,GAAG,WAAW;IAClB;AACJ;AACO,IAAI,iBAAiB,SAAU,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM;IACjF,OAAO,gBAAgB,QAAQ,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,CAAC,IAAI,CAAC,SACvE,iEAAiE;IACjE,SAAU,aAAa,EAAE,GAAG;QAAI,OAAO,SAAU,CAAC;YAAI,IAAI;YAAI,OAAO,CAAC,KAAK,cAAc,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG;QAAS;IAAG,GAAG,WAAW,OAAO;AAC/K;AACA,IAAI,SAAS;AACN,IAAI,OAAO,SAAU,MAAM;IAC9B,IAAI,OAAO,KAAK,GAAG;IACnB,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IACxC;IACA,OAAO,SAAS,MAAM,SAAS,SAAS,OAAO;AACnD;AACO,IAAI,oBAAoB,SAAU,OAAO;IAC5C,OAAO,YAAY,QAAQ,CAAC,QAAQ,OAAO,CAAC,WAAW,OAAO,cAAc,QAAQ,OAAO,CAAC,WAAW,OAAO,OAAO;AACzH;AACA,IAAI,uBAAuB,SAAU,OAAO;IACxC,IAAI,OAAO,YAAY,eAAe,YAAY,IAAI;QAClD,OAAO,EAAE;IACb;IACA,OAAO,MAAM,OAAO,CAAC,WAAW,UAAU,QAAQ,KAAK,CAAC;AAC5D;AAEO,IAAI,eAAe,SAAU,WAAW,EAAE,YAAY;IAAI,OAAO,qBAAqB,aAAa,MAAM,CAAC,qBAAqB;AAAgB;AAC/I,IAAI,8BAA8B;IAAc,OAAO,OAAO,UAAU,IAAI,OAAO,WAAW,SAAS,CAAC,eAAe,KAAK;AAAY;AACxI,IAAI,UAAU,SAAU,IAAI;IAC/B,IAAI,CAAC,CAAC,iBAAiB,KAAK,SAAS,GAAG;QACpC,+BAA+B;QAC/B,IAAI,UAAU;QACd,IAAI,WAAW,KAAK,UAAU;QAC9B,MAAO,YAAY,KAAM;YACrB,UAAU;YACV,WAAW,QAAQ,UAAU;QACjC;QACA,OAAO,YAAY,KAAK,aAAa;IACzC;IACA,OAAO,KAAK,WAAW;AAC3B;AACO,IAAI,UAAU,SAAU,MAAM,EAAE,IAAI;IACvC,IAAI,WAAW,WAAW;QACtB,IAAI,OAAO,IAAI,IAAI,QAAQ,OAAO,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,IAAI,CAAC,GAAG,KAAK,YAAY;YACjG,OAAO,IAAI,CAAC,GAAG,CAAC;QACpB,OACK;YACD,OAAO,OAAO,CAAC;QACnB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/ScriptLoader2.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { uuid } from './Utils';\nvar injectScriptTag = function (doc, item, handler) {\n    var _a, _b;\n    var scriptTag = doc.createElement('script');\n    scriptTag.referrerPolicy = 'origin';\n    scriptTag.type = 'application/javascript';\n    scriptTag.id = item.id;\n    scriptTag.src = item.src;\n    scriptTag.async = (_a = item.async) !== null && _a !== void 0 ? _a : false;\n    scriptTag.defer = (_b = item.defer) !== null && _b !== void 0 ? _b : false;\n    var loadHandler = function () {\n        scriptTag.removeEventListener('load', loadHandler);\n        scriptTag.removeEventListener('error', errorHandler);\n        handler(item.src);\n    };\n    var errorHandler = function (err) {\n        scriptTag.removeEventListener('load', loadHandler);\n        scriptTag.removeEventListener('error', errorHandler);\n        handler(item.src, err);\n    };\n    scriptTag.addEventListener('load', loadHandler);\n    scriptTag.addEventListener('error', errorHandler);\n    if (doc.head) {\n        doc.head.appendChild(scriptTag);\n    }\n};\nvar createDocumentScriptLoader = function (doc) {\n    var lookup = {};\n    var scriptLoadOrErrorHandler = function (src, err) {\n        var item = lookup[src];\n        item.done = true;\n        item.error = err;\n        for (var _i = 0, _a = item.handlers; _i < _a.length; _i++) {\n            var h = _a[_i];\n            h(src, err);\n        }\n        item.handlers = [];\n    };\n    var loadScripts = function (items, success, failure) {\n        // eslint-disable-next-line no-console\n        var failureOrLog = function (err) { return failure !== undefined ? failure(err) : console.error(err); };\n        if (items.length === 0) {\n            failureOrLog(new Error('At least one script must be provided'));\n            return;\n        }\n        var successCount = 0;\n        var failed = false;\n        var loaded = function (_src, err) {\n            if (failed) {\n                return;\n            }\n            if (err) {\n                failed = true;\n                failureOrLog(err);\n            }\n            else if (++successCount === items.length) {\n                success();\n            }\n        };\n        for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n            var item = items_1[_i];\n            var existing = lookup[item.src];\n            if (existing) {\n                if (existing.done) {\n                    loaded(item.src, existing.error);\n                }\n                else {\n                    existing.handlers.push(loaded);\n                }\n            }\n            else {\n                // create a new entry\n                var id = uuid('tiny-');\n                lookup[item.src] = {\n                    id: id,\n                    src: item.src,\n                    done: false,\n                    error: null,\n                    handlers: [loaded],\n                };\n                injectScriptTag(doc, __assign({ id: id }, item), scriptLoadOrErrorHandler);\n            }\n        }\n    };\n    var deleteScripts = function () {\n        var _a;\n        for (var _i = 0, _b = Object.values(lookup); _i < _b.length; _i++) {\n            var item = _b[_i];\n            var scriptTag = doc.getElementById(item.id);\n            if (scriptTag != null && scriptTag.tagName === 'SCRIPT') {\n                (_a = scriptTag.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(scriptTag);\n            }\n        }\n        lookup = {};\n    };\n    var getDocument = function () { return doc; };\n    return {\n        loadScripts: loadScripts,\n        deleteScripts: deleteScripts,\n        getDocument: getDocument\n    };\n};\nvar createScriptLoader = function () {\n    var cache = [];\n    var getDocumentScriptLoader = function (doc) {\n        var loader = cache.find(function (l) { return l.getDocument() === doc; });\n        if (loader === undefined) {\n            loader = createDocumentScriptLoader(doc);\n            cache.push(loader);\n        }\n        return loader;\n    };\n    var loadList = function (doc, items, delay, success, failure) {\n        var doLoad = function () { return getDocumentScriptLoader(doc).loadScripts(items, success, failure); };\n        if (delay > 0) {\n            setTimeout(doLoad, delay);\n        }\n        else {\n            doLoad();\n        }\n    };\n    var reinitialize = function () {\n        for (var loader = cache.pop(); loader != null; loader = cache.pop()) {\n            loader.deleteScripts();\n        }\n    };\n    return {\n        loadList: loadList,\n        reinitialize: reinitialize\n    };\n};\nexport var ScriptLoader = createScriptLoader();\n"], "names": [], "mappings": ";;;AAWA;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;;AAEA,IAAI,kBAAkB,SAAU,GAAG,EAAE,IAAI,EAAE,OAAO;IAC9C,IAAI,IAAI;IACR,IAAI,YAAY,IAAI,aAAa,CAAC;IAClC,UAAU,cAAc,GAAG;IAC3B,UAAU,IAAI,GAAG;IACjB,UAAU,EAAE,GAAG,KAAK,EAAE;IACtB,UAAU,GAAG,GAAG,KAAK,GAAG;IACxB,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACrE,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACrE,IAAI,cAAc;QACd,UAAU,mBAAmB,CAAC,QAAQ;QACtC,UAAU,mBAAmB,CAAC,SAAS;QACvC,QAAQ,KAAK,GAAG;IACpB;IACA,IAAI,eAAe,SAAU,GAAG;QAC5B,UAAU,mBAAmB,CAAC,QAAQ;QACtC,UAAU,mBAAmB,CAAC,SAAS;QACvC,QAAQ,KAAK,GAAG,EAAE;IACtB;IACA,UAAU,gBAAgB,CAAC,QAAQ;IACnC,UAAU,gBAAgB,CAAC,SAAS;IACpC,IAAI,IAAI,IAAI,EAAE;QACV,IAAI,IAAI,CAAC,WAAW,CAAC;IACzB;AACJ;AACA,IAAI,6BAA6B,SAAU,GAAG;IAC1C,IAAI,SAAS,CAAC;IACd,IAAI,2BAA2B,SAAU,GAAG,EAAE,GAAG;QAC7C,IAAI,OAAO,MAAM,CAAC,IAAI;QACtB,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK,GAAG;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,KAAK,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACvD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,EAAE,KAAK;QACX;QACA,KAAK,QAAQ,GAAG,EAAE;IACtB;IACA,IAAI,cAAc,SAAU,KAAK,EAAE,OAAO,EAAE,OAAO;QAC/C,sCAAsC;QACtC,IAAI,eAAe,SAAU,GAAG;YAAI,OAAO,YAAY,YAAY,QAAQ,OAAO,QAAQ,KAAK,CAAC;QAAM;QACtG,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB,aAAa,IAAI,MAAM;YACvB;QACJ;QACA,IAAI,eAAe;QACnB,IAAI,SAAS;QACb,IAAI,SAAS,SAAU,IAAI,EAAE,GAAG;YAC5B,IAAI,QAAQ;gBACR;YACJ;YACA,IAAI,KAAK;gBACL,SAAS;gBACT,aAAa;YACjB,OACK,IAAI,EAAE,iBAAiB,MAAM,MAAM,EAAE;gBACtC;YACJ;QACJ;QACA,IAAK,IAAI,KAAK,GAAG,UAAU,OAAO,KAAK,QAAQ,MAAM,EAAE,KAAM;YACzD,IAAI,OAAO,OAAO,CAAC,GAAG;YACtB,IAAI,WAAW,MAAM,CAAC,KAAK,GAAG,CAAC;YAC/B,IAAI,UAAU;gBACV,IAAI,SAAS,IAAI,EAAE;oBACf,OAAO,KAAK,GAAG,EAAE,SAAS,KAAK;gBACnC,OACK;oBACD,SAAS,QAAQ,CAAC,IAAI,CAAC;gBAC3B;YACJ,OACK;gBACD,qBAAqB;gBACrB,IAAI,KAAK,CAAA,GAAA,wLAAA,CAAA,OAAI,AAAD,EAAE;gBACd,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG;oBACf,IAAI;oBACJ,KAAK,KAAK,GAAG;oBACb,MAAM;oBACN,OAAO;oBACP,UAAU;wBAAC;qBAAO;gBACtB;gBACA,gBAAgB,KAAK,SAAS;oBAAE,IAAI;gBAAG,GAAG,OAAO;YACrD;QACJ;IACJ;IACA,IAAI,gBAAgB;QAChB,IAAI;QACJ,IAAK,IAAI,KAAK,GAAG,KAAK,OAAO,MAAM,CAAC,SAAS,KAAK,GAAG,MAAM,EAAE,KAAM;YAC/D,IAAI,OAAO,EAAE,CAAC,GAAG;YACjB,IAAI,YAAY,IAAI,cAAc,CAAC,KAAK,EAAE;YAC1C,IAAI,aAAa,QAAQ,UAAU,OAAO,KAAK,UAAU;gBACrD,CAAC,KAAK,UAAU,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC;YACpF;QACJ;QACA,SAAS,CAAC;IACd;IACA,IAAI,cAAc;QAAc,OAAO;IAAK;IAC5C,OAAO;QACH,aAAa;QACb,eAAe;QACf,aAAa;IACjB;AACJ;AACA,IAAI,qBAAqB;IACrB,IAAI,QAAQ,EAAE;IACd,IAAI,0BAA0B,SAAU,GAAG;QACvC,IAAI,SAAS,MAAM,IAAI,CAAC,SAAU,CAAC;YAAI,OAAO,EAAE,WAAW,OAAO;QAAK;QACvE,IAAI,WAAW,WAAW;YACtB,SAAS,2BAA2B;YACpC,MAAM,IAAI,CAAC;QACf;QACA,OAAO;IACX;IACA,IAAI,WAAW,SAAU,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO;QACxD,IAAI,SAAS;YAAc,OAAO,wBAAwB,KAAK,WAAW,CAAC,OAAO,SAAS;QAAU;QACrG,IAAI,QAAQ,GAAG;YACX,WAAW,QAAQ;QACvB,OACK;YACD;QACJ;IACJ;IACA,IAAI,eAAe;QACf,IAAK,IAAI,SAAS,MAAM,GAAG,IAAI,UAAU,MAAM,SAAS,MAAM,GAAG,GAAI;YACjE,OAAO,aAAa;QACxB;IACJ;IACA,OAAO;QACH,UAAU;QACV,cAAc;IAClB;AACJ;AACO,IAAI,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/TinyMCE.js"], "sourcesContent": ["var getTinymce = function (view) {\n    var global = view;\n    return global && global.tinymce ? global.tinymce : null;\n};\nexport { getTinymce };\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa,SAAU,IAAI;IAC3B,IAAI,SAAS;IACb,OAAO,UAAU,OAAO,OAAO,GAAG,OAAO,OAAO,GAAG;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/components/Editor.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport * as React from 'react';\nimport { ScriptLoader } from '../ScriptLoader2';\nimport { getTinymce } from '../TinyMCE';\nimport { isFunction, isTextareaOrInput, mergePlugins, uuid, configHandlers, isBeforeInputEventAvailable, isInDoc, setMode } from '../Utils';\nimport { EditorPropTypes } from './EditorPropTypes';\n/**\n * @see {@link https://www.tiny.cloud/docs/tinymce/7/react-ref/} for the TinyMCE React Technical Reference\n */\nvar Editor = /** @class */ (function (_super) {\n    __extends(Editor, _super);\n    function Editor(props) {\n        var _a, _b, _c;\n        var _this = _super.call(this, props) || this;\n        _this.rollbackTimer = undefined;\n        _this.valueCursor = undefined;\n        _this.rollbackChange = function () {\n            var editor = _this.editor;\n            var value = _this.props.value;\n            if (editor && value && value !== _this.currentContent) {\n                editor.undoManager.ignore(function () {\n                    editor.setContent(value);\n                    // only restore cursor on inline editors when they are focused\n                    // as otherwise it will cause a focus grab\n                    if (_this.valueCursor && (!_this.inline || editor.hasFocus())) {\n                        try {\n                            editor.selection.moveToBookmark(_this.valueCursor);\n                        }\n                        catch (e) { /* ignore */ }\n                    }\n                });\n            }\n            _this.rollbackTimer = undefined;\n        };\n        _this.handleBeforeInput = function (_evt) {\n            if (_this.props.value !== undefined && _this.props.value === _this.currentContent && _this.editor) {\n                if (!_this.inline || _this.editor.hasFocus()) {\n                    try {\n                        // getBookmark throws exceptions when the editor has not been focused\n                        // possibly only in inline mode but I'm not taking chances\n                        _this.valueCursor = _this.editor.selection.getBookmark(3);\n                    }\n                    catch (e) { /* ignore */ }\n                }\n            }\n        };\n        _this.handleBeforeInputSpecial = function (evt) {\n            if (evt.key === 'Enter' || evt.key === 'Backspace' || evt.key === 'Delete') {\n                _this.handleBeforeInput(evt);\n            }\n        };\n        _this.handleEditorChange = function (_evt) {\n            var editor = _this.editor;\n            if (editor && editor.initialized) {\n                var newContent = editor.getContent();\n                if (_this.props.value !== undefined && _this.props.value !== newContent && _this.props.rollback !== false) {\n                    // start a timer and revert to the value if not applied in time\n                    if (!_this.rollbackTimer) {\n                        _this.rollbackTimer = window.setTimeout(_this.rollbackChange, typeof _this.props.rollback === 'number' ? _this.props.rollback : 200);\n                    }\n                }\n                if (newContent !== _this.currentContent) {\n                    _this.currentContent = newContent;\n                    if (isFunction(_this.props.onEditorChange)) {\n                        _this.props.onEditorChange(newContent, editor);\n                    }\n                }\n            }\n        };\n        _this.handleEditorChangeSpecial = function (evt) {\n            if (evt.key === 'Backspace' || evt.key === 'Delete') {\n                _this.handleEditorChange(evt);\n            }\n        };\n        _this.initialise = function (attempts) {\n            var _a, _b, _c;\n            if (attempts === void 0) { attempts = 0; }\n            var target = _this.elementRef.current;\n            if (!target) {\n                return; // Editor has been unmounted\n            }\n            if (!isInDoc(target)) {\n                // this is probably someone trying to help by rendering us offscreen\n                // but we can't do that because the editor iframe must be in the document\n                // in order to have state\n                if (attempts === 0) {\n                    // we probably just need to wait for the current events to be processed\n                    setTimeout(function () { return _this.initialise(1); }, 1);\n                }\n                else if (attempts < 100) {\n                    // wait for ten seconds, polling every tenth of a second\n                    setTimeout(function () { return _this.initialise(attempts + 1); }, 100);\n                }\n                else {\n                    // give up, at this point it seems that more polling is unlikely to help\n                    throw new Error('tinymce can only be initialised when in a document');\n                }\n                return;\n            }\n            var tinymce = getTinymce(_this.view);\n            if (!tinymce) {\n                throw new Error('tinymce should have been loaded into global scope');\n            }\n            var finalInit = __assign(__assign(__assign(__assign({}, _this.props.init), { selector: undefined, target: target, readonly: _this.props.disabled, inline: _this.inline, plugins: mergePlugins((_a = _this.props.init) === null || _a === void 0 ? void 0 : _a.plugins, _this.props.plugins), toolbar: (_b = _this.props.toolbar) !== null && _b !== void 0 ? _b : (_c = _this.props.init) === null || _c === void 0 ? void 0 : _c.toolbar }), (_this.props.licenseKey ? { license_key: _this.props.licenseKey } : {})), { setup: function (editor) {\n                    _this.editor = editor;\n                    _this.bindHandlers({});\n                    // When running in inline mode the editor gets the initial value\n                    // from the innerHTML of the element it is initialized on.\n                    // However we don't want to take on the responsibility of sanitizing\n                    // to remove XSS in the react integration so we have a chicken and egg\n                    // problem... We avoid it by sneaking in a set content before the first\n                    // \"official\" setContent and using TinyMCE to do the sanitization.\n                    if (_this.inline && !isTextareaOrInput(target)) {\n                        editor.once('PostRender', function (_evt) {\n                            editor.setContent(_this.getInitialValue(), { no_events: true });\n                        });\n                    }\n                    if (_this.props.init && isFunction(_this.props.init.setup)) {\n                        _this.props.init.setup(editor);\n                    }\n                }, init_instance_callback: function (editor) {\n                    var _a, _b;\n                    // check for changes that happened since tinymce.init() was called\n                    var initialValue = _this.getInitialValue();\n                    _this.currentContent = (_a = _this.currentContent) !== null && _a !== void 0 ? _a : editor.getContent();\n                    if (_this.currentContent !== initialValue) {\n                        _this.currentContent = initialValue;\n                        // same as resetContent in TinyMCE 5\n                        editor.setContent(initialValue);\n                        editor.undoManager.clear();\n                        editor.undoManager.add();\n                        editor.setDirty(false);\n                    }\n                    var disabled = (_b = _this.props.disabled) !== null && _b !== void 0 ? _b : false;\n                    setMode(_this.editor, disabled ? 'readonly' : 'design');\n                    // ensure existing init_instance_callback is called\n                    if (_this.props.init && isFunction(_this.props.init.init_instance_callback)) {\n                        _this.props.init.init_instance_callback(editor);\n                    }\n                } });\n            if (!_this.inline) {\n                target.style.visibility = '';\n            }\n            if (isTextareaOrInput(target)) {\n                target.value = _this.getInitialValue();\n            }\n            tinymce.init(finalInit);\n        };\n        _this.id = _this.props.id || uuid('tiny-react');\n        _this.elementRef = React.createRef();\n        _this.inline = (_c = (_a = _this.props.inline) !== null && _a !== void 0 ? _a : (_b = _this.props.init) === null || _b === void 0 ? void 0 : _b.inline) !== null && _c !== void 0 ? _c : false;\n        _this.boundHandlers = {};\n        return _this;\n    }\n    Object.defineProperty(Editor.prototype, \"view\", {\n        get: function () {\n            var _a, _b;\n            return (_b = (_a = this.elementRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument.defaultView) !== null && _b !== void 0 ? _b : window;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Editor.prototype.componentDidUpdate = function (prevProps) {\n        var _this = this;\n        var _a, _b;\n        if (this.rollbackTimer) {\n            clearTimeout(this.rollbackTimer);\n            this.rollbackTimer = undefined;\n        }\n        if (this.editor) {\n            this.bindHandlers(prevProps);\n            if (this.editor.initialized) {\n                this.currentContent = (_a = this.currentContent) !== null && _a !== void 0 ? _a : this.editor.getContent();\n                if (typeof this.props.initialValue === 'string' && this.props.initialValue !== prevProps.initialValue) {\n                    // same as resetContent in TinyMCE 5\n                    this.editor.setContent(this.props.initialValue);\n                    this.editor.undoManager.clear();\n                    this.editor.undoManager.add();\n                    this.editor.setDirty(false);\n                }\n                else if (typeof this.props.value === 'string' && this.props.value !== this.currentContent) {\n                    var localEditor_1 = this.editor;\n                    localEditor_1.undoManager.transact(function () {\n                        // inline editors grab focus when restoring selection\n                        // so we don't try to keep their selection unless they are currently focused\n                        var cursor;\n                        if (!_this.inline || localEditor_1.hasFocus()) {\n                            try {\n                                // getBookmark throws exceptions when the editor has not been focused\n                                // possibly only in inline mode but I'm not taking chances\n                                cursor = localEditor_1.selection.getBookmark(3);\n                            }\n                            catch (e) { /* ignore */ }\n                        }\n                        var valueCursor = _this.valueCursor;\n                        localEditor_1.setContent(_this.props.value);\n                        if (!_this.inline || localEditor_1.hasFocus()) {\n                            for (var _i = 0, _a = [cursor, valueCursor]; _i < _a.length; _i++) {\n                                var bookmark = _a[_i];\n                                if (bookmark) {\n                                    try {\n                                        localEditor_1.selection.moveToBookmark(bookmark);\n                                        _this.valueCursor = bookmark;\n                                        break;\n                                    }\n                                    catch (e) { /* ignore */ }\n                                }\n                            }\n                        }\n                    });\n                }\n                if (this.props.disabled !== prevProps.disabled) {\n                    var disabled = (_b = this.props.disabled) !== null && _b !== void 0 ? _b : false;\n                    setMode(this.editor, disabled ? 'readonly' : 'design');\n                }\n            }\n        }\n    };\n    Editor.prototype.componentDidMount = function () {\n        var _this = this;\n        var _a, _b, _c, _d, _e;\n        if (getTinymce(this.view) !== null) {\n            this.initialise();\n        }\n        else if (Array.isArray(this.props.tinymceScriptSrc) && this.props.tinymceScriptSrc.length === 0) {\n            (_b = (_a = this.props).onScriptsLoadError) === null || _b === void 0 ? void 0 : _b.call(_a, new Error('No `tinymce` global is present but the `tinymceScriptSrc` prop was an empty array.'));\n        }\n        else if ((_c = this.elementRef.current) === null || _c === void 0 ? void 0 : _c.ownerDocument) {\n            var successHandler = function () {\n                var _a, _b;\n                (_b = (_a = _this.props).onScriptsLoad) === null || _b === void 0 ? void 0 : _b.call(_a);\n                _this.initialise();\n            };\n            var errorHandler = function (err) {\n                var _a, _b;\n                (_b = (_a = _this.props).onScriptsLoadError) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n            };\n            ScriptLoader.loadList(this.elementRef.current.ownerDocument, this.getScriptSources(), (_e = (_d = this.props.scriptLoading) === null || _d === void 0 ? void 0 : _d.delay) !== null && _e !== void 0 ? _e : 0, successHandler, errorHandler);\n        }\n    };\n    Editor.prototype.componentWillUnmount = function () {\n        var _this = this;\n        var editor = this.editor;\n        if (editor) {\n            editor.off(this.changeEvents(), this.handleEditorChange);\n            editor.off(this.beforeInputEvent(), this.handleBeforeInput);\n            editor.off('keypress', this.handleEditorChangeSpecial);\n            editor.off('keydown', this.handleBeforeInputSpecial);\n            editor.off('NewBlock', this.handleEditorChange);\n            Object.keys(this.boundHandlers).forEach(function (eventName) {\n                editor.off(eventName, _this.boundHandlers[eventName]);\n            });\n            this.boundHandlers = {};\n            editor.remove();\n            this.editor = undefined;\n        }\n    };\n    Editor.prototype.render = function () {\n        return this.inline ? this.renderInline() : this.renderIframe();\n    };\n    Editor.prototype.changeEvents = function () {\n        var _a, _b, _c;\n        var isIE = (_c = (_b = (_a = getTinymce(this.view)) === null || _a === void 0 ? void 0 : _a.Env) === null || _b === void 0 ? void 0 : _b.browser) === null || _c === void 0 ? void 0 : _c.isIE();\n        return (isIE\n            ? 'change keyup compositionend setcontent CommentChange'\n            : 'change input compositionend setcontent CommentChange');\n    };\n    Editor.prototype.beforeInputEvent = function () {\n        return isBeforeInputEventAvailable() ? 'beforeinput SelectionChange' : 'SelectionChange';\n    };\n    Editor.prototype.renderInline = function () {\n        var _a = this.props.tagName, tagName = _a === void 0 ? 'div' : _a;\n        return React.createElement(tagName, {\n            ref: this.elementRef,\n            id: this.id,\n            tabIndex: this.props.tabIndex\n        });\n    };\n    Editor.prototype.renderIframe = function () {\n        return React.createElement('textarea', {\n            ref: this.elementRef,\n            style: { visibility: 'hidden' },\n            name: this.props.textareaName,\n            id: this.id,\n            tabIndex: this.props.tabIndex\n        });\n    };\n    Editor.prototype.getScriptSources = function () {\n        var _a, _b;\n        var async = (_a = this.props.scriptLoading) === null || _a === void 0 ? void 0 : _a.async;\n        var defer = (_b = this.props.scriptLoading) === null || _b === void 0 ? void 0 : _b.defer;\n        if (this.props.tinymceScriptSrc !== undefined) {\n            if (typeof this.props.tinymceScriptSrc === 'string') {\n                return [{ src: this.props.tinymceScriptSrc, async: async, defer: defer }];\n            }\n            // multiple scripts can be specified which allows for hybrid mode\n            return this.props.tinymceScriptSrc.map(function (item) {\n                if (typeof item === 'string') {\n                    // async does not make sense for multiple items unless\n                    // they are not dependent (which will be unlikely)\n                    return { src: item, async: async, defer: defer };\n                }\n                else {\n                    return item;\n                }\n            });\n        }\n        // fallback to the cloud when the tinymceScriptSrc is not specified\n        var channel = this.props.cloudChannel; // `cloudChannel` is in `defaultProps`, so it's always defined.\n        var apiKey = this.props.apiKey ? this.props.apiKey : 'no-api-key';\n        var cloudTinyJs = \"https://cdn.tiny.cloud/1/\".concat(apiKey, \"/tinymce/\").concat(channel, \"/tinymce.min.js\");\n        return [{ src: cloudTinyJs, async: async, defer: defer }];\n    };\n    Editor.prototype.getInitialValue = function () {\n        if (typeof this.props.initialValue === 'string') {\n            return this.props.initialValue;\n        }\n        else if (typeof this.props.value === 'string') {\n            return this.props.value;\n        }\n        else {\n            return '';\n        }\n    };\n    Editor.prototype.bindHandlers = function (prevProps) {\n        var _this = this;\n        if (this.editor !== undefined) {\n            // typescript chokes trying to understand the type of the lookup function\n            configHandlers(this.editor, prevProps, this.props, this.boundHandlers, function (key) { return _this.props[key]; });\n            // check if we should monitor editor changes\n            var isValueControlled = function (p) { return p.onEditorChange !== undefined || p.value !== undefined; };\n            var wasControlled = isValueControlled(prevProps);\n            var nowControlled = isValueControlled(this.props);\n            if (!wasControlled && nowControlled) {\n                this.editor.on(this.changeEvents(), this.handleEditorChange);\n                this.editor.on(this.beforeInputEvent(), this.handleBeforeInput);\n                this.editor.on('keydown', this.handleBeforeInputSpecial);\n                this.editor.on('keyup', this.handleEditorChangeSpecial);\n                this.editor.on('NewBlock', this.handleEditorChange);\n            }\n            else if (wasControlled && !nowControlled) {\n                this.editor.off(this.changeEvents(), this.handleEditorChange);\n                this.editor.off(this.beforeInputEvent(), this.handleBeforeInput);\n                this.editor.off('keydown', this.handleBeforeInputSpecial);\n                this.editor.off('keyup', this.handleEditorChangeSpecial);\n                this.editor.off('NewBlock', this.handleEditorChange);\n            }\n        }\n    };\n    Editor.propTypes = EditorPropTypes;\n    Editor.defaultProps = {\n        cloudChannel: '7',\n    };\n    return Editor;\n}(React.Component));\nexport { Editor };\n"], "names": [], "mappings": ";;;AA0BA;AACA;AACA;AACA;AACA;AA9BA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;;;;;;AAMA;;CAEC,GACD,IAAI,SAAwB,SAAU,MAAM;IACxC,UAAU,QAAQ;IAClB,SAAS,OAAO,KAAK;QACjB,IAAI,IAAI,IAAI;QACZ,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,UAAU,IAAI;QAC5C,MAAM,aAAa,GAAG;QACtB,MAAM,WAAW,GAAG;QACpB,MAAM,cAAc,GAAG;YACnB,IAAI,SAAS,MAAM,MAAM;YACzB,IAAI,QAAQ,MAAM,KAAK,CAAC,KAAK;YAC7B,IAAI,UAAU,SAAS,UAAU,MAAM,cAAc,EAAE;gBACnD,OAAO,WAAW,CAAC,MAAM,CAAC;oBACtB,OAAO,UAAU,CAAC;oBAClB,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAI,MAAM,WAAW,IAAI,CAAC,CAAC,MAAM,MAAM,IAAI,OAAO,QAAQ,EAAE,GAAG;wBAC3D,IAAI;4BACA,OAAO,SAAS,CAAC,cAAc,CAAC,MAAM,WAAW;wBACrD,EACA,OAAO,GAAG,CAAe;oBAC7B;gBACJ;YACJ;YACA,MAAM,aAAa,GAAG;QAC1B;QACA,MAAM,iBAAiB,GAAG,SAAU,IAAI;YACpC,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,aAAa,MAAM,KAAK,CAAC,KAAK,KAAK,MAAM,cAAc,IAAI,MAAM,MAAM,EAAE;gBAC/F,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,IAAI;oBAC1C,IAAI;wBACA,qEAAqE;wBACrE,0DAA0D;wBAC1D,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC;oBAC3D,EACA,OAAO,GAAG,CAAe;gBAC7B;YACJ;QACJ;QACA,MAAM,wBAAwB,GAAG,SAAU,GAAG;YAC1C,IAAI,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,UAAU;gBACxE,MAAM,iBAAiB,CAAC;YAC5B;QACJ;QACA,MAAM,kBAAkB,GAAG,SAAU,IAAI;YACrC,IAAI,SAAS,MAAM,MAAM;YACzB,IAAI,UAAU,OAAO,WAAW,EAAE;gBAC9B,IAAI,aAAa,OAAO,UAAU;gBAClC,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,aAAa,MAAM,KAAK,CAAC,KAAK,KAAK,cAAc,MAAM,KAAK,CAAC,QAAQ,KAAK,OAAO;oBACvG,+DAA+D;oBAC/D,IAAI,CAAC,MAAM,aAAa,EAAE;wBACtB,MAAM,aAAa,GAAG,OAAO,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,MAAM,KAAK,CAAC,QAAQ,KAAK,WAAW,MAAM,KAAK,CAAC,QAAQ,GAAG;oBACpI;gBACJ;gBACA,IAAI,eAAe,MAAM,cAAc,EAAE;oBACrC,MAAM,cAAc,GAAG;oBACvB,IAAI,CAAA,GAAA,wLAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,CAAC,cAAc,GAAG;wBACxC,MAAM,KAAK,CAAC,cAAc,CAAC,YAAY;oBAC3C;gBACJ;YACJ;QACJ;QACA,MAAM,yBAAyB,GAAG,SAAU,GAAG;YAC3C,IAAI,IAAI,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,UAAU;gBACjD,MAAM,kBAAkB,CAAC;YAC7B;QACJ;QACA,MAAM,UAAU,GAAG,SAAU,QAAQ;YACjC,IAAI,IAAI,IAAI;YACZ,IAAI,aAAa,KAAK,GAAG;gBAAE,WAAW;YAAG;YACzC,IAAI,SAAS,MAAM,UAAU,CAAC,OAAO;YACrC,IAAI,CAAC,QAAQ;gBACT,QAAQ,4BAA4B;YACxC;YACA,IAAI,CAAC,CAAA,GAAA,wLAAA,CAAA,UAAO,AAAD,EAAE,SAAS;gBAClB,oEAAoE;gBACpE,yEAAyE;gBACzE,yBAAyB;gBACzB,IAAI,aAAa,GAAG;oBAChB,uEAAuE;oBACvE,WAAW;wBAAc,OAAO,MAAM,UAAU,CAAC;oBAAI,GAAG;gBAC5D,OACK,IAAI,WAAW,KAAK;oBACrB,wDAAwD;oBACxD,WAAW;wBAAc,OAAO,MAAM,UAAU,CAAC,WAAW;oBAAI,GAAG;gBACvE,OACK;oBACD,wEAAwE;oBACxE,MAAM,IAAI,MAAM;gBACpB;gBACA;YACJ;YACA,IAAI,UAAU,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI;YACnC,IAAI,CAAC,SAAS;gBACV,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,YAAY,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,KAAK,CAAC,IAAI,GAAG;gBAAE,UAAU;gBAAW,QAAQ;gBAAQ,UAAU,MAAM,KAAK,CAAC,QAAQ;gBAAE,QAAQ,MAAM,MAAM;gBAAE,SAAS,CAAA,GAAA,wLAAA,CAAA,eAAY,AAAD,EAAE,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,EAAE,MAAM,KAAK,CAAC,OAAO;gBAAG,SAAS,CAAC,KAAK,MAAM,KAAK,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;YAAC,IAAK,MAAM,KAAK,CAAC,UAAU,GAAG;gBAAE,aAAa,MAAM,KAAK,CAAC,UAAU;YAAC,IAAI,CAAC,IAAK;gBAAE,OAAO,SAAU,MAAM;oBACzgB,MAAM,MAAM,GAAG;oBACf,MAAM,YAAY,CAAC,CAAC;oBACpB,gEAAgE;oBAChE,0DAA0D;oBAC1D,oEAAoE;oBACpE,sEAAsE;oBACtE,uEAAuE;oBACvE,kEAAkE;oBAClE,IAAI,MAAM,MAAM,IAAI,CAAC,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;wBAC5C,OAAO,IAAI,CAAC,cAAc,SAAU,IAAI;4BACpC,OAAO,UAAU,CAAC,MAAM,eAAe,IAAI;gCAAE,WAAW;4BAAK;wBACjE;oBACJ;oBACA,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAA,GAAA,wLAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG;wBACxD,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC3B;gBACJ;gBAAG,wBAAwB,SAAU,MAAM;oBACvC,IAAI,IAAI;oBACR,kEAAkE;oBAClE,IAAI,eAAe,MAAM,eAAe;oBACxC,MAAM,cAAc,GAAG,CAAC,KAAK,MAAM,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,UAAU;oBACrG,IAAI,MAAM,cAAc,KAAK,cAAc;wBACvC,MAAM,cAAc,GAAG;wBACvB,oCAAoC;wBACpC,OAAO,UAAU,CAAC;wBAClB,OAAO,WAAW,CAAC,KAAK;wBACxB,OAAO,WAAW,CAAC,GAAG;wBACtB,OAAO,QAAQ,CAAC;oBACpB;oBACA,IAAI,WAAW,CAAC,KAAK,MAAM,KAAK,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;oBAC5E,CAAA,GAAA,wLAAA,CAAA,UAAO,AAAD,EAAE,MAAM,MAAM,EAAE,WAAW,aAAa;oBAC9C,mDAAmD;oBACnD,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAA,GAAA,wLAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,sBAAsB,GAAG;wBACzE,MAAM,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC;oBAC5C;gBACJ;YAAE;YACN,IAAI,CAAC,MAAM,MAAM,EAAE;gBACf,OAAO,KAAK,CAAC,UAAU,GAAG;YAC9B;YACA,IAAI,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;gBAC3B,OAAO,KAAK,GAAG,MAAM,eAAe;YACxC;YACA,QAAQ,IAAI,CAAC;QACjB;QACA,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,EAAE,IAAI,CAAA,GAAA,wLAAA,CAAA,OAAI,AAAD,EAAE;QAClC,MAAM,UAAU,iBAAG,6JAAA,CAAA,YAAe;QAClC,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACzL,MAAM,aAAa,GAAG,CAAC;QACvB,OAAO;IACX;IACA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,QAAQ;QAC5C,KAAK;YACD,IAAI,IAAI;YACR,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACpJ;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,SAAS,CAAC,kBAAkB,GAAG,SAAU,SAAS;QACrD,IAAI,QAAQ,IAAI;QAChB,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,aAAa,IAAI,CAAC,aAAa;YAC/B,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACzB,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,IAAI,CAAC,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU;gBACxG,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,YAAY,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,UAAU,YAAY,EAAE;oBACnG,oCAAoC;oBACpC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY;oBAC9C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK;oBAC7B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG;oBAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACzB,OACK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;oBACvF,IAAI,gBAAgB,IAAI,CAAC,MAAM;oBAC/B,cAAc,WAAW,CAAC,QAAQ,CAAC;wBAC/B,qDAAqD;wBACrD,4EAA4E;wBAC5E,IAAI;wBACJ,IAAI,CAAC,MAAM,MAAM,IAAI,cAAc,QAAQ,IAAI;4BAC3C,IAAI;gCACA,qEAAqE;gCACrE,0DAA0D;gCAC1D,SAAS,cAAc,SAAS,CAAC,WAAW,CAAC;4BACjD,EACA,OAAO,GAAG,CAAe;wBAC7B;wBACA,IAAI,cAAc,MAAM,WAAW;wBACnC,cAAc,UAAU,CAAC,MAAM,KAAK,CAAC,KAAK;wBAC1C,IAAI,CAAC,MAAM,MAAM,IAAI,cAAc,QAAQ,IAAI;4BAC3C,IAAK,IAAI,KAAK,GAAG,KAAK;gCAAC;gCAAQ;6BAAY,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;gCAC/D,IAAI,WAAW,EAAE,CAAC,GAAG;gCACrB,IAAI,UAAU;oCACV,IAAI;wCACA,cAAc,SAAS,CAAC,cAAc,CAAC;wCACvC,MAAM,WAAW,GAAG;wCACpB;oCACJ,EACA,OAAO,GAAG,CAAe;gCAC7B;4BACJ;wBACJ;oBACJ;gBACJ;gBACA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,QAAQ,EAAE;oBAC5C,IAAI,WAAW,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;oBAC3E,CAAA,GAAA,wLAAA,CAAA,UAAO,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,aAAa;gBACjD;YACJ;QACJ;IACJ;IACA,OAAO,SAAS,CAAC,iBAAiB,GAAG;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,IAAI,IAAI,IAAI,IAAI;QACpB,IAAI,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,IAAI,MAAM,MAAM;YAChC,IAAI,CAAC,UAAU;QACnB,OACK,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,KAAK,GAAG;YAC7F,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,MAAM;QAC3G,OACK,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,EAAE;YAC3F,IAAI,iBAAiB;gBACjB,IAAI,IAAI;gBACR,CAAC,KAAK,CAAC,KAAK,MAAM,KAAK,EAAE,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;gBACrF,MAAM,UAAU;YACpB;YACA,IAAI,eAAe,SAAU,GAAG;gBAC5B,IAAI,IAAI;gBACR,CAAC,KAAK,CAAC,KAAK,MAAM,KAAK,EAAE,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;YAClG;YACA,gMAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,GAAG,gBAAgB;QACnO;IACJ;IACA,OAAO,SAAS,CAAC,oBAAoB,GAAG;QACpC,IAAI,QAAQ,IAAI;QAChB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,QAAQ;YACR,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB;YACvD,OAAO,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB;YAC1D,OAAO,GAAG,CAAC,YAAY,IAAI,CAAC,yBAAyB;YACrD,OAAO,GAAG,CAAC,WAAW,IAAI,CAAC,wBAAwB;YACnD,OAAO,GAAG,CAAC,YAAY,IAAI,CAAC,kBAAkB;YAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,SAAU,SAAS;gBACvD,OAAO,GAAG,CAAC,WAAW,MAAM,aAAa,CAAC,UAAU;YACxD;YACA,IAAI,CAAC,aAAa,GAAG,CAAC;YACtB,OAAO,MAAM;YACb,IAAI,CAAC,MAAM,GAAG;QAClB;IACJ;IACA,OAAO,SAAS,CAAC,MAAM,GAAG;QACtB,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY;IAChE;IACA,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,IAAI,IAAI,IAAI;QACZ,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI;QAC9L,OAAQ,OACF,yDACA;IACV;IACA,OAAO,SAAS,CAAC,gBAAgB,GAAG;QAChC,OAAO,CAAA,GAAA,wLAAA,CAAA,8BAA2B,AAAD,MAAM,gCAAgC;IAC3E;IACA,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,QAAQ;QAC/D,qBAAO,6JAAA,CAAA,gBAAmB,CAAC,SAAS;YAChC,KAAK,IAAI,CAAC,UAAU;YACpB,IAAI,IAAI,CAAC,EAAE;YACX,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;QACjC;IACJ;IACA,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,qBAAO,6JAAA,CAAA,gBAAmB,CAAC,YAAY;YACnC,KAAK,IAAI,CAAC,UAAU;YACpB,OAAO;gBAAE,YAAY;YAAS;YAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY;YAC7B,IAAI,IAAI,CAAC,EAAE;YACX,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;QACjC;IACJ;IACA,OAAO,SAAS,CAAC,gBAAgB,GAAG;QAChC,IAAI,IAAI;QACR,IAAI,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QACzF,IAAI,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QACzF,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,WAAW;YAC3C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,UAAU;gBACjD,OAAO;oBAAC;wBAAE,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB;wBAAE,OAAO;wBAAO,OAAO;oBAAM;iBAAE;YAC7E;YACA,iEAAiE;YACjE,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAU,IAAI;gBACjD,IAAI,OAAO,SAAS,UAAU;oBAC1B,sDAAsD;oBACtD,kDAAkD;oBAClD,OAAO;wBAAE,KAAK;wBAAM,OAAO;wBAAO,OAAO;oBAAM;gBACnD,OACK;oBACD,OAAO;gBACX;YACJ;QACJ;QACA,mEAAmE;QACnE,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,+DAA+D;QACtG,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACrD,IAAI,cAAc,4BAA4B,MAAM,CAAC,QAAQ,aAAa,MAAM,CAAC,SAAS;QAC1F,OAAO;YAAC;gBAAE,KAAK;gBAAa,OAAO;gBAAO,OAAO;YAAM;SAAE;IAC7D;IACA,OAAO,SAAS,CAAC,eAAe,GAAG;QAC/B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,UAAU;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY;QAClC,OACK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,UAAU;YAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;QAC3B,OACK;YACD,OAAO;QACX;IACJ;IACA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QAC/C,IAAI,QAAQ,IAAI;QAChB,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;YAC3B,yEAAyE;YACzE,CAAA,GAAA,wLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,SAAU,GAAG;gBAAI,OAAO,MAAM,KAAK,CAAC,IAAI;YAAE;YACjH,4CAA4C;YAC5C,IAAI,oBAAoB,SAAU,CAAC;gBAAI,OAAO,EAAE,cAAc,KAAK,aAAa,EAAE,KAAK,KAAK;YAAW;YACvG,IAAI,gBAAgB,kBAAkB;YACtC,IAAI,gBAAgB,kBAAkB,IAAI,CAAC,KAAK;YAChD,IAAI,CAAC,iBAAiB,eAAe;gBACjC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB;gBAC3D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB;gBAC9D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,wBAAwB;gBACvD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,yBAAyB;gBACtD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,IAAI,CAAC,kBAAkB;YACtD,OACK,IAAI,iBAAiB,CAAC,eAAe;gBACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB;gBAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB;gBAC/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,wBAAwB;gBACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,yBAAyB;gBACvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,kBAAkB;YACvD;QACJ;IACJ;IACA,OAAO,SAAS,GAAG,gNAAA,CAAA,kBAAe;IAClC,OAAO,YAAY,GAAG;QAClB,cAAc;IAClB;IACA,OAAO;AACX,EAAE,6JAAA,CAAA,YAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/%40tinymce/tinymce-react/lib/es2015/main/ts/index.js"], "sourcesContent": ["import { Editor } from './components/Editor';\nexport { Editor };\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}