{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/client/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,gIAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/FeedbackForm.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/client/components/ui/button\";\r\nimport { Textarea } from \"@/client/components/ui/textarea\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/client/components/ui/card\";\r\n\r\ninterface FeedbackFormProps {\r\n  onRevise: (feedback: string) => Promise<void>;\r\n}\r\n\r\nexport default function FeedbackForm({ onRevise }: FeedbackFormProps) {\r\n  const [feedback, setFeedback] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!feedback.trim()) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      await onRevise(feedback);\r\n      setFeedback(\"\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"text-lg\">Réviser la newsletter</CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n          <Textarea\r\n            placeholder=\"Entrez vos commentaires pour réviser la newsletter...\"\r\n            value={feedback}\r\n            onChange={(e) => setFeedback(e.target.value)}\r\n            rows={3}\r\n          />\r\n          <Button \r\n            type=\"submit\" \r\n            disabled={loading || !feedback.trim()}\r\n            className=\"w-full\"\r\n          >\r\n            {loading ? \"Révision en cours...\" : \"Réviser\"}\r\n          </Button>\r\n        </form>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// \"use client\";\r\n\r\n// import { useState } from \"react\";\r\n\r\n// export default function FeedbackForm({ onRevise }: { onRevise: (feedback: string) => Promise<void> }) {\r\n//   const [feedback, setFeedback] = useState(\"\");\r\n//   const [isLoading, setIsLoading] = useState(false);\r\n\r\n//   const handleRevise = async () => {\r\n//     setIsLoading(true);\r\n//     await onRevise(feedback);\r\n//     setIsLoading(false);\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"mt-4\">\r\n//       <textarea\r\n//         placeholder=\"Entrer vos remarques ici...\"\r\n//         value={feedback}\r\n//         onChange={(e) => setFeedback(e.target.value)}\r\n//         rows={3}\r\n//         className=\"w-full border p-3 rounded-md resize-none mb-2 text-gray-900 placeholder-gray-500\"\r\n//       />\r\n//       <button\r\n//         onClick={handleRevise}\r\n//         disabled={isLoading}\r\n//         className={`w-full py-2 rounded-md transition ${\r\n//           isLoading\r\n//             ? \"bg-green-400 cursor-not-allowed\"\r\n//             : \"bg-green-600 hover:bg-green-700 text-white\"\r\n//         }`}\r\n//       >\r\n//         {isLoading ? \"amélioration en cours\" : \"Proposer des améliorations\"}\r\n//       </button>\r\n//     </div>\r\n//   );\r\n// }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAMe,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,WAAW;QACX,IAAI;YACF,MAAM,SAAS;YACf,YAAY;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,OAAI;;0BACH,6LAAC,6IAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;oBAAC,WAAU;8BAAU;;;;;;;;;;;0BAEjC,6LAAC,6IAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC,iJAAA,CAAA,WAAQ;4BACP,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,MAAM;;;;;;sCAER,6LAAC,+IAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU,WAAW,CAAC,SAAS,IAAI;4BACnC,WAAU;sCAET,UAAU,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhD,EAgBA,gBAAgB;CAEhB,oCAAoC;CAEpC,0GAA0G;CAC1G,kDAAkD;CAClD,uDAAuD;CAEvD,uCAAuC;CACvC,0BAA0B;CAC1B,gCAAgC;CAChC,2BAA2B;CAC3B,OAAO;CAEP,aAAa;CACb,6BAA6B;CAC7B,kBAAkB;CAClB,oDAAoD;CACpD,2BAA2B;CAC3B,wDAAwD;CACxD,mBAAmB;CACnB,uGAAuG;CACvG,WAAW;CACX,gBAAgB;CAChB,iCAAiC;CACjC,+BAA+B;CAC/B,2DAA2D;CAC3D,sBAAsB;CACtB,kDAAkD;CAClD,6DAA6D;CAC7D,cAAc;CACd,UAAU;CACV,+EAA+E;CAC/E,kBAAkB;CAClB,aAAa;CACb,OAAO;CACP,IAAI;GA7FoB;KAAA", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/TinyMCEEditorCDN.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ndeclare global {\n  interface Window {\n    tinymce: any;\n  }\n}\n\ninterface TinyMCEEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  height?: number;\n}\n\nexport default function TinyMCEEditorCDN({ content, onChange, height = 500 }: TinyMCEEditorProps) {\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const editorInitialized = useRef(false);\n\n  useEffect(() => {\n    // Charger TinyMCE depuis CDN\n    if (!window.tinymce && !document.querySelector('script[src*=\"tinymce\"]')) {\n      const script = document.createElement('script');\n      script.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js';\n      script.referrerPolicy = 'origin';\n      document.head.appendChild(script);\n      \n      script.onload = () => {\n        initializeTinyMCE();\n      };\n    } else if (window.tinymce && !editorInitialized.current) {\n      initializeTinyMCE();\n    }\n\n    return () => {\n      if (window.tinymce && textareaRef.current) {\n        window.tinymce.remove(`#${textareaRef.current.id}`);\n      }\n    };\n  }, []);\n\n  const initializeTinyMCE = () => {\n    if (!textareaRef.current || editorInitialized.current) return;\n    \n    editorInitialized.current = true;\n    \n    // Masquer l'avertissement de la clé API\n    const hideApiWarning = () => {\n      // Masquer toutes les notifications d'avertissement\n      const selectors = [\n        '.tox-notification--warning',\n        '.tox-notification-container .tox-notification--warning',\n        '[class*=\"tox-notification\"]',\n        '.tox-notification',\n        '.tox-notification-container'\n      ];\n\n      selectors.forEach(selector => {\n        const elements = document.querySelectorAll(selector);\n        elements.forEach(el => {\n          if (el.textContent?.includes('API key') ||\n              el.textContent?.includes('valid API key') ||\n              el.textContent?.includes('required to continue')) {\n            (el as HTMLElement).style.display = 'none';\n            (el as HTMLElement).style.visibility = 'hidden';\n            (el as HTMLElement).style.opacity = '0';\n            (el as HTMLElement).style.height = '0';\n            (el as HTMLElement).style.overflow = 'hidden';\n          }\n        });\n      });\n\n      // Masquer aussi les éléments parents\n      const notifications = document.querySelectorAll('[data-alloy-id*=\"warning\"], [data-alloy-id*=\"notification\"]');\n      notifications.forEach(el => {\n        if (el.textContent?.includes('API key')) {\n          (el as HTMLElement).style.display = 'none';\n        }\n      });\n    };\n\n    window.tinymce.init({\n      target: textareaRef.current,\n      height: height,\n      menubar: true,\n      plugins: [\n        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n        'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',\n        'emoticons', 'template', 'codesample', 'textcolor', 'colorpicker'\n      ],\n      toolbar: [\n        'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table',\n        'align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat | code fullscreen preview'\n      ],\n      font_family_formats: \n        'Arial=Arial,Helvetica,sans-serif; ' +\n        'Georgia=Georgia,serif; ' +\n        'Helvetica=Helvetica,Arial,sans-serif; ' +\n        'Times New Roman=Times,Times New Roman,serif; ' +\n        'Verdana=Verdana,Geneva,sans-serif',\n      font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 36pt 48pt 72pt',\n      \n      // Configuration spécifique pour les emails\n      content_style: `\n        body { \n          font-family: 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; \n          font-size: 16px; \n          line-height: 1.6;\n          color: #333;\n          max-width: 700px;\n          margin: 0 auto;\n          padding: 20px;\n        }\n        h1 { color: #1a73e8; font-size: 28px; font-weight: bold; }\n        h2 { color: #2563eb; font-size: 22px; font-weight: 600; }\n        h3 { color: #333; font-size: 18px; font-weight: 600; }\n        a { color: #1a73e8; }\n        img { max-width: 100%; height: auto; }\n        .cta-button { \n          display: inline-block; \n          padding: 12px 24px; \n          background: #1a73e8; \n          color: #fff !important; \n          text-decoration: none; \n          border-radius: 6px; \n          font-weight: 600;\n          margin: 10px 0;\n        }\n      `,\n      \n      // Templates prédéfinis pour newsletters\n      templates: [\n        {\n          title: 'Titre + Texte + CTA',\n          description: 'Section avec titre, paragraphe et bouton',\n          content: `\n            <h2>Votre titre ici</h2>\n            <p>Votre contenu principal ici. Décrivez les points importants de votre newsletter.</p>\n            <div style=\"text-align: center; margin: 20px 0;\">\n              <a href=\"#\" class=\"cta-button\">Votre bouton d'action</a>\n            </div>\n          `\n        },\n        {\n          title: 'Article avec image',\n          description: 'Section article avec image à gauche',\n          content: `\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <tr>\n                <td style=\"width: 150px; vertical-align: top; padding-right: 20px;\">\n                  <img src=\"https://via.placeholder.com/150x100\" alt=\"Image\" style=\"width: 100%; height: auto; border-radius: 8px;\">\n                </td>\n                <td style=\"vertical-align: top;\">\n                  <h3>Titre de l'article</h3>\n                  <p>Description de votre article ou actualité. Ajoutez ici les détails importants.</p>\n                  <a href=\"#\" style=\"color: #1a73e8;\">Lire la suite →</a>\n                </td>\n              </tr>\n            </table>\n          `\n        },\n        {\n          title: 'Liste de liens',\n          description: 'Liste de liens utiles',\n          content: `\n            <h3>📌 Liens utiles</h3>\n            <ul style=\"list-style: none; padding: 0;\">\n              <li style=\"margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;\">\n                <a href=\"#\" style=\"font-weight: 600;\">Titre du lien 1</a><br>\n                <span style=\"color: #666; font-size: 14px;\">Description courte du lien</span>\n              </li>\n              <li style=\"margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;\">\n                <a href=\"#\" style=\"font-weight: 600;\">Titre du lien 2</a><br>\n                <span style=\"color: #666; font-size: 14px;\">Description courte du lien</span>\n              </li>\n            </ul>\n          `\n        }\n      ],\n      \n      // Configuration pour les emails (styles inline)\n      cleanup: true,\n      verify_html: false,\n      convert_urls: false,\n      remove_script_host: false,\n      relative_urls: false,\n      \n      // Événements\n      setup: function(editor: any) {\n        editor.on('init', function() {\n          editor.setContent(content);\n          // Masquer l'avertissement après initialisation\n          setTimeout(hideApiWarning, 1000);\n          setTimeout(hideApiWarning, 3000);\n        });\n        \n        editor.on('change keyup undo redo', function() {\n          const content = editor.getContent();\n          onChange(content);\n        });\n        \n        // Bouton personnalisé pour CTA\n        editor.ui.registry.addButton('cta', {\n          text: 'CTA',\n          tooltip: 'Insérer un bouton Call-to-Action',\n          onAction: function() {\n            const text = prompt('Texte du bouton :') || 'Cliquez ici';\n            const url = prompt('URL du lien :') || '#';\n            editor.insertContent(`\n              <div style=\"text-align: center; margin: 20px 0;\">\n                <a href=\"${url}\" style=\"display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600;\">${text}</a>\n              </div>\n            `);\n          }\n        });\n        \n        // Bouton pour convertir en styles inline\n        editor.ui.registry.addButton('inlinestyles', {\n          text: 'Email',\n          tooltip: 'Optimiser pour email (styles inline)',\n          onAction: function() {\n            const content = editor.getContent();\n            const optimized = optimizeForEmail(content);\n            editor.setContent(optimized);\n          }\n        });\n      },\n      \n      // Ajouter nos boutons personnalisés à la toolbar\n      toolbar1: 'undo redo | blocks fontfamily fontsize | bold italic underline | forecolor backcolor | link image cta inlinestyles',\n      toolbar2: 'align lineheight | checklist numlist bullist indent outdent | template | removeformat | code fullscreen preview'\n    });\n  };\n\n  // Fonction pour optimiser le HTML pour les emails\n  const optimizeForEmail = (html: string): string => {\n    let optimized = html;\n    \n    // Convertir les classes en styles inline\n    const styleConversions = [\n      [/class=\"cta-button\"/g, 'style=\"display: inline-block; padding: 12px 24px; background: #1a73e8; color: #fff !important; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0;\"'],\n      [/<h1>/g, '<h1 style=\"color: #1a73e8; font-size: 28px; font-weight: bold; margin: 20px 0 10px 0;\">'],\n      [/<h2>/g, '<h2 style=\"color: #2563eb; font-size: 22px; font-weight: 600; margin: 15px 0 8px 0;\">'],\n      [/<h3>/g, '<h3 style=\"color: #333; font-size: 18px; font-weight: 600; margin: 12px 0 6px 0;\">'],\n      [/<p>/g, '<p style=\"color: #333; font-size: 16px; line-height: 1.6; margin: 10px 0;\">'],\n      [/<img([^>]*)>/g, '<img$1 style=\"max-width: 100%; height: auto; border-radius: 8px;\">'],\n    ];\n    \n    styleConversions.forEach(([regex, replacement]) => {\n      optimized = optimized.replace(regex as RegExp, replacement as string);\n    });\n    \n    return optimized;\n  };\n\n  return (\n    <div className=\"tinymce-container\">\n      <textarea\n        ref={textareaRef}\n        id={`tinymce-${Date.now()}`}\n        defaultValue={content}\n        style={{ width: '100%', height: `${height}px` }}\n      />\n      \n      <div className=\"mt-4 text-sm text-gray-600\">\n        <p><strong>💡 Conseils d'utilisation :</strong></p>\n        <ul className=\"list-disc list-inside space-y-1\">\n          <li>Utilisez les <strong>Templates</strong> pour des mises en page prêtes</li>\n          <li>Le bouton <strong>CTA</strong> insère des boutons d'action optimisés</li>\n          <li>Le bouton <strong>Email</strong> convertit automatiquement en styles inline</li>\n          <li>Prévisualisez avec <strong>Preview</strong> avant d'envoyer</li>\n        </ul>\n      </div>\n      \n      {/* CSS pour masquer l'avertissement API */}\n      <style jsx>{`\n        :global(.tox-notification--warning),\n        :global(.tox-notification-container .tox-notification--warning) {\n          display: none !important;\n        }\n        :global(.tox-notification[data-alloy-id*=\"warning\"]) {\n          display: none !important;\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AAgBe,SAAS,iBAAiB,KAAuD;QAAvD,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,GAAG,EAAsB,GAAvD;;IACvC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,6BAA6B;YAC7B,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,SAAS,aAAa,CAAC,2BAA2B;gBACxE,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,OAAO,GAAG,GAAG;gBACb,OAAO,cAAc,GAAG;gBACxB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,OAAO,MAAM;kDAAG;wBACd;oBACF;;YACF,OAAO,IAAI,OAAO,OAAO,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBACvD;YACF;YAEA;8CAAO;oBACL,IAAI,OAAO,OAAO,IAAI,YAAY,OAAO,EAAE;wBACzC,OAAO,OAAO,CAAC,MAAM,CAAC,AAAC,IAA0B,OAAvB,YAAY,OAAO,CAAC,EAAE;oBAClD;gBACF;;QACF;qCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,OAAO,IAAI,kBAAkB,OAAO,EAAE;QAEvD,kBAAkB,OAAO,GAAG;QAE5B,wCAAwC;QACxC,MAAM,iBAAiB;YACrB,mDAAmD;YACnD,MAAM,YAAY;gBAChB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,UAAU,OAAO,CAAC,CAAA;gBAChB,MAAM,WAAW,SAAS,gBAAgB,CAAC;gBAC3C,SAAS,OAAO,CAAC,CAAA;wBACX,iBACA,kBACA;oBAFJ,IAAI,EAAA,kBAAA,GAAG,WAAW,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,iBACzB,mBAAA,GAAG,WAAW,cAAd,uCAAA,iBAAgB,QAAQ,CAAC,uBACzB,mBAAA,GAAG,WAAW,cAAd,uCAAA,iBAAgB,QAAQ,CAAC,0BAAyB;wBACnD,GAAmB,KAAK,CAAC,OAAO,GAAG;wBACnC,GAAmB,KAAK,CAAC,UAAU,GAAG;wBACtC,GAAmB,KAAK,CAAC,OAAO,GAAG;wBACnC,GAAmB,KAAK,CAAC,MAAM,GAAG;wBAClC,GAAmB,KAAK,CAAC,QAAQ,GAAG;oBACvC;gBACF;YACF;YAEA,qCAAqC;YACrC,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;YAChD,cAAc,OAAO,CAAC,CAAA;oBAChB;gBAAJ,KAAI,kBAAA,GAAG,WAAW,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,YAAY;oBACtC,GAAmB,KAAK,CAAC,OAAO,GAAG;gBACtC;YACF;QACF;QAEA,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,QAAQ,YAAY,OAAO;YAC3B,QAAQ;YACR,SAAS;YACT,SAAS;gBACP;gBAAW;gBAAY;gBAAS;gBAAQ;gBAAS;gBAAW;gBAC5D;gBAAU;gBAAiB;gBAAgB;gBAAQ;gBACnD;gBAAkB;gBAAS;gBAAS;gBAAQ;gBAAQ;gBACpD;gBAAa;gBAAY;gBAAc;gBAAa;aACrD;YACD,SAAS;gBACP;gBACA;aACD;YACD,qBACE,uCACA,4BACA,2CACA,kDACA;YACF,mBAAmB;YAEnB,2CAA2C;YAC3C,eAAgB;YA2BhB,wCAAwC;YACxC,WAAW;gBACT;oBACE,OAAO;oBACP,aAAa;oBACb,SAAU;gBAOZ;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,SAAU;gBAcZ;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,SAAU;gBAaZ;aACD;YAED,gDAAgD;YAChD,SAAS;YACT,aAAa;YACb,cAAc;YACd,oBAAoB;YACpB,eAAe;YAEf,aAAa;YACb,OAAO,SAAS,MAAW;gBACzB,OAAO,EAAE,CAAC,QAAQ;oBAChB,OAAO,UAAU,CAAC;oBAClB,+CAA+C;oBAC/C,WAAW,gBAAgB;oBAC3B,WAAW,gBAAgB;gBAC7B;gBAEA,OAAO,EAAE,CAAC,0BAA0B;oBAClC,MAAM,UAAU,OAAO,UAAU;oBACjC,SAAS;gBACX;gBAEA,+BAA+B;gBAC/B,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO;oBAClC,MAAM;oBACN,SAAS;oBACT,UAAU;wBACR,MAAM,OAAO,OAAO,wBAAwB;wBAC5C,MAAM,MAAM,OAAO,oBAAoB;wBACvC,OAAO,aAAa,CAAC,AAAC,+FAE8J,OAArK,KAAI,mKAAsK,OAAL,MAAK;oBAG3L;gBACF;gBAEA,yCAAyC;gBACzC,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,gBAAgB;oBAC3C,MAAM;oBACN,SAAS;oBACT,UAAU;wBACR,MAAM,UAAU,OAAO,UAAU;wBACjC,MAAM,YAAY,iBAAiB;wBACnC,OAAO,UAAU,CAAC;oBACpB;gBACF;YACF;YAEA,iDAAiD;YACjD,UAAU;YACV,UAAU;QACZ;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,yCAAyC;QACzC,MAAM,mBAAmB;YACvB;gBAAC;gBAAuB;aAA+K;YACvM;gBAAC;gBAAS;aAA0F;YACpG;gBAAC;gBAAS;aAAwF;YAClG;gBAAC;gBAAS;aAAqF;YAC/F;gBAAC;gBAAQ;aAA8E;YACvF;gBAAC;gBAAiB;aAAqE;SACxF;QAED,iBAAiB,OAAO,CAAC;gBAAC,CAAC,OAAO,YAAY;YAC5C,YAAY,UAAU,OAAO,CAAC,OAAiB;QACjD;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;kDAAc;;0BACb,6LAAC;gBACC,KAAK;gBACL,IAAI,AAAC,WAAqB,OAAX,KAAK,GAAG;gBACvB,cAAc;gBACd,OAAO;oBAAE,OAAO;oBAAQ,QAAQ,AAAC,GAAS,OAAP,QAAO;gBAAI;;;;;;;0BAGhD,6LAAC;0DAAc;;kCACb,6LAAC;;kCAAE,cAAA,6LAAC;;sCAAO;;;;;;;;;;;kCACX,6LAAC;kEAAa;;0CACZ,6LAAC;;;oCAAG;kDAAa,6LAAC;;kDAAO;;;;;;oCAAkB;;;;;;;0CAC3C,6LAAC;;;oCAAG;kDAAU,6LAAC;;kDAAO;;;;;;oCAAY;;;;;;;0CAClC,6LAAC;;;oCAAG;kDAAU,6LAAC;;kDAAO;;;;;;oCAAc;;;;;;;0CACpC,6LAAC;;;oCAAG;kDAAmB,6LAAC;;kDAAO;;;;;;oCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBzD;GAhRwB;KAAA", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/TinyMCEGuide.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Card } from '@/client/components/ui/card';\r\nimport { Button } from '@/client/components/ui/button';\r\nimport { ChevronDown, ChevronRight, HelpCircle } from 'lucide-react';\r\n\r\nexport default function TinyMCEGuide() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const features = [\r\n    {\r\n      title: \"📝 Templates prédéfinis\",\r\n      description: \"Utilisez le menu 'Templates' pour insérer des mises en page toutes prêtes (Titre+CTA, Article avec image, etc.)\"\r\n    },\r\n    {\r\n      title: \"🎨 Formatage avancé\",\r\n      description: \"Police, taille, couleurs, alignement - tout comme dans Word ! Les couleurs sont automatiquement optimisées pour l'email.\"\r\n    },\r\n    {\r\n      title: \"🔗 Bouton CTA magique\",\r\n      description: \"Le bouton 'CTA' dans la barre d'outils crée automatiquement des boutons d'action optimisés pour les newsletters.\"\r\n    },\r\n    {\r\n      title: \"📧 Mode Email\",\r\n      description: \"Le bouton 'Email' convertit automatiquement votre contenu avec des styles inline compatibles avec tous les clients email.\"\r\n    },\r\n    {\r\n      title: \"🖼️ Images et médias\",\r\n      description: \"Insérez facilement des images, tableaux et liens. Les images sont automatiquement redimensionnées.\"\r\n    },\r\n    {\r\n      title: \"👀 Prévisualisation\",\r\n      description: \"Utilisez 'Preview' pour voir exactement à quoi ressemblera votre newsletter avant de l'envoyer.\"\r\n    }\r\n  ];\r\n\r\n  const tips = [\r\n    \"Commencez par choisir un template pour gagner du temps\",\r\n    \"Utilisez les titres H1/H2 pour structurer votre contenu\",\r\n    \"Ajoutez des boutons CTA colorés pour encourager l'action\",\r\n    \"Prévisualisez toujours avant de sauvegarder\",\r\n    \"Le bouton 'Email' optimise automatiquement pour l'envoi\"\r\n  ];\r\n\r\n  return (\r\n    <Card className=\"mb-4\">\r\n      <Button\r\n        variant=\"ghost\"\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"w-full justify-between p-4 h-auto\"\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <HelpCircle className=\"w-5 h-5 text-blue-600\" />\r\n          <span className=\"font-medium\">Guide d'utilisation TinyMCE</span>\r\n        </div>\r\n        {isOpen ? <ChevronDown className=\"w-4 h-4\" /> : <ChevronRight className=\"w-4 h-4\" />}\r\n      </Button>\r\n      \r\n      {isOpen && (\r\n        <div className=\"px-4 pb-4 space-y-4\">\r\n          <div>\r\n            <h4 className=\"font-semibold text-green-700 mb-2\">✨ Fonctionnalités principales</h4>\r\n            <div className=\"grid gap-3\">\r\n              {features.map((feature, index) => (\r\n                <div key={index} className=\"p-3 bg-green-50 rounded-lg\">\r\n                  <div className=\"font-medium text-sm text-green-800\">{feature.title}</div>\r\n                  <div className=\"text-xs text-green-700 mt-1\">{feature.description}</div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h4 className=\"font-semibold text-blue-700 mb-2\">💡 Conseils pratiques</h4>\r\n            <ul className=\"space-y-2\">\r\n              {tips.map((tip, index) => (\r\n                <li key={index} className=\"flex items-start gap-2 text-sm text-blue-700\">\r\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\r\n                  <span>{tip}</span>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n          \r\n          <div className=\"p-3 bg-yellow-50 rounded-lg border border-yellow-200\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-yellow-600\">⚠️</span>\r\n              <div className=\"text-sm\">\r\n                <div className=\"font-medium text-yellow-800\">Important pour les emails</div>\r\n                <div className=\"text-yellow-700\">Cliquez sur le bouton 'Email' avant de sauvegarder pour garantir la compatibilité avec tous les clients email (Gmail, Outlook, etc.)</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,OAAO;QACX;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC,6IAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,+IAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iOAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;oBAE/B,uBAAS,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAAe,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;YAGzE,wBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAsC,QAAQ,KAAK;;;;;;0DAClE,6LAAC;gDAAI,WAAU;0DAA+B,QAAQ,WAAW;;;;;;;uCAFzD;;;;;;;;;;;;;;;;kCAQhB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAG,WAAU;0CACX,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,6LAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;kCAQf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/client/components/EditableNewsletter.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { Button } from '@/client/components/ui/button';\r\nimport TinyMC<PERSON><PERSON>orCD<PERSON> from './TinyMCEEditorCDN';\r\nimport TinyMCEGuide from './TinyMCEGuide';\r\n\r\n\r\n\r\n// ===== Wrapper visuel pour l’aperçu (facultatif) =====\r\nconst WRAP_START =\r\n  `<div class=\"newsletter-container\" style=\"font-family:'Segoe UI', Roboto, sans-serif;max-width:700px;margin:auto;padding:30px;background-color:#ffffff;color:#333;\">`;\r\nconst WRAP_END = `</div>`;\r\n\r\nif (typeof window !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.innerHTML = `\r\n    .newsletter-container img { display:block; margin:auto; }\r\n    .newsletter-container h1,.newsletter-container h2,.newsletter-container h3 { color:#1a73e8; }\r\n    .newsletter-container a { color:#1a73e8; text-decoration:underline; }\r\n  `;\r\n  document.head.appendChild(style);\r\n}\r\n\r\ninterface Props {\r\n  initialContent: string;\r\n  theme: string;\r\n  onSave: (content: string) => void;\r\n  editing: boolean;\r\n  onEditingChange: (editing: boolean) => void;\r\n}\r\n\r\nexport default function EditableNewsletter({\r\n  initialContent,\r\n  theme,\r\n  onSave,\r\n  editing,\r\n  onEditingChange,\r\n}: Props) {\r\n  const [content, setContent] = useState(initialContent);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setContent(initialContent);\r\n  }, [initialContent]);\r\n\r\n  const handleSave = async () => {\r\n    setIsSaving(true);\r\n    try {\r\n      const res = await fetch('http://localhost:8000/save-newsletter', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ theme, content }),\r\n      });\r\n      \r\n      if (!res.ok) {\r\n        const errorData = await res.json().catch(() => ({}));\r\n        throw new Error(errorData.detail || `Erreur ${res.status}: ${res.statusText}`);\r\n      }\r\n      \r\n      const data = await res.json();\r\n      \r\n      // Vérifier que le contenu révisé est valide\r\n      if (data.content && typeof data.content === 'string' && data.content.trim()) {\r\n        onSave(data.content);\r\n      } else {\r\n        // Si pas de contenu révisé, utiliser le contenu original\r\n        console.warn('Aucun contenu révisé reçu, utilisation du contenu original');\r\n        onSave(content);\r\n      }\r\n      \r\n      onEditingChange(false);\r\n    } catch (e: any) {\r\n      console.error('Erreur lors de la sauvegarde:', e);\r\n      alert(`Erreur lors de l'enregistrement: ${e.message || e}`);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setContent(initialContent);\r\n    onEditingChange(false);\r\n  };\r\n\r\n  // Fonction pour nettoyer le HTML et éviter les erreurs d'affichage\r\n  const sanitizeContent = (html: string): string => {\r\n    if (!html || typeof html !== 'string') return '';\r\n    \r\n    // Supprimer les scripts potentiellement dangereux\r\n    let cleaned = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '');\r\n    \r\n    // Supprimer les balises on* (événements JavaScript)\r\n    cleaned = cleaned.replace(/\\s*on\\w+\\s*=\\s*[\"'][^\"']*[\"']/gi, '');\r\n    \r\n    // Supprimer les balises style avec du JavaScript\r\n    cleaned = cleaned.replace(/<style[^>]*>[\\s\\S]*?<\\/style>/gi, '');\r\n    \r\n    return cleaned;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {!editing ? (\r\n        <div \r\n          dangerouslySetInnerHTML={{ \r\n            __html: WRAP_START + sanitizeContent(content) + WRAP_END \r\n          }} \r\n        />\r\n      ) : (\r\n        <div>\r\n          {/* Guide d'utilisation pour TinyMCE */}\r\n          <TinyMCEGuide />\r\n          \r\n          {/* Éditeur TinyMCE */}\r\n          <TinyMCEEditorCDN content={content} onChange={setContent} />\r\n          \r\n          <div className=\"flex gap-4 mt-4\">\r\n            <Button \r\n              onClick={handleSave} \r\n              disabled={isSaving}\r\n              className={`relative overflow-hidden transition-all duration-300 ${\r\n                isSaving \r\n                  ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse' \r\n                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'\r\n              } text-white shadow-lg hover:shadow-xl transform hover:scale-105`}\r\n            >\r\n              {isSaving ? (\r\n                <>\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-[length:200%_100%] animate-pulse\"></div>\r\n                  <div className=\"relative flex items-center gap-2\">\r\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                    <span>Enregistrement...</span>\r\n                  </div>\r\n                </>\r\n              ) : (\r\n                <span>Enregistrer</span>\r\n              )}\r\n            </Button>\r\n            <Button onClick={handleCancel} variant=\"outline\" disabled={isSaving}>Annuler</Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AASA,wDAAwD;AACxD,MAAM,aACH;AACH,MAAM,WAAY;AAElB,wCAAmC;IACjC,MAAM,QAAQ,SAAS,aAAa,CAAC;IACrC,MAAM,SAAS,GAAI;IAKnB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAUe,SAAS,mBAAmB,KAMnC;QANmC,EACzC,cAAc,EACd,KAAK,EACL,MAAM,EACN,OAAO,EACP,eAAe,EACT,GANmC;;IAOzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,WAAW;QACb;uCAAG;QAAC;KAAe;IAEnB,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,yCAAyC;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAQ;YACxC;YAEA,IAAI,CAAC,IAAI,EAAE,EAAE;gBACX,MAAM,YAAY,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBAClD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,AAAC,UAAwB,OAAf,IAAI,MAAM,EAAC,MAAmB,OAAf,IAAI,UAAU;YAC7E;YAEA,MAAM,OAAO,MAAM,IAAI,IAAI;YAE3B,4CAA4C;YAC5C,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,YAAY,KAAK,OAAO,CAAC,IAAI,IAAI;gBAC3E,OAAO,KAAK,OAAO;YACrB,OAAO;gBACL,yDAAyD;gBACzD,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,gBAAgB;QAClB,EAAE,OAAO,GAAQ;YACf,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,AAAC,oCAAkD,OAAf,EAAE,OAAO,IAAI;QACzD,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,gBAAgB;IAClB;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,kDAAkD;QAClD,IAAI,UAAU,KAAK,OAAO,CAAC,uDAAuD;QAElF,oDAAoD;QACpD,UAAU,QAAQ,OAAO,CAAC,mCAAmC;QAE7D,iDAAiD;QACjD,UAAU,QAAQ,OAAO,CAAC,mCAAmC;QAE7D,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,CAAC,wBACA,6LAAC;YACC,yBAAyB;gBACvB,QAAQ,aAAa,gBAAgB,WAAW;YAClD;;;;;iCAGF,6LAAC;;8BAEC,6LAAC,+IAAA,CAAA,UAAY;;;;;8BAGb,6LAAC,mJAAA,CAAA,UAAgB;oBAAC,SAAS;oBAAS,UAAU;;;;;;8BAE9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+IAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAW,AAAC,wDAIX,OAHC,WACI,kGACA,oFACL;sCAEA,yBACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;6DAIV,6LAAC;0CAAK;;;;;;;;;;;sCAGV,6LAAC,+IAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,SAAQ;4BAAU,UAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAMjF;GAjHwB;KAAA", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, ChangeEvent, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/client/components/ui/tabs\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/client/components/ui/card\";\r\nimport { <PERSON><PERSON> } from \"@/client/components/ui/button\";\r\nimport { Input } from \"@/client/components/ui/input\";\r\nimport { Textarea } from \"@/client/components/ui/textarea\";\r\nimport { Badge } from \"@/client/components/ui/badge\";\r\nimport { \r\n  PenTool, \r\n  Eye, \r\n  Send, \r\n  FileText, \r\n  Upload, \r\n  Mail,\r\n  Sparkles,\r\n  Clock,\r\n  CheckCircle,\r\n  Trash2,\r\n  AlertCircle\r\n} from \"lucide-react\";\r\nimport NewsletterDisplay from \"@/client/components/NewsletterDisplay\";\r\nimport FeedbackForm from \"@/client/components/FeedbackForm\";\r\nimport CopyButton from \"@/client/components/CopyButton\";\r\nimport EditableNewsletter from '@/client/components/EditableNewsletter';\r\n\r\nexport default function NewsletterApp() {\r\n  // Generation tab state\r\n  const [theme, setTheme] = useState(\"\");\r\n  const [description, setDescription] = useState(\"\");\r\n  const [newsletter, setNewsletter] = useState(\"\");\r\n  const [revised, setRevised] = useState(\"\");\r\n  const [file, setFile] = useState<File | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showFileUpload, setShowFileUpload] = useState(false);\r\n  const content = revised || newsletter;\r\n  const [isEditing, setIsEditing] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchNewsletters();\r\n  }, []);\r\n \r\n  // Visualization tab state - real API data\r\n  const [newsletters, setNewsletters] = useState<string[]>([]);\r\n  const [selectedFile, setSelectedFile] = useState<string | null>(null);\r\n  const [selectedContent, setSelectedContent] = useState<string>(\"\");\r\n\r\n  // Sending tab state\r\n  const [csvFile, setCsvFile] = useState<File | null>(null);\r\n  const [sendingProgress, setSendingProgress] = useState(0);\r\n  const [isSending, setIsSending] = useState(false);\r\n  const [status, setStatus] = useState<string>(\"\");\r\n\r\n  const fetchNewsletters = async () => {\r\n    try {\r\n      const res = await axios.get(\"http://localhost:8000/list-newsletters\");\r\n      setNewsletters(res.data.files || []);\r\n    } catch {\r\n      setStatus(\"Erreur lors du chargement des newsletters.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (filename: string) => {\r\n    try {\r\n      const res = await axios.get(`http://localhost:8000/static/${filename}`, {\r\n        responseType: \"text\",\r\n      });\r\n      setSelectedFile(filename);\r\n      setSelectedContent(res.data);\r\n    } catch {\r\n      setStatus(\"Erreur lors de l'affichage du contenu.\");\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (filename: string) => {\r\n    if (!confirm(`Supprimer \"${filename}\" ?`)) return;\r\n\r\n    try {\r\n      await axios.delete(\"http://localhost:8000/delete-newsletter\", {\r\n        params: { filename },\r\n      });\r\n      setStatus(`\"${filename}\" supprimée.`);\r\n      fetchNewsletters(); // recharger la liste\r\n      if (selectedFile === filename) {\r\n        setSelectedFile(null);\r\n        setSelectedContent(\"\");\r\n      }\r\n    } catch (e: any) {\r\n      setStatus(\"Erreur de suppression : \" + (e.response?.data?.detail || e.message));\r\n    }\r\n  };\r\n\r\n  const generateFromWeb = async () => {\r\n    if (!theme || !description) {\r\n      alert(\"Thème et description requis.\");\r\n      return;\r\n    }\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/generate-web\", {\r\n        theme,\r\n        description,\r\n      });\r\n      setNewsletter(res.data.newsletter);\r\n      setRevised(\"\");\r\n      // Refresh the newsletter list to include the new one\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la génération depuis le web.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const generateFromFile = async () => {\r\n    if (!theme || !file || !description) {\r\n      alert(\"Veuillez fournir un thème, description et sélectionner un fichier.\");\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"theme\", theme);\r\n    formData.append(\"description\", description);\r\n    formData.append(\"file\", file);\r\n\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/generate-file\", formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      setNewsletter(res.data.newsletter);\r\n      setRevised(\"\");\r\n      // Refresh the newsletter list to include the new one\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la génération depuis un fichier.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const reviseNewsletter = async (feedback: string) => {\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/revise\", {\r\n        feedback,\r\n        theme,\r\n        description,\r\n      });\r\n      setRevised(res.data.revised_newsletter);\r\n      setNewsletter(\"\"); // on efface l'ancienne version\r\n      \r\n      // Recharger la liste des newsletters pour l'onglet visualisation\r\n      fetchNewsletters();\r\n    } catch {\r\n      alert(\"Erreur lors de la révision.\");\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      setFile(e.target.files[0]);\r\n    }\r\n  };\r\n\r\n  const handleCsvFileChange = (e: ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      setCsvFile(e.target.files[0]);\r\n    }\r\n  };\r\n\r\n  const sendNewsletter = async () => {\r\n    if (!selectedFile || !csvFile) {\r\n      alert(\"Veuillez sélectionner une newsletter et un fichier CSV.\");\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"newsletter_file\", selectedFile);\r\n    formData.append(\"csv_file\", csvFile);\r\n\r\n    setIsSending(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:8000/send-newsletter\", formData);\r\n      setStatus(res.data.detail);\r\n      alert(\"Newsletter envoyée avec succès !\");\r\n    } catch (e: any) {\r\n      setStatus(\"Erreur lors de l'envoi : \" + (e.response?.data?.detail || e.message));\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  const handleSaveNewsletter = (updatedContent: string) => {\r\n    // Vérifier que le contenu est valide\r\n    if (!updatedContent || typeof updatedContent !== 'string') {\r\n      console.error('Contenu invalide reçu:', updatedContent);\r\n      alert('Erreur: Contenu invalide reçu');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      // Mettre à jour le contenu dans la génération\r\n      if (revised) {\r\n        setRevised(updatedContent);\r\n      } else {\r\n        setNewsletter(updatedContent);\r\n      }\r\n      // Recharger la liste des newsletters\r\n      fetchNewsletters();\r\n    } catch (error) {\r\n      console.error('Erreur lors de la mise à jour du contenu:', error);\r\n      alert('Erreur lors de la mise à jour du contenu');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        {/* Header */}\r\n        <div className=\"relative mb-12\">\r\n          {/* Background gradient */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-black/60 via-gray-900/40 to-blue-900/30 rounded-3xl\"></div>\r\n          \r\n          {/* Header content */}\r\n          <div className=\"relative flex flex-col lg:flex-row items-center justify-between p-8 lg:p-12\">\r\n            {/* Logo and title section */}\r\n            <div className=\"flex items-center gap-6 mb-6 lg:mb-0\">\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-black via-gray-900 to-blue-900 blur-lg opacity-40\"></div>\r\n                <img\r\n                  src=\"http://holokia.com/wp-content/uploads/2025/04/logo_holokia_noir.png\"\r\n                  alt=\"Holokia\"\r\n                  className=\"relative h-24 w-auto object-contain drop-shadow-2xl\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-3xl lg:text-5xl font-bold text-white mb-2\">\r\n                  Newsletter\r\n                  <span className=\"bg-gradient-to-r from-blue-300 via-gray-200 to-white bg-clip-text text-transparent\"> IA</span>\r\n                </h1>\r\n                <p className=\"text-gray-300 text-sm lg:text-base\">\r\n                  Générateur intelligent par Holokia\r\n                </p>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Decorative elements */}\r\n            <div className=\"hidden lg:flex items-center gap-4\">\r\n              <div className=\"flex space-x-2\">\r\n                <div className=\"w-3 h-3 bg-blue-400 rounded-full animate-pulse\"></div>\r\n                <div className=\"w-3 h-3 bg-gray-500 rounded-full animate-pulse\" style={{animationDelay: '0.2s'}}></div>\r\n                <div className=\"w-3 h-3 bg-black rounded-full animate-pulse\" style={{animationDelay: '0.4s'}}></div>\r\n              </div>\r\n              <div className=\"h-8 w-px bg-gradient-to-b from-blue-400 via-gray-400 to-transparent\"></div>\r\n              <div className=\"text-xs text-gray-400 font-medium\">\r\n                Powered by AI\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Bottom accent */}\r\n          <div className=\"h-1 bg-gradient-to-r from-black via-blue-600 to-gray-500 rounded-full mx-8 lg:mx-12\"></div>\r\n        </div>\r\n\r\n        {/* Main Tabs Interface */}\r\n        <Tabs defaultValue=\"generation\" className=\"w-full max-w-7xl mx-auto\">\r\n          <TabsList className=\"grid w-full grid-cols-3 mb-8 bg-black/50 backdrop-blur-md border-gray-600/50\">\r\n            <TabsTrigger value=\"generation\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <PenTool className=\"w-5 h-5 mr-2\" />\r\n              Génération\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"visualization\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <Eye className=\"w-5 h-5 mr-2\" />\r\n              Visualisation\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"sending\" className=\"text-lg py-3 text-gray-300 hover:text-white hover:bg-black/30 data-[state=active]:bg-gradient-to-r data-[state=active]:from-black/70 data-[state=active]:to-blue-900/50 data-[state=active]:text-white\">\r\n              <Send className=\"w-5 h-5 mr-2\" />\r\n              Envoi\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Generation Tab */}\r\n          <TabsContent value=\"generation\" className=\"space-y-6\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n              {/* Form Column - 1/3 */}\r\n              <Card className=\"lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"w-5 h-5 text-blue-700\" />\r\n                    Paramètres de génération\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium mb-2\">Thème de la newsletter</label>\r\n                    <Input\r\n                      placeholder=\"Ex: Technologie, Marketing, Santé...\"\r\n                      value={theme}\r\n                      onChange={(e) => setTheme(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  \r\n                  <div>\r\n                    <label className=\"block text-sm font-medium mb-2\">Description détaillée</label>\r\n                    <Textarea\r\n                      placeholder=\"Décrivez le contenu souhaité pour votre newsletter...\"\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      rows={4}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <Button\r\n                      onClick={generateFromWeb}\r\n                      disabled={loading}\r\n                      className=\"w-full bg-gradient-to-r from-black via-blue-900 to-gray-800 hover:from-gray-900 hover:via-blue-800 hover:to-black\"\r\n                      size=\"lg\"\r\n                    >\r\n                      {loading ? \"Génération en cours...\" : \"Générer via web\"}\r\n                    </Button>\r\n\r\n                    <Button\r\n                      onClick={() => setShowFileUpload(!showFileUpload)}\r\n                      variant=\"outline\"\r\n                      className=\"w-full\"\r\n                      size=\"lg\"\r\n                    >\r\n                      <Upload className=\"w-4 h-4 mr-2\" />\r\n                      Charger un fichier\r\n                    </Button>\r\n\r\n                    {showFileUpload && (\r\n                      <Card className=\"border-dashed\">\r\n                        <CardContent className=\"pt-6\">\r\n                          <div className=\"space-y-4\">\r\n                            <div>\r\n                              <label className=\"block text-sm font-medium mb-2\">\r\n                                Sélectionnez un fichier :\r\n                              </label>\r\n                              <Input\r\n                                type=\"file\"\r\n                                accept=\".txt,.pdf,.doc,.docx,.rtf,.md\"\r\n                                onChange={handleFileChange}\r\n                              />\r\n                              {file && (\r\n                                <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                                  <FileText className=\"w-4 h-4\" />\r\n                                  Fichier sélectionné : <strong>{file.name}</strong>\r\n                                </p>\r\n                              )}\r\n                            </div>\r\n                            <Button\r\n                              onClick={generateFromFile}\r\n                              disabled={loading || !file}\r\n                              className=\"w-full\"\r\n                            >\r\n                              {loading ? \"Génération en cours...\" : \"Générer via fichier\"}\r\n                            </Button>\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n             {/* Preview Column - 2/3 */}\r\n<Card className=\"lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n  <CardHeader className=\"flex justify-between items-center\">\r\n    <CardTitle>Aperçu de la newsletter</CardTitle>\r\n    {content && (\r\n      <Button\r\n        variant=\"outline\"\r\n        onClick={() => setIsEditing(!isEditing)}\r\n        className=\"text-sm\"\r\n      >\r\n        {isEditing ? \"Annuler\" : \"Modifier\"}\r\n      </Button>\r\n    )}\r\n  </CardHeader>\r\n  <CardContent>\r\n    {content ? (\r\n      <div className=\"space-y-4\">\r\n        <EditableNewsletter\r\n          initialContent={content}\r\n          theme={theme}\r\n          onSave={handleSaveNewsletter}\r\n          editing={isEditing}\r\n          onEditingChange={setIsEditing}\r\n        />\r\n\r\n        <div className=\"space-y-3\">\r\n          <FeedbackForm onRevise={reviseNewsletter} />\r\n        </div>\r\n      </div>\r\n    ) : (\r\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n        <div className=\"w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200\">\r\n          <FileText className=\"w-8 h-8 text-blue-400\" />\r\n        </div>\r\n        <p className=\"text-blue-600\">Aucune newsletter générée</p>\r\n        <p className=\"text-sm text-blue-500\">\r\n          Remplissez le formulaire et cliquez sur générer\r\n        </p>\r\n      </div>\r\n    )}\r\n  </CardContent>\r\n</Card>\r\n\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Visualization Tab */}\r\n          <TabsContent value=\"visualization\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n              {/* Liste des newsletters - 1/3 */}\r\n              <Card className=\"lg:col-span-1 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <FileText className=\"w-5 h-5\" />\r\n                    Newsletters disponibles\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-2\">\r\n                    {newsletters.length > 0 ? (\r\n                      newsletters.map((file) => (\r\n                        <div key={file} className=\"flex items-center justify-between p-3 bg-blue-50/80 rounded-lg border border-blue-100 hover:bg-blue-100/60 transition-colors\">\r\n                          <span className=\"truncate max-w-[60%] font-medium\">{file}</span>\r\n                          <div className=\"flex gap-2\">\r\n                            <Button\r\n                              onClick={() => handleView(file)}\r\n                              size=\"sm\"\r\n                              variant=\"outline\"\r\n                            >\r\n                              <Eye className=\"w-4 h-4 mr-1\" />\r\n                              Voir\r\n                            </Button>\r\n                            <Button\r\n                              onClick={() => handleDelete(file)}\r\n                              size=\"sm\"\r\n                              variant=\"destructive\"\r\n                            >\r\n                              <Trash2 className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </div>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div className=\"text-center py-8 text-blue-600\">\r\n                        <FileText className=\"w-12 h-12 mx-auto mb-3 text-blue-300\" />\r\n                        <p>Aucune newsletter disponible</p>\r\n                        <p className=\"text-sm text-blue-500\">Générez votre première newsletter dans l'onglet \"Génération\"</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Aperçu de la newsletter sélectionnée - 2/3 */}\r\n              <Card className=\"lg:col-span-2 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-5 h-5\" />\r\n                    Aperçu\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {selectedFile ? (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"flex items-center gap-2 p-2 bg-blue-100/80 rounded-lg border border-blue-200\">\r\n                        <FileText className=\"w-4 h-4 text-blue-700\" />\r\n                        <span className=\"font-medium text-blue-900\">{selectedFile}</span>\r\n                      </div>\r\n                      <div className=\"max-h-96 overflow-auto border border-blue-200 rounded-lg p-4 bg-white/95 backdrop-blur-sm\">\r\n                        <div \r\n                          className=\"prose prose-sm max-w-none\"\r\n                          dangerouslySetInnerHTML={{ __html: selectedContent }}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n                      <div className=\"w-16 h-16 bg-blue-100/80 rounded-full flex items-center justify-center mb-4 border border-blue-200\">\r\n                        <Eye className=\"w-8 h-8 text-blue-400\" />\r\n                      </div>\r\n                      <p className=\"text-blue-600\">Aucune newsletter sélectionnée</p>\r\n                      <p className=\"text-sm text-blue-500\">Cliquez sur \"Voir\" pour afficher une newsletter</p>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Sending Tab */}\r\n          <TabsContent value=\"sending\">\r\n            <Card className=\"bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n              <CardHeader>\r\n                <CardTitle>Envoi de newsletter</CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium mb-2\">\r\n                        Sélectionner une newsletter\r\n                      </label>\r\n                      <select \r\n                        className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\r\n                        value={selectedFile || \"\"}\r\n                        onChange={(e) => setSelectedFile(e.target.value)}\r\n                      >\r\n                        <option value=\"\">Choisir une newsletter...</option>\r\n                        {newsletters.map((file) => (\r\n                          <option key={file} value={file}>\r\n                            {file}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                      {selectedFile && (\r\n                        <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                          <CheckCircle className=\"w-4 h-4\" />\r\n                          Newsletter sélectionnée : <strong>{selectedFile}</strong>\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium mb-2\">\r\n                        Fichier CSV des emails\r\n                      </label>\r\n                      <Input\r\n                        type=\"file\"\r\n                        accept=\".csv\"\r\n                        onChange={handleCsvFileChange}\r\n                      />\r\n                      {csvFile && (\r\n                        <p className=\"mt-2 text-sm text-green-600 flex items-center gap-2\">\r\n                          <FileText className=\"w-4 h-4\" />\r\n                          Fichier CSV : <strong>{csvFile.name}</strong>\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <Button\r\n                      onClick={sendNewsletter}\r\n                      disabled={!selectedFile || !csvFile || isSending}\r\n                      className=\"w-full\"\r\n                      size=\"lg\"\r\n                    >\r\n                      <Send className=\"w-4 h-4 mr-2\" />\r\n                      {isSending ? \"Envoi en cours...\" : \"Envoyer la newsletter\"}\r\n                    </Button>\r\n                  </div>\r\n\r\n                  <Card className=\"bg-gradient-to-br from-blue-50/90 to-gray-100/80 border-blue-200/50 backdrop-blur-md\">\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-blue-800\">Informations</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"text-sm text-blue-700 space-y-2\">\r\n                      <p>• Le fichier CSV doit contenir une colonne \"email\"</p>\r\n                      <p>• Format accepté : <EMAIL></p>\r\n                      <p>• Maximum 1000 emails par envoi</p>\r\n                      <p>• L'envoi peut prendre quelque minutes</p>\r\n                    </CardContent>\r\n                  </Card>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n       \r\n        {/* Status Message */}\r\n        {status && (\r\n          <Card className=\"mt-6 border-l-4 border-l-blue-600 bg-white/95 backdrop-blur-md border-gray-300/50 shadow-2xl\">\r\n            <CardContent className=\"pt-6\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <AlertCircle className=\"w-5 h-5 text-blue-700\" />\r\n                <p className=\"text-sm text-gray-900\">{status}</p>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAEA;;;AA1BA;;;;;;;;;;;AA4Be,SAAS;;IACtB,uBAAuB;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,UAAU,WAAW;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,oBAAoB;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;YAC5B,eAAe,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;QACrC,EAAE,UAAM;YACN,UAAU;QACZ;IACF;IAIA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,gCAAwC,OAAT,WAAY;gBACtE,cAAc;YAChB;YACA,gBAAgB;YAChB,mBAAmB,IAAI,IAAI;QAC7B,EAAE,UAAM;YACN,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,AAAC,cAAsB,OAAT,UAAS,SAAO;QAE3C,IAAI;YACF,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,2CAA2C;gBAC5D,QAAQ;oBAAE;gBAAS;YACrB;YACA,UAAU,AAAC,IAAY,OAAT,UAAS;YACvB,oBAAoB,qBAAqB;YACzC,IAAI,iBAAiB,UAAU;gBAC7B,gBAAgB;gBAChB,mBAAmB;YACrB;QACF,EAAE,OAAO,GAAQ;gBACyB,kBAAA;YAAxC,UAAU,6BAA6B,CAAC,EAAA,cAAA,EAAE,QAAQ,cAAV,mCAAA,mBAAA,YAAY,IAAI,cAAhB,uCAAA,iBAAkB,MAAM,KAAI,EAAE,OAAO;QAC/E;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,MAAM;YACN;QACF;QACA,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,sCAAsC;gBACjE;gBACA;YACF;YACA,cAAc,IAAI,IAAI,CAAC,UAAU;YACjC,WAAW;YACX,qDAAqD;YACrD;QACF,EAAE,UAAM;YACN,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa;YACnC,MAAM;YACN;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QACzB,SAAS,MAAM,CAAC,eAAe;QAC/B,SAAS,MAAM,CAAC,QAAQ;QAExB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,uCAAuC,UAAU;gBAC5E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,cAAc,IAAI,IAAI,CAAC,UAAU;YACjC,WAAW;YACX,qDAAqD;YACrD;QACF,EAAE,UAAM;YACN,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,gCAAgC;gBAC3D;gBACA;gBACA;YACF;YACA,WAAW,IAAI,IAAI,CAAC,kBAAkB;YACtC,cAAc,KAAK,+BAA+B;YAElD,iEAAiE;YACjE;QACF,EAAE,UAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAC7B,MAAM;YACN;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,mBAAmB;QACnC,SAAS,MAAM,CAAC,YAAY;QAE5B,aAAa;QACb,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAAyC;YACtE,UAAU,IAAI,IAAI,CAAC,MAAM;YACzB,MAAM;QACR,EAAE,OAAO,GAAQ;gBAC0B,kBAAA;YAAzC,UAAU,8BAA8B,CAAC,EAAA,cAAA,EAAE,QAAQ,cAAV,mCAAA,mBAAA,YAAY,IAAI,cAAhB,uCAAA,iBAAkB,MAAM,KAAI,EAAE,OAAO;QAChF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,qCAAqC;QACrC,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;YACzD,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;YACN;QACF;QAEA,IAAI;YACF,8CAA8C;YAC9C,IAAI,SAAS;gBACX,WAAW;YACb,OAAO;gBACL,cAAc;YAChB;YACA,qCAAqC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;;;;;;;sDAGd,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;wDAAiD;sEAE7D,6LAAC;4DAAK,WAAU;sEAAqF;;;;;;;;;;;;8DAEvG,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;8CAOtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;oDAAiD,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;8DAC9F,6LAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;;;;;;;sDAE7F,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAOvD,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC,6IAAA,CAAA,OAAI;oBAAC,cAAa;oBAAa,WAAU;;sCACxC,6LAAC,6IAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,6IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAa,WAAU;;sDACxC,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGtC,6LAAC,6IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAgB,WAAU;;sDAC3C,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,6LAAC,6IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMrC,6LAAC,6IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,6IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,6IAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAA0B;;;;;;;;;;;;0DAIlD,6LAAC,6IAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiC;;;;;;0EAClD,6LAAC,8IAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAI5C,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiC;;;;;;0EAClD,6LAAC,iJAAA,CAAA,WAAQ;gEACP,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gEAC9C,MAAM;;;;;;;;;;;;kEAIV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+IAAA,CAAA,SAAM;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;gEACV,MAAK;0EAEJ,UAAU,2BAA2B;;;;;;0EAGxC,6LAAC,+IAAA,CAAA,SAAM;gEACL,SAAS,IAAM,kBAAkB,CAAC;gEAClC,SAAQ;gEACR,WAAU;gEACV,MAAK;;kFAEL,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAIpC,gCACC,6LAAC,6IAAA,CAAA,OAAI;gEAAC,WAAU;0EACd,cAAA,6LAAC,6IAAA,CAAA,cAAW;oEAAC,WAAU;8EACrB,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAM,WAAU;kGAAiC;;;;;;kGAGlD,6LAAC,8IAAA,CAAA,QAAK;wFACJ,MAAK;wFACL,QAAO;wFACP,UAAU;;;;;;oFAEX,sBACC,6LAAC;wFAAE,WAAU;;0GACX,6LAAC,iNAAA,CAAA,WAAQ;gGAAC,WAAU;;;;;;4FAAY;0GACV,6LAAC;0GAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;0FAI9C,6LAAC,+IAAA,CAAA,SAAM;gFACL,SAAS;gFACT,UAAU,WAAW,CAAC;gFACtB,WAAU;0FAET,UAAU,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpE,6LAAC,6IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,6IAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC,6IAAA,CAAA,YAAS;kEAAC;;;;;;oDACV,yBACC,6LAAC,+IAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa,CAAC;wDAC7B,WAAU;kEAET,YAAY,YAAY;;;;;;;;;;;;0DAI/B,6LAAC,6IAAA,CAAA,cAAW;0DACT,wBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qJAAA,CAAA,UAAkB;4DACjB,gBAAgB;4DAChB,OAAO;4DACP,QAAQ;4DACR,SAAS;4DACT,iBAAiB;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,+IAAA,CAAA,UAAY;gEAAC,UAAU;;;;;;;;;;;;;;;;yEAI5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnC,6LAAC,6IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,6IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,6IAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,6LAAC,6IAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,qBACf,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+IAAA,CAAA,SAAM;4EACL,SAAS,IAAM,WAAW;4EAC1B,MAAK;4EACL,SAAQ;;8FAER,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,6LAAC,+IAAA,CAAA,SAAM;4EACL,SAAS,IAAM,aAAa;4EAC5B,MAAK;4EACL,SAAQ;sFAER,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;2DAhBd;;;;kFAsBZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;0EAAE;;;;;;0EACH,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,6LAAC,6IAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,6IAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAI/B,6LAAC,6IAAA,CAAA,cAAW;0DACT,6BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAE/C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,yBAAyB;oEAAE,QAAQ;gEAAgB;;;;;;;;;;;;;;;;yEAKzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,6LAAC,6IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,6IAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,6IAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,6IAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,6LAAC;oEACC,WAAU;oEACV,OAAO,gBAAgB;oEACvB,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sFAE/C,6LAAC;4EAAO,OAAM;sFAAG;;;;;;wEAChB,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;gFAAkB,OAAO;0FACvB;+EADU;;;;;;;;;;;gEAKhB,8BACC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAY;sFACT,6LAAC;sFAAQ;;;;;;;;;;;;;;;;;;sEAKzC,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,6LAAC,8IAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,QAAO;oEACP,UAAU;;;;;;gEAEX,yBACC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC,iNAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;sFAClB,6LAAC;sFAAQ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sEAKzC,6LAAC,+IAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU,CAAC,gBAAgB,CAAC,WAAW;4DACvC,WAAU;4DACV,MAAK;;8EAEL,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,YAAY,sBAAsB;;;;;;;;;;;;;8DAIvC,6LAAC,6IAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,6LAAC,6IAAA,CAAA,aAAU;sEACT,cAAA,6LAAC,6IAAA,CAAA,YAAS;gEAAC,WAAU;0EAAgB;;;;;;;;;;;sEAEvC,6LAAC,6IAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6LAAC;8EAAE;;;;;;8EACH,6LAAC;8EAAE;;;;;;8EACH,6LAAC;8EAAE;;;;;;8EACH,6LAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUhB,wBACC,6LAAC,6IAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,6IAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAtjBwB;KAAA", "debugId": null}}]}