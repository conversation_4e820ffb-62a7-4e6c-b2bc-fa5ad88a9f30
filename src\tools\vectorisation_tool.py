from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.schema import Document
from typing import List


embedding_model = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")


def vectorize_scraped_data(snippets: List[str]) -> FAISS:
    model = embedding_model
    docs = [Document(page_content=chunk) for chunk in snippets]
    return FAISS.from_documents(docs, embedding=model)












# from langchain_community.vectorstores import FAISS
# from langchain_community.embeddings import HuggingFaceEmbeddings
# from langchain.schema import Document
# from typing import List




# embedding_model = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")

# print("✅ vectorisation_tool.py utilisé depuis MCP")

# def vectorize_scraped_data(snippets: List[str]) -> FAISS:
#     docs = [Document(page_content=chunk) for chunk in snippets]
#     return FAISS.from_documents(docs, embedding_model)



