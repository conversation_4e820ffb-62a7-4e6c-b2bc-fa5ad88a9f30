{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/emoticons/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.9.1 (2025-05-29)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    /* eslint-disable @typescript-eslint/no-wrapper-object-types */\n    const isSimpleType = (type) => (value) => typeof value === type;\n    const eq = (t) => (a) => t === a;\n    const isNull = eq(null);\n    const isUndefined = eq(undefined);\n    const isNullable = (a) => a === null || a === undefined;\n    const isNonNullable = (a) => !isNullable(a);\n    const isFunction = isSimpleType('function');\n\n    const noop = () => { };\n    const constant = (value) => {\n        return () => {\n            return value;\n        };\n    };\n    const never = constant(false);\n\n    /**\n     * The `Optional` type represents a value (of any type) that potentially does\n     * not exist. Any `Optional<T>` can either be a `Some<T>` (in which case the\n     * value does exist) or a `None` (in which case the value does not exist). This\n     * module defines a whole lot of FP-inspired utility functions for dealing with\n     * `Optional` objects.\n     *\n     * Comparison with null or undefined:\n     * - We don't get fancy null coalescing operators with `Optional`\n     * - We do get fancy helper functions with `Optional`\n     * - `Optional` support nesting, and allow for the type to still be nullable (or\n     * another `Optional`)\n     * - There is no option to turn off strict-optional-checks like there is for\n     * strict-null-checks\n     */\n    class Optional {\n        // The internal representation has a `tag` and a `value`, but both are\n        // private: able to be console.logged, but not able to be accessed by code\n        constructor(tag, value) {\n            this.tag = tag;\n            this.value = value;\n        }\n        // --- Identities ---\n        /**\n         * Creates a new `Optional<T>` that **does** contain a value.\n         */\n        static some(value) {\n            return new Optional(true, value);\n        }\n        /**\n         * Create a new `Optional<T>` that **does not** contain a value. `T` can be\n         * any type because we don't actually have a `T`.\n         */\n        static none() {\n            return Optional.singletonNone;\n        }\n        /**\n         * Perform a transform on an `Optional` type. Regardless of whether this\n         * `Optional` contains a value or not, `fold` will return a value of type `U`.\n         * If this `Optional` does not contain a value, the `U` will be created by\n         * calling `onNone`. If this `Optional` does contain a value, the `U` will be\n         * created by calling `onSome`.\n         *\n         * For the FP enthusiasts in the room, this function:\n         * 1. Could be used to implement all of the functions below\n         * 2. Forms a catamorphism\n         */\n        fold(onNone, onSome) {\n            if (this.tag) {\n                return onSome(this.value);\n            }\n            else {\n                return onNone();\n            }\n        }\n        /**\n         * Determine if this `Optional` object contains a value.\n         */\n        isSome() {\n            return this.tag;\n        }\n        /**\n         * Determine if this `Optional` object **does not** contain a value.\n         */\n        isNone() {\n            return !this.tag;\n        }\n        // --- Functor (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value. If\n         * you provide a function to turn a T into a U, this is the function you use\n         * to turn an `Optional<T>` into an `Optional<U>`. If this **does** contain\n         * a value then the output will also contain a value (that value being the\n         * output of `mapper(this.value)`), and if this **does not** contain a value\n         * then neither will the output.\n         */\n        map(mapper) {\n            if (this.tag) {\n                return Optional.some(mapper(this.value));\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Monad (name stolen from Haskell / maths) ---\n        /**\n         * Perform a transform on an `Optional` object, **if** there is a value.\n         * Unlike `map`, here the transform itself also returns an `Optional`.\n         */\n        bind(binder) {\n            if (this.tag) {\n                return binder(this.value);\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Traversable (name stolen from Haskell / maths) ---\n        /**\n         * For a given predicate, this function finds out if there **exists** a value\n         * inside this `Optional` object that meets the predicate. In practice, this\n         * means that for `Optional`s that do not contain a value it returns false (as\n         * no predicate-meeting value exists).\n         */\n        exists(predicate) {\n            return this.tag && predicate(this.value);\n        }\n        /**\n         * For a given predicate, this function finds out if **all** the values inside\n         * this `Optional` object meet the predicate. In practice, this means that\n         * for `Optional`s that do not contain a value it returns true (as all 0\n         * objects do meet the predicate).\n         */\n        forall(predicate) {\n            return !this.tag || predicate(this.value);\n        }\n        filter(predicate) {\n            if (!this.tag || predicate(this.value)) {\n                return this;\n            }\n            else {\n                return Optional.none();\n            }\n        }\n        // --- Getters ---\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.\n         */\n        getOr(replacement) {\n            return this.tag ? this.value : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value.  Unlike `getOr`, in this method the `replacement` object is also\n         * `Optional` - meaning that this method will always return an `Optional`.\n         */\n        or(replacement) {\n            return this.tag ? this : replacement;\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided `Optional` object does not contain a\n         * value. Unlike `getOr`, in this method the `replacement` value is\n         * \"thunked\" - that is to say that you don't pass a value to `getOrThunk`, you\n         * pass a function which (if called) will **return** the `value` you want to\n         * use.\n         */\n        getOrThunk(thunk) {\n            return this.tag ? this.value : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, using a default\n         * `replacement` value if the provided Optional object does not contain a\n         * value.\n         *\n         * Unlike `or`, in this method the `replacement` value is \"thunked\" - that is\n         * to say that you don't pass a value to `orThunk`, you pass a function which\n         * (if called) will **return** the `value` you want to use.\n         *\n         * Unlike `getOrThunk`, in this method the `replacement` value is also\n         * `Optional`, meaning that this method will always return an `Optional`.\n         */\n        orThunk(thunk) {\n            return this.tag ? this : thunk();\n        }\n        /**\n         * Get the value out of the inside of the `Optional` object, throwing an\n         * exception if the provided `Optional` object does not contain a value.\n         *\n         * WARNING:\n         * You should only be using this function if you know that the `Optional`\n         * object **is not** empty (otherwise you're throwing exceptions in production\n         * code, which is bad).\n         *\n         * In tests this is more acceptable.\n         *\n         * Prefer other methods to this, such as `.each`.\n         */\n        getOrDie(message) {\n            if (!this.tag) {\n                throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n            }\n            else {\n                return this.value;\n            }\n        }\n        // --- Interop with null and undefined ---\n        /**\n         * Creates an `Optional` value from a nullable (or undefined-able) input.\n         * Null, or undefined, is converted to `None`, and anything else is converted\n         * to `Some`.\n         */\n        static from(value) {\n            return isNonNullable(value) ? Optional.some(value) : Optional.none();\n        }\n        /**\n         * Converts an `Optional` to a nullable type, by getting the value if it\n         * exists, or returning `null` if it does not.\n         */\n        getOrNull() {\n            return this.tag ? this.value : null;\n        }\n        /**\n         * Converts an `Optional` to an undefined-able type, by getting the value if\n         * it exists, or returning `undefined` if it does not.\n         */\n        getOrUndefined() {\n            return this.value;\n        }\n        // --- Utilities ---\n        /**\n         * If the `Optional` contains a value, perform an action on that value.\n         * Unlike the rest of the methods on this type, `.each` has side-effects. If\n         * you want to transform an `Optional<T>` **into** something, then this is not\n         * the method for you. If you want to use an `Optional<T>` to **do**\n         * something, then this is the method for you - provided you're okay with not\n         * doing anything in the case where the `Optional` doesn't have a value inside\n         * it. If you're not sure whether your use-case fits into transforming\n         * **into** something or **doing** something, check whether it has a return\n         * value. If it does, you should be performing a transform.\n         */\n        each(worker) {\n            if (this.tag) {\n                worker(this.value);\n            }\n        }\n        /**\n         * Turn the `Optional` object into an array that contains all of the values\n         * stored inside the `Optional`. In practice, this means the output will have\n         * either 0 or 1 elements.\n         */\n        toArray() {\n            return this.tag ? [this.value] : [];\n        }\n        /**\n         * Turn the `Optional` object into a string for debugging or printing. Not\n         * recommended for production code, but good for debugging. Also note that\n         * these days an `Optional` object can be logged to the console directly, and\n         * its inner value (if it exists) will be visible.\n         */\n        toString() {\n            return this.tag ? `some(${this.value})` : 'none()';\n        }\n    }\n    // Sneaky optimisation: every instance of Optional.none is identical, so just\n    // reuse the same object\n    Optional.singletonNone = new Optional(false);\n\n    /* eslint-disable @typescript-eslint/unbound-method */\n    const nativeSlice = Array.prototype.slice;\n    const exists = (xs, pred) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            if (pred(x, i)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const map$1 = (xs, f) => {\n        // pre-allocating array size when it's guaranteed to be known\n        // http://jsperf.com/push-allocated-vs-dynamic/22\n        const len = xs.length;\n        const r = new Array(len);\n        for (let i = 0; i < len; i++) {\n            const x = xs[i];\n            r[i] = f(x, i);\n        }\n        return r;\n    };\n    // Unwound implementing other functions in terms of each.\n    // The code size is roughly the same, and it should allow for better optimisation.\n    // const each = function<T, U>(xs: T[], f: (x: T, i?: number, xs?: T[]) => void): void {\n    const each$1 = (xs, f) => {\n        for (let i = 0, len = xs.length; i < len; i++) {\n            const x = xs[i];\n            f(x, i);\n        }\n    };\n    isFunction(Array.from) ? Array.from : (x) => nativeSlice.call(x);\n\n    // There are many variations of Object iteration that are faster than the 'for-in' style:\n    // http://jsperf.com/object-keys-iteration/107\n    //\n    // Use the native keys if it is available (IE9+), otherwise fall back to manually filtering\n    const keys = Object.keys;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n        const props = keys(obj);\n        for (let k = 0, len = props.length; k < len; k++) {\n            const i = props[k];\n            const x = obj[i];\n            f(x, i);\n        }\n    };\n    const map = (obj, f) => {\n        return tupleMap(obj, (x, i) => ({\n            k: i,\n            v: f(x, i)\n        }));\n    };\n    const tupleMap = (obj, f) => {\n        const r = {};\n        each(obj, (x, i) => {\n            const tuple = f(x, i);\n            r[tuple.k] = tuple.v;\n        });\n        return r;\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const Cell = (initial) => {\n        let value = initial;\n        const get = () => {\n            return value;\n        };\n        const set = (v) => {\n            value = v;\n        };\n        return {\n            get,\n            set\n        };\n    };\n\n    const shallow = (old, nu) => {\n        return nu;\n    };\n    const baseMerge = (merger) => {\n        return (...objects) => {\n            if (objects.length === 0) {\n                throw new Error(`Can't merge zero objects`);\n            }\n            const ret = {};\n            for (let j = 0; j < objects.length; j++) {\n                const curObject = objects[j];\n                for (const key in curObject) {\n                    if (has(curObject, key)) {\n                        ret[key] = merger(ret[key], curObject[key]);\n                    }\n                }\n            }\n            return ret;\n        };\n    };\n    const merge = baseMerge(shallow);\n\n    const singleton = (doRevoke) => {\n        const subject = Cell(Optional.none());\n        const revoke = () => subject.get().each(doRevoke);\n        const clear = () => {\n            revoke();\n            subject.set(Optional.none());\n        };\n        const isSet = () => subject.get().isSome();\n        const get = () => subject.get();\n        const set = (s) => {\n            revoke();\n            subject.set(Optional.some(s));\n        };\n        return {\n            clear,\n            isSet,\n            get,\n            set\n        };\n    };\n    const value = () => {\n        const subject = singleton(noop);\n        const on = (f) => subject.get().each(f);\n        return {\n            ...subject,\n            on\n        };\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const contains = (str, substr, start = 0, end) => {\n        const idx = str.indexOf(substr, start);\n        if (idx !== -1) {\n            return isUndefined(end) ? true : idx + substr.length <= end;\n        }\n        else {\n            return false;\n        }\n    };\n    /** Does 'str' start with 'prefix'?\n     *  Note: all strings start with the empty string.\n     *        More formally, for all strings x, startsWith(x, \"\").\n     *        This is so that for all strings x and y, startsWith(y + x, y)\n     */\n    const startsWith = (str, prefix) => {\n        return checkRange(str, prefix, 0);\n    };\n\n    // Run a function fn after rate ms. If another invocation occurs\n    // during the time it is waiting, reschedule the function again\n    // with the new arguments.\n    const last = (fn, rate) => {\n        let timer = null;\n        const cancel = () => {\n            if (!isNull(timer)) {\n                clearTimeout(timer);\n                timer = null;\n            }\n        };\n        const throttle = (...args) => {\n            cancel();\n            timer = setTimeout(() => {\n                timer = null;\n                fn.apply(null, args);\n            }, rate);\n        };\n        return {\n            cancel,\n            throttle\n        };\n    };\n\n    const insertEmoticon = (editor, ch) => {\n        editor.insertContent(ch);\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Resource');\n\n    const DEFAULT_ID = 'tinymce.plugins.emoticons';\n    const option = (name) => (editor) => editor.options.get(name);\n    const register$2 = (editor, pluginUrl) => {\n        const registerOption = editor.options.register;\n        registerOption('emoticons_database', {\n            processor: 'string',\n            default: 'emojis'\n        });\n        registerOption('emoticons_database_url', {\n            processor: 'string',\n            default: `${pluginUrl}/js/${getEmojiDatabase(editor)}${editor.suffix}.js`\n        });\n        registerOption('emoticons_database_id', {\n            processor: 'string',\n            default: DEFAULT_ID\n        });\n        registerOption('emoticons_append', {\n            processor: 'object',\n            default: {}\n        });\n        registerOption('emoticons_images_url', {\n            processor: 'string',\n            default: 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/15.1.0/72x72/'\n        });\n    };\n    const getEmojiDatabase = option('emoticons_database');\n    const getEmojiDatabaseUrl = option('emoticons_database_url');\n    const getEmojiDatabaseId = option('emoticons_database_id');\n    const getAppendedEmoji = option('emoticons_append');\n    const getEmojiImageUrl = option('emoticons_images_url');\n\n    const ALL_CATEGORY = 'All';\n    const categoryNameMap = {\n        symbols: 'Symbols',\n        people: 'People',\n        animals_and_nature: 'Animals and Nature',\n        food_and_drink: 'Food and Drink',\n        activity: 'Activity',\n        travel_and_places: 'Travel and Places',\n        objects: 'Objects',\n        flags: 'Flags',\n        user: 'User Defined'\n    };\n    const translateCategory = (categories, name) => has(categories, name) ? categories[name] : name;\n    const getUserDefinedEmoji = (editor) => {\n        const userDefinedEmoticons = getAppendedEmoji(editor);\n        return map(userDefinedEmoticons, (value) => \n        // Set some sane defaults for the custom emoji entry\n        ({ keywords: [], category: 'user', ...value }));\n    };\n    // TODO: Consider how to share this loading across different editors\n    const initDatabase = (editor, databaseUrl, databaseId) => {\n        const categories = value();\n        const all = value();\n        const emojiImagesUrl = getEmojiImageUrl(editor);\n        const getEmoji = (lib) => {\n            // Note: This is a little hacky, but the database doesn't provide a way for us to tell what sort of database is being used\n            if (startsWith(lib.char, '<img')) {\n                return lib.char.replace(/src=\"([^\"]+)\"/, (match, url) => `src=\"${emojiImagesUrl}${url}\"`);\n            }\n            else {\n                return lib.char;\n            }\n        };\n        const processEmojis = (emojis) => {\n            const cats = {};\n            const everything = [];\n            each(emojis, (lib, title) => {\n                const entry = {\n                    // Omitting fitzpatrick_scale\n                    title,\n                    keywords: lib.keywords,\n                    char: getEmoji(lib),\n                    category: translateCategory(categoryNameMap, lib.category)\n                };\n                const current = cats[entry.category] !== undefined ? cats[entry.category] : [];\n                cats[entry.category] = current.concat([entry]);\n                everything.push(entry);\n            });\n            categories.set(cats);\n            all.set(everything);\n        };\n        editor.on('init', () => {\n            global.load(databaseId, databaseUrl).then((emojis) => {\n                const userEmojis = getUserDefinedEmoji(editor);\n                processEmojis(merge(emojis, userEmojis));\n            }, (err) => {\n                // eslint-disable-next-line no-console\n                console.log(`Failed to load emojis: ${err}`);\n                categories.set({});\n                all.set([]);\n            });\n        });\n        const listCategory = (category) => {\n            if (category === ALL_CATEGORY) {\n                return listAll();\n            }\n            return categories.get().bind((cats) => Optional.from(cats[category])).getOr([]);\n        };\n        const listAll = () => all.get().getOr([]);\n        const listCategories = () => \n        // TODO: Category key order should be adjusted to match the standard\n        [ALL_CATEGORY].concat(keys(categories.get().getOr({})));\n        const waitForLoad = () => {\n            if (hasLoaded()) {\n                return Promise.resolve(true);\n            }\n            else {\n                return new Promise((resolve, reject) => {\n                    let numRetries = 15;\n                    const interval = setInterval(() => {\n                        if (hasLoaded()) {\n                            clearInterval(interval);\n                            resolve(true);\n                        }\n                        else {\n                            numRetries--;\n                            if (numRetries < 0) {\n                                // eslint-disable-next-line no-console\n                                console.log('Could not load emojis from url: ' + databaseUrl);\n                                clearInterval(interval);\n                                reject(false);\n                            }\n                        }\n                    }, 100);\n                });\n            }\n        };\n        const hasLoaded = () => categories.isSet() && all.isSet();\n        return {\n            listCategories,\n            hasLoaded,\n            waitForLoad,\n            listAll,\n            listCategory\n        };\n    };\n\n    const emojiMatches = (emoji, lowerCasePattern) => contains(emoji.title.toLowerCase(), lowerCasePattern) ||\n        exists(emoji.keywords, (k) => contains(k.toLowerCase(), lowerCasePattern));\n    const emojisFrom = (list, pattern, maxResults) => {\n        const matches = [];\n        const lowerCasePattern = pattern.toLowerCase();\n        const reachedLimit = maxResults.fold(() => never, (max) => (size) => size >= max);\n        for (let i = 0; i < list.length; i++) {\n            // TODO: more intelligent search by showing title matches at the top, keyword matches after that (use two arrays and concat at the end)\n            if (pattern.length === 0 || emojiMatches(list[i], lowerCasePattern)) {\n                matches.push({\n                    value: list[i].char,\n                    text: list[i].title,\n                    icon: list[i].char\n                });\n                if (reachedLimit(matches.length)) {\n                    break;\n                }\n            }\n        }\n        return matches;\n    };\n\n    const patternName = 'pattern';\n    const open = (editor, database) => {\n        const initialState = {\n            pattern: '',\n            results: emojisFrom(database.listAll(), '', Optional.some(300))\n        };\n        const currentTab = Cell(ALL_CATEGORY);\n        const scan = (dialogApi) => {\n            const dialogData = dialogApi.getData();\n            const category = currentTab.get();\n            const candidates = database.listCategory(category);\n            const results = emojisFrom(candidates, dialogData[patternName], category === ALL_CATEGORY ? Optional.some(300) : Optional.none());\n            dialogApi.setData({\n                results\n            });\n        };\n        const updateFilter = last((dialogApi) => {\n            scan(dialogApi);\n        }, 200);\n        const searchField = {\n            label: 'Search',\n            type: 'input',\n            name: patternName\n        };\n        const resultsField = {\n            type: 'collection',\n            name: 'results'\n            // TODO TINY-3229 implement collection columns properly\n            // columns: 'auto'\n        };\n        const getInitialState = () => {\n            const body = {\n                type: 'tabpanel',\n                // All tabs have the same fields.\n                tabs: map$1(database.listCategories(), (cat) => ({\n                    title: cat,\n                    name: cat,\n                    items: [searchField, resultsField]\n                }))\n            };\n            return {\n                title: 'Emojis',\n                size: 'normal',\n                body,\n                initialData: initialState,\n                onTabChange: (dialogApi, details) => {\n                    currentTab.set(details.newTabName);\n                    updateFilter.throttle(dialogApi);\n                },\n                onChange: updateFilter.throttle,\n                onAction: (dialogApi, actionData) => {\n                    if (actionData.name === 'results') {\n                        insertEmoticon(editor, actionData.value);\n                        dialogApi.close();\n                    }\n                },\n                buttons: [\n                    {\n                        type: 'cancel',\n                        text: 'Close',\n                        primary: true\n                    }\n                ]\n            };\n        };\n        const dialogApi = editor.windowManager.open(getInitialState());\n        dialogApi.focus(patternName);\n        if (!database.hasLoaded()) {\n            dialogApi.block('Loading emojis...');\n            database.waitForLoad().then(() => {\n                dialogApi.redial(getInitialState());\n                updateFilter.throttle(dialogApi);\n                dialogApi.focus(patternName);\n                dialogApi.unblock();\n            }).catch((_err) => {\n                dialogApi.redial({\n                    title: 'Emojis',\n                    body: {\n                        type: 'panel',\n                        items: [\n                            {\n                                type: 'alertbanner',\n                                level: 'error',\n                                icon: 'warning',\n                                text: 'Could not load emojis'\n                            }\n                        ]\n                    },\n                    buttons: [\n                        {\n                            type: 'cancel',\n                            text: 'Close',\n                            primary: true\n                        }\n                    ],\n                    initialData: {\n                        pattern: '',\n                        results: []\n                    }\n                });\n                dialogApi.focus(patternName);\n                dialogApi.unblock();\n            });\n        }\n    };\n\n    const register$1 = (editor, database) => {\n        editor.addCommand('mceEmoticons', () => open(editor, database));\n    };\n\n    const setup = (editor) => {\n        editor.on('PreInit', () => {\n            editor.parser.addAttributeFilter('data-emoticon', (nodes) => {\n                each$1(nodes, (node) => {\n                    node.attr('data-mce-resize', 'false');\n                    node.attr('data-mce-placeholder', '1');\n                });\n            });\n        });\n    };\n\n    const init = (editor, database) => {\n        editor.ui.registry.addAutocompleter('emoticons', {\n            trigger: ':',\n            columns: 'auto',\n            minChars: 2,\n            fetch: (pattern, maxResults) => database.waitForLoad().then(() => {\n                const candidates = database.listAll();\n                return emojisFrom(candidates, pattern, Optional.some(maxResults));\n            }),\n            onAction: (autocompleteApi, rng, value) => {\n                editor.selection.setRng(rng);\n                editor.insertContent(value);\n                autocompleteApi.hide();\n            }\n        });\n    };\n\n    const onSetupEditable = (editor) => (api) => {\n        const nodeChanged = () => {\n            api.setEnabled(editor.selection.isEditable());\n        };\n        editor.on('NodeChange', nodeChanged);\n        nodeChanged();\n        return () => {\n            editor.off('NodeChange', nodeChanged);\n        };\n    };\n    const register = (editor) => {\n        const onAction = () => editor.execCommand('mceEmoticons');\n        editor.ui.registry.addButton('emoticons', {\n            tooltip: 'Emojis',\n            icon: 'emoji',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n        editor.ui.registry.addMenuItem('emoticons', {\n            text: 'Emojis...',\n            icon: 'emoji',\n            onAction,\n            onSetup: onSetupEditable(editor)\n        });\n    };\n\n    /**\n     * This class contains all core logic for the emoticons plugin.\n     *\n     * @class tinymce.emoticons.Plugin\n     * @private\n     */\n    var Plugin = () => {\n        global$1.add('emoticons', (editor, pluginUrl) => {\n            register$2(editor, pluginUrl);\n            const databaseUrl = getEmojiDatabaseUrl(editor);\n            const databaseId = getEmojiDatabaseId(editor);\n            const database = initDatabase(editor, databaseUrl, databaseId);\n            register$1(editor, database);\n            register(editor);\n            init(editor, database);\n            setup(editor);\n            return {\n                getAllEmojis: () => database.waitForLoad().then(() => database.listAll())\n            };\n        });\n    };\n\n    Plugin();\n    /** *****\n     * DO NOT EXPORT ANYTHING\n     *\n     * IF YOU DO ROLLUP WILL LEAVE A GLOBAL ON THE PAGE\n     *******/\n\n})();\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,CAAC;IACG;IAEA,IAAI,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAE1C,6DAA6D,GAC7D,MAAM,eAAe,CAAC,OAAS,CAAC,QAAU,OAAO,UAAU;IAC3D,MAAM,KAAK,CAAC,IAAM,CAAC,IAAM,MAAM;IAC/B,MAAM,SAAS,GAAG;IAClB,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,CAAC,IAAM,MAAM,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,CAAC,IAAM,CAAC,WAAW;IACzC,MAAM,aAAa,aAAa;IAEhC,MAAM,OAAO,KAAQ;IACrB,MAAM,WAAW,CAAC;QACd,OAAO;YACH,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,SAAS;IAEvB;;;;;;;;;;;;;;KAcC,GACD,MAAM;QAOF,qBAAqB;QACrB;;SAEC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA;;;SAGC,GACD,OAAO,OAAO;YACV,OAAO,SAAS,aAAa;QACjC;QACA;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE,MAAM,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO;YACX;QACJ;QACA;;SAEC,GACD,SAAS;YACL,OAAO,IAAI,CAAC,GAAG;QACnB;QACA;;SAEC,GACD,SAAS;YACL,OAAO,CAAC,IAAI,CAAC,GAAG;QACpB;QACA,qDAAqD;QACrD;;;;;;;SAOC,GACD,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAC1C,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,mDAAmD;QACnD;;;SAGC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,OAAO,IAAI,CAAC,KAAK;YAC5B,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,yDAAyD;QACzD;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC3C;QACA;;;;;SAKC,GACD,OAAO,SAAS,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;QAC5C;QACA,OAAO,SAAS,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG;gBACpC,OAAO,IAAI;YACf,OACK;gBACD,OAAO,SAAS,IAAI;YACxB;QACJ;QACA,kBAAkB;QAClB;;;;SAIC,GACD,MAAM,WAAW,EAAE;YACf,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;SAKC,GACD,GAAG,WAAW,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;SAOC,GACD,WAAW,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;;;;;;;;;SAWC,GACD,QAAQ,KAAK,EAAE;YACX,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG;QAC7B;QACA;;;;;;;;;;;;SAYC,GACD,SAAS,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;YACvE,OACK;gBACD,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA,0CAA0C;QAC1C;;;;SAIC,GACD,OAAO,KAAK,KAAK,EAAE;YACf,OAAO,cAAc,SAAS,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;QACtE;QACA;;;SAGC,GACD,YAAY;YACR,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG;QACnC;QACA;;;SAGC,GACD,iBAAiB;YACb,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,oBAAoB;QACpB;;;;;;;;;;SAUC,GACD,KAAK,MAAM,EAAE;YACT,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK;YACrB;QACJ;QACA;;;;SAIC,GACD,UAAU;YACN,OAAO,IAAI,CAAC,GAAG,GAAG;gBAAC,IAAI,CAAC,KAAK;aAAC,GAAG,EAAE;QACvC;QACA;;;;;SAKC,GACD,WAAW;YACP,OAAO,IAAI,CAAC,GAAG,GAAG,AAAC,QAAkB,OAAX,IAAI,CAAC,KAAK,EAAC,OAAK;QAC9C;QArOA,sEAAsE;QACtE,0EAA0E;QAC1E,YAAY,GAAG,EAAE,KAAK,CAAE;YACpB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,KAAK,GAAG;QACjB;IAiOJ;IACA,6EAA6E;IAC7E,wBAAwB;IACxB,SAAS,aAAa,GAAG,IAAI,SAAS;IAEtC,oDAAoD,GACpD,MAAM,cAAc,MAAM,SAAS,CAAC,KAAK;IACzC,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI;gBACZ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,QAAQ,CAAC,IAAI;QACf,6DAA6D;QAC7D,iDAAiD;QACjD,MAAM,MAAM,GAAG,MAAM;QACrB,MAAM,IAAI,IAAI,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,yDAAyD;IACzD,kFAAkF;IAClF,wFAAwF;IACxF,MAAM,SAAS,CAAC,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,IAAK;YAC3C,MAAM,IAAI,EAAE,CAAC,EAAE;YACf,EAAE,GAAG;QACT;IACJ;IACA,WAAW,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAM,YAAY,IAAI,CAAC;IAE9D,yFAAyF;IACzF,8CAA8C;IAC9C,EAAE;IACF,2FAA2F;IAC3F,MAAM,OAAO,OAAO,IAAI;IACxB,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,cAAc;IAC5C,MAAM,OAAO,CAAC,KAAK;QACf,MAAM,QAAQ,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,IAAI,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG;QACT;IACJ;IACA,MAAM,MAAM,CAAC,KAAK;QACd,OAAO,SAAS,KAAK,CAAC,GAAG,IAAM,CAAC;gBAC5B,GAAG;gBACH,GAAG,EAAE,GAAG;YACZ,CAAC;IACL;IACA,MAAM,WAAW,CAAC,KAAK;QACnB,MAAM,IAAI,CAAC;QACX,KAAK,KAAK,CAAC,GAAG;YACV,MAAM,QAAQ,EAAE,GAAG;YACnB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;QACxB;QACA,OAAO;IACX;IACA,MAAM,MAAM,CAAC,KAAK,MAAQ,eAAe,IAAI,CAAC,KAAK;IAEnD,MAAM,OAAO,CAAC;QACV,IAAI,QAAQ;QACZ,MAAM,MAAM;YACR,OAAO;QACX;QACA,MAAM,MAAM,CAAC;YACT,QAAQ;QACZ;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,UAAU,CAAC,KAAK;QAClB,OAAO;IACX;IACA,MAAM,YAAY,CAAC;QACf,OAAO;6CAAI;gBAAA;;YACP,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACtB,MAAM,IAAI,MAAO;YACrB;YACA,MAAM,MAAM,CAAC;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACrC,MAAM,YAAY,OAAO,CAAC,EAAE;gBAC5B,IAAK,MAAM,OAAO,UAAW;oBACzB,IAAI,IAAI,WAAW,MAAM;wBACrB,GAAG,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI;oBAC9C;gBACJ;YACJ;YACA,OAAO;QACX;IACJ;IACA,MAAM,QAAQ,UAAU;IAExB,MAAM,YAAY,CAAC;QACf,MAAM,UAAU,KAAK,SAAS,IAAI;QAClC,MAAM,SAAS,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC7B;QACA,MAAM,QAAQ,IAAM,QAAQ,GAAG,GAAG,MAAM;QACxC,MAAM,MAAM,IAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,CAAC;YACT;YACA,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC;QAC9B;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,MAAM,QAAQ;QACV,MAAM,UAAU,UAAU;QAC1B,MAAM,KAAK,CAAC,IAAM,QAAQ,GAAG,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,GAAG,OAAO;YACV;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC,KAAK,QAAQ,QAAU,WAAW,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,QAAQ,OAAO,MAAM,MAAM;IACxI,MAAM,WAAW,SAAC,KAAK;YAAQ,yEAAQ,GAAG;QACtC,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ;QAChC,IAAI,QAAQ,CAAC,GAAG;YACZ,OAAO,YAAY,OAAO,OAAO,MAAM,OAAO,MAAM,IAAI;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IACA;;;;KAIC,GACD,MAAM,aAAa,CAAC,KAAK;QACrB,OAAO,WAAW,KAAK,QAAQ;IACnC;IAEA,gEAAgE;IAChE,+DAA+D;IAC/D,0BAA0B;IAC1B,MAAM,OAAO,CAAC,IAAI;QACd,IAAI,QAAQ;QACZ,MAAM,SAAS;YACX,IAAI,CAAC,OAAO,QAAQ;gBAChB,aAAa;gBACb,QAAQ;YACZ;QACJ;QACA,MAAM,WAAW;6CAAI;gBAAA;;YACjB;YACA,QAAQ,WAAW;gBACf,QAAQ;gBACR,GAAG,KAAK,CAAC,MAAM;YACnB,GAAG;QACP;QACA,OAAO;YACH;YACA;QACJ;IACJ;IAEA,MAAM,iBAAiB,CAAC,QAAQ;QAC5B,OAAO,aAAa,CAAC;IACzB;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAExC,MAAM,aAAa;IACnB,MAAM,SAAS,CAAC,OAAS,CAAC,SAAW,OAAO,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,aAAa,CAAC,QAAQ;QACxB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ;QAC9C,eAAe,sBAAsB;YACjC,WAAW;YACX,SAAS;QACb;QACA,eAAe,0BAA0B;YACrC,WAAW;YACX,SAAS,AAAC,GAAkB,OAAhB,WAAU,QAAiC,OAA3B,iBAAiB,SAAwB,OAAd,OAAO,MAAM,EAAC;QACzE;QACA,eAAe,yBAAyB;YACpC,WAAW;YACX,SAAS;QACb;QACA,eAAe,oBAAoB;YAC/B,WAAW;YACX,SAAS,CAAC;QACd;QACA,eAAe,wBAAwB;YACnC,WAAW;YACX,SAAS;QACb;IACJ;IACA,MAAM,mBAAmB,OAAO;IAChC,MAAM,sBAAsB,OAAO;IACnC,MAAM,qBAAqB,OAAO;IAClC,MAAM,mBAAmB,OAAO;IAChC,MAAM,mBAAmB,OAAO;IAEhC,MAAM,eAAe;IACrB,MAAM,kBAAkB;QACpB,SAAS;QACT,QAAQ;QACR,oBAAoB;QACpB,gBAAgB;QAChB,UAAU;QACV,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,MAAM;IACV;IACA,MAAM,oBAAoB,CAAC,YAAY,OAAS,IAAI,YAAY,QAAQ,UAAU,CAAC,KAAK,GAAG;IAC3F,MAAM,sBAAsB,CAAC;QACzB,MAAM,uBAAuB,iBAAiB;QAC9C,OAAO,IAAI,sBAAsB,CAAC,QAClC,oDAAoD;YACpD,CAAC;gBAAE,UAAU,EAAE;gBAAE,UAAU;gBAAQ,GAAG,KAAK;YAAC,CAAC;IACjD;IACA,oEAAoE;IACpE,MAAM,eAAe,CAAC,QAAQ,aAAa;QACvC,MAAM,aAAa;QACnB,MAAM,MAAM;QACZ,MAAM,iBAAiB,iBAAiB;QACxC,MAAM,WAAW,CAAC;YACd,0HAA0H;YAC1H,IAAI,WAAW,IAAI,IAAI,EAAE,SAAS;gBAC9B,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,MAAQ,AAAC,QAAwB,OAAjB,gBAAqB,OAAJ,KAAI;YAC1F,OACK;gBACD,OAAO,IAAI,IAAI;YACnB;QACJ;QACA,MAAM,gBAAgB,CAAC;YACnB,MAAM,OAAO,CAAC;YACd,MAAM,aAAa,EAAE;YACrB,KAAK,QAAQ,CAAC,KAAK;gBACf,MAAM,QAAQ;oBACV,6BAA6B;oBAC7B;oBACA,UAAU,IAAI,QAAQ;oBACtB,MAAM,SAAS;oBACf,UAAU,kBAAkB,iBAAiB,IAAI,QAAQ;gBAC7D;gBACA,MAAM,UAAU,IAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE;gBAC9E,IAAI,CAAC,MAAM,QAAQ,CAAC,GAAG,QAAQ,MAAM,CAAC;oBAAC;iBAAM;gBAC7C,WAAW,IAAI,CAAC;YACpB;YACA,WAAW,GAAG,CAAC;YACf,IAAI,GAAG,CAAC;QACZ;QACA,OAAO,EAAE,CAAC,QAAQ;YACd,OAAO,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC;gBACvC,MAAM,aAAa,oBAAoB;gBACvC,cAAc,MAAM,QAAQ;YAChC,GAAG,CAAC;gBACA,sCAAsC;gBACtC,QAAQ,GAAG,CAAC,AAAC,0BAA6B,OAAJ;gBACtC,WAAW,GAAG,CAAC,CAAC;gBAChB,IAAI,GAAG,CAAC,EAAE;YACd;QACJ;QACA,MAAM,eAAe,CAAC;YAClB,IAAI,aAAa,cAAc;gBAC3B,OAAO;YACX;YACA,OAAO,WAAW,GAAG,GAAG,IAAI,CAAC,CAAC,OAAS,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE;QAClF;QACA,MAAM,UAAU,IAAM,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE;QACxC,MAAM,iBAAiB,IACvB,oEAAoE;YACpE;gBAAC;aAAa,CAAC,MAAM,CAAC,KAAK,WAAW,GAAG,GAAG,KAAK,CAAC,CAAC;QACnD,MAAM,cAAc;YAChB,IAAI,aAAa;gBACb,OAAO,QAAQ,OAAO,CAAC;YAC3B,OACK;gBACD,OAAO,IAAI,QAAQ,CAAC,SAAS;oBACzB,IAAI,aAAa;oBACjB,MAAM,WAAW,YAAY;wBACzB,IAAI,aAAa;4BACb,cAAc;4BACd,QAAQ;wBACZ,OACK;4BACD;4BACA,IAAI,aAAa,GAAG;gCAChB,sCAAsC;gCACtC,QAAQ,GAAG,CAAC,qCAAqC;gCACjD,cAAc;gCACd,OAAO;4BACX;wBACJ;oBACJ,GAAG;gBACP;YACJ;QACJ;QACA,MAAM,YAAY,IAAM,WAAW,KAAK,MAAM,IAAI,KAAK;QACvD,OAAO;YACH;YACA;YACA;YACA;YACA;QACJ;IACJ;IAEA,MAAM,eAAe,CAAC,OAAO,mBAAqB,SAAS,MAAM,KAAK,CAAC,WAAW,IAAI,qBAClF,OAAO,MAAM,QAAQ,EAAE,CAAC,IAAM,SAAS,EAAE,WAAW,IAAI;IAC5D,MAAM,aAAa,CAAC,MAAM,SAAS;QAC/B,MAAM,UAAU,EAAE;QAClB,MAAM,mBAAmB,QAAQ,WAAW;QAC5C,MAAM,eAAe,WAAW,IAAI,CAAC,IAAM,OAAO,CAAC,MAAQ,CAAC,OAAS,QAAQ;QAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,uIAAuI;YACvI,IAAI,QAAQ,MAAM,KAAK,KAAK,aAAa,IAAI,CAAC,EAAE,EAAE,mBAAmB;gBACjE,QAAQ,IAAI,CAAC;oBACT,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI;oBACnB,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK;oBACnB,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI;gBACtB;gBACA,IAAI,aAAa,QAAQ,MAAM,GAAG;oBAC9B;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IAEA,MAAM,cAAc;IACpB,MAAM,OAAO,CAAC,QAAQ;QAClB,MAAM,eAAe;YACjB,SAAS;YACT,SAAS,WAAW,SAAS,OAAO,IAAI,IAAI,SAAS,IAAI,CAAC;QAC9D;QACA,MAAM,aAAa,KAAK;QACxB,MAAM,OAAO,CAAC;YACV,MAAM,aAAa,UAAU,OAAO;YACpC,MAAM,WAAW,WAAW,GAAG;YAC/B,MAAM,aAAa,SAAS,YAAY,CAAC;YACzC,MAAM,UAAU,WAAW,YAAY,UAAU,CAAC,YAAY,EAAE,aAAa,eAAe,SAAS,IAAI,CAAC,OAAO,SAAS,IAAI;YAC9H,UAAU,OAAO,CAAC;gBACd;YACJ;QACJ;QACA,MAAM,eAAe,KAAK,CAAC;YACvB,KAAK;QACT,GAAG;QACH,MAAM,cAAc;YAChB,OAAO;YACP,MAAM;YACN,MAAM;QACV;QACA,MAAM,eAAe;YACjB,MAAM;YACN,MAAM;QAGV;QACA,MAAM,kBAAkB;YACpB,MAAM,OAAO;gBACT,MAAM;gBACN,iCAAiC;gBACjC,MAAM,MAAM,SAAS,cAAc,IAAI,CAAC,MAAQ,CAAC;wBAC7C,OAAO;wBACP,MAAM;wBACN,OAAO;4BAAC;4BAAa;yBAAa;oBACtC,CAAC;YACL;YACA,OAAO;gBACH,OAAO;gBACP,MAAM;gBACN;gBACA,aAAa;gBACb,aAAa,CAAC,WAAW;oBACrB,WAAW,GAAG,CAAC,QAAQ,UAAU;oBACjC,aAAa,QAAQ,CAAC;gBAC1B;gBACA,UAAU,aAAa,QAAQ;gBAC/B,UAAU,CAAC,WAAW;oBAClB,IAAI,WAAW,IAAI,KAAK,WAAW;wBAC/B,eAAe,QAAQ,WAAW,KAAK;wBACvC,UAAU,KAAK;oBACnB;gBACJ;gBACA,SAAS;oBACL;wBACI,MAAM;wBACN,MAAM;wBACN,SAAS;oBACb;iBACH;YACL;QACJ;QACA,MAAM,YAAY,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5C,UAAU,KAAK,CAAC;QAChB,IAAI,CAAC,SAAS,SAAS,IAAI;YACvB,UAAU,KAAK,CAAC;YAChB,SAAS,WAAW,GAAG,IAAI,CAAC;gBACxB,UAAU,MAAM,CAAC;gBACjB,aAAa,QAAQ,CAAC;gBACtB,UAAU,KAAK,CAAC;gBAChB,UAAU,OAAO;YACrB,GAAG,KAAK,CAAC,CAAC;gBACN,UAAU,MAAM,CAAC;oBACb,OAAO;oBACP,MAAM;wBACF,MAAM;wBACN,OAAO;4BACH;gCACI,MAAM;gCACN,OAAO;gCACP,MAAM;gCACN,MAAM;4BACV;yBACH;oBACL;oBACA,SAAS;wBACL;4BACI,MAAM;4BACN,MAAM;4BACN,SAAS;wBACb;qBACH;oBACD,aAAa;wBACT,SAAS;wBACT,SAAS,EAAE;oBACf;gBACJ;gBACA,UAAU,KAAK,CAAC;gBAChB,UAAU,OAAO;YACrB;QACJ;IACJ;IAEA,MAAM,aAAa,CAAC,QAAQ;QACxB,OAAO,UAAU,CAAC,gBAAgB,IAAM,KAAK,QAAQ;IACzD;IAEA,MAAM,QAAQ,CAAC;QACX,OAAO,EAAE,CAAC,WAAW;YACjB,OAAO,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;gBAC/C,OAAO,OAAO,CAAC;oBACX,KAAK,IAAI,CAAC,mBAAmB;oBAC7B,KAAK,IAAI,CAAC,wBAAwB;gBACtC;YACJ;QACJ;IACJ;IAEA,MAAM,OAAO,CAAC,QAAQ;QAClB,OAAO,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa;YAC7C,SAAS;YACT,SAAS;YACT,UAAU;YACV,OAAO,CAAC,SAAS,aAAe,SAAS,WAAW,GAAG,IAAI,CAAC;oBACxD,MAAM,aAAa,SAAS,OAAO;oBACnC,OAAO,WAAW,YAAY,SAAS,SAAS,IAAI,CAAC;gBACzD;YACA,UAAU,CAAC,iBAAiB,KAAK;gBAC7B,OAAO,SAAS,CAAC,MAAM,CAAC;gBACxB,OAAO,aAAa,CAAC;gBACrB,gBAAgB,IAAI;YACxB;QACJ;IACJ;IAEA,MAAM,kBAAkB,CAAC,SAAW,CAAC;YACjC,MAAM,cAAc;gBAChB,IAAI,UAAU,CAAC,OAAO,SAAS,CAAC,UAAU;YAC9C;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;YACA,OAAO;gBACH,OAAO,GAAG,CAAC,cAAc;YAC7B;QACJ;IACA,MAAM,WAAW,CAAC;QACd,MAAM,WAAW,IAAM,OAAO,WAAW,CAAC;QAC1C,OAAO,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa;YACtC,SAAS;YACT,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;QACA,OAAO,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa;YACxC,MAAM;YACN,MAAM;YACN;YACA,SAAS,gBAAgB;QAC7B;IACJ;IAEA;;;;;KAKC,GACD,IAAI,SAAS;QACT,SAAS,GAAG,CAAC,aAAa,CAAC,QAAQ;YAC/B,WAAW,QAAQ;YACnB,MAAM,cAAc,oBAAoB;YACxC,MAAM,aAAa,mBAAmB;YACtC,MAAM,WAAW,aAAa,QAAQ,aAAa;YACnD,WAAW,QAAQ;YACnB,SAAS;YACT,KAAK,QAAQ;YACb,MAAM;YACN,OAAO;gBACH,cAAc,IAAM,SAAS,WAAW,GAAG,IAAI,CAAC,IAAM,SAAS,OAAO;YAC1E;QACJ;IACJ;IAEA;AACA;;;;WAIO,GAEX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/NEWSLETTER/src/interfaces/frontend/node_modules/tinymce/plugins/emoticons/index.js"], "sourcesContent": ["// Exports the \"emoticons\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/emoticons')\n//   ES2015:\n//     import 'tinymce/plugins/emoticons'\nrequire('./plugin.js');"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,SAAS;AACT,cAAc;AACd,2CAA2C;AAC3C,YAAY;AACZ,yCAAyC", "ignoreList": [0], "debugId": null}}]}