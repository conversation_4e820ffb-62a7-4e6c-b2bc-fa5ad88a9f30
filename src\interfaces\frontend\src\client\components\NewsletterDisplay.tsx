import { Card, CardContent } from "@/client/components/ui/card";

interface NewsletterDisplayProps {
  content: string;
}

export default function NewsletterDisplay({ content }: NewsletterDisplayProps) {
  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div 
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </CardContent>
    </Card>
  );
}
























// // export default function NewsletterDisplay({ content }: { content: string }) {
// //   return (
// //     <div className="bg-gray-50 border mt-6 p-4 rounded-md h-60 overflow-auto text-sm text-gray-800 whitespace-pre-wrap">
// //       {content}
// //     </div>
// //   );
// // }

// // export default function NewsletterDisplay({ content }: { content: string }) {
// //   return (
// //     <div
// //       className="bg-gray-50 border mt-6 p-4 rounded-md text-sm text-gray-800"
// //       dangerouslySetInnerHTML={{ __html: content }}
      
// //     />
    
// //   );
// // }

// export default function NewsletterDisplay({ content }: { content: string }) {
//   return (
//     <div className="mt-6">
//       {/* Rendu HTML de la newsletter */}
//       <div
//         className="bg-gray-50 border p-4 rounded-md text-sm text-gray-800"
//         dangerouslySetInnerHTML={{ __html: content }}
//       />

//       {/* Boutons de téléchargement */}
//       <div className="flex gap-4 mt-4">
//         <a
//           href="http://localhost:8000/download-html"
//           target="_blank"
//           rel="noopener noreferrer"
//           className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
//         >
//           Télécharger la Newslatter
//         </a>
//       </div>
//     </div>
//   );
// }
